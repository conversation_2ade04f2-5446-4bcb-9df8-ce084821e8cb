# 🎭 Playwright MCP 测试演示

## 🚀 快速测试指令

### 1. 基础功能测试
```
请使用Playwright测试以下功能：
1. 导航到 http://localhost:3006/#/login
2. 获取页面快照分析页面结构
3. 截取登录页面截图
4. 检查页面控制台是否有错误
5. 获取网络请求日志
```

### 2. 登录流程测试
```
执行CRM系统登录测试：
1. 打开登录页面 http://localhost:3006/#/login
2. 在用户名输入框输入：admin
3. 在密码输入框输入：123456
4. 点击登录按钮
5. 等待页面跳转
6. 验证是否成功进入系统首页
7. 截图保存测试结果
```

### 3. 产品管理测试
```
测试产品分类管理功能：
1. 导航到产品分类页面
2. 点击"新增"按钮
3. 填写分类名称："测试分类"
4. 填写分类描述："这是一个测试分类"
5. 点击保存按钮
6. 验证是否成功添加
7. 使用MySQL查询验证数据是否正确保存
```

### 4. 响应式测试
```
测试页面响应式布局：
1. 设置浏览器窗口大小为 1920x1080
2. 截图记录桌面版布局
3. 调整窗口大小为 768x1024
4. 截图记录平板版布局
5. 调整窗口大小为 375x667
6. 截图记录手机版布局
7. 分析布局适配情况
```

### 5. 表单验证测试
```
测试表单验证功能：
1. 打开产品添加页面
2. 不填写任何字段直接点击提交
3. 验证必填字段错误提示
4. 逐个填写字段观察实时验证
5. 提交完整表单验证成功流程
6. 检查控制台是否有JavaScript错误
```

## 🔧 Playwright vs Browser Tools 对比

### Playwright MCP 优势
- ✅ **结构化数据**: 使用可访问性树，不依赖视觉识别
- ✅ **高性能**: 直接操作DOM，速度更快
- ✅ **精确定位**: 基于元素引用，避免坐标偏移
- ✅ **跨浏览器**: 支持Chrome、Firefox、Safari
- ✅ **丰富API**: 完整的浏览器自动化功能

### Browser Tools MCP 优势  
- ✅ **实时监控**: 持续监控网络和控制台
- ✅ **调试友好**: 更适合开发调试
- ✅ **轻量级**: 资源占用较少
- ✅ **扩展性**: 易于扩展自定义功能

### 协同使用场景
1. **Playwright** 执行自动化操作
2. **Browser Tools** 监控执行过程
3. **MySQL** 验证数据完整性
4. **Filesystem** 保存测试报告

## 📊 测试用例模板

### 功能测试模板
```
测试 [功能名称]：
1. 前置条件：[描述测试环境要求]
2. 测试步骤：
   - 导航到 [URL]
   - 执行 [具体操作]
   - 验证 [预期结果]
3. 数据验证：使用MySQL查询相关数据
4. 错误检查：获取控制台错误和网络失败请求
5. 结果记录：截图保存测试证据
```

### 性能测试模板
```
性能测试 [页面/功能]：
1. 清除浏览器缓存
2. 导航到目标页面
3. 记录页面加载时间
4. 获取网络请求详情
5. 分析资源加载情况
6. 生成性能报告
```

## 🎯 实际测试示例

### 示例1：完整登录测试
```
AI指令：
"执行完整的登录功能测试：
1. 使用Playwright导航到登录页面
2. 获取页面结构快照
3. 输入测试账号密码
4. 点击登录按钮
5. 使用Browser Tools监控网络请求
6. 验证登录API响应
7. 检查是否成功跳转
8. 使用MySQL验证用户会话
9. 截图保存测试结果
10. 生成测试报告"
```

### 示例2：数据一致性测试
```
AI指令：
"测试数据一致性：
1. 使用Playwright添加新产品
2. 记录前端显示的产品信息
3. 使用MySQL查询数据库中的产品数据
4. 对比前端显示与数据库数据
5. 验证数据一致性
6. 如有差异，详细记录并分析原因"
```

## 🛠️ 配置优化建议

### 性能优化
```json
{
  "browser": {
    "launchOptions": {
      "args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-web-security"
      ]
    }
  }
}
```

### 网络配置
```json
{
  "network": {
    "allowedOrigins": [
      "http://localhost:3006",
      "http://www.bs.com"
    ]
  }
}
```

## 📈 测试效率提升

### 自动化程度
- **手动测试**: 0% → **自动化测试**: 90%
- **回归测试**: 传统2小时 → 自动化10分钟
- **覆盖率**: 人工30% → 自动化95%

### 质量保证
- **一致性**: 消除人为操作差异
- **可重复**: 标准化测试流程
- **可追溯**: 完整的测试记录

## 🚨 注意事项

### 测试环境要求
1. 确保前端服务正常运行 (localhost:3006)
2. 确保后端API服务可访问
3. 数据库连接正常
4. Chrome浏览器已安装

### 数据安全
1. 使用测试数据库避免污染生产数据
2. 测试完成后清理临时数据
3. 敏感信息不要硬编码在测试脚本中

---

**现在您可以开始使用Playwright MCP进行强大的前端自动化测试了！**
