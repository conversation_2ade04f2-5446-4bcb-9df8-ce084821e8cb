# 统一工作流对接详细技术文档

## 📋 文档概述

**文档版本：** v2.0  
**更新日期：** 2025-01-24  
**适用范围：** 所有业务模块与工作流系统的集成  
**技术栈：** ThinkPHP8 + 动态工厂模式 + FormServiceInterface  

## 🏗️ 架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Business Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  DailyPriceOrderService  │  CrmContractService  │  其他业务   │
│  ├─ submitApproval()     │  ├─ submitApproval() │  Service   │
│  ├─ recallApproval()     │  ├─ recallApproval() │            │
│  └─ voidApproval()       │  └─ voidApproval()   │            │
├─────────────────────────────────────────────────────────────┤
│                   工作流适配层 (Adapter Layer)                │
├─────────────────────────────────────────────────────────────┤
│  DailyPriceOrderWorkflowService  │  CrmContractWorkflowService │
│  (继承 WorkflowableService)      │  (继承 WorkflowableService)  │
├─────────────────────────────────────────────────────────────┤
│                   统一接口层 (Interface Layer)               │
├─────────────────────────────────────────────────────────────┤
│  FormServiceInterface (7个标准方法)                          │
│  ├─ getFormData()     ├─ saveForm()      ├─ updateForm()    │
│  ├─ deleteForm()      ├─ updateFormStatus()                 │
│  ├─ getInstanceTitle() └─ validateFormData()                │
├─────────────────────────────────────────────────────────────┤
│                   动态工厂层 (Factory Layer)                 │
├─────────────────────────────────────────────────────────────┤
│  DynamicWorkflowFactory (基于workflow_type表动态创建)        │
│  ├─ createFormServiceByBusinessCode()                       │
│  ├─ createServiceByBusinessCode()                           │
│  └─ createModelByBusinessCode()                             │
├─────────────────────────────────────────────────────────────┤
│                   工作流引擎层 (Engine Layer)                │
├─────────────────────────────────────────────────────────────┤
│  WorkflowEngineService  │  BusinessWorkflowService          │
│  ├─ startWorkflow()     │  ├─ createWorkflowForBusiness()   │
│  ├─ processApproval()   │  ├─ syncWorkflowStatus()          │
│  ├─ terminateWorkflow() │  └─ triggerBusinessAfterProcess() │
│  └─ voidApprovedInstance()                                  │
├─────────────────────────────────────────────────────────────┤
│                   数据持久层 (Data Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  workflow_instance  │  workflow_task  │  workflow_history   │
│  workflow_type      │  workflow_definition                  │
│  业务表 (daily_price_order, crm_contract, etc.)            │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心组件说明

#### 2.1 FormServiceInterface (统一接口)
```php
interface FormServiceInterface
{
    // 数据操作方法
    public function getFormData(int $id): array;
    public function saveForm(array $data): array;
    public function updateForm(int $id, array $data): bool;
    public function deleteForm(int $id): bool;
    
    // 工作流集成方法
    public function updateFormStatus(int $id, int $status, array $extra = []): bool;
    public function getInstanceTitle($formData): string;
    public function validateFormData(array $data, string $scene = 'create'): array;
}
```

#### 2.2 DynamicWorkflowFactory (动态工厂)
```php
class DynamicWorkflowFactory
{
    // 基于workflow_type表动态创建FormService
    public static function createFormServiceByBusinessCode(string $businessCode): ?FormServiceInterface;
    
    // 基于workflow_type表动态创建Service
    public static function createServiceByBusinessCode(string $businessCode): ?BaseService;
    
    // 基于workflow_type表动态创建Model
    public static function createModelByBusinessCode(string $businessCode): ?BaseModel;
}
```

#### 2.3 WorkflowableService (业务基类)
```php
abstract class WorkflowableService
{
    // 抽象方法 - 子类必须实现
    abstract protected function getBusinessCode(): string;
    abstract protected function getApprovalTitle(object $record): string;
    
    // 核心工作流方法
    public function submitApproval(int $businessId, array $options = []): array;
    public function withdrawApproval(int $businessId): array;
    public function voidApproval(int $businessId, string $reason = ''): array;
}
```

## 🔧 核心接口详解

### 1. 工作流生命周期管理

#### 1.1 提交审批 (submitApproval)
```php
/**
 * 提交审批流程
 * 
 * @param int $businessId 业务记录ID
 * @param array $options 可选参数
 * @return array 操作结果
 */
public function submitApproval(int $businessId, array $options = []): array
{
    // 1. 获取业务记录并验证
    $record = $this->getBusinessRecord($businessId);
    $this->validateForApproval($record);
    
    // 2. 使用动态工厂创建FormService
    $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($this->getBusinessCode());
    
    // 3. 创建工作流实例
    $businessWorkflowService = new BusinessWorkflowService();
    $result = $businessWorkflowService->createWorkflowForBusiness($workflowParams);
    
    // 4. 更新业务状态
    $this->updateBusinessStatus($businessId, [
        'approval_status' => WorkflowStatusConstant::STATUS_PROCESSING,
        'workflow_instance_id' => $result['instance_id'],
        'submit_time' => date('Y-m-d H:i:s')
    ]);
    
    return ['success' => true, 'instance_id' => $result['instance_id']];
}
```

#### 1.2 撤回审批 (withdrawApproval)
```php
/**
 * 撤回审批流程
 * 
 * @param int $businessId 业务记录ID
 * @return array 操作结果
 */
public function withdrawApproval(int $businessId): array
{
    // 1. 获取业务记录
    $record = $this->getBusinessRecord($businessId);
    
    // 2. 验证是否可以撤回
    if ($record->approval_status !== WorkflowStatusConstant::STATUS_PROCESSING) {
        throw new BusinessException('只有审批中的记录才能撤回');
    }
    
    // 3. 调用工作流引擎撤回
    $workflowService = WorkflowInstanceService::getInstance();
    $result = $workflowService->recallApplication($record->workflow_instance_id);
    
    // 4. 更新业务状态
    $this->updateBusinessStatus($businessId, [
        'approval_status' => WorkflowStatusConstant::STATUS_RECALLED,
        'recall_time' => date('Y-m-d H:i:s')
    ]);
    
    return ['success' => true, 'message' => '撤回成功'];
}
```

#### 1.3 作废审批 (voidApproval)
```php
/**
 * 作废已通过的审批
 * 
 * @param int $businessId 业务记录ID
 * @param string $reason 作废原因
 * @return array 操作结果
 */
public function voidApproval(int $businessId, string $reason = ''): array
{
    // 1. 获取业务记录
    $record = $this->getBusinessRecord($businessId);
    
    // 2. 验证是否可以作废
    if ($record->approval_status !== WorkflowStatusConstant::STATUS_COMPLETED) {
        throw new BusinessException('只有已通过的记录才能作废');
    }
    
    // 3. 调用工作流引擎作废
    $engineService = WorkflowEngineService::getInstance();
    $result = $engineService->voidApprovedInstance($record->workflow_instance_id, $reason);
    
    // 4. 更新业务状态
    $this->updateBusinessStatus($businessId, [
        'approval_status' => WorkflowStatusConstant::STATUS_VOID,
        'void_time' => date('Y-m-d H:i:s'),
        'void_reason' => $reason
    ]);
    
    return ['success' => true, 'message' => '作废成功'];
}
```

### 2. 状态同步机制

#### 2.1 工作流状态常量
```php
class WorkflowStatusConstant
{
    // 通用状态定义
    const STATUS_DRAFT = 0;        // 草稿
    const STATUS_PROCESSING = 1;   // 审批中
    const STATUS_COMPLETED = 2;    // 已通过
    const STATUS_REJECTED = 3;     // 已拒绝
    const STATUS_TERMINATED = 4;   // 已终止
    const STATUS_RECALLED = 5;     // 已撤回
    const STATUS_VOID = 6;         // 已作废
    
    // 向后兼容别名
    const SAVED = self::STATUS_DRAFT;
    const APPROVING = self::STATUS_PROCESSING;
    const APPROVED = self::STATUS_COMPLETED;
    const REJECTED = self::STATUS_REJECTED;
    const TERMINATED = self::STATUS_TERMINATED;
    const RECALLED = self::STATUS_RECALLED;
}
```

#### 2.2 状态同步流程
```php
/**
 * 工作流状态变更时自动同步业务表状态
 */
public function syncWorkflowStatus(int $instanceId, int $newStatus, array $extra = []): bool
{
    // 1. 获取工作流实例
    $instance = WorkflowInstance::find($instanceId);
    
    // 2. 使用动态工厂获取FormService
    $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance->business_code);
    
    // 3. 调用FormService更新业务状态
    $result = $formService->updateFormStatus($instance->business_id, $newStatus, $extra);
    
    // 4. 触发业务后处理
    $this->triggerBusinessAfterProcess($instance, $newStatus, $extra);
    
    return $result;
}
```

## 📊 数据库设计

### 1. 核心表结构

#### 1.1 workflow_type (工作流类型表)
```sql
CREATE TABLE `workflow_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '类型名称',
  `module_code` varchar(50) NOT NULL COMMENT '模块代码',
  `business_code` varchar(100) NOT NULL COMMENT '业务代码',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_code` (`business_code`)
) COMMENT='工作流类型配置表';
```

#### 1.2 业务表标准字段
```sql
-- 所有业务表都应包含以下工作流相关字段
ALTER TABLE `business_table` ADD COLUMN `approval_status` tinyint(1) DEFAULT 0 COMMENT '审批状态';
ALTER TABLE `business_table` ADD COLUMN `workflow_instance_id` int(11) DEFAULT 0 COMMENT '工作流实例ID';
ALTER TABLE `business_table` ADD COLUMN `submit_time` datetime NULL COMMENT '提交时间';
ALTER TABLE `business_table` ADD COLUMN `approval_time` datetime NULL COMMENT '审批时间';
ALTER TABLE `business_table` ADD COLUMN `submitter_id` int(11) DEFAULT 0 COMMENT '提交人ID';
ALTER TABLE `business_table` ADD COLUMN `void_time` datetime NULL COMMENT '作废时间';
ALTER TABLE `business_table` ADD COLUMN `void_reason` varchar(500) NULL COMMENT '作废原因';
```

### 2. 配置数据示例

#### 2.1 workflow_type配置
```sql
INSERT INTO workflow_type (name, module_code, business_code, status) VALUES
('请假申请', 'hr', 'hr_leave', 1),
('出差申请', 'hr', 'hr_travel', 1),
('合同审批', 'crm', 'crm_contract', 1),
('回款审批', 'crm', 'crm_contract_receivable', 1),
('每日报价审批', 'daily', 'daily_price_order', 1);
```

## 🚀 新业务接入指南

### 1. 标准接入流程

#### 步骤1：创建业务Service实现FormServiceInterface
```php
class NewBusinessService extends BaseService implements FormServiceInterface
{
    use CrudServiceTrait;
    
    public function __construct()
    {
        $this->model = new NewBusiness();
        parent::__construct();
    }
    
    // 实现所有7个FormServiceInterface方法
    public function getFormData(int $id): array { /* 实现 */ }
    public function saveForm(array $data): array { /* 实现 */ }
    public function updateForm(int $id, array $data): bool { /* 实现 */ }
    public function deleteForm(int $id): bool { /* 实现 */ }
    public function updateFormStatus(int $id, int $status, array $extra = []): bool { /* 实现 */ }
    public function getInstanceTitle($formData): string { /* 实现 */ }
    public function validateFormData(array $data, string $scene = 'create'): array { /* 实现 */ }
}
```

#### 步骤2：创建工作流适配Service
```php
class NewBusinessWorkflowService extends WorkflowableService
{
    protected function getBusinessCode(): string
    {
        return 'new_module_new_business';
    }
    
    protected function getApprovalTitle(object $record): string
    {
        return "新业务审批-{$record->name}";
    }
    
    protected function validateForApproval(object $record): void
    {
        parent::validateForApproval($record);
        // 添加业务特定验证
    }
    
    protected function getBusinessRecord(int $businessId): object
    {
        return NewBusiness::find($businessId);
    }
}
```

#### 步骤3：配置workflow_type表
```sql
INSERT INTO workflow_type (name, module_code, business_code, status)
VALUES ('新业务审批', 'new_module', 'new_module_new_business', 1);
```

#### 步骤4：验证集成
```bash
php think test:form-service-interface
```

### 2. 业务Controller集成示例
```php
class NewBusinessController extends BaseController
{
    protected NewBusinessService $service;
    protected NewBusinessWorkflowService $workflowService;
    
    public function __construct()
    {
        $this->service = new NewBusinessService();
        $this->workflowService = new NewBusinessWorkflowService();
    }
    
    /**
     * 提交审批
     */
    public function submitApproval()
    {
        $id = $this->request->param('id/d');
        $result = $this->workflowService->submitApproval($id);
        return $this->success($result['message'] ?? '提交成功');
    }
    
    /**
     * 撤回审批
     */
    public function withdrawApproval()
    {
        $id = $this->request->param('id/d');
        $result = $this->workflowService->withdrawApproval($id);
        return $this->success($result['message'] ?? '撤回成功');
    }
    
    /**
     * 作废审批
     */
    public function voidApproval()
    {
        $id = $this->request->param('id/d');
        $reason = $this->request->param('reason', '');
        $result = $this->workflowService->voidApproval($id, $reason);
        return $this->success($result['message'] ?? '作废成功');
    }
}
```

## 🧪 测试验证

### 1. 接口实现完整性测试
```bash
# 检查所有Service的FormServiceInterface实现
php think test:form-service-interface
```

### 2. 统一模型保存方法测试
```bash
# 测试saveByCreate、saveByUpdate、batchSave方法
php think test:unified-model-save
```

### 3. 工作流集成功能测试
```bash
# 测试完整的工作流生命周期
php think test:workflow-integration
```

## ⚠️ 注意事项

### 1. 开发规范
- 所有业务Service必须实现FormServiceInterface的7个方法
- 工作流适配Service必须继承WorkflowableService
- 使用统一的状态常量WorkflowStatusConstant
- 业务表必须包含标准的工作流字段

### 2. 错误处理
- 使用BusinessException抛出业务异常
- 记录详细的操作日志
- 提供友好的错误提示信息

### 3. 性能优化
- 使用事务保证数据一致性
- 批量操作使用batchSave方法
- 合理使用缓存机制

### 4. 安全考虑
- 验证用户权限
- 防止状态篡改
- 记录操作审计日志

## 📚 相关文档

- [FormServiceInterface统一化实施完成报告](./FormServiceInterface统一化实施完成报告.md)
- [第4阶段统一模型层保存方法实施完成报告](./第4阶段统一模型层保存方法实施完成报告.md)
- [工作流业务集成实施指南](./工作流业务集成实施指南.md)
- [工作流业务集成架构设计方案](./工作流业务集成架构设计方案.md)

---

**文档维护者：** 开发团队  
**最后更新：** 2025-01-24  
**版本历史：** v2.0 - 统一工作流对接技术文档
