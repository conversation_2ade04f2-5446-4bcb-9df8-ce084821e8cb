<script setup lang="ts">
  import {
    ElMessage,
    ElMessageBox,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElPagination,
    ElEmpty,
    ElButton,
    ElCard,
    ElTag,
    ElTabs,
    ElTabPane
  } from 'element-plus'
  import { DailyPriceOrderApi } from '@/api/daily/dailyPriceOrder'
  import { ApiStatus } from '@/utils/http/status'
  import { MoreFilled, Plus, Refresh } from '@element-plus/icons-vue'

  import EnhancedFormDialog from './enhanced-form-dialog.vue'
  import DetailDialog from './detail-dialog.vue'
  import ArtTableFullScreen from '@/components/core/tables/ArtTableFullScreen.vue'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'

  // 卡片数据与分页
  const cardData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(12) // 卡片式布局，每页显示更多
  const total = ref(0)

  // tabs状态 - 默认显示草稿
  const activeTab = ref('draft')

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  const { hasButtonPermission, hasButtonPermissionReactive } = useCustomerPermission()

  // 响应式权限检查
  const canAddOrEdit = computed(() =>
    hasButtonPermissionReactive.value('daily:daily_price_order:add') ||
    hasButtonPermissionReactive.value('daily:daily_price_order:edit')
  )

  const canViewDetail = computed(() =>
    hasButtonPermissionReactive.value('daily:daily_price_order:detail')
  )

  onMounted(() => {
    getCardData()
  })

  // 监听权限变化，设置默认tab
  watch([canAddOrEdit, canViewDetail], () => {
    // 如果没有新增/编辑权限，默认显示已生效tab
    if (!canAddOrEdit.value && canViewDetail.value) {
      activeTab.value = 'approved'
    }
  }, { immediate: true })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getCardData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getCardData()
  }

  // 获取卡片数据
  const getCardData = async () => {
    loading.value = true
    try {
      const params: any = {
        page: currentPage.value,
        limit: pageSize.value
      }

      // 根据tab状态过滤数据
      if (activeTab.value === 'draft') {
        params.approval_status = 0 // 草稿
      } else if (activeTab.value === 'processing') {
        params.approval_status = 1 // 审批中
      } else if (activeTab.value === 'approved') {
        params.approval_status = 2 // 已通过
      } else {
        // other: 其他状态 (3,4,5,6) - 不传approval_status，后端会返回非已通过的数据
        // 根据后端逻辑，不传approval_status参数时会返回非已通过的数据
      }

      const res = await DailyPriceOrderApi.list(params)

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 12
        cardData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    getCardData()
  }

  // 切换tab
  const handleTabChange = (tab: any) => {
    activeTab.value = tab as string
    currentPage.value = 1 // 重置页码
    getCardData()
  }

  // 显示详情
  const showDetail = async (item: any) => {
    // 只有已通过的才能查看详情
    /*if (item.approval_status !== 2) {
      ElMessage.warning('只有已通过的报价单才能查看详情')
      return
    }*/

    try {
      loading.value = true
      const res = await DailyPriceOrderApi.detail(item.id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  // 这个函数已经在下面的条件块中定义了

  // 优化后的权限控制方法
  const getAvailableActions = (item: any) => {
    const actions: string[] = []

    // 已撤回状态不能编辑
    if (item.approval_status === 5) {
      return actions
    }

    // 编辑权限：草稿、已拒绝状态
    if (
      hasButtonPermission('daily:daily_price_order:edit') &&
      [0, 3].includes(item.approval_status)
    ) {
      actions.push('edit')
    }

    // 提交审批：草稿状态且有明细
    if (
      hasButtonPermission('daily:daily_price_order:submitApproval') &&
      item.approval_status === 0 &&
      (item.total_items || 0) > 0
    ) {
      actions.push('submitApproval')
    }

    // 撤回审批：审批中状态
    if (
      hasButtonPermission('daily:daily_price_order:recallApproval') &&
      item.approval_status === 1
    ) {
      actions.push('withdrawApproval')
    }

    // 作废：已通过状态
    if (hasButtonPermission('daily:daily_price_order:voidOrder') && item.approval_status === 2) {
      actions.push('voidOrder')
    }

    // 删除：草稿状态
    if (hasButtonPermission('daily:daily_price_order:delete') && item.approval_status === 0) {
      actions.push('delete')
    }

    return actions
  }

  // 审批操作方法
  const handleSubmitApproval = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要提交审批吗？', '确认提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await DailyPriceOrderApi.submitApproval(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('提交审批成功')
        await getCardData()
      }
    } catch (error) {
      // 用户取消
    } finally {
      loading.value = false
    }
  }

  const handleWithdrawApproval = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要撤回审批吗？', '确认撤回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await DailyPriceOrderApi.recallApproval(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('撤回审批成功')
        await getCardData()
      }
    } catch (error) {
      // 用户取消
    } finally {
      loading.value = false
    }
  }

  const handleVoidOrder = async (id: number) => {
    try {
      const { value: reason } = await ElMessageBox.prompt('请输入作废原因', '作废报价单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '请输入作废原因'
          }
          return true
        }
      })

      loading.value = true
      const res = await DailyPriceOrderApi.voidOrder(id, reason)

      if (res.code === ApiStatus.success) {
        ElMessage.success('作废成功')
        await getCardData()
      }
    } catch (error) {
      // 用户取消
    } finally {
      loading.value = false
    }
  }

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await DailyPriceOrderApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getCardData()
      }
    } catch (error) {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 导入导出对话框引用
  // const importExportDialogRef = ref()

  // 显示导入对话框
  /*const showImportDialog = () => {
    importExportDialogRef.value?.showDialog('import')
  }*/

  // 导入导出成功回调
  /*const handleImportExportSuccess = () => {
    getTableData()
  }

  // 显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }*/

  // 表单对话框引用
  const enhancedFormDialogRef = ref()

  // 显示表单对话框
  /* const showFormDialog = (type: string, id?: number) => {
     formDialogRef.value?.showDialog(type, id)
   }*/

  // 显示增强版表单对话框
  const showEnhancedFormDialog = (type: string, id?: number) => {
    enhancedFormDialogRef.value?.showDialog(type, id)
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getCardData()
  }

  // 工具方法
  const getStatusText = (status: number) => {
    const statusMap: Record<number, string> = {
      0: '草稿',
      1: '审批中',
      2: '已通过',
      3: '已拒绝',
      4: '已终止',
      5: '已撤回',
      6: '已作废'
    }
    return statusMap[status] || '未知'
  }

  const getStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      0: 'info',
      1: 'warning',
      2: 'success',
      3: 'danger',
      4: 'danger',
      5: 'info',
      6: 'danger'
    }
    return typeMap[status] || 'info'
  }

  const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="daily-price-order-page">
      <div class="page-header">
        <div class="header-left">
          <h2 class="page-title">每日报价管理</h2>
          <p class="page-desc">管理每日产品报价信息</p>
        </div>
        <div class="header-right">
          <ElButton
            v-if="hasButtonPermission('daily:daily_price_order:add')"
            type="primary"
            @click="showEnhancedFormDialog('add')"
          >
            <el-icon>
              <Plus />
            </el-icon>
            新增报价单
          </ElButton>
          <ElButton @click="handleRefresh">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </ElButton>
        </div>
      </div>

      <!-- Tabs -->
      <div class="tabs-container">
        <ElTabs v-model="activeTab" @tab-change="handleTabChange">
          <ElTabPane
            v-if="canAddOrEdit"
            label="草稿"
            name="draft"
          />
          <ElTabPane
            v-if="canAddOrEdit"
            label="审批中"
            name="processing"
          />
          <ElTabPane
            v-if="canViewDetail"
            label="已生效"
            name="approved"
          />
          <ElTabPane
            v-if="canAddOrEdit"
            label="其他"
            name="other"
          />
        </ElTabs>
      </div>

      <!-- 卡片列表 -->
      <div class="card-container" v-loading="loading">
        <div class="card-grid">
          <ElCard v-for="item in cardData" :key="item.id" class="price-order-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="header-info">
                  <h3 class="order-title">{{ item.title || `${item.price_date}报价` }}</h3>
                  <span class="order-date">{{ item.price_date }}</span>
                </div>
                <div class="header-status">
                  <ElTag :type="getStatusType(item.approval_status)" effect="dark">
                    {{ getStatusText(item.approval_status) }}
                  </ElTag>
                </div>
              </div>
            </template>

            <div class="card-content">
              <div class="content-row">
                <div class="info-item">
                  <span class="label">产品总数:</span>
                  <span class="value">{{ item.total_items || 0 }} 个</span>
                </div>
                <div class="info-item">
                  <span class="label">创建时间:</span>
                  <span class="value">{{ formatDateTime(item.created_at) }}</span>
                </div>
              </div>

              <div class="content-row" v-if="item.submit_time">
                <div class="info-item">
                  <span class="label">提交时间:</span>
                  <span class="value">{{ formatDateTime(item.submit_time) }}</span>
                </div>
              </div>

              <div class="card-actions">
                <ElButton
                  type="primary"
                  v-if="hasButtonPermission('daily:daily_price_order:detail')"
                  size="small"
                  @click="showDetail(item)"
                >
                  详情
                </ElButton>

                <ElDropdown
                  v-if="getAvailableActions(item).length > 0"
                  trigger="hover"
                  placement="bottom-end"
                >
                  <ElButton size="small" class="more-btn">
                    更多
                    <el-icon class="el-icon--right">
                      <MoreFilled />
                    </el-icon>
                  </ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem
                        v-if="getAvailableActions(item).includes('edit')"
                        @click="showEnhancedFormDialog('edit', item.id)"
                      >
                        编辑
                      </ElDropdownItem>
                      <ElDropdownItem
                        v-if="getAvailableActions(item).includes('submitApproval')"
                        @click="handleSubmitApproval(item.id)"
                      >
                        提交审批
                      </ElDropdownItem>
                      <ElDropdownItem
                        v-if="getAvailableActions(item).includes('withdrawApproval')"
                        @click="handleWithdrawApproval(item.id)"
                      >
                        撤回审批
                      </ElDropdownItem>
                      <ElDropdownItem
                        v-if="getAvailableActions(item).includes('voidOrder')"
                        @click="handleVoidOrder(item.id)"
                        divided
                      >
                        作废
                      </ElDropdownItem>
                      <ElDropdownItem
                        v-if="getAvailableActions(item).includes('delete')"
                        @click="handleDelete(item.id)"
                        divided
                      >
                        删除
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </div>
          </ElCard>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && cardData.length === 0" class="empty-state">
          <el-empty description="暂无报价单数据">
            <ElButton type="primary" @click="showEnhancedFormDialog('add')">
              新增报价单
            </ElButton>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <ElPagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 详情对话框 -->
      <DetailDialog v-model="detailDialogVisible" :detail-data="detailData" :loading="loading" />

      <!-- 增强版表单对话框 -->
      <EnhancedFormDialog ref="enhancedFormDialogRef" @success="handleFormSubmitSuccess" />
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .daily-price-order-page {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 500px;
  }

  .tabs-container {
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-left {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
      }

      .page-desc {
        margin: 0;
        color: #909399;
        font-size: 14px;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .card-container {
    background-color: #fff;
    min-height: 550px;
    padding: 15px;
    border-radius: 8px 8px 0 0;
  }

  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
  }

  .price-order-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0;

      .header-info {
        flex: 1;

        .order-title {
          margin: 0 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          line-height: 1.4;
        }

        .order-date {
          color: #909399;
          font-size: 14px;
        }
      }

      .header-status {
        margin-left: 12px;
      }
    }

    .card-content {
      .content-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        &:last-of-type {
          margin-bottom: 16px;
        }

        .info-item {
          flex: 1;

          .label {
            color: #909399;
            font-size: 13px;
            margin-right: 8px;
          }

          .value {
            color: #303133;
            font-size: 13px;
            font-weight: 500;
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 8px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;

        .more-btn {
          margin-left: auto;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 10px 20px;
    background: white;
    border-radius: 0 0 8px 8px;
    //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 详情对话框样式 */
  :deep(.detail-dialog) {
    .el-dialog__body {
      height: 500px !important;
      padding: 20px !important;
      overflow: hidden !important;
    }

    .detail-content {
      height: 100%;
      overflow-y: auto;
      padding-right: 10px;
    }

    /* 滚动条样式优化 */
    .detail-content::-webkit-scrollbar {
      width: 6px;
    }

    .detail-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .detail-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .detail-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .daily-price-order-page {
      padding: 12px;
    }

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .header-right {
        justify-content: center;
      }
    }

    .card-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .price-order-card {
      .card-content {
        .content-row {
          flex-direction: column;
          gap: 8px;
        }

        .card-actions {
          flex-direction: column;

          .more-btn {
            margin-left: 0;
          }
        }
      }
    }
  }
</style>
