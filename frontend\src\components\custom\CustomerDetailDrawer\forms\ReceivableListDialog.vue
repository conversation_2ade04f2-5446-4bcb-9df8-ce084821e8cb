<template>
  <el-dialog v-model="visible" title="回款记录" width="60%" :close-on-click-modal="false">
    <div class="receivable-list">
      <div class="list-header">
        <div class="header-info">
          <span class="contract-name">{{ contractData?.contract_name }}</span>
          <span class="contract-amount"
            >合同金额：¥{{
              (typeof contractData?.contract_amount === 'string'
                ? parseFloat(contractData.contract_amount)
                : contractData?.contract_amount
              )?.toLocaleString() || '0'
            }}</span
          >
          <span class="paid-amount"
            >已付金额：¥{{
              (typeof contractData?.paid_amount === 'string'
                ? parseFloat(contractData.paid_amount)
                : contractData?.paid_amount
              )?.toLocaleString() || '0'
            }}</span
          >
        </div>
        <div class="header-actions">
          <el-button type="default" size="small" :loading="loading" @click="handleRefresh">
            <el-icon>
              <Refresh />
            </el-icon>
            刷新
          </el-button>
          <el-button type="primary" size="small" @click="handleAddReceivable">
            <el-icon>
              <Plus />
            </el-icon>
            新增回款
          </el-button>
        </div>
      </div>

      <el-table v-loading="loading" :data="receivableList" stripe style="width: 100%">
        <!--        <el-table-column prop="receivable_number" label="回款编号" width="150" />-->
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="amount" label="回款金额" width="120">
          <template #default="{ row }">
            ¥{{
              (typeof row.amount === 'string'
                ? parseFloat(row.amount)
                : row.amount
              )?.toLocaleString() || '0'
            }}
          </template>
        </el-table-column>
        <el-table-column prop="received_date" label="回款日期" width="120" />
        <el-table-column prop="payment_method" label="付款方式" width="100" />
        <!--        <el-table-column prop="bank_name" label="收款银行" width="120" show-overflow-tooltip />-->
        <!--        <el-table-column prop="bank_account" label="银行账号" width="140" show-overflow-tooltip />-->
        <el-table-column prop="approval_status" label="审批状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row)">
              {{ getStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <ArtButtonTable text="详情" @click="handleViewReceivable(row)" />
            <!-- 只有草稿状态(0)或已拒绝状态(3)的回款可以编辑 -->
            <!--            <ArtButtonTable
                          v-if="canEditReceivable(row)"
                          text="编辑"
                          type="edit"
                          @click="handleEditReceivable(row)"
                        />-->
            <el-dropdown v-if="hasDropdownActions(row)">
              <ArtButtonTable text="更多" type="more" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="canEditReceivable(row)"
                    @click="handleEditReceivable(row)"
                  >
                    编辑
                  </el-dropdown-item>
                  <!-- 提交审批：草稿状态(0)或已拒绝状态(3)可以提交 -->
                  <el-dropdown-item
                    v-if="canSubmitReceivable(row)"
                    @click="handleSubmitReceivable(row)"
                  >
                    提交
                  </el-dropdown-item>
                  <!-- 撤回审批：审批中状态(1)可以撤回 -->
                  <el-dropdown-item
                    v-if="canWithdrawReceivable(row)"
                    @click="handleWithdrawReceivable(row)"
                  >
                    撤回
                  </el-dropdown-item>
                  <!-- 删除：只有草稿状态(0)或已拒绝状态(3)可以删除 -->
                  <el-dropdown-item
                    v-if="canDeleteReceivable(row)"
                    @click="handleDeleteReceivable(row)"
                    divided
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadReceivableList"
          @current-change="loadReceivableList"
        />
      </div>
    </div>

    <!-- 回款表单对话框 -->
    <ReceivableFormDialog
      v-model="showReceivableForm"
      :customer-id="customerId"
      :contract-id="contractData?.id"
      :receivable-data="currentReceivable"
      @success="handleFormSuccess"
    />

    <!-- 回款详情对话框 -->
    <ReceivableDetailDialog v-model="showReceivableDetail" :receivable-id="currentReceivableId" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { Plus, Refresh } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import ReceivableFormDialog from './ReceivableFormDialog.vue'
  import ReceivableDetailDialog from './ReceivableDetailDialog.vue'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  // import { useCustomerPermission } from '@/composables/useCustomerPermission'
  // import { BgColorEnum } from '@/enums/appEnum'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number
    contractData?: any
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: 0,
    contractData: null
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
  }>()

  // 权限验证
  // const { hasButtonPermission } = useCustomerPermission()

  // 响应式数据
  const loading = ref(false)
  const receivableList = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const showReceivableForm = ref(false)
  const currentReceivable = ref<any>(null)
  const showReceivableDetail = ref(false)
  const currentReceivableId = ref<number>(0)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 监听对话框显示状态
  watch(visible, (newVal) => {
    if (newVal && props.contractData?.id) {
      loadReceivableList()
    }
  })

  // 加载回款列表
  const loadReceivableList = async () => {
    if (!props.contractData?.id) return
    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getReceivableList(props.contractData.id, {
        page: currentPage.value,
        limit: pageSize.value
      })

      if (res.code === ApiStatus.success) {
        receivableList.value = res.data?.list || []
        total.value = res.data?.total || 0
      }
    } catch (error) {
      console.error('加载回款记录失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 刷新列表
  const handleRefresh = () => {
    loadReceivableList()
  }

  // 查看回款详情
  const handleViewReceivable = (receivable: any) => {
    currentReceivableId.value = receivable.id
    showReceivableDetail.value = true
  }

  // 新增回款
  const handleAddReceivable = () => {
    currentReceivable.value = null
    showReceivableForm.value = true
  }

  // 编辑回款
  const handleEditReceivable = async (receivable: any) => {
    try {
      loading.value = true
      const res = await CrmCustomerDetailApi.getReceivableDetail(receivable.id)

      if (res.code === ApiStatus.success) {
        currentReceivable.value = res.data
        showReceivableForm.value = true
      }
    } catch (error) {
      console.error('获取回款详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 删除回款
  const handleDeleteReceivable = async (receivable: any) => {
    try {
      await ElMessageBox.confirm(`确定要删除回款记录吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await CrmCustomerDetailApi.deleteReceivable(receivable.id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await loadReceivableList() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除回款记录失败:', error)
      }
    }
  }

  // 表单成功处理
  const handleFormSuccess = () => {
    loadReceivableList() // 刷新列表
  }

  // 获取状态标签类型
  const getStatusType = (
    receivable: any
  ): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    // 使用approval_status字段
    const status = receivable.approval_status ?? 0
    const statusMap: Record<number, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      0: 'info', // 草稿
      1: 'warning', // 审批中
      2: 'success', // 已通过
      3: 'danger', // 已拒绝
      4: 'info', // 已终止
      5: 'info', // 已撤回
      6: 'danger' // 已作废
    }
    return statusMap[status] || 'info'
  }

  // 获取状态文本
  const getStatusText = (receivable: any) => {
    // 使用approval_status字段
    const status = receivable.approval_status ?? 0
    const statusMap: Record<number, string> = {
      0: '草稿',
      1: '审批中',
      2: '已通过',
      3: '已拒绝',
      4: '已终止',
      5: '已撤回',
      6: '已作废'
    }
    return statusMap[status] || '草稿'
  }

  // 判断收款是否可以编辑
  const canEditReceivable = (receivable: any) => {
    // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以编辑
    const status = receivable.approval_status ?? 0
    return status === 0 || status === 3
  }

  // 判断收款是否可以删除
  const canDeleteReceivable = (receivable: any) => {
    // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以删除
    const status = receivable.approval_status ?? 0
    return status === 0 || status === 3
  }

  // 判断收款是否可以提交审批
  const canSubmitReceivable = (receivable: any) => {
    // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以提交
    const status = receivable.approval_status ?? 0
    return status === 0 || status === 3
  }

  // 判断收款是否可以撤回
  const canWithdrawReceivable = (receivable: any) => {
    // 使用approval_status字段，只有审批中状态(1)的收款可以撤回
    const status = receivable.approval_status ?? 0
    return status === 1
  }

  // 判断是否有下拉菜单操作
  const hasDropdownActions = (receivable: any) => {
    return (
      canSubmitReceivable(receivable) ||
      canWithdrawReceivable(receivable) ||
      canDeleteReceivable(receivable)
    )
  }

  // 处理提交收款审批
  const handleSubmitReceivable = async (receivable: any) => {
    try {
      await ElMessageBox.confirm(`确定要提交收款记录进行审批吗？`, '提交确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })

      const res = await CrmCustomerDetailApi.submitReceivableApproval(receivable.id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('提交审批成功')
        await loadReceivableList() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交收款审批失败:', error)
        ElMessage.error('提交审批失败')
      }
    }
  }

  // 处理撤回收款审批
  const handleWithdrawReceivable = async (receivable: any) => {
    try {
      await ElMessageBox.confirm(`确定要撤回收款记录的审批吗？`, '撤回确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await CrmCustomerDetailApi.withdrawReceivableApproval(receivable.id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('撤回成功')
        await loadReceivableList() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('撤回收款审批失败:', error)
      }
    }
  }
</script>

<style scoped lang="scss">
  .receivable-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #f5f7fa;
      border-radius: 4px;

      .header-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .contract-name {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .contract-amount,
        .paid-amount {
          font-size: 14px;
          color: #606266;
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;
        align-items: center;

        .el-button {
          height: 32px;
          padding: 8px 15px;
          font-size: 14px;
        }
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      text-align: right;
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
