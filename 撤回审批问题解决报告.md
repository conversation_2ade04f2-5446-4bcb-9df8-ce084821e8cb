# 撤回审批问题解决报告

## 🔍 问题现象

**用户反馈：** 撤回审批提示成功，但实际状态没有更新成功

## 🕵️ 问题排查过程

### 1. 初步分析
通过调试发现撤回审批功能涉及以下组件：
- `DailyPriceOrderService::recallApproval()` 
- `DailyPriceOrderWorkflowService::withdrawApproval()`
- `WorkflowableService::withdrawApproval()`
- `DynamicWorkflowFactory::createFormServiceByBusinessCode()`

### 2. 关键发现

#### 数据库连接不一致
- **ThinkPHP连接：** 远程数据库 `192.168.68.34` 的 `www_bs_com`
- **Python脚本连接：** 可能是本地数据库
- **影响：** 查看的数据和实际操作的数据不在同一个数据库

#### workflow_type配置问题
- **问题：** 远程数据库中缺少 `daily_price_order` 的 workflow_type 配置
- **结果：** `DynamicWorkflowFactory::createFormServiceByBusinessCode()` 失败
- **错误信息：** "FormServiceInterface not implemented for daily_price_order"

### 3. 错误调用链

```
DailyPriceOrderService::recallApproval()
  ↓
DailyPriceOrderWorkflowService::withdrawApproval()
  ↓  
WorkflowableService::withdrawApproval()
  ↓
WorkflowableService::submitApproval() [第73行]
  ↓
DynamicWorkflowFactory::createFormServiceByBusinessCode()
  ↓
❌ 抛出异常: "FormServiceInterface not implemented for daily_price_order"
```

## 🔧 解决方案

### 1. 已完成的修复

#### ✅ 添加workflow_type配置
```sql
INSERT INTO workflow_type (name, module_code, business_code, status, created_id, updated_id) 
VALUES ('每日报价审批', 'daily', 'daily_price_order', 1, 1, 1);
```

#### ✅ 清理重复记录
- 删除了重复的 daily_price_order 配置记录
- 保留了ID为26的记录

#### ✅ 验证Service实现
- 确认 `DailyPriceOrderService` 正确实现了 `FormServiceInterface`
- 确认动态工厂可以正常创建Service（在独立测试中）

### 2. 仍存在的问题

#### ❌ 动态工厂在运行时失败
- 独立测试：✅ 动态工厂创建成功
- 实际运行：❌ 动态工厂创建失败
- **可能原因：** 缓存、类加载顺序、或上下文环境差异

## 🎯 最终解决方案

### 方案1：绕过动态工厂（推荐）

修改 `WorkflowableService::withdrawApproval()` 方法，直接使用已知的Service实例而不是通过动态工厂创建：

```php
// 在 WorkflowableService::withdrawApproval() 方法中
// 替换第73行的动态工厂调用

// 原代码：
$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($this->getBusinessCode());

// 修改为：
$businessCode = $this->getBusinessCode();
if ($businessCode === 'daily_price_order') {
    $formService = new \app\daily\service\DailyPriceOrderService();
} else {
    $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
}
```

### 方案2：修复动态工厂缓存问题

检查并清理可能的缓存：
1. 清理ThinkPHP缓存
2. 检查是否有类加载缓存
3. 重启Web服务器

### 方案3：使用直接状态更新

在 `DailyPriceOrderService` 中添加直接状态更新方法：

```php
public function recallApprovalDirect(int $id): bool
{
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    if ($record->approval_status !== DailyPriceOrder::STATUS_PENDING) {
        throw new BusinessException('只有审批中的记录可以撤回');
    }
    
    // 直接更新状态
    return $record->saveByUpdate([
        'approval_status' => DailyPriceOrder::STATUS_RECALLED,
        'workflow_instance_id' => 0
    ]);
}
```

## 📋 实施建议

### 立即实施（方案1）
1. 修改 `WorkflowableService::withdrawApproval()` 方法
2. 为 daily_price_order 添加特殊处理
3. 测试撤回功能

### 后续优化
1. 深入调查动态工厂在运行时失败的根本原因
2. 完善错误处理和日志记录
3. 考虑重构工作流架构以减少对动态工厂的依赖

## 🧪 测试验证

### 测试步骤
1. 创建一个每日报价单
2. 提交审批
3. 执行撤回操作
4. 验证状态是否正确更新为"已撤回"

### 预期结果
- ✅ 撤回操作成功
- ✅ 状态正确更新为 `STATUS_RECALLED = 5`
- ✅ workflow_instance_id 清零
- ✅ 前端显示正确的状态

## 📊 影响评估

### 风险评估
- **低风险：** 方案1只影响daily_price_order的撤回功能
- **中等风险：** 方案2可能影响其他使用动态工厂的功能
- **高风险：** 方案3绕过了工作流引擎，可能导致数据不一致

### 推荐方案
**推荐使用方案1**，因为：
1. 风险最低
2. 实施简单
3. 不影响其他功能
4. 可以快速解决用户问题

## 📝 后续跟进

1. **监控：** 实施后监控撤回功能的使用情况
2. **优化：** 调查动态工厂问题的根本原因
3. **文档：** 更新工作流对接文档，添加故障排查指南
4. **测试：** 完善自动化测试覆盖撤回功能

---

**报告日期：** 2025-01-24  
**问题状态：** 已识别解决方案，待实施  
**优先级：** 高（影响用户正常使用）
