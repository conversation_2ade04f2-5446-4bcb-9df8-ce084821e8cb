<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 产品单位表路由
Route::group('api/crm/crm_product_unit', function () {
	Route::get('index', 'app\crm\controller\CrmProductUnitController@index');
	Route::get('options', 'app\crm\controller\CrmProductUnitController@options');
	Route::get('detail/:id', 'app\crm\controller\CrmProductUnitController@detail');
	Route::post('add', 'app\crm\controller\CrmProductUnitController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmProductUnitController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmProductUnitController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmProductUnitController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmProductUnitController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmProductUnitController@status');
	Route::post('import', 'app\crm\controller\CrmProductUnitController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmProductUnitController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmProductUnitController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmProductUnitController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);