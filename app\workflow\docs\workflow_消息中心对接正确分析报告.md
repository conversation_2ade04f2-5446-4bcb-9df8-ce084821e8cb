# Workflow模块消息中心对接正确分析报告

## 📋 重新分析概述

**分析时间**: 2025-07-16  
**重要发现**: 使用中文变量键名是正确的！  
**分析结论**: 需要重新理解消息中心的变量替换机制

## 🔍 变量替换机制深度分析

### 1. 消息中心变量替换流程

#### 步骤1: NoticeDispatcherService.extractVariables()
```php
// 从模板配置中提取变量
protected function extractVariables(array $data, array $template): array
{
    $result = [];
    $config = json_decode($template['variables_config'] ?? '{}', true);

    if (isset($config['variables']) && is_array($config['variables'])) {
        foreach ($config['variables'] as $var) {
            $value = $this->getNestedValue($data, $var['field']);  // 用field从输入数据提取值
            if ($value !== null) {
                $result[$var['code']] = $value;                   // 用code作为键存储
            }
        }
    }
    
    return $result;
}
```

#### 步骤2: NoticeTemplateService.renderTemplate()
```php
// 渲染模板内容，替换${变量名}占位符
public function renderTemplate(string $template, array $variables): string
{
    return preg_replace_callback('/\${([^}]+)}/', function ($matches) use ($variables) {
        $key = $matches[1];           // 提取${变量名}中的变量名
        return $variables[$key] ?? '';  // 用变量名作为键查找值
    }, $template);
}
```

### 2. 关键理解

**模板内容中的变量名必须与variables数组的键名完全一致！**

例如：
- 模板内容：`"标题：${流程标题}\n当前环节：${任务名称}"`
- variables数组必须：`['流程标题' => '值1', '任务名称' => '值2']`

## 📊 当前实现状况重新评估

### ✅ WorkflowInstanceService (原来是正确的！)

```php
// app/workflow/service/WorkflowInstanceService.php:459-465
$variables = [
    '任务名称'   => $task['node_name'],      // ✅ 正确！对应模板中的${任务名称}
    '流程标题'   => $instance['title'],      // ✅ 正确！对应模板中的${流程标题}
    '提交人姓名' => $instance['submitter_name'], // ❌ 问题：模板中是${提交人}
    '提交时间'   => $instance['created_at'], // ✅ 正确！对应模板中的${提交时间}
    'detail_url' => '/workflow/task/detail?id=' . $task['id']
];
```

### ❌ WorkflowTaskService (有问题！)

```php
// app/workflow/service/WorkflowTaskService.php:1135-1145
$variables = [
    'task_name'      => $task['node_name'],      // ❌ 错误！应该是'任务名称'
    'title'          => $instance['title'],      // ❌ 错误！应该是'流程标题'
    'submitter_name' => $instance['submitter_name'], // ❌ 错误！应该是'提交人'
    'created_at'     => $instance['created_at'], // ❌ 错误！应该是'提交时间'
    'detail_url'     => '/workflow/task/detail?id=' . $task['id']
];
```

### ❌ WorkflowEngine (有问题！)

```php
// app/workflow/service/WorkflowEngine.php:431-437
$variables = [
    'task_name'      => $node['nodeName'] ?? '审批任务', // ❌ 错误！应该是'任务名称'
    'title'          => $instance['title'],              // ❌ 错误！应该是'流程标题'
    'submitter_name' => $instance['submitter_name'],     // ❌ 错误！应该是'提交人'
    'created_at'     => $instance['created_at'],         // ❌ 错误！应该是'提交时间'
    'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id']
];
```

## 🔍 模板内容分析

### workflow_task_approval 模板内容
```
"标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}\n提交时间：${提交时间}\n紧急程度：${紧急程度}"
```

**需要的变量键名**:
- `流程标题` (不是 `title`)
- `任务名称` (不是 `task_name`)
- `提交人` (不是 `submitter_name`)
- `提交时间` (不是 `created_at`)
- `紧急程度` (可选)

### 其他模板的变量名

根据之前的分析，其他模板使用的是英文变量名：
- workflow_task_approved: `${title}`, `${result}`, `${approver_name}` 等
- workflow_task_cc: `${title}`, `${submitter_name}`, `${node_name}` 等

## 🛠️ 正确的修复方案

### 1. 修复WorkflowInstanceService (部分修复)

```php
// 文件: app/workflow/service/WorkflowInstanceService.php:459-465
$variables = [
    '任务名称'   => $task['node_name'],           // ✅ 正确
    '流程标题'   => $instance['title'],           // ✅ 正确
    '提交人'     => $instance['submitter_name'],  // ✅ 修复：改为'提交人'
    '提交时间'   => $instance['created_at'],      // ✅ 正确
    'detail_url' => '/workflow/task/detail?id=' . $task['id']
];
```

### 2. 修复WorkflowTaskService

```php
// 文件: app/workflow/service/WorkflowTaskService.php:1135-1145
$variables = [
    '任务名称'   => $task['node_name'],           // ✅ 修复：改为中文
    '流程标题'   => $instance['title'],           // ✅ 修复：改为中文
    '提交人'     => $instance['submitter_name'],  // ✅ 修复：改为中文
    '提交时间'   => $instance['created_at'],      // ✅ 修复：改为中文
    'detail_url' => '/workflow/task/detail?id=' . $task['id']
];
```

### 3. 修复WorkflowEngine

```php
// 文件: app/workflow/service/WorkflowEngine.php:431-437
$variables = [
    '任务名称'   => $node['nodeName'] ?? '审批任务', // ✅ 修复：改为中文
    '流程标题'   => $instance['title'],             // ✅ 修复：改为中文
    '提交人'     => $instance['submitter_name'],    // ✅ 修复：改为中文
    '提交时间'   => $instance['created_at'],        // ✅ 修复：改为中文
    'detail_url' => '/workflow/task/detail?instance_id=' . $instance['id']
];
```

## 🧪 验证测试

### 测试workflow_task_approval模板

```php
// 正确的测试数据
$testData = [
    '任务名称'   => '部门经理审批',
    '流程标题'   => '张三的请假申请',
    '提交人'     => '张三',
    '提交时间'   => '2025-07-16 15:30:00',
    'detail_url' => '/workflow/task/detail?id=123'
];

// 模板内容
$template = "标题：${流程标题}\n当前环节：${任务名称}\n提交人：${提交人}\n提交时间：${提交时间}";

// 期望结果
$expected = "标题：张三的请假申请\n当前环节：部门经理审批\n提交人：张三\n提交时间：2025-07-16 15:30:00";
```

## 📋 问题根因分析

### 1. 混合使用中英文变量名
- **workflow_task_approval**: 使用中文变量名 `${流程标题}`
- **其他模板**: 使用英文变量名 `${title}`

### 2. 代码实现不一致
- **WorkflowInstanceService**: 使用中文键名（部分正确）
- **WorkflowTaskService**: 使用英文键名（错误）
- **WorkflowEngine**: 使用英文键名（错误）

### 3. 模板配置与实际使用脱节
- 模板配置中的 `code` 字段与实际模板内容中的变量名不匹配
- 导致变量提取和替换机制失效

## 🎯 最终修复策略

### 方案A: 统一使用中文变量名（推荐）
1. 修复所有服务类，统一使用中文变量键名
2. 保持模板内容不变
3. 更新模板配置，确保一致性

### 方案B: 统一使用英文变量名
1. 修改模板内容，使用英文变量名
2. 保持代码中的英文键名不变
3. 更新模板配置

**推荐方案A**，因为：
- workflow_task_approval模板已经在使用中文变量名
- 中文变量名更直观，便于理解
- 减少模板内容的修改风险

## 🚀 立即行动项

### 🔴 高优先级
1. **修复WorkflowInstanceService**: 将 `'提交人姓名'` 改为 `'提交人'`
2. **修复WorkflowTaskService**: 所有英文键名改为中文
3. **修复WorkflowEngine**: 所有英文键名改为中文

### 🟡 中优先级
1. **统一其他模板**: 决定是否将其他模板也改为中文变量名
2. **更新模板配置**: 确保配置与实际使用一致

### 🟢 低优先级
1. **代码重构**: 提取公共的变量映射方法
2. **文档更新**: 更新变量命名规范

## ✅ 修复验证

修复完成后，workflow_task_approval消息应该能够正确发送，模板变量能够正确替换。

**关键点**: 变量键名必须与模板内容中的 `${变量名}` 完全一致！
