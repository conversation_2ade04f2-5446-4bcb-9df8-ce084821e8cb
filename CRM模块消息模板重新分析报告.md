# CRM模块消息模板重新分析报告

## 📊 基于crm_data.sql的重新分析

**分析时间**: 2025-07-16  
**数据源**: `app/crm/crm_data.sql`  
**重要发现**: CRM审批通知可以复用工作流模板！

## 🔍 CRM实际数据表结构分析

### 核心业务表

| 表名 | 功能 | 审批需求 | 消息通知场景 |
|------|------|----------|-------------|
| `crm_lead` | 线索管理 | ❌ 无审批 | 分配、跟进提醒 |
| `crm_customer` | 客户管理 | ❌ 无审批 | 分配、共享、公海回收 |
| `crm_contract` | 合同管理 | ✅ **有审批** | **复用工作流模板** |
| `crm_contract_receivable` | 回款记录 | ✅ **有审批** | **复用工作流模板** |
| `crm_follow_record` | 跟进记录 | ❌ 无审批 | 跟进提醒 |
| `crm_customer_share` | 客户共享 | ❌ 无审批 | 共享通知 |

### 关键发现

#### 1. 审批流程集成
```sql
-- 合同表中的审批字段
`workflow_instance_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '工作流实例ID'

-- 回款表中的审批字段  
`workflow_instance_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '工作流实例ID'
```

#### 2. 审批通知复用策略
- **合同审批** → 复用 `workflow_task_approval` 等工作流模板
- **回款审批** → 复用 `workflow_task_approval` 等工作流模板
- **无需单独的CRM审批模板**

## 📋 CRM模块真正需要的消息模板

### 🔴 高优先级（核心业务通知）

#### 1. 线索管理通知
- **`crm_lead_assign`** - 线索分配通知
- **`crm_lead_follow_remind`** - 线索跟进提醒

#### 2. 客户管理通知  
- **`crm_customer_assign`** - 客户分配通知
- **`crm_customer_share_add`** - 客户共享添加通知
- **`crm_customer_follow_remind`** - 客户跟进提醒
- **`crm_customer_sea_recycle`** - 客户回收到公海通知

#### 3. 跟进管理通知
- **`crm_follow_record_add`** - 新增跟进记录通知
- **`crm_follow_plan_remind`** - 跟进计划提醒

### 🟡 中优先级（辅助功能通知）

#### 4. 合同管理通知（非审批）
- **`crm_contract_expire_remind`** - 合同到期提醒
- **`crm_contract_payment_remind`** - 合同付款提醒

#### 5. 系统提醒通知
- **`crm_data_statistics_report`** - 数据统计报告通知

## 🎯 修正后的实施策略

### ✅ **无需修复现有CRM模板**
原因：现有的5个CRM模板实际上可能是测试数据或错误数据，真正的CRM业务应该：
1. **审批通知** → 复用工作流模板
2. **业务通知** → 创建新的CRM专用模板

### ✅ **重点实施Workflow模块修复**
因为CRM的合同审批和回款审批都依赖工作流，所以Workflow模块的修复是关键。

### ✅ **新增CRM业务通知模板**
只需要添加非审批类的业务通知模板。

## 🚀 立即执行计划

### 第一步：修复Workflow模块（立即执行）
1. 修复ApprovalNodeHandler.php
2. 修复WorkflowTaskService.php中的其他消息类型
3. 修复WorkflowInstanceService.php中的抄送通知
4. 补充终止通知功能
5. 全面测试验证

### 第二步：新增CRM业务模板（后续执行）
只添加真正需要的业务通知模板，不包括审批类模板。

## 📊 变量命名规范确认

### Workflow模板变量（CRM审批复用）
```php
// 合同审批、回款审批等都使用这些变量
[
    '任务名称' => '合同审批' / '回款审批',
    '流程标题' => '张三的合同审批申请',
    '提交人'   => '张三',
    '提交时间' => '2025-07-16 15:30:00'
]
```

### CRM业务模板变量
```php
// 线索分配通知
[
    '线索名称' => '张三的咨询',
    '分配人'   => '李四',
    '分配时间' => '2025-07-16 15:30:00'
]

// 客户分配通知
[
    '客户名称' => '上海某某公司',
    '客户电话' => '13800138000',
    '分配人'   => '李四',
    '分配时间' => '2025-07-16 15:30:00'
]
```

## 🎯 总结

### ✅ **关键认识**
1. **CRM审批通知 = 工作流通知** - 无需单独模板
2. **CRM业务通知 ≠ 审批通知** - 需要专用模板
3. **Workflow修复是关键** - 影响CRM审批功能

### 📋 **修正后的优先级**
1. **🔴 立即执行**: Workflow模块代码修复和测试
2. **🟡 计划执行**: 新增CRM业务通知模板
3. **🟢 后续完善**: Project模块通知模板

### 🚀 **下一步行动**
立即开始Workflow模块的代码修复实施！
