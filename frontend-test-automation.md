# 🎭 Playwright MCP 前端自动化测试指南

## 📋 安装完成状态

### ✅ 已配置的 MCP 服务器
1. **Playwright MCP** - 浏览器自动化和测试
2. **Browser Tools MCP** - 浏览器调试和监控
3. **ThinkPHP Filesystem** - 项目文件访问
4. **MySQL Database** - 数据库查询验证

## 🚀 启动服务

### 方法1：使用批处理文件
```bash
# 启动 Playwright MCP 服务器
start-playwright-mcp.bat

# 启动 Browser Tools 服务器（可选，用于调试）
start-browser-tools.bat
```

### 方法2：手动启动
```bash
# Playwright MCP (端口 8931)
npx @playwright/mcp@latest --config playwright-mcp-config.json --port 8931

# Browser Tools (端口 3025)
npx @agentdeskai/browser-tools-server
```

## 🎯 Playwright MCP 核心功能

### 🔧 浏览器控制工具
- `browser_navigate` - 导航到指定URL
- `browser_snapshot` - 获取页面可访问性快照
- `browser_take_screenshot` - 截取页面截图
- `browser_click` - 点击页面元素
- `browser_type` - 在输入框中输入文本
- `browser_wait_for` - 等待元素或文本出现

### 📊 页面分析工具
- `browser_console_messages` - 获取控制台消息
- `browser_network_requests` - 获取网络请求日志
- `browser_evaluate` - 执行JavaScript代码

### 🗂️ 标签页管理
- `browser_tab_new` - 打开新标签页
- `browser_tab_list` - 列出所有标签页
- `browser_tab_select` - 切换标签页
- `browser_tab_close` - 关闭标签页

## 🧪 前端测试场景

### 1. 登录功能测试
```
AI指令：
"使用Playwright测试CRM系统登录功能：
1. 打开 http://localhost:3006/#/login
2. 输入用户名：admin
3. 输入密码：123456
4. 点击登录按钮
5. 验证是否成功跳转到首页
6. 检查是否有错误信息"
```

### 2. 产品分类CRUD测试
```
AI指令：
"测试产品分类管理功能：
1. 导航到产品分类页面
2. 点击新增按钮
3. 填写分类名称和描述
4. 保存并验证是否成功
5. 编辑刚创建的分类
6. 删除测试数据"
```

### 3. 表单验证测试
```
AI指令：
"测试产品添加表单验证：
1. 打开产品添加页面
2. 不填写必填字段直接提交
3. 验证错误提示是否正确显示
4. 逐个填写字段并验证实时验证
5. 提交完整表单并验证成功提示"
```

### 4. 响应式布局测试
```
AI指令：
"测试页面响应式布局：
1. 设置浏览器窗口为1920x1080
2. 截图记录桌面版布局
3. 调整窗口到768x1024（平板）
4. 截图记录平板布局
5. 调整窗口到375x667（手机）
6. 截图记录手机布局
7. 验证所有元素是否正确显示"
```

## 🔄 Playwright + Browser Tools 协同使用

### 测试流程设计
1. **Playwright** 执行自动化操作
2. **Browser Tools** 监控网络请求和控制台
3. **MySQL** 验证数据库状态
4. **Filesystem** 生成测试报告

### 协同测试示例
```
AI指令：
"执行完整的产品管理测试：
1. 使用Playwright打开产品管理页面
2. 使用Browser Tools监控网络请求
3. 执行添加产品操作
4. 验证API请求是否成功
5. 使用MySQL查询验证数据是否正确保存
6. 检查控制台是否有错误
7. 生成测试报告保存到文件"
```

## 📝 测试脚本模板

### 基础测试模板
```
"请使用以下步骤测试[功能名称]：
1. 导航到 [URL]
2. 等待页面加载完成
3. 执行 [具体操作]
4. 验证 [预期结果]
5. 截图保存测试结果
6. 检查控制台错误
7. 验证网络请求状态"
```

### 数据验证模板
```
"执行数据完整性测试：
1. 使用Playwright执行 [操作]
2. 使用MySQL查询相关数据
3. 对比前端显示与数据库数据
4. 验证数据一致性
5. 生成验证报告"
```

## ⚙️ 配置说明

### Playwright MCP 配置特点
- **端口**: 8931 (避免与其他服务冲突)
- **浏览器**: Chrome (更好的兼容性)
- **视窗**: 1280x720 (标准测试分辨率)
- **功能**: 支持标签页、PDF生成、坐标点击

### 网络访问控制
- 允许访问本地开发服务器
- 支持HTTP和HTTPS协议
- 配置了常用的开发端口

## 🎨 高级测试功能

### 1. 性能测试
```
AI指令：
"执行页面性能测试：
1. 导航到目标页面
2. 获取网络请求时间
3. 测量页面加载时间
4. 检查资源加载情况
5. 生成性能报告"
```

### 2. 可访问性测试
```
AI指令：
"测试页面可访问性：
1. 获取页面可访问性快照
2. 检查表单标签是否正确
3. 验证键盘导航功能
4. 测试屏幕阅读器兼容性"
```

### 3. 跨浏览器测试
```
AI指令：
"执行跨浏览器兼容性测试：
1. 在Chrome中执行测试
2. 切换到Firefox重复测试
3. 对比测试结果
4. 记录兼容性问题"
```

## 🚨 故障排除

### 常见问题解决
1. **端口冲突**: 修改配置文件中的端口号
2. **浏览器启动失败**: 检查Chrome是否正确安装
3. **网络请求被阻止**: 检查allowedOrigins配置
4. **测试超时**: 增加wait_for的等待时间

### 调试技巧
```
AI指令：
"调试测试失败问题：
1. 获取当前页面截图
2. 查看控制台错误信息
3. 检查网络请求状态
4. 获取页面HTML结构
5. 分析失败原因并提供解决方案"
```

## 📊 测试报告生成

### 自动化报告
```
AI指令：
"生成测试执行报告：
1. 汇总所有测试结果
2. 统计成功/失败数量
3. 记录发现的问题
4. 生成改进建议
5. 保存为Markdown文件"
```

## 🎉 使用建议

### 最佳实践
1. **测试前准备**: 确保开发环境正常运行
2. **数据隔离**: 使用测试数据库避免污染生产数据
3. **定期清理**: 清理测试生成的临时文件
4. **版本控制**: 将测试脚本纳入版本管理

### 效率提升
- 使用模板化的测试指令
- 结合多个MCP工具进行综合测试
- 建立测试用例库便于复用
- 定期更新测试配置以适应项目变化

---

**配置完成！现在您可以使用强大的Playwright MCP进行全面的前端自动化测试！**
