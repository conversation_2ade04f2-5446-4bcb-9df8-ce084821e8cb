<?php
declare(strict_types=1);

namespace app\workflow\factory;

use app\common\core\base\BaseService;
use app\common\core\base\BaseModel;
use app\common\exception\BusinessException;
use app\crm\model\CrmContract;
use app\crm\model\CrmContractReceivable;
use app\daily\model\DailyPriceOrder;
use app\hr\model\HrLeave;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\model\WorkflowType;
use think\facade\Log;

/**
 * 动态工作流工厂类
 * 基于workflow_type表动态创建Service和Model
 */
class DynamicWorkflowFactory
{
	/**
	 * 基于workflow_type表动态创建Service
	 *
	 * @param string $businessCode 业务代码
	 * @return BaseService|null
	 */
	public static function createServiceByBusinessCode(string $businessCode): ?BaseService
	{
		$workflowType = WorkflowType::where('business_code', $businessCode)
		                            ->find();

		if (!$workflowType) {
			return null;
		}
		
		// 动态构建Service类名：app\{module_code}\service\{ModuleBusiness}Service
		// 例如：hr_leave -> HrLeaveService, crm_contract -> CrmContractService, crm_contract_receivable -> CrmContractReceivableService
		$businessName = str_replace('_', '', ucwords($businessCode, '_'));
		$serviceClass = "\\app\\{$workflowType->module_code}\\service\\{$businessName}Service";

		return class_exists($serviceClass)
			? new $serviceClass()
			: null;
	}
	
	/**
	 * 基于workflow_type表动态创建Model
	 *
	 * @param string $businessCode 业务代码
	 * @return BaseModel|null
	 */
	public static function createModelByBusinessCode(string $businessCode): ?BaseModel
	{
		// 已知业务类型的直接映射（避免数据库查询问题）
		/*$modelMap = [
			'daily_price_order' => DailyPriceOrder::class,
			'hr_leave' => HrLeave::class,
			'crm_contract' => CrmContract::class,
			'crm_contract_receivable' => CrmContractReceivable::class,
		];

		if (isset($modelMap[$businessCode])) {
			$modelClass = $modelMap[$businessCode];
			if (class_exists($modelClass)) {
				return new $modelClass();
			}
		}*/

		// 回退到动态查询
		$workflowType = WorkflowType::where('business_code', $businessCode)
		                            ->find();

		if (!$workflowType) {
			return null;
		}

		// 动态构建Model类名：app\{module_code}\model\{ModuleBusiness}
		// 例如：hr_leave -> HrLeave, crm_contract -> CrmContract, crm_contract_receivable -> CrmContractReceivable
		$businessName = str_replace('_', '', ucwords($businessCode, '_'));
		$modelClass   = "\\app\\{$workflowType->module_code}\\model\\{$businessName}";

		return class_exists($modelClass)
			? new $modelClass()
			: null;
	}
	
	/**
	 * 动态创建FormService（优先使用动态工厂，回退到FormServiceFactory）
	 *
	 * @param string $businessCode 业务代码
	 * @return FormServiceInterface|null
	 */
	public static function createFormServiceByBusinessCode(string $businessCode): ?FormServiceInterface
	{
		// 回退到动态工厂
		$service = self::createServiceByBusinessCode($businessCode);
		if ($service instanceof FormServiceInterface) {
			return $service;
		}

		// 记录错误但不抛出异常，让调用方处理
		Log::error("FormServiceInterface not implemented", [
			'business_code' => $businessCode,
			'service_created' => $service ? 'yes' : 'no',
			'service_class' => $service ? get_class($service) : 'null'
		]);

		return null;
	}
	
	/**
	 * 获取业务配置信息
	 *
	 * @param string $businessCode 业务代码
	 * @return array|null
	 */
	public static function getBusinessConfig(string $businessCode): ?array
	{
		$workflowType = WorkflowType::where('business_code', $businessCode)
		                            ->find();
		
		return $workflowType
			? [
				'module_code'      => $workflowType->module_code,
				'business_code'    => $workflowType->business_code,
				'name'             => $workflowType->name,
				'service_class'    => self::buildServiceClass($workflowType),
				'model_class'      => self::buildModelClass($workflowType),
				'controller_class' => self::buildControllerClass($workflowType)
			]
			: null;
	}
	
	/**
	 * 构建Service类名
	 *
	 * @param object $workflowType
	 * @return string
	 */
	private static function buildServiceClass($workflowType): string
	{
		$businessName = str_replace('_', '', ucwords($workflowType->business_code, '_'));
		return "\\app\\{$workflowType->module_code}\\service\\{$businessName}Service";
	}
	
	/**
	 * 构建Model类名
	 *
	 * @param object $workflowType
	 * @return string
	 */
	private static function buildModelClass($workflowType): string
	{
		$businessName = str_replace('_', '', ucwords($workflowType->business_code, '_'));
		return "\\app\\{$workflowType->module_code}\\model\\{$businessName}";
	}
	
	/**
	 * 构建Controller类名
	 *
	 * @param object $workflowType
	 * @return string
	 */
	private static function buildControllerClass($workflowType): string
	{
		$businessName = str_replace('_', '', ucwords($workflowType->business_code, '_'));
		return "\\app\\{$workflowType->module_code}\\controller\\{$businessName}Controller";
	}
}
