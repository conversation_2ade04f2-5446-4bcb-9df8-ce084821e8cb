<?php
declare(strict_types=1);

namespace app\workflow\traits;

use app\common\exception\BusinessException;
use app\workflow\service\UnifiedWorkflowService;
use think\facade\Log;

/**
 * 统一工作流操作Trait
 * 
 * 替代WorkflowableService，提供更简洁的工作流操作接口
 * 业务Service可以使用此Trait来快速集成工作流功能
 */
trait UnifiedWorkflowTrait
{
    /**
     * 获取业务代码
     * 使用此Trait的类必须实现此方法
     * 
     * @return string
     */
    abstract protected function getBusinessCode(): string;
    
    /**
     * 提交工作流审批
     * 
     * @param int $businessId 业务记录ID
     * @param array $options 选项参数
     * @return array 操作结果
     * @throws BusinessException
     */
    protected function submitWorkflowApproval(int $businessId, array $options = []): array
    {
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            return $unifiedWorkflowService->executeWorkflowOperation('submit', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'operator_id' => $options['operator_id'] ?? get_user_id(),
                'title' => $options['title'] ?? null,
                'definition_id' => $options['definition_id'] ?? null
            ]);
        } catch (BusinessException $e) {
            Log::error('提交工作流审批失败', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 撤回工作流审批
     * 
     * @param int $businessId 业务记录ID
     * @param array $options 选项参数
     * @return array 操作结果
     * @throws BusinessException
     */
    protected function withdrawWorkflowApproval(int $businessId, array $options = []): array
    {
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            return $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'operator_id' => $options['operator_id'] ?? get_user_id(),
                'reason' => $options['reason'] ?? '用户撤回申请'
            ]);
        } catch (BusinessException $e) {
            Log::error('撤回工作流审批失败', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 作废工作流审批
     * 
     * @param int $businessId 业务记录ID
     * @param array $options 选项参数
     * @return array 操作结果
     * @throws BusinessException
     */
    protected function voidWorkflowApproval(int $businessId, array $options = []): array
    {
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            return $unifiedWorkflowService->executeWorkflowOperation('void', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'operator_id' => $options['operator_id'] ?? get_user_id(),
                'reason' => $options['reason'] ?? '业务作废'
            ]);
        } catch (BusinessException $e) {
            Log::error('作废工作流审批失败', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 终止工作流审批
     * 
     * @param int $businessId 业务记录ID
     * @param array $options 选项参数
     * @return array 操作结果
     * @throws BusinessException
     */
    protected function terminateWorkflowApproval(int $businessId, array $options = []): array
    {
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            return $unifiedWorkflowService->executeWorkflowOperation('terminate', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'operator_id' => $options['operator_id'] ?? get_user_id(),
                'reason' => $options['reason'] ?? '流程终止'
            ]);
        } catch (BusinessException $e) {
            Log::error('终止工作流审批失败', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 检查工作流状态
     * 
     * @param int $businessId 业务记录ID
     * @return array 状态信息
     */
    protected function checkWorkflowStatus(int $businessId): array
    {
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $formService = $unifiedWorkflowService->getFormService($this->getBusinessCode());
            $businessData = $formService->getFormData($businessId);
            
            return [
                'approval_status' => $businessData['approval_status'] ?? 0,
                'workflow_instance_id' => $businessData['workflow_instance_id'] ?? null,
                'can_submit' => ($businessData['approval_status'] ?? 0) === 0,
                'can_withdraw' => ($businessData['approval_status'] ?? 0) === 1 && !empty($businessData['workflow_instance_id']),
                'can_void' => in_array($businessData['approval_status'] ?? 0, [1, 2]) && !empty($businessData['workflow_instance_id']),
                'can_terminate' => in_array($businessData['approval_status'] ?? 0, [1, 2]) && !empty($businessData['workflow_instance_id'])
            ];
        } catch (\Exception $e) {
            Log::error('检查工作流状态失败', [
                'business_code' => $this->getBusinessCode(),
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'approval_status' => 0,
                'workflow_instance_id' => null,
                'can_submit' => false,
                'can_withdraw' => false,
                'can_void' => false,
                'can_terminate' => false
            ];
        }
    }
}
