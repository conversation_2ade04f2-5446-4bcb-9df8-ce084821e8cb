<template>
  <div class="follow-form">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="跟进方式" prop="follow_type">
        <el-select v-model="formData.follow_type" placeholder="请选择跟进方式" style="width: 100%">
          <el-option label="电话" value="phone" />
          <el-option label="会议" value="meeting" />
          <el-option label="邮件" value="email" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="跟进时间" prop="follow_date">
        <el-date-picker
          v-model="formData.follow_date"
          type="datetime"
          placeholder="选择跟进时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="跟进内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="请输入跟进内容..."
          maxlength="1000"
          show-word-limit
          resize="none"
        />
      </el-form-item>
      
      <el-form-item label="下次计划" prop="next_plan">
        <el-input
          v-model="formData.next_plan"
          type="textarea"
          :rows="3"
          placeholder="请输入下次跟进计划..."
          maxlength="500"
          show-word-limit
          resize="none"
        />
      </el-form-item>
      
      <el-form-item label="下次跟进" prop="next_date">
        <el-date-picker
          v-model="formData.next_date"
          type="datetime"
          placeholder="选择下次跟进时间"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="附件" prop="attachments">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
          :limit="5"
          class="upload-demo"
        >
          <el-button size="small" type="text">
            <el-icon><Paperclip /></el-icon>
            添加附件
          </el-button>
        </el-upload>
      </el-form-item>
      
      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleCancel" size="default">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSubmit" 
            :loading="loading"
            size="default"
          >
            保存跟进
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { Paperclip } from '@element-plus/icons-vue'
import { TaskApi } from '@/api/project/projectApi'

// Props
interface Props {
  taskId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'success': []
  'cancel': []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const fileList = ref<any[]>([])

const formData = reactive({
  follow_type: '',
  follow_date: '',
  content: '',
  next_plan: '',
  next_date: '',
  attachments: []
})

// 验证规则
const rules = {
  follow_type: [
    { required: true, message: '请选择跟进方式', trigger: 'change' }
  ],
  follow_date: [
    { required: true, message: '请选择跟进时间', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '跟进内容长度在 1 到 1000 个字符', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  // 默认设置当前时间为跟进时间
  const now = new Date()
  formData.follow_date = now.toISOString().slice(0, 19).replace('T', ' ')
})

// 方法
const handleFileChange = (file: any) => {
  console.log('文件变化:', file)
}

const handleFileRemove = (file: any) => {
  console.log('移除文件:', file)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 处理附件
    const attachments = fileList.value.map(file => ({
      name: file.name,
      size: file.size,
      type: file.raw?.type || '',
      url: '' // 这里需要实际的上传逻辑
    }))

    const data = {
      task_id: props.taskId,
      ...formData,
      attachments
    }

    await TaskApi.addFollow(data)
    
    ElMessage.success('跟进记录保存成功')
    
    // 重置表单
    Object.assign(formData, {
      follow_type: '',
      follow_date: '',
      content: '',
      next_plan: '',
      next_date: '',
      attachments: []
    })
    fileList.value = []
    
    emit('success')
  } catch (error: any) {
    console.error('保存跟进记录失败:', error)
    ElMessage.error(error.message || '保存跟进记录失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  // 重置表单
  Object.assign(formData, {
    follow_type: '',
    follow_date: '',
    content: '',
    next_plan: '',
    next_date: '',
    attachments: []
  })
  fileList.value = []
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.follow-form {
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-textarea__inner) {
    border-radius: 8px;
    border: 1px solid #e1e6ef;
    
    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }
  }

  :deep(.el-select) {
    .el-input__wrapper {
      border-radius: 8px;
    }
  }

  :deep(.el-date-editor) {
    border-radius: 8px;
  }
}
</style>
