<script setup lang="ts">
  import { ref, reactive, defineExpose, defineEmits } from 'vue'
  import { ElMessage, ElMessageBox, FormInstance, FormItemRule } from 'element-plus'
  import { User, Document } from '@element-plus/icons-vue'
  import { CrmLeadPoolApi } from '@/api/crm/crmLeadPool'
  import { UserApi } from '@/api/system/user'
  import { ApiStatus } from '@/utils/http/status'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const loading = ref(false)
  const leadId = ref(0)
  const leadInfo = ref<any>({})

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    to_user_id: '',
    reason: ''
  })

  // 重置表单数据的默认值
  const getDefaultFormData = () => ({
    to_user_id: '',
    reason: ''
  })

  // 表单验证规则
  const formRules = reactive({
    to_user_id: [
      { required: true, message: '请选择分配目标用户', trigger: 'change' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value && typeof value !== 'number' && !Number.isInteger(Number(value))) {
            callback(new Error('请选择有效的用户'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    reason: [
      { min: 2, max: 200, message: '分配原因长度在 2 到 200 个字符', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.trim().length < 2) {
            callback(new Error('分配原因不能只包含空格'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  })

  // 用户选项（这里应该从API获取）
  const userOptions = ref<any[]>([])

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, getDefaultFormData())
    formRef.value?.resetFields()
  }

  // 打开对话框
  const openDialog = async (id: number, leadData?: any) => {
    leadId.value = id
    leadInfo.value = leadData || {}
    dialogVisible.value = true
    resetForm()
    
    // 加载用户选项
    await loadUserOptions()
  }

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false
    resetForm()
  }

  // 加载用户选项
  const loadUserOptions = async () => {
    try {
      // 获取销售人员列表
      const res = await UserApi.getSalesUsers({
        status: 1, // 只获取启用的用户
        limit: 100 // 获取前100个用户
      })

      if (res.code === ApiStatus.success) {
        userOptions.value = res.data.map((user: any) => ({
          id: user.id,
          name: user.name || user.username,
          department: user.department_name || '未分配部门',
          position: user.position || ''
        }))
      } else {
        // 如果API失败，使用模拟数据
        userOptions.value = [
          { id: 1, name: '张三', department: '销售部', position: '销售经理' },
          { id: 2, name: '李四', department: '销售部', position: '销售专员' },
          { id: 3, name: '王五', department: '市场部', position: '市场专员' }
        ]
      }
    } catch (error) {
      console.error('加载用户选项失败:', error)
      // 出错时使用模拟数据
      userOptions.value = [
        { id: 1, name: '张三', department: '销售部', position: '销售经理' },
        { id: 2, name: '李四', department: '销售部', position: '销售专员' },
        { id: 3, name: '王五', department: '市场部', position: '市场专员' }
      ]
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      // 获取选中的用户信息
      const selectedUser = userOptions.value.find(user => user.id == formData.to_user_id)
      const userName = selectedUser ? selectedUser.name : '未知用户'

      // 确认分配
      await ElMessageBox.confirm(
        `确定要将线索"${leadInfo.value.lead_name}"分配给"${userName}"吗？`,
        '确认分配',
        {
          confirmButtonText: '确定分配',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true

      const res = await CrmLeadPoolApi.assign(leadId.value, formData)

      if (res.code === ApiStatus.success) {
        ElMessage.success('分配成功')
        emit('success')
        closeDialog()
      } else {
        ElMessage.error(res.message || '分配失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('分配失败:', error)
        ElMessage.error('分配失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 暴露方法给父组件
  defineExpose({
    openDialog,
    closeDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="分配线索"
    width="600px"
    destroy-on-close
    @close="closeDialog"
  >
    <div class="assign-dialog">
      <!-- 线索信息 -->
      <div class="lead-info">
        <h4>线索信息</h4>
        <div class="info-row">
          <span class="label">线索姓名：</span>
          <span class="value">{{ leadInfo.lead_name || '-' }}</span>
          <span class="label">公司名称：</span>
          <span class="value">{{ leadInfo.company || '-' }}</span>
        </div>
        <div class="info-row">
          <span class="label">联系电话：</span>
          <span class="value">{{ leadInfo.mobile || leadInfo.phone || '-' }}</span>
          <span class="label">线索来源：</span>
          <span class="value">{{ leadInfo.source || '-' }}</span>
        </div>
      </div>

      <!-- 分配表单 -->
      <div class="assign-form">
        <h4>分配信息</h4>
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="left"
        >
          <ElFormItem label="分配给" prop="to_user_id">
            <ElSelect
              v-model="formData.to_user_id"
              placeholder="请选择分配目标用户"
              style="width: 100%"
              filterable
              :loading="userOptions.length === 0"
              loading-text="加载用户列表中..."
              no-data-text="暂无可分配的用户"
            >
              <ElOption
                v-for="user in userOptions"
                :key="user.id"
                :label="`${user.name} (${user.department})`"
                :value="user.id"
              >
                <div class="user-option">
                  <span class="user-name">{{ user.name }}</span>
                  <span class="user-info">
                    <span class="user-dept">{{ user.department }}</span>
                    <span v-if="user.position" class="user-position">{{ user.position }}</span>
                  </span>
                </div>
              </ElOption>
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="分配原因" prop="reason">
            <ElInput
              v-model="formData.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入分配原因（可选）"
              maxlength="200"
              show-word-limit
            />
          </ElFormItem>
        </ElForm>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="closeDialog" :disabled="loading">取消</ElButton>
        <ElButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
          :disabled="loading || !formData.to_user_id"
        >
          <span v-if="loading">分配中...</span>
          <span v-else>确认分配</span>
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
  .assign-dialog {
    .lead-info {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 6px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #606266;
          font-size: 13px;
          min-width: 80px;
        }

        .value {
          color: #303133;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    .assign-form {
      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .user-option {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .user-name {
          font-weight: 500;
        }

        .user-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .user-dept {
            color: #909399;
            font-size: 12px;
          }

          .user-position {
            color: #606266;
            font-size: 11px;
            margin-top: 2px;
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
