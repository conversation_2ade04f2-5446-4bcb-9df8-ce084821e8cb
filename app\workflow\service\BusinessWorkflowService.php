<?php
declare(strict_types=1);

namespace app\workflow\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\factory\DynamicWorkflowFactory;
use app\workflow\model\WorkflowInstance;
use app\workflow\factory\FormServiceFactory;
use app\workflow\service\WorkflowStatusSyncService;
use think\facade\Db;
use think\facade\Log;

/**
 * 业务工作流服务类
 * 负责业务与工作流的集成，状态同步等核心功能
 */
class BusinessWorkflowService extends BaseService
{
	
	
	public function __construct()
	{
		$this->model = new WorkflowInstance();
		parent::__construct();
	}
	
	/**
	 * 为业务创建工作流实例
	 *
	 * @param array $params 参数
	 * @return array 结果
	 * @throws BusinessException
	 */
	public function createWorkflowForBusiness(array $params): array
	{
		// 参数验证
		$this->validateParams($params);
		
		Db::startTrans();
		try {
			// 1. 验证业务数据存在且状态允许提交
			$businessRecord = $this->validateBusinessData($params);
			
			// 2. 获取工作流定义
			$definition = $this->getWorkflowDefinition($params['definition_id']);
			
			// 3. 创建工作流实例
			$instanceId = $this->createWorkflowInstance($params, $definition, $businessRecord);
			
			// 4. 同步业务表状态
			$this->syncBusinessStatus($params['business_code'], $params['business_id'], [
				'workflow_instance_id' => $instanceId,
				'approval_status'      => WorkflowStatusConstant::APPROVING,
				'submit_time'          => date('Y-m-d H:i:s'),
				'submitter_id'         => $params['submitter_id']
			]);
			
			// 5. 启动工作流引擎
			$this->startWorkflowEngine($instanceId);
			
			Db::commit();
			
			Log::info('业务工作流创建成功', [
				'business_code' => $params['business_code'],
				'business_id'   => $params['business_id'],
				'instance_id'   => $instanceId
			]);
			
			return [
				'success'     => true,
				'instance_id' => $instanceId,
				'business_id' => $params['business_id']
			];
			
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('业务工作流创建失败', [
				'params' => $params,
				'error'  => $e->getMessage()
			]);
			throw new BusinessException('创建工作流失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 处理工作流状态变更回调 未使用
	 *
	 * @param int   $instanceId 工作流实例ID
	 * @param int   $newStatus  新状态
	 * @param array $extra      额外数据
	 * @return bool
	 */
	public function handleWorkflowStatusChange(int $instanceId, int $newStatus, array $extra = []): bool
	{
		Db::startTrans();
		try {
			// 获取工作流实例
			$instance = $this->crudService->getDetail($instanceId);
			if (!$instance) {
				throw new BusinessException('工作流实例不存在');
			}
			
			// 同步业务表状态
			$statusData = [
				'approval_status' => $newStatus,
			];
			
			// 根据状态添加时间字段
			if ($newStatus === WorkflowStatusConstant::APPROVING) {
				$statusData['submit_time'] = date('Y-m-d H:i:s');
			}
			elseif (in_array($newStatus, [
				WorkflowStatusConstant::APPROVED,
				WorkflowStatusConstant::REJECTED,
				WorkflowStatusConstant::TERMINATED,
				WorkflowStatusConstant::RECALLED
			])) {
				$statusData['approval_time'] = date('Y-m-d H:i:s');
			}
			
			$this->syncBusinessStatus($instance->business_code, (int)$instance->business_id, $statusData);
			
			// 触发业务后处理
			$this->triggerBusinessAfterProcess($instance, $newStatus, $extra);
			
			Db::commit();
			return true;
			
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('工作流状态同步失败', [
				'instance_id' => $instanceId,
				'new_status'  => $newStatus,
				'error'       => $e->getMessage()
			]);
			return false;
		}
	}
	
	/**
	 * 撤回工作流
	 *
	 * @param string $businessCode 业务代码
	 * @param int    $businessId   业务ID
	 * @return bool
	 */
	public function withdrawWorkflow(string $businessCode, int $businessId): bool
	{
		try {
			// 获取业务记录
			$businessRecord = $this->getBusinessRecord($businessCode, $businessId);
			if (!$businessRecord || !$businessRecord->workflow_instance_id) {
				throw new BusinessException('未找到关联的工作流实例');
			}
			
			// 检查状态是否允许撤回
			if ($businessRecord->approval_status !== WorkflowStatusConstant::APPROVING) {
				throw new BusinessException('只有审批中的记录才能撤回');
			}
			
			// 使用统一状态同步服务
			$syncService = new WorkflowStatusSyncService();
			$result      = $syncService->syncAllWorkflowStatus($businessRecord->workflow_instance_id, WorkflowStatusConstant::STATUS_RECALLED, '申请已撤回', request()->adminId ?? 0);
			
			if (!$result) {
				throw new BusinessException('状态同步失败');
			}
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('撤回工作流失败', [
				'business_code' => $businessCode,
				'business_id'   => $businessId,
				'error'         => $e->getMessage()
			]);
			throw new BusinessException('撤回失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 验证参数
	 */
	private function validateParams(array $params): void
	{
		$required = [
			'business_code',
			'business_id',
			'definition_id',
			'submitter_id'
		];
		foreach ($required as $field) {
			if (empty($params[$field])) {
				throw new BusinessException("参数 {$field} 不能为空");
			}
		}
		
		// 使用动态工厂验证业务类型是否支持
		$model = DynamicWorkflowFactory::createModelByBusinessCode($params['business_code']);
		if (!$model) {
			throw new BusinessException("不支持的业务类型：{$params['business_code']}");
		}
	}
	
	/**
	 * 验证业务数据
	 */
	private function validateBusinessData(array $params): object
	{
		// 使用动态工厂创建Model
		$model = DynamicWorkflowFactory::createModelByBusinessCode($params['business_code']);
		
		if (!$model) {
			throw new BusinessException("不支持的业务类型：{$params['business_code']}");
		}
		
		$record = $model->find($params['business_id']);
		if (!$record) {
			throw new BusinessException('业务记录不存在');
		}
		
		// 检查状态是否允许提交
		if (isset($record->approval_status) && !in_array($record->approval_status, [
				0,
				3
			])) {
			throw new BusinessException('当前状态不允许提交审批');
		}
		
		return $record;
	}
	
	/**
	 * 获取工作流定义
	 */
	private function getWorkflowDefinition(int $definitionId): object
	{
		$definitionService = WorkflowDefinitionService::getInstance();
		$definition        = $definitionService->crudService->getDetail($definitionId);
		
		if (!$definition || $definition->status !== 1) {
			throw new BusinessException('工作流定义不存在或已停用');
		}
		
		return $definition;
	}
	
	/**
	 * 创建工作流实例
	 */
	private function createWorkflowInstance(array $params, object $definition, object $businessRecord): int
	{
		$processId = $this->generateProcessId($params['business_code']);
		
		$instanceData = [
			'process_id'        => $processId,
			'definition_id'     => $definition->id,
			'type_id'           => $definition->type_id,
			'business_code'     => $params['business_code'],
			'business_id'       => (string)$params['business_id'],
			'title'             => $params['title'] ?? $this->generateTitle($params['business_code'], $businessRecord),
			'status'            => WorkflowStatusConstant::APPROVING,
			'submitter_id'      => $params['submitter_id'],
			'submitter_dept_id' => $params['submitter_dept_id'] ?? 0,
			'start_time'        => date('Y-m-d H:i:s'),
			'process_data'      => $definition->flow_config,
			'form_data'         => json_encode($businessRecord->toArray()),
			'tenant_id'         => request()->tenantId ?? 0
		];
		
		return $this->crudService->add($instanceData);
	}
	
	/**
	 * 同步工作流状态（优化版本 - 避免重复处理）
	 *
	 * 注意：此方法已优化，不再重复更新业务表状态
	 * 业务表状态由WorkflowStatusSyncService统一处理
	 *
	 * @param int $instanceId 工作流实例ID
	 * @param int $newStatus  新状态
	 * @return bool
	 */
	public function syncWorkflowStatus(int $instanceId, int $newStatus): bool
	{
		try {
			// 获取工作流实例
			$instance = $this->crudService->getDetail($instanceId);
			if (!$instance) {
				throw new BusinessException('工作流实例不存在');
			}
			
			// 只负责触发业务后处理，不再重复更新业务表状态
			// 业务表状态已由WorkflowStatusSyncService::syncBusinessStatus统一处理
			$this->triggerBusinessAfterProcess($instance, $newStatus, []);
			
			Log::info('工作流业务后处理完成', [
				'instance_id'   => $instanceId,
				'business_code' => $instance->business_code,
				'business_id'   => $instance->business_id,
				'new_status'    => $newStatus
			]);
			
			return true;
			
		}
		catch (\Exception $e) {
			Log::error('工作流业务后处理失败', [
				'instance_id' => $instanceId,
				'new_status'  => $newStatus,
				'error'       => $e->getMessage()
			]);
			return false;
		}
	}
	
	/**
	 * 同步业务状态
	 */
	public function syncBusinessStatus(int|string $businessCode, int|string $businessId, array $statusData): bool
	{
		// 使用动态工厂创建Model
		$model = DynamicWorkflowFactory::createModelByBusinessCode($businessCode);
		
		if (!$model) {
			Log::error('同步业务状态失败：不支持的业务类型', [
				'business_code' => $businessCode,
				'business_id'   => $businessId
			]);
			return false;
		}
		
		return $model->where('id', $businessId)
		             ->update($statusData) !== false;
	}
	
	/**
	 * 启动工作流引擎
	 */
	private function startWorkflowEngine(int $instanceId): void
	{
		try {
			$instance              = $this->crudService->getDetail($instanceId, ['submitter']);
			$workflowEngineService = new WorkflowEngineService();
			$workflowEngineService->startWorkflow($instance->toArray());
		}
		catch (\Exception $e) {
			Log::error('启动工作流引擎失败', [
				'instance_id' => $instanceId,
				'error'       => $e->getMessage()
			]);
			// 不抛出异常，避免影响主流程
		}
	}
	
	/**
	 * 触发业务后处理（优化版本 - 移除method_exists检查）
	 */
	private function triggerBusinessAfterProcess(object $instance, int $newStatus, array $extra): void
	{
		try {
			// 通过FormService触发业务后处理 - 使用动态工厂
			$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance->business_code);
			if ($formService) {
				// 不再需要method_exists检查，因为FormServiceInterface保证了方法存在
				$result = $formService->afterWorkflowStatusChange((int)$instance->business_id, $newStatus, $extra);
				
				Log::info('业务后处理完成', [
					'instance_id'   => $instance->id,
					'business_code' => $instance->business_code,
					'business_id'   => $instance->business_id,
					'status'        => $newStatus,
					'result'        => $result
						? 'success'
						: 'failed'
				]);
			}
			else {
				Log::warning('未找到对应的FormService', [
					'instance_id'   => $instance->id,
					'business_code' => $instance->business_code
				]);
			}
		}
		catch (\Exception $e) {
			Log::error('业务后处理失败', [
				'instance_id'   => $instance->id,
				'business_code' => $instance->business_code,
				'business_id'   => $instance->business_id,
				'status'        => $newStatus,
				'error'         => $e->getMessage(),
				'trace'         => $e->getTraceAsString()
			]);
		}
	}
	
	/**
	 * 获取业务记录
	 */
	private function getBusinessRecord(string $businessCode, int $businessId): ?object
	{
		// 使用动态工厂创建Model
		$model = DynamicWorkflowFactory::createModelByBusinessCode($businessCode);
		if (!$model) {
			return null;
		}
		return $model->find($businessId);
	}
	
	/**
	 * 生成流程ID
	 */
	private function generateProcessId(string $businessCode): string
	{
		$prefix = strtoupper(str_replace('_', '', $businessCode));
		$date   = date('YmdHis');
		$rand   = mt_rand(1000, 9999);
		return "{$prefix}-{$date}-{$rand}";
	}
	
	/**
	 * 生成标题
	 */
	private function generateTitle(string $businessCode, object $businessRecord): string
	{
		$titleMap = [
			'crm_contract'   => "合同审批-{$businessRecord->contract_name}",
			'crm_receivable' => "回款审批-{$businessRecord->receivable_amount}元",
		];
		
		return $titleMap[$businessCode] ?? "业务审批-{$businessCode}";
	}
}
