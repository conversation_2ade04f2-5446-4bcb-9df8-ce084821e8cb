-- 任务评论和跟进测试数据
-- 请根据实际的项目ID和任务ID调整数据

-- 查找现有的项目和任务
-- SELECT id, title FROM project_project LIMIT 5;
-- SELECT id, title FROM project_task LIMIT 5;

-- 假设项目ID为1，任务ID为1和2，用户ID为1
SET @project_id = 1;
SET @task_id_1 = 1;
SET @task_id_2 = 2;
SET @user_id = 1;

-- 插入任务评论测试数据
INSERT INTO `project_task_record` (`task_id`, `record_type`, `content`, `follow_type`, `follow_date`, `next_plan`, `next_date`, `attachments`, `creator_id`, `created_at`, `updated_at`) VALUES

-- 任务1的评论
(@task_id_1, 1, '这个任务的需求分析已经完成，可以开始设计阶段了。', NULL, NULL, NULL, NULL, NULL, @user_id, NOW(), NOW()),
(@task_id_1, 1, '设计稿已经完成，请大家review一下，有问题及时反馈。', NULL, NULL, NULL, NULL, '["design_v1.pdf", "wireframe.png"]', @user_id, NOW(), NOW()),
(@task_id_1, 1, '发现了一个小问题，用户登录流程需要优化，已经修改。', NULL, NULL, NULL, NULL, NULL, @user_id, NOW(), NOW()),

-- 任务2的评论
(@task_id_2, 1, '开始开发这个功能模块，预计需要3天时间。', NULL, NULL, NULL, NULL, NULL, @user_id, NOW(), NOW()),
(@task_id_2, 1, '遇到了一个技术难点，正在研究解决方案。', NULL, NULL, NULL, NULL, NULL, @user_id, NOW(), NOW());

-- 插入任务跟进测试数据
INSERT INTO `project_task_record` (`task_id`, `record_type`, `content`, `follow_type`, `follow_date`, `next_plan`, `next_date`, `attachments`, `creator_id`, `created_at`, `updated_at`) VALUES

-- 任务1的跟进记录
(@task_id_1, 2, '与客户进行了电话沟通，确认了需求细节，客户对当前进度很满意。', 1, '2025-01-20 10:30:00', '下周一开始UI设计', '2025-01-27 09:00:00', NULL, @user_id, NOW(), NOW()),
(@task_id_1, 2, '召开了项目会议，讨论了技术方案，团队达成一致意见。', 2, '2025-01-21 14:00:00', '完成数据库设计', '2025-01-25 17:00:00', '["meeting_notes.docx"]', @user_id, NOW(), NOW()),
(@task_id_1, 2, '发送邮件给相关部门，同步项目进度和下一步计划。', 3, '2025-01-22 16:30:00', '等待部门反馈', '2025-01-24 12:00:00', NULL, @user_id, NOW(), NOW()),

-- 任务2的跟进记录
(@task_id_2, 2, '电话联系了技术专家，咨询了实现方案，获得了有价值的建议。', 1, '2025-01-21 11:00:00', '实施专家建议的方案', '2025-01-26 10:00:00', NULL, @user_id, NOW(), NOW()),
(@task_id_2, 2, '与产品经理开会讨论功能细节，明确了具体的实现要求。', 2, '2025-01-22 15:30:00', '开始编码实现', '2025-01-25 09:00:00', '["requirements_v2.pdf"]', @user_id, NOW(), NOW()),
(@task_id_2, 2, '其他沟通：与UI设计师协调界面设计，确保开发与设计的一致性。', 4, '2025-01-23 13:15:00', '完成界面开发', '2025-01-28 18:00:00', '["ui_specs.zip"]', @user_id, NOW(), NOW());

-- 验证插入的数据
SELECT 
    ptr.id,
    ptr.task_id,
    CASE ptr.record_type 
        WHEN 1 THEN '评论' 
        WHEN 2 THEN '跟进' 
        ELSE '未知' 
    END as record_type_text,
    ptr.content,
    CASE ptr.follow_type 
        WHEN 1 THEN '电话' 
        WHEN 2 THEN '会议' 
        WHEN 3 THEN '邮件' 
        WHEN 4 THEN '其他' 
        ELSE NULL 
    END as follow_type_text,
    ptr.follow_date,
    ptr.next_plan,
    ptr.next_date,
    ptr.attachments,
    ptr.created_at
FROM project_task_record ptr 
WHERE ptr.task_id IN (@task_id_1, @task_id_2)
ORDER BY ptr.task_id, ptr.record_type, ptr.created_at;

-- 统计数据
SELECT 
    task_id,
    record_type,
    CASE record_type 
        WHEN 1 THEN '评论' 
        WHEN 2 THEN '跟进' 
        ELSE '未知' 
    END as type_name,
    COUNT(*) as count
FROM project_task_record 
WHERE task_id IN (@task_id_1, @task_id_2)
GROUP BY task_id, record_type
ORDER BY task_id, record_type;

-- 如果需要清理测试数据，可以使用以下SQL：
/*
DELETE FROM project_task_record 
WHERE task_id IN (@task_id_1, @task_id_2) 
  AND creator_id = @user_id 
  AND created_at >= CURDATE();
*/
