# 修复watch formId导致的重复请求问题

## 🚨 问题描述

编辑申请时，虽然 FormManager 正确跳过了 `showForm` 调用，但仍然出现重复的详情接口请求：

```
FormManager收到编辑数据: {id: 134, formData: {…}}
直接传递表单数据到业务组件
hr_leave-form setFormData called with data: {...}  ✅ 正确设置数据
FormManager: 编辑模式或已有数据，跳过 showForm 调用  ✅ 正确跳过
hr_leave-form loadFormData 获取到的数据: {id: 37, ...}  ❌ 仍然发起请求
```

## 🔍 问题分析

### 根本原因
在 `hr_leave-form.vue` 中有一个 watch 监听 `props.formId` 的变化：

```typescript
// 问题代码
watch(() => props.formId, (newId) => {
  if (newId && dialogVisible.value) {
    loadFormData(newId)  // 这里触发了重复请求
  }
}, { immediate: false })
```

### 触发时序
1. `FormManager.setEditData()` 设置 `currentFormId.value = data.id`
2. `FormManager` 通过 `:formId="currentFormId"` 传递给子组件
3. `hr_leave-form.vue` 的 `setFormData` 设置数据并显示对话框
4. `props.formId` 变化触发 watch
5. 由于 `dialogVisible.value = true`，条件满足，调用 `loadFormData`

### 数据传递链
```
Application.vue (editApplication)
  ↓ setEditData({id: 134, formData: {...}})
FormManager.vue
  ↓ currentFormId.value = 134
  ↓ :formId="134" 传递给子组件
  ↓ setFormData({id: 134, ...})
hr_leave-form.vue
  ↓ 设置数据，显示对话框
  ↓ watch 监听到 props.formId 变化
  ↓ loadFormData(134) ❌ 重复请求
```

## ✅ 修复方案

### 1. 添加状态标记
引入 `isDataSetBySetFormData` 标记，区分数据来源：

```typescript
// 标记是否通过setFormData设置了数据
const isDataSetBySetFormData = ref(false)
```

### 2. 修改 watch 条件
只有在非 `setFormData` 设置数据时才触发 `loadFormData`：

```typescript
watch(() => props.formId, (newId) => {
  console.log('hr_leave-form watch formId 变化:', newId, '对话框可见:', dialogVisible.value, '是否通过setFormData设置:', isDataSetBySetFormData.value)
  
  // 只有在对话框可见、有新ID、且不是通过setFormData设置数据时才加载
  if (newId && dialogVisible.value && !isDataSetBySetFormData.value) {
    console.log('hr_leave-form watch 触发 loadFormData')
    loadFormData(newId)
  } else {
    console.log('hr_leave-form watch 跳过 loadFormData')
  }
}, { immediate: false })
```

### 3. 在 setFormData 中设置标记
```typescript
const setFormData = (data: any) => {
  if (data) {
    // 标记数据是通过setFormData设置的
    isDataSetBySetFormData.value = true
    
    // ... 其他逻辑
    
    // 显示对话框
    dialogVisible.value = true
  }
}
```

### 4. 重置标记
在对话框关闭时重置标记：

```typescript
const handleCancel = () => {
  dialogVisible.value = false
  // 重置标记
  isDataSetBySetFormData.value = false
  emit('cancel')
}

const handleDialogClose = () => {
  dialogVisible.value = false
  // 重置标记
  isDataSetBySetFormData.value = false
  emit('cancel')
}
```

## 🎯 修复逻辑

### 场景1：新建表单
```
1. FormManager 不设置 currentFormId
2. hr_leave-form 的 props.formId 为空或0
3. watch 不触发 loadFormData
4. 显示空表单 ✅
```

### 场景2：编辑表单（通过showForm）
```
1. FormManager 调用 showForm(id)
2. hr_leave-form 的 showForm 方法调用 loadFormData
3. props.formId 变化，但 isDataSetBySetFormData = false
4. watch 触发 loadFormData（正常的重复，但数据一致） ✅
```

### 场景3：编辑表单（通过setFormData）
```
1. FormManager 调用 setFormData(data)
2. isDataSetBySetFormData = true
3. props.formId 变化，但 watch 跳过 loadFormData
4. 只使用 setFormData 的数据 ✅
```

## 🧪 测试验证

### 测试步骤
1. 打开浏览器开发者工具的Network面板
2. 访问工作流申请页面
3. 点击编辑一个草稿申请
4. 观察控制台日志和Network请求

### 预期结果
```
FormManager收到编辑数据: {id: 134, formData: {…}}
直接传递表单数据到业务组件
hr_leave-form setFormData called with data: {...}
hr_leave-form watch formId 变化: 134 对话框可见: true 是否通过setFormData设置: true
hr_leave-form watch 跳过 loadFormData  ✅
```

- ✅ 只有一次详情接口请求（来自 Application.vue）
- ✅ 表单正确显示并填充数据
- ✅ watch 正确跳过 loadFormData 调用
- ✅ 控制台显示详细的调试信息

## 📋 修复清单

- ✅ 添加 `isDataSetBySetFormData` 状态标记
- ✅ 修改 watch 条件，添加标记检查
- ✅ 在 `setFormData` 中设置标记
- ✅ 在对话框关闭时重置标记
- ✅ 添加详细的调试日志

## 🔄 相关文件修改

1. `frontend/src/views/workflow/components/business-forms/hr_leave-form.vue`
   - 添加 `isDataSetBySetFormData` 状态标记
   - 修改 watch `props.formId` 的条件逻辑
   - 在 `setFormData` 中设置标记
   - 在 `handleCancel` 和 `handleDialogClose` 中重置标记

## 🎯 关键改进点

1. **状态区分**：明确区分数据来源（setFormData vs loadFormData）
2. **条件优化**：使用更精确的条件判断是否需要加载数据
3. **生命周期管理**：正确管理标记的设置和重置
4. **调试友好**：添加详细的日志输出便于问题排查

## 💡 设计思路

这个修复采用了"数据来源标记"的设计模式：
- 通过标记区分数据的不同来源
- 避免在已有数据时的重复加载
- 保持新建和编辑场景的正确行为
- 确保状态的正确清理和重置

修复完成后，编辑申请时不再会因为 watch `props.formId` 而重复请求详情接口。
