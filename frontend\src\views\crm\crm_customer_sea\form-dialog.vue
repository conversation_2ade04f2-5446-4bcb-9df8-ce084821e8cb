<script setup lang="ts">
  import { ref, reactive, computed } from 'vue'
  import {
    ElMessage,
    ElDialog,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElButton,
    ElRow,
    ElCol,
    ElSwitch,
    FormRules
  } from 'element-plus'
  import { CrmCustomerSeaApi } from '@/api/crm/crmCustomerSea'
  import { ApiStatus } from '@/utils/http/status'
  import RegionSelector from '@/components/custom/RegionSelector/index.vue'
  import { Document, User } from '@element-plus/icons-vue'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number | string
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: ''
  })

  // 组件事件
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 响应式数据
  const loading = ref(false)
  const formRef = ref()

  // 表单数据
  const formData = reactive({
    customer_name: '',
    level: 1,
    industry: '',
    source: '',
    phone: '',
    email: '',
    website: '',
    region_province: '',
    region_city: '',
    region_district: '',
    address: '',
    zip_code: '',
    description: '',
    remark: '',
    status: 1,
    in_sea: 1,
    into_sea_time: '',
    sea_id: 0,
    lock_status: 0,
    lock_expire_time: '',
    recycle_reason: ''
  })

  // 地区数据处理
  const regionData = computed({
    get() {
      return {
        region_province: formData.region_province,
        region_city: formData.region_city,
        region_district: formData.region_district
      }
    },
    set(value: any) {
      if (value && typeof value === 'object') {
        formData.region_province = value.region_province || ''
        formData.region_city = value.region_city || ''
        formData.region_district = value.region_district || ''
      } else {
        formData.region_province = ''
        formData.region_city = ''
        formData.region_district = ''
      }
    }
  })

  // 地区变化处理
  const handleRegionChange = (value: any, selectedData: any) => {
    console.log('地区选择变化:', value, selectedData)
    // 确保地区数据已经通过 computed 属性正确更新到 formData 中
    if (value && typeof value === 'object') {
      console.log('地区数据已更新到表单:', {
        region_province: formData.region_province,
        region_city: formData.region_city,
        region_district: formData.region_district
      })
    }
  }

  // 表单验证规则
  const rules = {
    customer_name: [
      { required: true, message: '请输入客户名称', trigger: 'blur' },
      { min: 2, max: 200, message: '客户名称长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    level: [{ required: true, message: '请选择客户级别', trigger: 'change' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
  } as FormRules

  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const dialogTitle = computed(() => {
    return props.customerId ? '编辑公海客户' : '新增公海客户'
  })

  // 客户级别选项
  const levelOptions = [
    { label: '普通客户', value: 1 },
    { label: '重要客户', value: 2 },
    { label: '战略客户', value: 3 }
  ]

  // 客户来源选项
  const sourceOptions = [
    { label: '网络推广', value: '网络推广' },
    { label: '电话营销', value: '电话营销' },
    { label: '客户介绍', value: '客户介绍' },
    { label: '展会', value: '展会' },
    { label: '其他', value: '其他' }
  ]

  // 状态选项
  /*const statusOptions = [
    { label: '正常', value: 1 },
    { label: '禁用', value: 0 }
  ]*/

  // 锁定状态选项
  /*const lockStatusOptions = [
    { label: '未锁定', value: 0 },
    { label: '已锁定', value: 1 }
  ]*/

  // 获取客户详情
  const getCustomerDetail = async () => {
    if (!props.customerId) return

    loading.value = true
    try {
      const res = await CrmCustomerSeaApi.detail(props.customerId)
      if (res.code === ApiStatus.success) {
        Object.assign(formData, res.data)
      }
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, {
      customer_name: '',
      level: 1,
      industry: '',
      source: '',
      phone: '',
      email: '',
      website: '',
      region_province: '',
      region_city: '',
      region_district: '',
      address: '',
      zip_code: '',
      description: '',
      remark: '',
      status: 1,
      in_sea: 1,
      into_sea_time: '',
      sea_id: 0,
      lock_status: 0,
      lock_expire_time: '',
      recycle_reason: ''
    })
    formRef.value?.clearValidate()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 准备提交数据，排除ID字段
      const submitData = { ...formData }

      const res = await CrmCustomerSeaApi.edit({ ...submitData, id: props.customerId })

      if (res.code === ApiStatus.success) {
        ElMessage.success('保存成功')
        emit('success')
        dialogVisible.value = false
      } else {
        ElMessage.error(res.message || '保存失败')
      }
    } catch (error) {
      console.error('表单提交失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 取消操作
  const handleCancel = () => {
    dialogVisible.value = false
  }

  // 监听对话框打开
  const handleDialogOpen = () => {
    resetForm()
    if (props.customerId) {
      getCustomerDetail()
    }
  }

  // 暴露方法给父组件
  defineExpose({
    resetForm,
    getCustomerDetail
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1000px"
    top="5vh"
    destroy-on-close
    :close-on-click-modal="false"
    @open="handleDialogOpen"
    class="customer-sea-form-dialog"
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        label-position="right"
        v-loading="loading"
        class="customer-sea-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            <span>基本信息</span>
          </div>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="客户名称" prop="customer_name" required>
                <ElInput v-model="formData.customer_name" placeholder="请输入客户名称" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="客户级别" prop="level" required>
                <ElSelect
                  v-model="formData.level"
                  placeholder="请选择客户级别"
                  style="width: 100%"
                  clearable
                >
                  <ElOption
                    v-for="item in levelOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="所属行业" prop="industry">
                <ElInput v-model="formData.industry" placeholder="请输入所属行业" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="客户来源" prop="source">
                <ElSelect
                  v-model="formData.source"
                  placeholder="请选择客户来源"
                  style="width: 100%"
                  clearable
                >
                  <ElOption
                    v-for="item in sourceOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="客户状态" prop="status">
                <ElSwitch
                  v-model="formData.status"
                  :active-value="1"
                  :inactive-value="0"
                  active-text="启用"
                  inactive-text="禁用"
                  inline-prompt
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </div>

        <!-- 联系信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <User />
            </el-icon>
            <span>联系信息</span>
          </div>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="联系电话" prop="phone">
                <ElInput v-model="formData.phone" placeholder="请输入联系电话" clearable />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="邮箱地址" prop="email">
                <ElInput v-model="formData.email" placeholder="请输入邮箱地址" clearable />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="20">
            <ElCol :span="24">
              <ElFormItem label="网站地址" prop="website">
                <ElInput v-model="formData.website" placeholder="请输入网站地址" clearable />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="所在地区" prop="website">
                <RegionSelector
                  v-model="regionData"
                  return-type="object"
                  :field-mapping="{
                    province: 'region_province',
                    city: 'region_city',
                    district: 'region_district'
                  }"
                  placeholder="请选择省市区"
                  style="width: 100%"
                  size="large"
                  @change="handleRegionChange"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="24">
              <ElFormItem label="详细地址" prop="address">
                <ElInput
                  v-model="formData.address"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入详细地址"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注" />
          </ElFormItem>
        </div>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit" :loading="loading"> 确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .customer-sea-form-dialog {
    .dialog-content {
      max-height: 70vh;
      overflow-y: auto;
    }

    .customer-sea-form {
      padding: 0 20px;
    }

    /* 表单分组样式 */

    .form-section {
      margin-bottom: 28px;
      padding: 28px;
      background: white;
      border-radius: 12px;
      border: 1px solid #e4e7ed;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 28px;
      padding-bottom: 16px;
      border-bottom: 2px solid #409eff;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-title .el-icon {
      color: #409eff;
      font-size: 18px;
    }

    /* 联系人信息提示样式 */

    .contact-info-tip {
      margin-bottom: 24px;
    }

    .dialog-footer {
      text-align: right;
      padding: 20px 0 0 0;
    }
  }
</style>
