<?php
/**
 * MySQL SQL执行工具
 * 用于替代MCP数据库工具执行权限测试SQL
 */

// 数据库配置
$config = [
    'host' => '*************',
    'port' => 3306,
    'username' => 'www_bs_com',
    'password' => 'PdadjMXmNy8Pn9tj',
    'database' => 'www_bs_com',
    'charset' => 'utf8mb4'
];

// 创建数据库连接
try {
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['charset']}"
    ]);
    
    echo "✅ 数据库连接成功！\n\n";
    
} catch (PDOException $e) {
    die("❌ 数据库连接失败: " . $e->getMessage() . "\n");
}

/**
 * 执行SQL文件
 */
function executeSqlFile($pdo, $filename) {
    if (!file_exists($filename)) {
        echo "❌ 文件不存在: $filename\n";
        return false;
    }
    
    $sql = file_get_contents($filename);
    if ($sql === false) {
        echo "❌ 无法读取文件: $filename\n";
        return false;
    }
    
    // 分割SQL语句
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^(--|#)/', $stmt);
        }
    );
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty(trim($statement))) continue;
        
        try {
            $result = $pdo->exec($statement);
            $successCount++;
            
            // 如果是SELECT语句，显示结果
            if (stripos(trim($statement), 'SELECT') === 0) {
                $stmt = $pdo->query($statement);
                $rows = $stmt->fetchAll();
                
                if (!empty($rows)) {
                    echo "📊 查询结果:\n";
                    foreach ($rows as $row) {
                        echo "   " . json_encode($row, JSON_UNESCAPED_UNICODE) . "\n";
                    }
                    echo "\n";
                }
            } else {
                echo "✅ 执行成功: " . substr(trim($statement), 0, 50) . "...\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "❌ 执行失败: " . $e->getMessage() . "\n";
            echo "   SQL: " . substr(trim($statement), 0, 100) . "...\n\n";
        }
    }
    
    echo "\n📈 执行统计: 成功 $successCount 条, 失败 $errorCount 条\n\n";
    return $errorCount === 0;
}

/**
 * 执行单条查询
 */
function executeQuery($pdo, $sql) {
    try {
        $stmt = $pdo->query($sql);
        $rows = $stmt->fetchAll();
        
        echo "📊 查询结果:\n";
        if (empty($rows)) {
            echo "   (无数据)\n";
        } else {
            foreach ($rows as $row) {
                echo "   " . json_encode($row, JSON_UNESCAPED_UNICODE) . "\n";
            }
        }
        echo "\n";
        
        return $rows;
        
    } catch (PDOException $e) {
        echo "❌ 查询失败: " . $e->getMessage() . "\n";
        return false;
    }
}

// 主程序
echo "=== MySQL SQL执行工具 ===\n\n";

// 检查命令行参数
if ($argc > 1) {
    $action = $argv[1];
    
    switch ($action) {
        case 'test':
            echo "🔍 测试数据库连接...\n";
            $result = executeQuery($pdo, "SELECT 'Database connection test' as message, NOW() as current_time");
            break;
            
        case 'structure':
            echo "🔍 获取表结构信息...\n";
            if (file_exists('get_table_structure.sql')) {
                executeSqlFile($pdo, 'get_table_structure.sql');
            } else {
                echo "❌ get_table_structure.sql 文件不存在\n";
            }
            break;
            
        case 'create':
            echo "🚀 执行权限测试数据创建...\n";
            if (file_exists('execute_permission_test.sql')) {
                executeSqlFile($pdo, 'execute_permission_test.sql');
            } else {
                echo "❌ execute_permission_test.sql 文件不存在\n";
            }
            break;
            
        case 'verify':
            echo "✅ 执行权限测试验证...\n";
            if (file_exists('verify_permission_test.sql')) {
                executeSqlFile($pdo, 'verify_permission_test.sql');
            } else {
                echo "❌ verify_permission_test.sql 文件不存在\n";
            }
            break;
            
        case 'query':
            if ($argc > 2) {
                $sql = $argv[2];
                echo "🔍 执行查询: $sql\n";
                executeQuery($pdo, $sql);
            } else {
                echo "❌ 请提供SQL查询语句\n";
                echo "用法: php execute_sql.php query \"SELECT * FROM system_admin LIMIT 5\"\n";
            }
            break;
            
        default:
            echo "❌ 未知操作: $action\n";
            echo "可用操作: test, structure, create, verify, query\n";
            break;
    }
} else {
    // 交互模式
    echo "请选择操作:\n";
    echo "1. 测试数据库连接\n";
    echo "2. 获取表结构信息\n";
    echo "3. 创建权限测试数据\n";
    echo "4. 验证权限测试数据\n";
    echo "5. 自定义查询\n";
    echo "请输入选项 (1-5): ";
    
    $choice = trim(fgets(STDIN));
    
    switch ($choice) {
        case '1':
            executeQuery($pdo, "SELECT 'Database connection test' as message, NOW() as current_time");
            break;
            
        case '2':
            if (file_exists('get_table_structure.sql')) {
                executeSqlFile($pdo, 'get_table_structure.sql');
            } else {
                echo "❌ get_table_structure.sql 文件不存在\n";
            }
            break;
            
        case '3':
            if (file_exists('execute_permission_test.sql')) {
                executeSqlFile($pdo, 'execute_permission_test.sql');
            } else {
                echo "❌ execute_permission_test.sql 文件不存在\n";
            }
            break;
            
        case '4':
            if (file_exists('verify_permission_test.sql')) {
                executeSqlFile($pdo, 'verify_permission_test.sql');
            } else {
                echo "❌ verify_permission_test.sql 文件不存在\n";
            }
            break;
            
        case '5':
            echo "请输入SQL查询: ";
            $sql = trim(fgets(STDIN));
            if (!empty($sql)) {
                executeQuery($pdo, $sql);
            }
            break;
            
        default:
            echo "❌ 无效选项\n";
            break;
    }
}

echo "=== 执行完成 ===\n";
?>
