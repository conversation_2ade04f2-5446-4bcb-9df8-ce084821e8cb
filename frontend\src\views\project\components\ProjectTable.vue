<template>
  <div class="project-table">
    <el-table
      :data="projects"
      :loading="props.loading"
      stripe
      style="width: 100%"
      @row-click="handleRowClick"
    >
      <el-table-column prop="id" label="ID" width="80" />

      <el-table-column prop="name" label="项目名称" min-width="200">
        <template #default="{ row }">
          <div class="project-name-cell">
            <span class="name" @click.stop="$emit('view-detail', row)">
              {{ row.name }}
            </span>
            <el-tag
              v-if="row.status"
              :type="getStatusType(row.status)"
              size="small"
              class="status-tag"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="项目描述" min-width="200">
        <template #default="{ row }">
          <span class="description" :title="row.description">
            {{ row.description || '暂无描述' }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="进度" width="120">
        <template #default="{ row }">
          <div class="progress-cell">
            <el-progress
              :percentage="Math.round(row.progress || 0)"
              :stroke-width="6"
              :show-text="false"
            />
            <span class="progress-text">{{ Math.round(row.progress || 0) }}%</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="统计" width="150">
        <template #default="{ row }">
          <div class="stats-cell">
            <div class="stat-item">
              <el-icon>
                <Tickets />
              </el-icon>
              <span>{{ row.task_count || 0 }}</span>
            </div>
            <div class="stat-item">
              <el-icon>
                <User />
              </el-icon>
              <span>{{ row.member_count || 0 }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="owner_name" label="负责人" width="120">
        <template #default="{ row }">
          <span>{{ row.owner_name || '未分配' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="end_date" label="截止时间" width="120">
        <template #default="{ row }">
          <span :class="{ overdue: isOverdue(row.end_date) }">
            {{ formatDate(row.end_date) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="260" fixed="right">
        <template #default="{ row }">
          <div class="operation-buttons" @click.stop>
            <ArtButtonTable text="详情" @click="handleViewDetail(row)" />
            <ArtButtonTable text="编辑" type="edit" @click="handleEdit(row)" />
            <ArtButtonTable text="删除" type="delete" @click="handleDelete(row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
  import { Tickets, User } from '@element-plus/icons-vue'
  import { formatDate } from '@/utils/date'
  import { useRouter } from 'vue-router'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'

  // Props
  interface Props {
    projects: any[]
    loading?: boolean
  }

  const props = defineProps<Props>()

  // Router
  const router = useRouter()

  // Emits
  const emit = defineEmits<{
    'view-detail': [project: any]
    edit: [project: any]
    delete: [project: any]
  }>()

  // 方法
  const handleRowClick = (row: any) => {
    // 点击行查看详情，跳转到项目详情页
    if (row.id) {
      router.push(`/project/detail/${row.id}`)
    }
  }

  // 操作按钮处理方法 - 阻止事件冒泡
  const handleViewDetail = (row: any) => {
    emit('view-detail', row)
  }

  const handleEdit = (row: any) => {
    emit('edit', row)
  }

  const handleDelete = (row: any) => {
    emit('delete', row)
  }

  const getStatusType = (status: number) => {
    const typeMap = {
      1: 'primary', // 进行中
      2: 'success', // 已完成
      3: 'warning', // 已暂停
      4: 'danger' // 已取消
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: number) => {
    const textMap = {
      1: '进行中',
      2: '已完成',
      3: '已暂停',
      4: '已取消'
    }
    return textMap[status] || '未知'
  }

  const isOverdue = (endDate: string) => {
    if (!endDate) return false
    return new Date(endDate) < new Date()
  }
</script>

<style scoped lang="scss">
  .project-table {
    // 优化渲染性能
    will-change: scroll-position;
    transform: translateZ(0);

    .project-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .name {
        cursor: pointer;
        color: #1664ff;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }

      .status-tag {
        flex-shrink: 0;
      }
    }

    .description {
      color: #86909c;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.4;
    }

    .progress-cell {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .progress-text {
        font-size: 12px;
        color: #86909c;
        text-align: center;
      }
    }

    .stats-cell {
      display: flex;
      gap: 12px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #86909c;

        .el-icon {
          font-size: 14px;
        }
      }
    }

    .overdue {
      color: #ff4d4f;
    }

    .operation-buttons {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
    }
  }

  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;
    }

    // 优化滚动性能
    .el-table__body-wrapper {
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      // 启用硬件加速
      transform: translateZ(0);
      backface-visibility: hidden;
      perspective: 1000px;
    }

    // 优化表格单元格渲染
    .el-table__cell {
      // 避免重排
      contain: layout style paint;
    }
  }
</style>
