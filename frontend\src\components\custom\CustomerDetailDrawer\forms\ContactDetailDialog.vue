<template>
  <el-dialog
    v-model="visible"
    title="联系人详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-loading="loading" class="contact-detail">
      <div v-if="contactDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">
            {{ contactDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            <el-tag :type="getGenderTagType(contactDetail.gender)">
              {{ getGenderText(contactDetail.gender) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="职位">
            {{ contactDetail.position || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ contactDetail.department || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ contactDetail.mobile || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="电话">
            {{ contactDetail.phone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ contactDetail.email || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="微信">
            {{ contactDetail.wechat || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="QQ">
            {{ contactDetail.qq || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="生日">
            {{ formatBirthday(contactDetail.birthday) }}
          </el-descriptions-item>
          <el-descriptions-item label="重要程度">
            <el-tag :type="getImportanceTagType(contactDetail.importance)">
              {{ getImportanceText(contactDetail.importance) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="角色类型">
            <el-tag v-if="contactDetail.role_type" :type="getRoleTypeTagType(contactDetail.role_type)">
              {{ getRoleTypeText(contactDetail.role_type) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否主要联系人">
            <el-tag :type="contactDetail.is_primary ? 'success' : 'info'">
              {{ contactDetail.is_primary ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ contactDetail.created_at }}
          </el-descriptions-item>
          <el-descriptions-item label="地址" :span="2">
            {{ contactDetail.address || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ contactDetail.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button 
          v-if="hasButtonPermission('crm:crm_customer_my:edit_contact')"
          type="primary" 
          @click="handleEdit">
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'

  // 组件属性
  interface Props {
    modelValue: boolean
    contactId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    contactId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    edit: [contactId: number]
  }>()

  // 权限验证
  const { hasButtonPermission } = useCustomerPermission()

  // 响应式数据
  const loading = ref(false)
  const contactDetail = ref<any>(null)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 监听对话框显示状态
  watch(visible, (newVal) => {
    if (newVal && props.contactId) {
      loadContactDetail()
    }
  })

  // 加载联系人详情
  const loadContactDetail = async () => {
    if (!props.contactId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getContactDetail(props.contactId)
      
      if (res.code === ApiStatus.success) {
        contactDetail.value = res.data
      } else {
        ElMessage.error(res.message || '加载联系人详情失败')
      }
    } catch (error) {
      console.error('加载联系人详情失败:', error)
      ElMessage.error('加载联系人详情失败')
    } finally {
      loading.value = false
    }
  }

  // 获取性别标签类型
  const getGenderTagType = (gender: number): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    switch (gender) {
      case 1: return 'primary'  // 男
      case 2: return 'danger'   // 女
      default: return 'info'    // 保密
    }
  }

  // 获取性别文本
  const getGenderText = (gender: number) => {
    const genderMap: Record<number, string> = {
      0: '保密',
      1: '男',
      2: '女'
    }
    return genderMap[gender] || '保密'
  }

  // 获取重要程度标签类型
  const getImportanceTagType = (importance: number): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      0: 'info',
      1: 'warning',
      2: 'danger'
    }
    return typeMap[importance] || 'info'
  }

  // 获取重要程度文本
  const getImportanceText = (importance: number) => {
    const importanceMap: Record<number, string> = {
      0: '普通',
      1: '重要',
      2: '核心'
    }
    return importanceMap[importance] || '普通'
  }

  // 获取角色类型标签类型
  const getRoleTypeTagType = (roleType: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      'decision': 'danger',
      'user': 'primary',
      'influence': 'warning'
    }
    return typeMap[roleType] || 'info'
  }

  // 获取角色类型文本
  const getRoleTypeText = (roleType: string) => {
    const roleTypeMap: Record<string, string> = {
      'decision': '决策者',
      'user': '使用者',
      'influence': '影响者'
    }
    return roleTypeMap[roleType] || roleType
  }

  // 格式化生日
  const formatBirthday = (birthday: string) => {
    if (!birthday) return '-'
    
    const date = new Date(birthday)
    if (isNaN(date.getTime())) return birthday
    
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${year}年${month}月${day}日`
  }

  // 编辑按钮处理
  const handleEdit = () => {
    emit('edit', props.contactId!)
    visible.value = false
  }
</script>

<style scoped lang="scss">
  .contact-detail {
    .detail-content {
      margin-top: 16px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
