import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 项目管理相关接口
 */
export class ProjectApi {
  /**
   * 获取项目列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project/index',
      params
    })
  }

  /**
   * 获取项目详情
   * @param id 项目ID
   */
  static detail(id: number | string) {
    return request.get<BaseResult>({
      url: `/project/project/detail/${id}`
    })
  }

  /**
   * 添加项目
   * @param data 项目数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project/add',
      data
    })
  }

  /**
   * 更新项目
   * @param id 项目ID
   * @param data 项目数据
   */
  static update(id: number, data: any) {
    return request.post<BaseResult>({
      url: `/project/project/edit/${id}`,
      data
    })
  }

  /**
   * 删除项目
   * @param id 项目ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project/delete/${id}`
    })
  }

  /**
   * 批量删除项目
   * @param ids 项目ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: '/project/project/batchDelete',
      data: { ids }
    })
  }

  /**
   * 更新项目字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project/updateField',
      data
    })
  }

  /**
   * 修改项目状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/project/project/status/${data.id}`,
      data
    })
  }

  // ==================== 自定义业务接口 ====================

  /**
   * 获取我的项目列表
   * @param params 查询参数
   */
  static myProjects(params?: any) {
    return request.get<BaseResult>({
      url: '/project/project/my',
      params
    })
  }

  /**
   * 获取项目详情（包含统计信息）
   * @param id 项目ID
   */
  static projectDetail(id: number | string) {
    return request.get<BaseResult>({
      url: `/project/project/project-detail/${id}`
    })
  }

  /**
   * 获取项目看板数据
   * @param projectId 项目ID
   */
  static kanban(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/kanban',
      params: { project_id: projectId }
    })
  }

  /**
   * 添加项目成员
   * @param data 成员数据
   */
  static addMember(data: { project_id: number; user_id: number; role: string }) {
    return request.post<BaseResult>({
      url: '/project/project/add-member',
      data
    })
  }

  /**
   * 移除项目成员
   * @param data 成员数据
   */
  static removeMember(data: { project_id: number; user_id: number }) {
    return request.post<BaseResult>({
      url: '/project/project/remove-member',
      data
    })
  }

  /**
   * 获取项目成员列表
   * @param projectId 项目ID
   */
  static members(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project_member/index',
      params: { project_id: projectId }
    })
  }

  /**
   * 更新成员角色
   * @param data 成员数据
   */
  static updateMemberRole(data: { project_id: number; user_id: number; role: string }) {
    return request.post<BaseResult>({
      url: '/project/project_member/updateField',
      data: { id: data.project_id, role: data.role }
    })
  }

  /**
   * 获取任务状态统计
   * @param projectId 项目ID
   */
  static taskStatusStats(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/task-status-stats',
      params: { project_id: projectId }
    })
  }

  /**
   * 获取任务优先级统计
   * @param projectId 项目ID
   */
  static taskPriorityStats(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/task-priority-stats',
      params: { project_id: projectId }
    })
  }

  /**
   * 获取项目进度趋势
   * @param projectId 项目ID
   */
  static progressTrend(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/progress-trend',
      params: { project_id: projectId }
    })
  }

  /**
   * 获取成员统计
   * @param projectId 项目ID
   */
  static memberStats(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/member-stats',
      params: { project_id: projectId }
    })
  }

  /**
   * 获取最近活动
   * @param projectId 项目ID
   */
  static recentActivities(projectId: number | string) {
    return request.get<BaseResult>({
      url: '/project/project/recent-activities',
      params: { project_id: projectId }
    })
  }

  /**
   * 获取项目成员选项（用于任务执行人选择）
   * @param projectId 项目ID
   */
  static memberOptions(projectId: number | string) {
    return request.get<BaseResult>({
      url: `/project/project/memberOptions/${projectId}`
    })
  }

  /**
   * 检查用户是否为项目负责人
   * @param projectId 项目ID
   */
  static checkProjectOwner(projectId: number | string) {
    return request.get<BaseResult>({
      url: `/project/project/checkOwner/${projectId}`
    })
  }

  /**
   * 获取项目负责人选项列表
   */
  static getOwnerOptions() {
    return request.get<BaseResult>({
      url: '/project/project/ownerOptions'
    })
  }
}

/**
 * 任务管理相关接口
 */
export class TaskApi {
  /**
   * 获取任务列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/task/index',
      params
    })
  }

  /**
   * 获取任务详情
   * @param id 任务ID
   */
  static detail(id: number | string) {
    return request.get<BaseResult>({
      url: `/project/task/detail/${id}`
    })
  }

  /**
   * 添加任务
   * @param data 任务数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/task/add',
      data
    })
  }

  /**
   * 更新任务
   * @param data 任务数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/task/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除任务
   * @param id 任务ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/task/delete/${id}`
    })
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: '/project/task/batchDelete',
      data: { ids }
    })
  }

  /**
   * 更新任务字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/task/updateField',
      data
    })
  }

  /**
   * 修改任务状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/project/task/status/${data.id}`,
      data
    })
  }

  // ==================== 自定义业务接口 ====================

  /**
   * 获取我的任务列表
   * @param params 查询参数
   */
  static myTasks(params?: any) {
    return request.get<BaseResult>({
      url: '/project/task/my',
      params
    })
  }

  /**
   * 更新任务状态
   * @param data 状态数据
   */
  static updateStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/project/task/update-status/${data.id}`,
      data
    })
  }

  /**
   * 分配任务
   * @param data 分配数据
   */
  static assign(data: { id: number | string; assignee_id: number }) {
    return request.post<BaseResult>({
      url: `/project/task/assign/${data.id}`,
      data
    })
  }

  // ==================== 任务记录相关接口 ====================

  /**
   * 添加任务评论
   * @param data 评论数据
   */
  static addComment(data: { task_id: number; content: string; attachments?: any[] }) {
    return request.post<BaseResult>({
      url: '/project/task/add-comment',
      data
    })
  }

  /**
   * 添加任务跟进
   * @param data 跟进数据
   */
  static addFollow(data: {
    task_id: number
    content: string
    follow_type: string
    follow_date: string
    next_plan?: string
    next_date?: string
    attachments?: any[]
  }) {
    return request.post<BaseResult>({
      url: '/project/task/add-follow',
      data
    })
  }

  /**
   * 获取任务评论列表
   * @param taskId 任务ID
   * @param params 查询参数
   */
  static getComments(taskId: number | string, params?: { page?: number; limit?: number }) {
    return request.get<PaginationResult<any[]>>({
      url: `/project/task/${taskId}/comments`,
      params
    })
  }

  /**
   * 获取任务跟进列表
   * @param taskId 任务ID
   * @param params 查询参数
   */
  static getFollows(taskId: number | string, params?: { page?: number; limit?: number }) {
    return request.get<PaginationResult<any[]>>({
      url: `/project/task/${taskId}/follows`,
      params
    })
  }

  /**
   * 编辑记录（评论或跟进）
   * @param recordId 记录ID
   * @param data 更新数据
   */
  static editRecord(recordId: number | string, data: any) {
    return request.post<BaseResult>({
      url: `/project/task/record/${recordId}`,
      data
    })
  }

  /**
   * 删除记录（评论或跟进）
   * @param recordId 记录ID
   */
  static deleteRecord(recordId: number | string) {
    return request.post<BaseResult>({
      url: `/project/task/record/${recordId}`
    })
  }
}

/**
 * 项目成员相关接口
 */
export class ProjectMemberApi {
  /**
   * 获取项目成员列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_member/index',
      params
    })
  }

  /**
   * 获取成员详情
   * @param id 成员ID
   */
  static detail(id: number | string) {
    return request.get<BaseResult>({
      url: `/project/project_member/detail/${id}`
    })
  }

  /**
   * 添加成员
   * @param data 成员数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_member/add',
      data
    })
  }

  /**
   * 更新成员
   * @param data 成员数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/project_member/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除成员
   * @param id 成员ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_member/delete/${id}`
    })
  }
}
