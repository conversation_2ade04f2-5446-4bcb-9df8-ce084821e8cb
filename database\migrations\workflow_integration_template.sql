-- 工作流业务表集成标准字段模板
-- 使用说明：将此模板中的字段添加到需要集成工作流的业务表中

-- ============================================
-- 工作流集成标准字段
-- ============================================

-- 工作流实例ID（关联工作流实例表）
ALTER TABLE `{table_name}` ADD COLUMN `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID';

-- 审批状态（冗余存储，提高查询性能）
ALTER TABLE `{table_name}` ADD COLUMN `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废';

-- 提交审批时间
ALTER TABLE `{table_name}` ADD COLUMN `submit_time` datetime DEFAULT NULL COMMENT '提交审批时间';

-- 审批完成时间
ALTER TABLE `{table_name}` ADD COLUMN `approval_time` datetime DEFAULT NULL COMMENT '审批完成时间';

-- ============================================
-- 必须的索引
-- ============================================

-- 工作流实例ID索引（用于关联查询）
ALTER TABLE `{table_name}` ADD INDEX `idx_workflow_instance_id` (`workflow_instance_id`);

-- 审批状态索引（用于状态筛选）
ALTER TABLE `{table_name}` ADD INDEX `idx_approval_status` (`approval_status`);

-- 提交时间索引（用于时间范围查询）
ALTER TABLE `{table_name}` ADD INDEX `idx_submit_time` (`submit_time`);

-- 审批完成时间索引（用于时间范围查询）
ALTER TABLE `{table_name}` ADD INDEX `idx_approval_time` (`approval_time`);

-- ============================================
-- 外键约束（可选，根据需要添加）
-- ============================================

-- 工作流实例外键约束
-- ALTER TABLE `{table_name}` ADD CONSTRAINT `fk_{table_name}_workflow_instance` 
--     FOREIGN KEY (`workflow_instance_id`) REFERENCES `workflow_instance` (`id`) 
--     ON DELETE SET NULL ON UPDATE CASCADE;

-- ============================================
-- 使用示例
-- ============================================

-- 示例1：为合同表添加工作流字段
-- 将 {table_name} 替换为 crm_contract
/*
ALTER TABLE `crm_contract` ADD COLUMN `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID';
ALTER TABLE `crm_contract` ADD COLUMN `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废';
ALTER TABLE `crm_contract` ADD COLUMN `submit_time` datetime DEFAULT NULL COMMENT '提交审批时间';
ALTER TABLE `crm_contract` ADD COLUMN `approval_time` datetime DEFAULT NULL COMMENT '审批完成时间';

ALTER TABLE `crm_contract` ADD INDEX `idx_workflow_instance_id` (`workflow_instance_id`);
ALTER TABLE `crm_contract` ADD INDEX `idx_approval_status` (`approval_status`);
ALTER TABLE `crm_contract` ADD INDEX `idx_submit_time` (`submit_time`);
ALTER TABLE `crm_contract` ADD INDEX `idx_approval_time` (`approval_time`);
*/

-- 示例2：为回款记录表添加工作流字段
-- 将 {table_name} 替换为 crm_contract_receivable
/*
ALTER TABLE `crm_contract_receivable` ADD COLUMN `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID';
ALTER TABLE `crm_contract_receivable` ADD COLUMN `approval_status` tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废';
ALTER TABLE `crm_contract_receivable` ADD COLUMN `submit_time` datetime DEFAULT NULL COMMENT '提交审批时间';
ALTER TABLE `crm_contract_receivable` ADD COLUMN `approval_time` datetime DEFAULT NULL COMMENT '审批完成时间';

ALTER TABLE `crm_contract_receivable` ADD INDEX `idx_workflow_instance_id` (`workflow_instance_id`);
ALTER TABLE `crm_contract_receivable` ADD INDEX `idx_approval_status` (`approval_status`);
ALTER TABLE `crm_contract_receivable` ADD INDEX `idx_submit_time` (`submit_time`);
ALTER TABLE `crm_contract_receivable` ADD INDEX `idx_approval_time` (`approval_time`);
*/
