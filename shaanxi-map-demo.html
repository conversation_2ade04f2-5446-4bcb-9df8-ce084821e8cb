<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>陕西省手绘地图演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .map-container {
            padding: 40px;
            text-align: center;
        }
        
        .map-wrapper {
            display: inline-block;
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            background: white;
            padding: 20px;
        }
        
        #shaanxi-map {
            width: 100%;
            max-width: 400px;
            height: auto;
        }
        
        .info-panel {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .selected-city {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .city-info {
            color: #666;
            line-height: 1.6;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        /* SVG 样式增强 */
        .city-region {
            fill: #d2691e;
            stroke: #8b4513;
            stroke-width: 2;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .city-region:hover {
            fill: #ff7f50;
            stroke-width: 3;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
            transform: scale(1.02);
        }
        
        .city-region.selected {
            fill: #ff6347;
            stroke: #dc143c;
            stroke-width: 4;
        }
        
        .city-label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 12px;
            font-weight: bold;
            fill: #2c1810;
            text-anchor: middle;
            pointer-events: none;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .map-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>陕西省手绘地图</h1>
            <p>点击地图上的城市区域查看详细信息</p>
        </div>
        
        <div class="map-container">
            <div class="instructions">
                <h3>使用说明</h3>
                <p>🖱️ 鼠标悬停查看城市高亮效果<br>
                   👆 点击城市区域获取城市信息<br>
                   📱 支持移动端触摸操作</p>
            </div>
            
            <div class="map-wrapper">
                <svg id="shaanxi-map" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <filter id="shadow">
                            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
                        </filter>
                    </defs>
                    
                    <!-- 榆林市 -->
                    <path id="yulin" class="city-region" 
                          d="M280,30 Q320,25 360,40 L370,70 Q350,90 320,85 L300,95 Q280,80 270,60 Q275,45 280,30 Z"
                          data-city="榆林市"/>
                    
                    <!-- 延安市 -->
                    <path id="yanan" class="city-region"
                          d="M180,120 Q230,115 270,130 L280,170 Q260,195 230,190 L200,205 Q175,185 170,160 Q175,140 180,120 Z"
                          data-city="延安市"/>
                    
                    <!-- 咸阳市 -->
                    <path id="xianyang" class="city-region"
                          d="M120,220 Q150,215 180,225 L185,250 Q165,265 140,260 L115,270 Q105,250 110,235 Q115,225 120,220 Z"
                          data-city="咸阳市"/>
                    
                    <!-- 铜川市 -->
                    <path id="tongchuan" class="city-region"
                          d="M200,200 Q220,195 240,205 L245,225 Q230,235 210,230 L195,240 Q185,225 190,210 Q195,205 200,200 Z"
                          data-city="铜川市"/>
                    
                    <!-- 西安市 -->
                    <path id="xian" class="city-region"
                          d="M140,260 Q170,255 200,265 L205,290 Q185,305 160,300 L135,310 Q125,290 130,275 Q135,265 140,260 Z"
                          data-city="西安市"/>
                    
                    <!-- 渭南市 -->
                    <path id="weinan" class="city-region"
                          d="M210,270 Q250,265 290,280 L295,310 Q275,325 245,320 L220,330 Q200,310 205,285 Q210,275 210,270 Z"
                          data-city="渭南市"/>
                    
                    <!-- 宝鸡市 -->
                    <path id="baoji" class="city-region"
                          d="M60,280 Q90,275 120,285 L125,315 Q105,330 80,325 L55,335 Q45,315 50,300 Q55,285 60,280 Z"
                          data-city="宝鸡市"/>
                    
                    <!-- 汉中市 -->
                    <path id="hanzhong" class="city-region"
                          d="M80,360 Q120,355 160,370 L165,400 Q145,415 115,410 L90,420 Q70,400 75,385 Q80,370 80,360 Z"
                          data-city="汉中市"/>
                    
                    <!-- 安康市 -->
                    <path id="ankang" class="city-region"
                          d="M180,380 Q220,375 260,390 L265,420 Q245,435 215,430 L190,440 Q170,420 175,405 Q180,390 180,380 Z"
                          data-city="安康市"/>
                    
                    <!-- 商洛市 -->
                    <path id="shangluo" class="city-region"
                          d="M270,320 Q310,315 350,330 L355,360 Q335,375 305,370 L280,380 Q260,360 265,345 Q270,330 270,320 Z"
                          data-city="商洛市"/>
                    
                    <!-- 城市标签 -->
                    <text x="320" y="65" class="city-label">榆林市</text>
                    <text x="225" y="160" class="city-label">延安市</text>
                    <text x="150" y="245" class="city-label">咸阳市</text>
                    <text x="220" y="220" class="city-label">铜川市</text>
                    <text x="170" y="285" class="city-label">西安市</text>
                    <text x="250" y="300" class="city-label">渭南市</text>
                    <text x="90" y="310" class="city-label">宝鸡市</text>
                    <text x="120" y="395" class="city-label">汉中市</text>
                    <text x="220" y="415" class="city-label">安康市</text>
                    <text x="310" y="355" class="city-label">商洛市</text>
                    
                    <!-- 地标建筑装饰 -->
                    <g id="landmarks">
                        <!-- 榆林古城墙 -->
                        <rect x="310" y="50" width="20" height="12" fill="#8b4513" rx="2"/>
                        <rect x="312" y="52" width="16" height="8" fill="#a0522d" rx="1"/>
                        
                        <!-- 延安宝塔 -->
                        <polygon points="225,145 230,135 235,145" fill="#654321"/>
                        <rect x="228" y="145" width="4" height="8" fill="#8b4513"/>
                        
                        <!-- 西安钟楼 -->
                        <rect x="165" y="270" width="10" height="8" fill="#daa520"/>
                        <polygon points="170,270 175,265 180,270" fill="#b8860b"/>
                        
                        <!-- 兵马俑 -->
                        <circle cx="190" cy="295" r="3" fill="#8b7355"/>
                        <circle cx="185" cy="298" r="2" fill="#8b7355"/>
                        <circle cx="195" cy="298" r="2" fill="#8b7355"/>
                    </g>
                    
                    <!-- 装饰性树木 -->
                    <g id="trees">
                        <circle cx="100" cy="100" r="3" fill="#228b22"/>
                        <rect x="99" y="103" width="2" height="4" fill="#8b4513"/>
                        
                        <circle cx="300" cy="200" r="3" fill="#228b22"/>
                        <rect x="299" y="203" width="2" height="4" fill="#8b4513"/>
                        
                        <circle cx="150" cy="350" r="3" fill="#228b22"/>
                        <rect x="149" y="353" width="2" height="4" fill="#8b4513"/>
                    </g>
                </svg>
            </div>
            
            <div class="info-panel">
                <div class="selected-city" id="selected-city">请点击地图上的城市</div>
                <div class="city-info" id="city-info">选择一个城市查看详细信息</div>
            </div>
        </div>
    </div>

    <script>
        // 城市信息数据
        const cityData = {
            '榆林市': {
                name: '榆林市',
                description: '陕西省最北部的地级市，素有"塞上明珠"之称，是国家重要的能源化工基地。',
                features: ['煤炭资源丰富', '毛乌素沙漠南缘', '古城文化悠久']
            },
            '延安市': {
                name: '延安市',
                description: '中国革命圣地，中共中央所在地（1935-1948），红色旅游胜地。',
                features: ['革命圣地', '宝塔山', '枣园革命旧址']
            },
            '咸阳市': {
                name: '咸阳市',
                description: '秦朝古都，中国第一个封建王朝的都城，历史文化名城。',
                features: ['秦始皇陵', '兵马俑', '古都文化']
            },
            '铜川市': {
                name: '铜川市',
                description: '陕西省地级市，以煤炭工业为主，正在转型发展文化旅游业。',
                features: ['煤炭工业', '药王山', '转型发展']
            },
            '西安市': {
                name: '西安市',
                description: '陕西省省会，十三朝古都，世界四大古都之一，丝绸之路起点。',
                features: ['十三朝古都', '兵马俑', '大雁塔', '古城墙']
            },
            '渭南市': {
                name: '渭南市',
                description: '陕西省地级市，关中平原东部，农业发达，有"陕西粮仓"之称。',
                features: ['农业发达', '华山', '关中平原']
            },
            '宝鸡市': {
                name: '宝鸡市',
                description: '陕西省地级市，古称陈仓，是关中平原西部重要城市。',
                features: ['青铜器之乡', '法门寺', '太白山']
            },
            '汉中市': {
                name: '汉中市',
                description: '陕西省地级市，汉朝发祥地，素有"汉家发祥地，中华聚宝盆"之美誉。',
                features: ['汉朝发祥地', '油菜花海', '朱鹮保护区']
            },
            '安康市': {
                name: '安康市',
                description: '陕西省地级市，秦巴山区重要城市，以茶叶和丝绸闻名。',
                features: ['秦巴山区', '安康茶叶', '汉江']
            },
            '商洛市': {
                name: '商洛市',
                description: '陕西省地级市，秦岭南麓，素有"秦岭最美是商洛"之称。',
                features: ['秦岭南麓', '丹江源头', '生态旅游']
            }
        };

        let selectedCityElement = null;

        // 初始化地图事件
        function initMap() {
            const cityRegions = document.querySelectorAll('.city-region');
            
            cityRegions.forEach(region => {
                // 点击事件
                region.addEventListener('click', function(e) {
                    e.preventDefault();
                    const cityName = this.getAttribute('data-city');
                    selectCity(cityName, this);
                });
                
                // 触摸事件（移动端）
                region.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    const cityName = this.getAttribute('data-city');
                    selectCity(cityName, this);
                });
            });
        }

        // 选择城市
        function selectCity(cityName, element) {
            // 移除之前选中的样式
            if (selectedCityElement) {
                selectedCityElement.classList.remove('selected');
            }
            
            // 添加选中样式
            element.classList.add('selected');
            selectedCityElement = element;
            
            // 显示城市信息
            displayCityInfo(cityName);
            
            // 弹出提示
            alert(`您选择了：${cityName}`);
        }

        // 显示城市信息
        function displayCityInfo(cityName) {
            const cityInfo = cityData[cityName];
            const selectedCityEl = document.getElementById('selected-city');
            const cityInfoEl = document.getElementById('city-info');
            
            if (cityInfo) {
                selectedCityEl.textContent = `已选择：${cityInfo.name}`;
                cityInfoEl.innerHTML = `
                    <p><strong>简介：</strong>${cityInfo.description}</p>
                    <p><strong>特色：</strong>${cityInfo.features.join('、')}</p>
                `;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            console.log('陕西省手绘地图演示页面已加载完成！');
        });
    </script>
</body>
</html>