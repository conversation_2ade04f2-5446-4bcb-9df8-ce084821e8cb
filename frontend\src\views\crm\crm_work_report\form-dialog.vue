<script setup lang="ts">
  import { ElMessage, FormInstance } from 'element-plus'
  import { CrmWorkReportApi } from '@/api/crm/crmWorkReport'
  import { ApiStatus } from '@/utils/http/status'
  import FormUploader from '@/components/custom/FormUploader/index.vue'
  // import ArtWangEditor from '@/components/core/forms/ArtWangEditor.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    title: '',
    type: '',
    report_date: '',
    content: '',
    summary: '',
    plan: '',
    attachments: ''
  })

  // 表单验证规则
  const rules = {
    title: [
      {
        required: true,
        message: '报告标题不能为空',
        trigger: 'blur'
      },
      {
        max: 200,
        message: '报告标题长度不能超过200个字符',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '报告类型:daily=日报,weekly=周报,monthly=月报不能为空',
        trigger: 'blur'
      }
    ],
    report_date: [
      {
        required: true,
        message: '报告日期不能为空',
        trigger: 'blur'
      }
    ],
    content: [
      {
        required: true,
        message: '报告内容不能为空',
        trigger: 'blur'
      }
    ]
  }

  // 显示对话框
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    dialogVisible.value = true

    // 重置表单数据
    Object.assign(formData, {
      title: '',
      type: '',
      report_date: '',
      content: '',
      summary: '',
      plan: '',
      attachments: ''
    })

    // 编辑模式下获取详情数据
    if (type === 'edit' && id) {
      try {
        loading.value = true
        const res = await CrmWorkReportApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 处理数据类型转换
          const data = { ...res.data }

          Object.assign(formData, data)
        }
      } finally {
        loading.value = false
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          // 处理提交数据转换
          const submitData = { ...formData }

          let res

          if (dialogType.value === 'add') {
            res = await CrmWorkReportApi.add(submitData)
          } else {
            res = await CrmWorkReportApi.update(submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            emit('success')
          }
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '✏️ 写工作汇报' : '✏️ 编辑工作汇报'"
    width="1000px"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
      >
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-title">📋 基本信息</div>
          <ElRow :gutter="20">
            <ElCol :span="24">
              <ElFormItem label="汇报标题" prop="title">
                <ElInput
                  v-model="formData.title"
                  placeholder="请输入汇报标题，如：2025年1月15日销售工作日报"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="汇报类型" prop="type">
                <ElSelect v-model="formData.type" placeholder="请选择汇报类型" style="width: 100%">
                  <ElOption label="📋 日报" value="daily" />
                  <ElOption label="📊 周报" value="weekly" />
                  <ElOption label="📈 月报" value="monthly" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="汇报日期" prop="report_date">
                <ElDatePicker
                  v-model="formData.report_date"
                  type="date"
                  placeholder="请选择汇报日期"
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </div>

        <!-- 汇报内容区域 -->
        <div class="form-section">
          <div class="section-title">📝 汇报内容</div>
          <ElFormItem label="工作内容" prop="content">
            <ElInput
              v-model="formData.content"
              type="textarea"
              :rows="6"
              placeholder="请详细描述本期工作内容..."
            />
          </ElFormItem>
          <ElFormItem label="工作总结">
            <ElInput
              v-model="formData.summary"
              type="textarea"
              :rows="4"
              placeholder="总结本期工作成果和遇到的问题..."
            />
          </ElFormItem>
          <ElFormItem label="下期计划">
            <ElInput
              v-model="formData.plan"
              type="textarea"
              :rows="4"
              placeholder="制定下期工作计划和目标..."
            />
          </ElFormItem>
        </div>

        <!-- 附件区域 -->
        <div class="form-section">
          <div class="section-title">📎 附件上传</div>
          <ElFormItem label="附件">
            <FormUploader
              v-model="formData.attachments"
              fileType="file"
              :limit="5"
              drag
            />
            <!--            <div class="upload-tip">支持 Word、Excel、PDF、图片等格式，单文件最大10MB</div>-->
          </ElFormItem>
        </div>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">
          📤 {{ dialogType === 'add' ? '提交汇报' : '更新汇报' }}
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
  .dialog-content {
    max-height: calc(90vh - 200px);
    overflow-y: auto;
    padding: 0 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid #e4e7ed;
  }

  /* 表单分区样式 */
  .form-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f7f8fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #1f2329;
    display: flex;
    align-items: center;
  }

  .upload-tip {
    font-size: 12px;
    color: #8f959e;
    margin-top: 8px;
  }

  /* 表单项优化 */
  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #1f2329;
  }

  :deep(.el-textarea__inner) {
    resize: vertical;
    min-height: 80px;
  }

  :deep(.el-input__inner) {
    border-radius: 6px;
  }

  :deep(.el-select) {
    width: 100%;
  }

  .avatar-uploader .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .avatar-uploader .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    line-height: 100px;
  }
</style>
