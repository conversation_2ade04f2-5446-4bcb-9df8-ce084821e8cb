<?php
declare(strict_types=1);

namespace app\workflow\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\common\exception\BusinessException;
use app\notice\service\NoticeDispatcherService;
use app\system\service\AdminService;
use app\system\model\AdminModel;
use app\workflow\model\WorkflowTask;
use app\workflow\constants\WorkflowStatusConstant;

// WorkflowOperationConstant已合并到WorkflowStatusConstant
use app\workflow\service\WorkflowUrlService;
use think\facade\Db;
use think\facade\Log;
use think\Model;

/**
 * 我的审批
 */
class WorkflowTaskService extends BaseService
{
	use CrudServiceTrait;
	
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new WorkflowTask();
		parent::__construct();
	}
	
	public function initialize(): void
	{
		$this->crudService->setEnableDataPermission(false);
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			// 在这里定义搜索字段配置
			// 例如：'username' => ['type' => 'like'],
		];
	}
	
	
	/**
	 * 获取任务列表
	 * */
	public function getWorkList($params): array
	{
		$adminId = request()->adminId;
		
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		
		// 构建查询条件
		$where = [];
		
		// 租户超级管理员获取
		if (!is_super_admin()) {
			$where[] = [
				'approver_id|agent_id' => $adminId
			];
		}
		
		$instanceModel = WorkflowInstanceService::getInstance()
		                                        ->getModel();
		$instanceIds   = [];
		
		// 状态处理
		$activeArr = [
			'all'      => -1,
			'todo'     => 0,
			'done'     => [
				1,
				2,
				3,
				4
			],
			'canceled' => 5,
			'cc'       => $adminId,
		];
		
		$active = $params['active'] ?? 'all';
		
		// 修复逻辑问题：优化任务类型和状态筛选
		if (isset($activeArr[$active])) {
			if ($active == 'cc') {
				// 抄送任务处理
				$where[] = [
					'task_type',
					'=',
					WorkflowStatusConstant::TASK_TYPE_CC
				];
			}
			else {
				// 审批任务处理
				$where[] = [
					'task_type',
					'=',
					WorkflowStatusConstant::TASK_TYPE_APPROVAL
				];
				
				// 状态筛选
				if ($active != 'all') {
					$where[] = [
						'status',
						'in',
						$activeArr[$active]
					];
				}
			}
		}
		
		// 关键词搜索
		if (!empty($params['keyword'])) {
			// 这里需要子查询，因为关键词需要搜索关联实例的标题
			$keywordInstanceIds = $instanceModel->where([
				'title',
				'like',
				'%' . $params['keyword'] . '%'
			])
			                                    ->column('id');
			
			if (empty($keywordInstanceIds)) {
				return [
					'total' => 0,
					'limit' => $limit,
					'page'  => $page,
					'list'  => []
				];
			}
			$instanceIds = array_merge($instanceIds, $keywordInstanceIds);
		}
		
		// 流程类型筛选
		if (!empty($params['definition_id'])) {
			$definitionIds = $instanceModel->where('definition_id', $params['definition_id'])
			                               ->column('id');
			if (empty($definitionIds)) {
				return [
					'total' => 0,
					'limit' => $limit,
					'page'  => $page,
					'list'  => []
				];
			}
			$instanceIds = array_merge($instanceIds, $definitionIds);
		}
		
		// 优化实例ID查询
		$instanceIds = array_unique($instanceIds);
		if (!empty($instanceIds)) {
			// 如果ID数量较少，使用IN
			if (count($instanceIds) < 500) {
				$where[] = [
					'instance_id',
					'in',
					$instanceIds
				];
			}
			else {
				// 如果ID数量较多，分批处理或使用临时表
				// 这里简化处理，实际可能需要更复杂的逻辑
				$chunks  = array_chunk($instanceIds, 500);
				$where[] = function ($query) use ($chunks) {
					foreach ($chunks as $index => $chunk) {
						$method = $index === 0
							? 'whereIn'
							: 'whereOrIn';
						$query->$method('instance_id', $chunk);
					}
				};
			}
		}
		
		if (!empty($params['date_range'])) {
			$where[] = [
				'handle_time',
				'between',
				$params['date_range']
			];
		}
		
		// 合并查询：使用ThinkPHP的分页方法同时获取数据和总数
		$result = $this->model->where($where)
		                      ->with([
			                      'instance' => [
				                      'submitter',
				                      'types',
				                      'dept'
			                      ],
		                      ])
		                      ->order(['created_at' => 'desc'])
		                      ->paginate([
			                      'list_rows' => $limit,
			                      'page'      => $page,
			                      'query'     => $params
			                      // 保留查询参数
		                      ]);
		
		return [
			'total' => $result->total(),
			'limit' => $limit,
			'page'  => $page,
			'list'  => $result->items()
		];
	}
	
	/**
	 * 获取抄送任务列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getCcList($params): array
	{
		// 强制设置为抄送任务类型
		$params['active'] = 'cc';
		
		// 调用通用的任务列表方法
		return $this->getWorkList($params);
	}
	
	/**
	 * 获取验证规则
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			// 在这里定义验证规则
			// 例如：'username' => 'require|unique:workflow_task',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 批量删除
	 *
	 * @param array|int $ids 要删除的ID数组或单个ID
	 * @return bool
	 */
	public function batchDelete($ids): bool
	{
		if (!is_array($ids)) {
			$ids = [$ids];
		}
		
		return $this->model->whereIn('id', $ids)
		                   ->delete();
	}
	
	/**
	 * 审批通过任务
	 *
	 * @param array $params 请求参数
	 * @return bool
	 */
	public function approveTask(array $params): bool
	{
		$taskId = $params['task_id'] ?? 0;
		if (empty($taskId)) {
			return false;
		}
		
		$task = $this->getModel()
		             ->where(['id' => $taskId])
		             ->findOrEmpty();
		if ($task->isEmpty()) {
			return false;
		}
		
		// 开启事务
		Db::startTrans();
		
		try {
			// 更新任务状态
			$task->status      = 1; // 已同意
			$task->opinion     = $params['opinion'] ?? '';
			$task->handle_time = date('Y-m-d H:i:s');
			$result            = $task->save();
			
			if (!$result) {
				throw new \Exception('更新任务状态失败');
			}
			
			// 获取实例信息
			$instanceService = WorkflowInstanceService::getInstance();
			$instance        = $instanceService->detail(intval($task->instance_id), ['submitter']);
			
			if ($instance->isEmpty()) {
				throw new \Exception('工作流实例不存在');
			}
			
			// 记录审批历史
			$historyService = new WorkflowHistoryService();
			$historyData    = [
				'instance_id'    => $instance->id,
				'process_id'     => $instance->process_id,
				'task_id'        => $task->task_id,
				'node_id'        => $task->node_id,
				'node_name'      => $task->node_name,
				'node_type'      => $task->node_type,
				'prev_node_id'   => $instance->current_node,
				'operator_id'    => request()->adminId,
				'operation'      => WorkflowStatusConstant::OPERATION_AGREE,
				'opinion'        => $params['opinion'] ?? '',
				// 使用参数中的意见，而不是任务中的
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance->tenant_id ?? 0
				// 添加租户ID
			];
			
			// 使用safeAddHistory方法避免重复记录
			$historyResult = $historyService->safeAddHistory($historyData);
			
			if (!$historyResult) {
				Log::warning('审批历史记录可能已存在，跳过添加');
			}
			
			// 发送审批结果通知
			$this->sendApprovalResultNotification($instance->toArray(), $task->toArray(), true // 通过
			);
			
			// 调用工作流引擎处理流程推进
			$engineService = new WorkflowEngineService();
			$engineResult  = $engineService->processApprovalResult($instance->toArray(), $task->toArray(), true // 通过
			);
			
			if (!$engineResult) {
				throw new \Exception('工作流引擎处理失败');
			}
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('审批通过失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 驳回任务
	 *
	 * @param array $params 请求参数
	 * @return bool
	 */
	public function rejectTask(array $params): bool
	{
		$taskId = $params['task_id'] ?? 0;
		if (empty($taskId)) {
			return false;
		}
		
		$task = $this->getOne(['id' => $taskId]);
		if ($task->isEmpty()) {
			return false;
		}
		
		// 开启事务
		Db::startTrans();
		
		try {
			// 更新任务状态
			$task->status      = 2; // 已驳回
			$task->opinion     = $params['opinion'] ?? '';
			$task->handle_time = date('Y-m-d H:i:s');
			$result            = $task->save();
			
			if (!$result) {
				throw new \Exception('更新任务状态失败');
			}
			
			// 获取实例信息
			$instanceService = new WorkflowInstanceService();
			$instance        = $instanceService->getOne(['id' => $task->instance_id]);
			
			if ($instance->isEmpty()) {
				throw new \Exception('工作流实例不存在');
			}
			
			// 记录审批历史（任务级别的驳回记录）
			$historyService = new WorkflowHistoryService();
			$historyData    = [
				'instance_id'    => $instance->id,
				'process_id'     => $instance->process_id,
				'task_id'        => $task->task_id,
				'node_id'        => $task->node_id,
				'node_name'      => $task->node_name,
				'node_type'      => $task->node_type,
				'prev_node_id'   => $instance->current_node,
				'operator_id'    => request()->adminId,
				'operation'      => WorkflowStatusConstant::OPERATION_REJECT,
				'opinion'        => $params['opinion'] ?? '',
				// 使用参数中的意见
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance->tenant_id ?? 0
			];
			
			// 使用safeAddHistory方法避免重复记录
			$historyResult = $historyService->safeAddHistory($historyData);
			
			if (!$historyResult) {
				Log::warning('驳回历史记录可能已存在，跳过添加');
			}
			
			// 发送审批结果通知
			$this->sendApprovalResultNotification($instance->toArray(), $task->toArray(), false // 驳回
			);
			
			// 调用工作流引擎处理流程推进
			$engineService = new WorkflowEngineService();
			$engineResult  = $engineService->processApprovalResult($instance->toArray(), $task->toArray(), false // 驳回
			);
			
			if (!$engineResult) {
				throw new \Exception('工作流引擎处理失败');
			}
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('驳回失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 发送审批结果通知
	 *
	 * @param array $instance   工作流实例
	 * @param array $task       任务数据
	 * @param bool  $isApproved 是否通过
	 * @return bool
	 */
	protected function sendApprovalResultNotification(array $instance, array $task, bool $isApproved): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 获取当前用户信息
			$currentUserInfo = request()->adminInfo['data'] ?? [];
			$currentUserName = $currentUserInfo['real_name'] ?? '系统';
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'title'         => $instance['title'],
				'result'        => $isApproved
					? '通过'
					: '拒绝',
				'opinion'       => $task['opinion'] ?? '',
				'approver_name' => $currentUserName,
				'completed_at'  => $task['handle_time'] ?? date('Y-m-d H:i:s')
			];
			
			// 确保request上下文正确设置
			$request = request();
			if (!$request->adminId) {
				$request->adminId = $task['approver_id'] ?? 1;
			}
			if (!$request->tenantId) {
				$request->tenantId = $instance['tenant_id'] ?? 1;
			}
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
				'instance_id' => $instance['id']
			]);
			
			// 发送审批结果通知给提交人
			$noticeService->send('workflow', WorkflowStatusConstant::MESSAGE_TASK_APPROVED, $variables, [$instance['submitter_id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送审批结果通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 催办任务
	 *
	 * @param array $params 催办参数
	 * @return bool
	 */
	public function urgeTask(array $params): bool
	{
		$instanceId = $params['instance_id'] ?? 0;
		$content    = $params['content'] ?? '请尽快处理';
		
		if (empty($instanceId)) {
			return false;
		}
		
		// 获取实例信息
		$instanceService = new WorkflowInstanceService();
		$instance        = $instanceService->getOne(['id' => $instanceId]);
		
		if ($instance->isEmpty()) {
			return false;
		}
		
		// 获取当前实例的待处理任务
		$pendingTasks = $this->getList([
			'instance_id' => $instanceId,
			'status'      => 0,
			// 待处理
			'task_type'   => WorkflowStatusConstant::TASK_TYPE_APPROVAL
		]);
		
		if (empty($pendingTasks)) {
			return false;
		}
		
		$adminId      = request()->adminId;
		$adminName    = request()->adminInfo['data']['real_name'] ?? '管理员';
		$successCount = 0;
		
		foreach ($pendingTasks as $task) {
			// 更新任务的催办状态
			$this->edit([
				'is_urged'  => 1,
				'urge_time' => date('Y-m-d H:i:s')
			], ['id' => $task['id']]);
			
			// 记录催办历史
			$historyService = new WorkflowHistoryService();
			$historyData    = [
				'instance_id'    => $instanceId,
				'process_id'     => $instance['process_id'],
				'task_id'        => $task['task_id'],
				'node_id'        => $task['node_id'],
				'node_name'      => $task['node_name'],
				'node_type'      => $task['node_type'],
				'prev_node_id'   => '',
				'operator_id'    => $adminId,
				'operation'      => WorkflowStatusConstant::OPERATION_URGE,
				// 催办操作
				'opinion'        => $content,
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance['tenant_id'] ?? 0
			];
			
			$historyService->getCrudService()
			               ->add($historyData);
			
			// 发送催办通知
			$this->sendUrgeNotification($instance->toArray(), $task, $adminName, $content);
			
			$successCount++;
		}
		
		return $successCount > 0;
	}
	
	/**
	 * 发送催办通知
	 *
	 * @param array  $instance   工作流实例
	 * @param array  $task       任务数据
	 * @param string $urgerName  催办人姓名
	 * @param string $urgeReason 催办原因
	 * @return bool
	 */
	protected function sendUrgeNotification(array $instance, array $task, string $urgerName, string $urgeReason): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'title'      => $instance['title'],
				'task_name'  => $task['node_name'],
				'urger_name' => $urgerName,
				'created_at' => date('Y-m-d H:i:s'),
				'reason'     => $urgeReason
			];
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('task.detail', [
				'task_id' => $task['id']
			]);
			
			// 发送催办通知给审批人
			$noticeService->send('workflow', WorkflowStatusConstant::MESSAGE_TASK_URGE, $variables, [$task['approver_id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送催办通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 标记抄送任务为已读
	 *
	 * @param string $taskId 任务ID
	 * @param int    $userId 用户ID
	 * @return bool
	 */
	public function markCcAsRead(string $taskId, int $userId): bool
	{
		$task = $this->getOne([
			'task_id'     => $taskId,
			'approver_id' => $userId,
			'task_type'   => WorkflowStatusConstant::TASK_TYPE_CC
		]);
		
		if ($task->isEmpty()) {
			return false;
		}
		
		return $task->save([
			'status'      => 1,
			// 已读
			'handle_time' => date('Y-m-d H:i:s')
		]);
	}
	
	/**
	 * 批量标记抄送任务为已读
	 *
	 * @param array $taskIds 任务ID数组
	 * @param int   $userId  用户ID
	 * @return int 成功标记的数量
	 */
	public function batchMarkCcAsRead(array $taskIds, int $userId): int
	{
		if (empty($taskIds)) {
			return 0;
		}
		
		$list = $this->getList([
			[
				'task_id',
				'in',
				$taskIds
			],
			[
				'approver_id',
				'=',
				$userId
			],
			[
				'task_type',
				'=',
				WorkflowStatusConstant::TASK_TYPE_CC
			],
			[
				'status',
				'=',
				0
			]
			// 只处理未读状态
		]);
		
		$count = $list->count();
		
		if ($count > 0) {
			$list->save([
				'status'      => 1,
				// 已读
				'handle_time' => date('Y-m-d H:i:s')
			]);
		}
		
		return $count;
	}
	
	/**
	 * 创建审批任务
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     节点数据
	 * @param array $user     审批人
	 * @return bool
	 */
	public function createTask(array $instance, array $node, array $user): bool
	{
		try {
			$userId = isset($user['id'])
				? intval($user['id'])
				: 0;
			if ($userId <= 0) {
				Log::error('无效的用户ID: ' . json_encode($user));
				return false;
			}
			
			// 检查是否已存在相同的任务
			$existingTask = $this->getModel()
			                     ->where([
				                     'instance_id' => $instance['id'],
				                     'node_id'     => $node['nodeId'],
				                     'approver_id' => $userId,
				                     'status'      => 0,
				                     // 待处理
				                     'deleted_at'  => null
			                     ])
			                     ->findOrEmpty();
			
			if (!$existingTask->isEmpty()) {
				Log::info('已存在相同的待处理任务，跳过创建: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
				return true;
			}
			
			// TODO: 实现时间限制和超时处理功能
			// 1. 根据节点配置的timeLimit字段设置任务的截止时间(due_time)
			// 2. 添加定时任务检查超时的审批任务
			// 3. 实现超时后的自动处理逻辑(自动通过、自动驳回、自动转交等)
			
			// 创建新任务
			$taskId   = md5($instance['id'] . $node['nodeId'] . $userId . microtime(true));
			$taskData = [
				'task_id'       => $taskId,
				'instance_id'   => $instance['id'],
				'process_id'    => $instance['process_id'],
				'node_id'       => $node['nodeId'],
				'node_name'     => $node['nodeName'] ?? '审批节点',
				'node_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
					? WorkflowStatusConstant::TASK_TYPE_CC
					: WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				'task_type'     => isset($node['type']) && $node['type'] == WorkflowStatusConstant::NODE_TYPE_COPYER
					? WorkflowStatusConstant::TASK_TYPE_CC
					: WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				'approver_id'   => $userId,
				'approver_name' => $user['real_name'] ?? $user['username'] ?? '',
				'status'        => 0,
				// 待处理
				'tenant_id'     => $instance['tenant_id'] ?? 0
			];
			
			$result = $this->getCrudService()
			               ->add($taskData);
			
			if ($result) {
				Log::info('成功创建审批任务: 实例ID=' . $instance['id'] . ', 节点ID=' . $node['nodeId'] . ', 用户ID=' . $userId);
			}
			else {
				Log::error('创建审批任务失败: ' . json_encode($taskData));
			}
			
			return (bool)$result;
		}
		catch (\Exception $e) {
			Log::error('创建审批任务异常: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 终止流程
	 *
	 * @param array $params 终止参数
	 * @return bool
	 */
	public function terminateWorkflow(array $params): bool
	{
		$instanceId = $params['instance_id'] ?? 0;
		$reason     = $params['reason'] ?? '管理员终止流程';
		
		if (empty($instanceId)) {
			return false;
		}
		
		// 调用工作流引擎服务终止流程
		$engineService = WorkflowEngineService::getInstance();
		return $engineService->terminateWorkflow(intval($instanceId), $reason, request()->adminId);
	}
	
	/**
	 * 转交任务
	 *
	 * @param int   $taskId  任务ID
	 * @param int   $userId  被转交人ID
	 * @param int   $adminId 当前操作人ID
	 * @param array $remark  备注信息
	 * @return bool
	 */
	public function transferTask(int $taskId, int $userId, int $adminId, string $remark = ''): bool
	{
		// 查询任务信息
		$taskInfo = $this->getOne(['id' => $taskId]);
		if ($taskInfo->isEmpty()) {
			return false;
		}
		
		// 获取用户信息
		$userInfo = AdminModel::where('id', $userId)
		                      ->find();
		if (empty($userInfo)) {
			return false;
		}
		
		$oldApprover = $taskInfo->approver_id;
		
		// 开启事务
		\think\facade\Db::startTrans();
		try {
			// 更新任务审批人
			$this->edit(['approver_id' => $userId], ['id' => $taskId]);
			
			// 获取工作流实例
			$instanceInfo = \app\workflow\model\WorkflowInstance::where('id', $taskInfo->instance_id)
			                                                    ->find();
			if (empty($instanceInfo)) {
				throw new \Exception('工作流实例不存在');
			}
			
			// 记录转交历史
			\app\workflow\service\WorkflowHistoryService::getInstance()
			                                            ->getCrudService()
			                                            ->add([
				                                            'instance_id'    => $taskInfo->instance_id,
				                                            'process_id'     => $taskInfo->process_id,
				                                            'task_id'        => $taskInfo->task_id,
				                                            'node_id'        => $taskInfo->node_id,
				                                            'node_name'      => $taskInfo->node_name,
				                                            'node_type'      => $taskInfo->node_type,
				                                            'prev_node_id'   => '',
				                                            'operator_id'    => $adminId,
				                                            'to_user_id'     => $userId,
				                                            'operation'      => WorkflowStatusConstant::OPERATION_TRANSFER,
				                                            'remark'         => $remark,
				                                            'operation_time' => date('Y-m-d H:i:s'),
				                                            'tenant_id'      => $taskInfo->tenant_id ?? 0
			                                            ]);
			
			// 发送转交通知
			$this->sendTransferNotification($instanceInfo->toArray(), $taskInfo->toArray(), $userInfo->toArray(), $oldApprover);
			
			\think\facade\Db::commit();
			return true;
		}
		catch (\Exception $e) {
			\think\facade\Db::rollback();
			\think\facade\Log::error('任务转交失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 发送任务转交通知
	 *
	 * @param array $instance   工作流实例
	 * @param array $task       任务数据
	 * @param array $toUser     接收人
	 * @param int   $fromUserId 原审批人ID
	 * @return bool
	 */
	protected function sendTransferNotification(array $instance, array $task, array $toUser, int $fromUserId): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'title'         => $instance['title'],
				'node_name'     => $task['node_name'],
				'from_user'     => AdminModel::where('id', $fromUserId)
				                             ->value('real_name'),
				'to_user'       => $toUser['real_name'],
				'transfer_time' => date('Y-m-d H:i:s'),
				'detail_url'    => '/workflow/task/detail?id=' . $task['id']
			];
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('task.detail', [
				'task_id' => $task['id']
			]);
			
			// 发送转交通知
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_TRANSFER, $variables, [$toUser['id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			\think\facade\Log::error('发送转交通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 回退流程到指定节点
	 *
	 * @param array $params 回退参数
	 * @return bool
	 */
	public function backToNode(array $params): bool
	{
		$instanceId   = $params['instance_id'] ?? 0;
		$targetNodeId = $params['target_node_id'] ?? '';
		$reason       = $params['reason'] ?? '流程回退';
		
		if (empty($instanceId) || empty($targetNodeId)) {
			return false;
		}
		
		// 获取工作流实例
		$instanceService = new WorkflowInstanceService();
		$instance        = $instanceService->getOne(['id' => $instanceId]);
		
		if ($instance->isEmpty()) {
			return false;
		}
		
		// 获取流程定义
		/*$definitionService = new WorkflowDefinitionService();
		$definition        = $definitionService->getOne(['id' => $instance->definition_id]);
		
		if ($definition->isEmpty()) {
			return false;
		}*/
		
		// 解析流程配置
		$flowConfig = $instance['process_data'];
		if (empty($flowConfig)) {
			return false;
		}
		
		// 开启事务
		Db::startTrans();
		
		try {
			// 取消当前所有未处理的任务
			$this->model->where([
				'instance_id' => $instanceId,
				'status'      => 0
				// 未处理的任务
			])
			            ->update([
				            'status'        => WorkflowStatusConstant::RECALLED,
				            // 已取消/已撤回
				            'cancel_time'   => date('Y-m-d H:i:s'),
				            'cancel_reason' => '流程回退至节点：' . $targetNodeId
			            ]);
			
			// 更新实例当前节点
			$instanceService->edit([
				'current_node' => $targetNodeId,
				'status'       => WorkflowStatusConstant::APPROVING
			], ['id' => $instanceId]);
			
			// 创建新的审批任务
			$engineService = new WorkflowEngineService();
			
			// 记录回退历史
			$historyService = new WorkflowHistoryService();
			$historyData    = [
				'instance_id'    => $instanceId,
				'process_id'     => $instance->process_id,
				'task_id'        => '',
				'node_id'        => $targetNodeId,
				'node_name'      => '流程回退',
				'node_type'      => 'backto',
				'prev_node_id'   => $instance->current_node,
				'operator_id'    => request()->adminId,
				'operation'      => WorkflowStatusConstant::OPERATION_RECALL,
				// 回退操作
				'opinion'        => $reason,
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance->tenant_id
			];
			
			$historyService->getCrudService()
			               ->add($historyData);
			
			// 找到目标节点并创建新的任务
			$nodeConfig = $flowConfig['nodeConfig'] ?? [];
			$this->findNodeAndCreateTasks($instance->toArray(), $nodeConfig, $targetNodeId);
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('回退流程失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 递归查找节点并创建任务
	 *
	 * @param array  $instance     工作流实例
	 * @param array  $node         当前节点
	 * @param string $targetNodeId 目标节点ID
	 * @return bool
	 */
	protected function findNodeAndCreateTasks(array $instance, array $node, string $targetNodeId): bool
	{
		// 如果当前节点是目标节点
		if (isset($node['nodeId']) && $node['nodeId'] === $targetNodeId) {
			// 如果是审批节点，创建审批任务
			if (isset($node['type']) && $node['type'] === '1') {
				$this->createApprovalTasksForBackTo($instance, $node);
				return true;
			}
			return false;
		}
		
		// 递归查找子节点
		if (!empty($node['childNode'])) {
			if ($this->findNodeAndCreateTasks($instance, $node['childNode'], $targetNodeId)) {
				return true;
			}
		}
		
		// 查找条件分支
		if (!empty($node['conditionNodes']) && is_array($node['conditionNodes'])) {
			foreach ($node['conditionNodes'] as $conditionNode) {
				if ($this->findNodeAndCreateTasks($instance, $conditionNode, $targetNodeId)) {
					return true;
				}
				
				// 检查条件节点的子节点
				if (!empty($conditionNode['childNode'])) {
					if ($this->findNodeAndCreateTasks($instance, $conditionNode['childNode'], $targetNodeId)) {
						return true;
					}
				}
			}
		}
		
		return false;
	}
	
	/**
	 * 为回退操作创建审批任务
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     审批节点
	 * @return void
	 */
	protected function createApprovalTasksForBackTo(array $instance, array $node): void
	{
		// 获取审批人列表
		$approvers = $node['nodeUserList'] ?? [];
		if (empty($approvers)) {
			return;
		}
		
		$historyService = new WorkflowHistoryService();
		
		foreach ($approvers as $user) {
			// 创建审批任务
			$taskData = [
				'task_id'     => uniqid('task_'),
				'instance_id' => $instance['id'],
				'process_id'  => $instance['process_id'],
				'node_id'     => $node['nodeId'],
				'node_name'   => $node['nodeName'],
				'node_type'   => 'approval',
				'task_type'   => WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				'approver_id' => $user['id'],
				'status'      => 0,
				// 待处理
				'sort'        => 0,
				'created_at'  => date('Y-m-d H:i:s'),
				'tenant_id'   => $instance['tenant_id'] ?? 0
			];
			
			$taskId = $this->getCrudService()
			               ->add($taskData);
			
			// 记录历史
			$historyData = [
				'instance_id'    => $instance['id'],
				'process_id'     => $instance['process_id'],
				'task_id'        => $taskData['task_id'],
				'node_id'        => $node['nodeId'],
				'node_name'      => $node['nodeName'],
				'node_type'      => 'approval',
				'prev_node_id'   => $instance['current_node'],
				'operator_id'    => request()->adminId,
				'operation'      => 1,
				// 创建任务
				'operation_time' => date('Y-m-d H:i:s'),
				'tenant_id'      => $instance['tenant_id'] ?? 0
			];
			
			$historyService->getCrudService()
			               ->add($historyData);
			
			// 发送任务通知
			$this->sendTaskApprovalNotification($instance, $taskData, $user);
		}
	}
	
	/**
	 * 发送审批任务通知
	 *
	 * @param array $instance 工作流实例
	 * @param array $task     任务数据
	 * @param array $user     审批人
	 * @return bool
	 */
	protected function sendTaskApprovalNotification(array $instance, array $task, array $user): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'task_name'      => $task['node_name'],
				'title'          => $instance['title'],
				'submitter_name' => $instance['submitter_name'],
				'created_at'     => $instance['created_at'],
				'detail_url'     => '/workflow/task/detail?id=' . $task['id']
			];
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('task.approval', [
				'task_id' => $task['id'] ?? $instance['id']
			]);
			
			// 发送审批任务通知
			$noticeService->send('workflow', WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, $variables, [$user['id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送审批任务通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	protected function autoApproveForSameUser(array $instance, array $node): void
	{
		// 记录自动通过的审批历史
		$historyData = [
			'instance_id'    => $instance['id'],
			'process_id'     => $instance['process_id'],
			'node_id'        => $node['nodeId'],
			'node_name'      => $node['nodeName'],
			'node_type'      => 'approval',
			'operator_id'    => $instance['submitter_id'],
			'operation'      => WorkflowStatusConstant::OPERATION_AGREE,
			'opinion'        => '系统自动通过(申请人与审批人相同)',
			'operation_time' => date('Y-m-d H:i:s'),
			'tenant_id'      => $instance['tenant_id'] ?? 0
		];
		
		WorkflowHistoryService::getInstance()
		                      ->add($historyData);
	}
	
	/**
	 * 获取任务详情
	 *
	 * @param int   $id   任务ID
	 * @param array $with 关联数据
	 * @return Model
	 */
	public function getTaskDetail(int $id, array $with = []): Model
	{
		// 确保默认加载实例和历史记录
		$defaultWith = [
			'instance' => function ($query) {
				$query->with([
					'definition',
					'submitter',
					'dept'
				]);
			}
		];
		
		$finalWith = $with;
		
		if (empty($finalWith)) {
			$finalWith = $defaultWith;
		}
		
		// 获取任务详情
		$task = $this->model->with($finalWith)
		                    ->findOrEmpty($id);
		if ($task->isEmpty()) {
			throw new BusinessException('任务不存在');
		}
		// 如果任务存在，加载历史记录
		if ($task->instance) {
			// 获取流程历史记录
			$historyService = new WorkflowHistoryService();
			$flowLogs       = $historyService->getList([
				'instance_id' => $task->instance_id
			], ['operation_time' => 'desc']);
			
			// 将历史记录添加到实例数据中
			$task->instance->flowLogs = $flowLogs;
		}
		
		return $task;
	}
} 