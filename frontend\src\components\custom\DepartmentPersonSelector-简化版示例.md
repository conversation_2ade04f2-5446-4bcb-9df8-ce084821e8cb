# DepartmentPersonSelector 简化版使用示例

## 🆕 最新更新 (v2.1)

### DepartmentPersonSelector 组件优化
- ✅ **左侧增加"全部"选项**：可以查看所有部门的人员，点击"全部"显示所有人员
- ✅ **去掉部门名称显示**：人员列表中只显示姓名和职位，不再显示部门信息
- ✅ **本地搜索功能**：搜索在当前已加载的数据中进行，提升搜索速度，无需发送API请求
- ✅ **使用WorkflowApi接口**：统一使用WorkflowApi.getDepartmentList()和getUserList()接口

### 数据格式适配
```json
// 部门数据格式
{
  "code": 1,
  "data": [
    {
      "id": 1,
      "parent_id": 0,
      "name": "技术部",
      "children": [...]
    }
  ]
}

// 人员数据格式
{
  "code": 1,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "status_text": "启用",
      "gender_text": "男"
    }
  ]
}
```

## 🎯 核心改进

### 1. 简化的数据结构
```typescript
// 之前：需要传入完整的人员信息
interface PersonItem {
  id: string | number
  name: string
  avatar?: string
  position?: string
  department?: string
}

// 现在：只需要传入ID
interface PersonItem {
  id: string | number
}
```

### 2. 组件名称优化
- `DepartmentPersonSelect` → `DepartmentPersonForm` (表单专用)
- `DepartmentPersonFilter` → `DepartmentPersonSearch` (搜索专用)

## 📱 使用示例

### 表单中的使用
```vue
<template>
  <el-form :model="form">
    <!-- 单选负责人 -->
    <el-form-item label="项目负责人">
      <DepartmentPersonForm
        v-model="form.owner"
        :multiple="false"
        placeholder="请选择负责人"
      />
    </el-form-item>
    
    <!-- 多选团队成员 -->
    <el-form-item label="团队成员">
      <DepartmentPersonForm
        v-model="form.members"
        :multiple="true"
        placeholder="请选择团队成员"
        :collapse-tags="true"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'
import { DepartmentPersonForm } from '@/components/custom'

const form = reactive({
  owner: null,        // { id: 1 } | null
  members: []         // [{ id: 1 }, { id: 2 }]
})
</script>
```

### 表格搜索中的使用
```vue
<template>
  <div>
    <!-- 搜索栏 -->
    <el-form :model="searchForm" inline>
      <el-form-item label="负责人">
        <DepartmentPersonSearch
          v-model="searchForm.owner"
          :multiple="false"
          placeholder="搜索负责人"
          size="small"
        />
      </el-form-item>
    </el-form>
    
    <!-- 表格 -->
    <el-table :data="tableData">
      <!-- 表格内容 -->
    </el-table>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { DepartmentPersonSearch } from '@/components/custom'

const searchForm = reactive({
  owner: null         // { id: 1 } | null
})

// 搜索时只需要使用ID
const loadTableData = () => {
  const params = {
    owner_id: searchForm.owner?.id
  }
  // 调用API...
}
</script>
```

### 工作流中的使用
```vue
<template>
  <DepartmentPersonSelector
    v-model="selectorVisible"
    :selected-data="selectedPersons"
    :multiple="true"
    title="选择审批人"
    @confirm="handleConfirm"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedPersons = ref([])  // [{ id: 1 }, { id: 2 }]

const handleConfirm = (persons) => {
  selectedPersons.value = persons
  
  // 获取选中的ID列表
  const ids = persons.map(p => p.id)
  console.log('选中的人员IDs:', ids)
}
</script>
```

## 🔧 组件内部处理

### 自动获取人员信息
```typescript
// 组件内部会自动处理：
// 1. 根据传入的ID获取人员详细信息
// 2. 缓存人员信息，避免重复请求
// 3. 在界面上正确显示人员姓名、头像等

// 用户只需要关心ID，其他信息组件自动处理
const personInfo = await UserApi.getUsersByIds([1, 2, 3])
```

### 数据缓存机制
```typescript
// 组件内部维护人员信息缓存
const personInfoCache = new Map()

// 避免重复请求相同人员的信息
if (!personInfoCache.has(personId)) {
  const info = await loadPersonInfo(personId)
  personInfoCache.set(personId, info)
}
```

## 📊 数据流示例

### 表单提交流程
```typescript
// 1. 用户选择人员（组件内部处理显示）
// 2. 表单数据只包含ID
const formData = {
  owner: { id: 1 },
  members: [{ id: 2 }, { id: 3 }]
}

// 3. 提交到后端
const submitData = {
  owner_id: formData.owner.id,
  member_ids: formData.members.map(m => m.id)
}

await api.createProject(submitData)
```

### 数据回显流程
```typescript
// 1. 从后端获取数据
const projectData = await api.getProject(id)

// 2. 转换为组件需要的格式
form.owner = projectData.owner_id ? { id: projectData.owner_id } : null
form.members = projectData.member_ids.map(id => ({ id }))

// 3. 组件自动显示人员信息（自动获取姓名等）
```

## ✅ 优势总结

### 1. 简化数据结构
- ✅ 只需要传入ID，减少数据冗余
- ✅ 避免前端维护完整人员信息
- ✅ 减少网络传输数据量

### 2. 自动信息获取
- ✅ 组件内部自动获取人员详细信息
- ✅ 智能缓存，避免重复请求
- ✅ 用户无感知的信息加载

### 3. 更好的性能
- ✅ 按需加载人员信息
- ✅ 缓存机制减少API调用
- ✅ 更小的数据传输量

### 4. 更清晰的职责
- ✅ 业务逻辑只关心ID
- ✅ 组件负责UI展示
- ✅ 数据获取由组件内部处理

## 🚀 迁移指南

### 从旧版本迁移
```typescript
// 旧版本
const oldData = [
  {
    id: 1,
    name: '张三',
    avatar: 'avatar.jpg',
    position: '工程师',
    department: '技术部'
  }
]

// 新版本（只需要ID）
const newData = [
  { id: 1 }
]
```

### API调用优化
```typescript
// 旧版本：前端需要获取完整人员信息
const users = await UserApi.getUsers()
const fullUserData = users.map(user => ({
  id: user.id,
  name: user.name,
  avatar: user.avatar,
  // ... 其他字段
}))

// 新版本：只需要ID，组件内部处理
const userIds = await UserApi.getUserIds()
const simpleUserData = userIds.map(id => ({ id }))
```

这样的设计让组件使用更加简单，数据结构更加清晰，性能也得到了优化！
