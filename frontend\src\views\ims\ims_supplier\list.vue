<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElMessageBox, ElDescriptions, ElDescriptionsItem } from 'element-plus'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { ImsSupplierApi } from '@/api/ims/imsSupplier'
  import { ApiStatus } from '@/utils/http/status'
  import { useAuth } from '@/composables/useAuth'

  import { LongTextColumn } from '@/components/core/tables/columns'

  import FormDialog from './form-dialog.vue'

  // 权限验证
  const { hasAuth } = useAuth()

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 定义表单搜索初始值
  const initialSearchState = {
    name: '',
    contact_name: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      prop: 'name',
      label: '供应商',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入供应商名称关键词'
      },
      onChange: handleFormChange
    },
    {
      prop: 'contact_name',
      label: '联系人',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入联系人名称'
      },
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 动态列配置 - 保留用于表格头部的列选择功能
  const { columnChecks } = useCheckedColumns(() => [
    {
      prop: 'id',
      label: 'ID',
      width: 80
    },
    {
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'contact_name',
      label: '联系人'
    },
    {
      prop: 'phone',
      label: '联系电话'
    },
    {
      prop: 'detailed_address',
      label: '详细地址'
    },
    {
      prop: 'remark',
      label: '备注'
    },
    {
      prop: 'creator_name',
      label: '创建人'
    },
    {
      prop: 'created_at',
      label: '创建时间'
    }
  ])

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await ImsSupplierApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await ImsSupplierApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  // 这个函数已经在下面的条件块中定义了

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await ImsSupplierApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } catch (error) {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 表单对话框引用
  const formDialogRef = ref()

  // 显示表单对话框
  const showFormDialog = (type: string, id?: number) => {
    formDialogRef.value?.showDialog(type, id)
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="ims-imsSupplier-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton v-auth="'ims:ims_supplier:add'" type="primary" @click="showFormDialog('add')">新增</ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :height="520"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- ID列 -->
          <ElTableColumn prop="id" label="ID" width="80" />

          <!-- 名称列 -->
          <ElTableColumn prop="name" label="名称" />

          <!-- 联系人列 -->
          <ElTableColumn prop="contact_name" label="联系人" />

          <!-- 联系电话列 -->
          <ElTableColumn prop="phone" label="联系电话" />

          <!-- 详细地址列 -->
          <ElTableColumn prop="detailed_address" label="详细地址" min-width="200">
            <template #default="scope">
              <div>
                {{
                  [
                    scope.row.province,
                    scope.row.city,
                    scope.row.district,
                    scope.row.detailed_address
                  ]
                    .filter(Boolean)
                    .join(' ') || '-'
                }}
              </div>
            </template>
          </ElTableColumn>

          <!-- 备注列 -->
          <LongTextColumn prop="remark" label="备注" :maxLength="50" />

          <!-- 创建人列 -->
          <ElTableColumn prop="creator_name" label="创建人" />

          <!-- 创建时间列 -->
          <ElTableColumn prop="created_at" label="创建时间" width="180" />

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="240">
            <template #default="scope">
              <div>
                <ArtButtonTable v-auth="'ims:ims_supplier:detail'" text="详情" type="detail" @click="showDetail(scope.row.id)" />
                <ArtButtonTable
                  v-auth="'ims:ims_supplier:edit'"
                  text="编辑"
                  type="edit"
                  @click="showFormDialog('edit', scope.row.id)"
                />
                <ArtButtonTable v-auth="'ims:ims_supplier:delete'" text="删除" type="delete" @click="handleDelete(scope.row.id)" />
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="供应商表详情"
          width="700px"
          destroy-on-close
          class="detail-dialog"
        >
          <div class="detail-content" style="height: 500px; overflow-y: auto; padding-right: 10px">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="供应商ID">
                {{ detailData.id || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="租户ID">
                {{ detailData.tenant_id || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="供应商名称">
                {{ detailData.name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="供应商编码">
                {{ detailData.code || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="联系人">
                {{ detailData.contact_name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="联系电话">
                {{ detailData.phone || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="省份">
                {{ detailData.province || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="城市">
                {{ detailData.city || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="区县">
                {{ detailData.district || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="详细地址">
                {{ detailData.detailed_address || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="备注">
                {{ detailData.remark || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建人">
                {{ detailData.creator_name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建时间">
                {{ detailData.created_at || '-' }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 表单组件 -->
        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .ims-imsSupplier-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    /* 详情对话框固定高度样式 */
    :deep(.detail-dialog) {
      .el-dialog__body {
        height: 500px !important;
        padding: 20px !important;
        overflow: hidden !important;
      }

      .detail-content {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
      }

      /* 滚动条样式优化 */
      .detail-content::-webkit-scrollbar {
        width: 6px;
      }

      .detail-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    }
  }
</style>
