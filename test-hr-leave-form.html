<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>假勤表单重复请求测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .problem {
            background-color: #ffebee;
            border-color: #f44336;
        }
        .solution {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .flow {
            background-color: #fff3e0;
            border-color: #ff9800;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        h3 {
            color: #555;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>假勤表单保存重复请求问题分析与修复</h1>

    <div class="section problem">
        <h2>🚨 问题描述</h2>
        <p>假勤表单保存时会提交两次请求，导致：</p>
        <ul>
            <li>重复的API调用</li>
            <li>可能的数据不一致</li>
            <li>用户体验不佳</li>
            <li>服务器资源浪费</li>
        </ul>
    </div>

    <div class="section flow">
        <h2>🔍 问题根因分析</h2>
        <h3>原始事件流程：</h3>
        <ol>
            <li>用户点击"保存草稿"按钮</li>
            <li>触发 <span class="highlight">hr_leave-form.vue</span> 的 <code>handleSave</code> 方法</li>
            <li><code>handleSave</code> 直接调用 <code>ApplicationApi.save()</code> <strong>（第一次请求）</strong></li>
            <li><code>handleSave</code> 调用 <code>emit('save', data)</code></li>
            <li>触发 <span class="highlight">form-manager.vue</span> 的 <code>handleSaveForm</code> 方法</li>
            <li><code>handleSaveForm</code> 再次调用 <code>ApplicationApi.save()</code> <strong>（第二次请求）</strong></li>
        </ol>

        <div class="code">
// 问题代码示例：
// hr_leave-form.vue 中的 handleSave
const handleSave = async () => {
  // ... 验证逻辑
  
  // 第一次API调用
  const response = await ApplicationApi.save(data)
  
  // 触发事件，导致第二次API调用
  emit('save', response.data)
}
        </div>
    </div>

    <div class="section solution">
        <h2>✅ 修复方案</h2>
        <h3>核心思路：职责分离</h3>
        <ul>
            <li><strong>hr_leave-form.vue</strong>：只负责数据验证和准备，不直接调用API</li>
            <li><strong>form-manager.vue</strong>：统一处理所有API调用</li>
            <li>通过事件机制传递数据，避免重复调用</li>
        </ul>

        <h3>修复后的事件流程：</h3>
        <ol>
            <li>用户点击"保存草稿"按钮</li>
            <li>触发 <span class="highlight">hr_leave-form.vue</span> 的 <code>handleSave</code> 方法</li>
            <li><code>handleSave</code> 只进行表单验证和数据准备</li>
            <li><code>handleSave</code> 调用 <code>emit('save', formData)</code></li>
            <li>触发 <span class="highlight">form-manager.vue</span> 的 <code>handleSaveForm</code> 方法</li>
            <li><code>handleSaveForm</code> 调用 <code>ApplicationApi.save()</code> <strong>（唯一的API请求）</strong></li>
            <li>成功后更新子组件的表单数据</li>
        </ol>
    </div>

    <div class="section">
        <h2>🔧 具体修改内容</h2>
        
        <h3>1. hr_leave-form.vue 修改</h3>
        <div class="code">
// 修改前：直接调用API
const handleSave = async () => {
  const response = await ApplicationApi.save(data)  // 第一次调用
  emit('save', response.data)  // 触发第二次调用
}

// 修改后：只准备数据
const handleSave = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return
  
  const submitData = { /* 准备数据 */ }
  emit('save', submitData)  // 只触发事件，不调用API
}
        </div>

        <h3>2. form-manager.vue 修改</h3>
        <div class="code">
// 增强的统一API处理
const handleSaveForm = async (formData) => {
  let res
  
  if (formData.id) {
    // 编辑模式
    res = await ApplicationApi.edit(formData.id, {
      business_code: formType.value,
      business_data: formData
    })
  } else {
    // 新增模式
    res = await ApplicationApi.save({
      business_code: formType.value,
      definition_id: typeId.value,
      business_data: formData
    })
  }
  
  // 更新子组件数据
  if (res.code === 1 && formComponentRef.value) {
    formComponentRef.value.updateFormData(res.data.data)
  }
}
        </div>

        <h3>3. 添加数据同步机制</h3>
        <div class="code">
// hr_leave-form.vue 中添加
const updateFormData = (newData) => {
  if (newData) {
    Object.assign(formData, newData)
  }
}

defineExpose({
  showForm,
  resetForm,
  loadFormData,
  updateFormData  // 新增
})
        </div>
    </div>

    <div class="section">
        <h2>🧪 测试验证</h2>
        <h3>测试步骤：</h3>
        <ol>
            <li>打开浏览器开发者工具的Network面板</li>
            <li>访问工作流申请页面</li>
            <li>选择"假勤"类型，填写表单</li>
            <li>点击"保存草稿"按钮</li>
            <li>观察Network面板中的请求数量</li>
        </ol>

        <h3>预期结果：</h3>
        <ul>
            <li>✅ 只有一次 <code>/workflow/myapp/save</code> 请求</li>
            <li>✅ 表单数据正确保存</li>
            <li>✅ 表单ID正确更新</li>
            <li>✅ 用户体验流畅</li>
        </ul>
    </div>

    <div class="section">
        <h2>📋 修复清单</h2>
        <ul>
            <li>✅ 修改 hr_leave-form.vue 的 handleSave 方法</li>
            <li>✅ 修改 hr_leave-form.vue 的 handleSubmit 方法</li>
            <li>✅ 增强 form-manager.vue 的 handleSaveForm 方法</li>
            <li>✅ 增强 form-manager.vue 的 handleSubmitForm 方法</li>
            <li>✅ 添加 updateFormData 方法用于数据同步</li>
            <li>✅ 添加调试日志用于验证修复效果</li>
            <li>✅ 处理loading状态的正确重置</li>
        </ul>
    </div>

    <div class="section">
        <h2>🎯 关键改进点</h2>
        <ul>
            <li><strong>单一职责原则</strong>：业务表单组件专注于UI和验证，API调用统一管理</li>
            <li><strong>事件驱动架构</strong>：通过事件机制实现组件间通信，避免紧耦合</li>
            <li><strong>状态同步机制</strong>：确保API响应数据能正确更新到表单组件</li>
            <li><strong>错误处理优化</strong>：统一的错误处理和用户反馈</li>
            <li><strong>调试友好</strong>：添加详细的日志输出，便于问题排查</li>
        </ul>
    </div>
</body>
</html>
