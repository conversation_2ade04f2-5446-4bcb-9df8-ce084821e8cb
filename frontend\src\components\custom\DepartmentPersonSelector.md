# DepartmentPersonSelector 部门人员选择器

## 组件简介

`DepartmentPersonSelector` 是一个通用的部门+人员选择组件，采用三栏布局设计：
- **左栏**：部门树形结构导航
- **中栏**：人员列表 + 搜索功能  
- **右栏**：已选择人员管理

## 功能特点

- ✅ 三栏布局，交互直观
- ✅ 部门树形导航
- ✅ 人员搜索功能
- ✅ 单选/多选模式
- ✅ 已选人员管理
- ✅ 自定义 API 接口
- ✅ 响应式设计
- ✅ TypeScript 支持

## 基础用法

```vue
<template>
  <div>
    <el-button @click="openSelector">选择人员</el-button>
    
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :selected-data="selectedPersons"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DepartmentPersonSelector from '@/components/custom/DepartmentPersonSelector.vue'

const selectorVisible = ref(false)
const selectedPersons = ref([])

const openSelector = () => {
  selectorVisible.value = true
}

const handleConfirm = (persons) => {
  selectedPersons.value = persons
  console.log('选择的人员:', persons)
}
</script>
```

## Props 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | boolean | false | 对话框显示状态 |
| selectedData | PersonItem[] | [] | 已选择的人员数据 |
| title | string | '选择人员' | 对话框标题 |
| width | string | '800px' | 对话框宽度 |
| departmentTitle | string | '部门列表' | 部门面板标题 |
| personTitle | string | '人员列表' | 人员面板标题 |
| selectedTitle | string | '已选择人员' | 已选面板标题 |
| multiple | boolean | true | 是否多选 |
| showAvatar | boolean | true | 是否显示头像 |
| showPosition | boolean | true | 是否显示职位 |
| showPersonCount | boolean | false | 是否显示部门人数 |
| showSelectAll | boolean | true | 是否显示全选按钮 |
| defaultExpandAll | boolean | false | 是否默认展开所有部门 |
| searchPlaceholder | string | '搜索人员' | 搜索框占位符 |
| emptyText | string | '暂无人员' | 空状态文本 |
| cancelText | string | '取消' | 取消按钮文本 |
| confirmText | string | '确定' | 确认按钮文本 |
| departmentApi | Function | - | 自定义部门 API |
| userApi | Function | - | 自定义人员 API |
| treeProps | Object | {label:'name',children:'children'} | 树形配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | boolean | 对话框显示状态变化 |
| confirm | PersonItem[] | 确认选择 |
| cancel | - | 取消选择 |

## 数据类型

```typescript
interface PersonItem {
  id: string | number
  name: string
  avatar?: string
  position?: string
  department?: string
  [key: string]: any
}

interface DepartmentItem {
  id: string | number
  name: string
  children?: DepartmentItem[]
  person_count?: number
  [key: string]: any
}
```

## 高级用法

### 单选模式

```vue
<DepartmentPersonSelector
  v-model="visible"
  :multiple="false"
  title="选择负责人"
  @confirm="handleConfirm"
/>
```

### 自定义 API

```vue
<template>
  <DepartmentPersonSelector
    v-model="visible"
    :department-api="customDepartmentApi"
    :user-api="customUserApi"
    @confirm="handleConfirm"
  />
</template>

<script setup>
// 自定义部门 API
const customDepartmentApi = async () => {
  const res = await MyApi.getDepartments()
  return res.data
}

// 自定义人员 API
const customUserApi = async (params) => {
  const res = await MyApi.getUsers(params)
  return res.data
}
</script>
```

### 工作流适配

```vue
<!-- 工作流中使用适配器 -->
<EmployeeSelectorNew
  v-model="visible"
  :selected-data="workflowData"
  title="选择审批人"
  @confirm="handleWorkflowConfirm"
/>
```

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```css
.dept-person-selector-container {
  --panel-bg-color: #f5f7fa;
  --border-color: #e6e6e6;
  --text-color: #303133;
  --hover-bg-color: #f5f7fa;
}
```

## 注意事项

1. 组件依赖 Element Plus，确保已正确安装和配置
2. 默认使用系统的部门和用户 API，可通过 props 自定义
3. 支持响应式设计，在移动端会自动调整布局
4. 建议在使用前先测试 API 接口的数据格式是否匹配

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的部门人员选择功能
- 支持单选/多选模式
- 支持自定义 API 接口
