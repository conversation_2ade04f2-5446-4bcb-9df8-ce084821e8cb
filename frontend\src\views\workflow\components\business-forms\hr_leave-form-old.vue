<template>
  <div class="hr-leave-form">
    <!-- 表单标题 -->
    <div class="form-header">
      <h3 class="form-title">
        <el-icon>
          <Calendar />
        </el-icon>
        请假申请表单
      </h3>
      <div class="form-status" v-if="formData.id">
        <el-tag :type="getStatusType(formData.approval_status)" size="large">
          {{ formData.approval_status_text || '草稿' }}
        </el-tag>
      </div>
    </div>

    <!-- 主表单 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      class="leave-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon>
            <User />
          </el-icon>
          基本信息
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请假类型" prop="leave_type" required>
              <el-select
                v-model="formData.leave_type"
                placeholder="请选择请假类型"
                style="width: 100%"
                :disabled="!canEdit"
              >
                <el-option
                  v-for="item in leaveTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="请假天数" prop="duration" required>
              <el-input-number
                v-model="formData.duration"
                :min="0.5"
                :max="365"
                :step="0.5"
                :precision="1"
                placeholder="请假天数"
                style="width: 100%"
                :disabled="!canEdit"
              />
              <div class="field-tip">系统会根据时间自动计算，也可手动调整</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 时间信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon>
            <Clock />
          </el-icon>
          时间信息
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="start_time" required>
              <el-date-picker
                v-model="formData.start_time"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled="!canEdit"
                @change="handleTimeChange"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="结束时间" prop="end_time" required>
              <el-date-picker
                v-model="formData.end_time"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled="!canEdit"
                @change="handleTimeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon>
            <Document />
          </el-icon>
          详细信息
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请假原因" prop="reason" required>
              <el-input
                v-model="formData.reason"
                type="textarea"
                :rows="4"
                placeholder="请详细说明请假原因（最多500字符）"
                maxlength="500"
                show-word-limit
                :disabled="!canEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急联系人" prop="emergency_contact">
              <el-input
                v-model="formData.emergency_contact"
                placeholder="请输入紧急联系人姓名"
                :disabled="!canEdit"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系电话" prop="emergency_phone">
              <el-input
                v-model="formData.emergency_phone"
                placeholder="请输入紧急联系人电话"
                :disabled="!canEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 附件信息 -->
      <div class="form-section">
        <div class="section-title">
          <el-icon>
            <Paperclip />
          </el-icon>
          附件信息
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="相关附件" prop="attachment">
              <el-upload
                v-model:file-list="fileList"
                action="/api/upload"
                multiple
                :limit="5"
                :disabled="!canEdit"
                :on-success="handleUploadSuccess"
                :on-remove="handleUploadRemove"
                :before-upload="beforeUpload"
              >
                <el-button type="primary" :disabled="!canEdit">
                  <el-icon>
                    <Upload />
                  </el-icon>
                  上传附件
                </el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传图片、PDF、Word文档，单个文件不超过10MB，最多5个文件
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="其他需要说明的信息（可选）"
                maxlength="200"
                show-word-limit
                :disabled="!canEdit"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 审批信息（仅查看时显示） -->
      <div class="form-section" v-if="formData.id && formData.approval_status > 0">
        <div class="section-title">
          <el-icon>
            <Checked />
          </el-icon>
          审批信息
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="提交时间">
              <el-input :value="formData.submit_time || '未提交'" readonly style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="审批时间">
              <el-input :value="formData.approval_time || '未审批'" readonly style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="formData.approval_opinion">
          <el-col :span="24">
            <el-form-item label="审批意见">
              <el-input :value="formData.approval_opinion" type="textarea" :rows="3" readonly />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel"> 取消 </el-button>

      <el-button v-if="canEdit" type="primary" @click="handleSave" :loading="saving">
        保存草稿
      </el-button>

      <el-button v-if="canSubmit" type="success" @click="handleSubmit" :loading="submitting">
        提交审批
      </el-button>

      <el-button v-if="canWithdraw" type="warning" @click="handleWithdraw" :loading="withdrawing">
        撤回申请
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {
    ElMessage,
    ElMessageBox,
    type FormInstance,
    type FormRules,
    type UploadFile
  } from 'element-plus'
  import {
    Calendar,
    User,
    Clock,
    Document,
    Paperclip,
    Upload,
    Checked
  } from '@element-plus/icons-vue'
  import HrLeaveApi, { type HrLeaveItem, type HrLeaveFormData } from '@/api/hr/hrLeave'

  /**
   * 组件属性定义
   */
  interface Props {
    /** 表单数据ID（编辑时传入） */
    id?: number | string
    /** 是否为只读模式 */
    readonly?: boolean
    /** 是否显示在对话框中 */
    inDialog?: boolean
  }

  /**
   * 组件事件定义
   */
  interface Emits {
    /** 保存成功事件 */
    (e: 'save', data: HrLeaveItem): void

    /** 提交成功事件 */
    (e: 'submit', data: { instance_id: number; leave_id: number }): void

    /** 撤回成功事件 */
    (e: 'withdraw'): void

    /** 取消事件 */
    (e: 'cancel'): void
  }

  // 组件属性和事件
  const props = withDefaults(defineProps<Props>(), {
    readonly: false,
    inDialog: false
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 表单数据 */
  const formData = reactive<HrLeaveFormData & Partial<HrLeaveItem>>({
    leave_type: 1,
    start_time: '',
    end_time: '',
    duration: 1,
    reason: '',
    emergency_contact: '',
    emergency_phone: '',
    attachment: [],
    remark: ''
  })

  /** 文件列表 */
  const fileList = ref<UploadFile[]>([])

  /** 请假类型选项 */
  const leaveTypeOptions = ref([
    { value: 1, label: '年假' },
    { value: 2, label: '事假' },
    { value: 3, label: '病假' },
    { value: 4, label: '婚假' },
    { value: 5, label: '产假' },
    { value: 6, label: '丧假' },
    { value: 7, label: '其他' }
  ])

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)
  const withdrawing = ref(false)

  // ==================== 计算属性 ====================

  /** 是否可以编辑 */
  const canEdit = computed(() => {
    if (props.readonly) return false
    return formData.can_edit !== false
  })

  /** 是否可以提交 */
  const canSubmit = computed(() => {
    if (props.readonly) return false
    return formData.can_submit !== false
  })

  /** 是否可以撤回 */
  const canWithdraw = computed(() => {
    if (props.readonly) return false
    return formData.can_withdraw !== false
  })

  // ==================== 表单验证规则 ====================

  /** 表单验证规则 */
  const formRules: FormRules = {
    leave_type: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
    start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
    end_time: [
      { required: true, message: '请选择结束时间', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && formData.start_time && value <= formData.start_time) {
            callback(new Error('结束时间必须晚于开始时间'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    duration: [
      { required: true, message: '请输入请假天数', trigger: 'blur' },
      {
        type: 'number',
        min: 0.5,
        max: 365,
        message: '请假天数必须在0.5-365天之间',
        trigger: 'blur'
      }
    ],
    reason: [
      { required: true, message: '请输入请假原因', trigger: 'blur' },
      { min: 5, max: 500, message: '请假原因长度在5-500个字符之间', trigger: 'blur' }
    ],
    emergency_phone: [
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur'
      }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 获取状态类型（用于标签显示）
   *
   * @param status 审批状态
   * @returns 标签类型
   */
  const getStatusType = (status: number): string => {
    const typeMap: Record<number, string> = {
      0: 'info', // 草稿
      1: 'warning', // 审批中
      2: 'success', // 已通过
      3: 'danger', // 已拒绝
      4: 'danger', // 已终止
      5: 'info', // 已撤回
      6: 'danger' // 已作废
    }
    return typeMap[status] || 'default'
  }

  /**
   * 处理时间变化
   * 自动计算请假天数
   */
  const handleTimeChange = () => {
    if (formData.start_time && formData.end_time) {
      const validation = HrLeaveApi.validateLeaveTime(formData.start_time, formData.end_time)
      if (validation.valid) {
        formData.duration = HrLeaveApi.calculateDuration(formData.start_time, formData.end_time)
      }
    }
  }

  /**
   * 文件上传成功处理
   *
   * @param response 上传响应
   * @param file 文件对象
   */
  const handleUploadSuccess = (response: any, file: UploadFile) => {
    if (response.code === 200) {
      if (!formData.attachment) {
        formData.attachment = []
      }
      if (Array.isArray(formData.attachment)) {
        formData.attachment.push(response.data.url)
      }
      ElMessage.success('文件上传成功')
    } else {
      ElMessage.error('文件上传失败：' + response.message)
    }
  }

  /**
   * 文件移除处理
   *
   * @param file 文件对象
   */
  const handleUploadRemove = (file: UploadFile) => {
    if (Array.isArray(formData.attachment)) {
      const index = formData.attachment.findIndex((url) => url === file.url)
      if (index > -1) {
        formData.attachment.splice(index, 1)
      }
    }
  }

  /**
   * 文件上传前检查
   *
   * @param file 文件对象
   * @returns 是否允许上传
   */
  const beforeUpload = (file: File): boolean => {
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持上传图片、PDF、Word文档')
      return false
    }

    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过10MB')
      return false
    }

    return true
  }

  /**
   * 加载表单数据
   *
   * @param id 记录ID
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      const response = await HrLeaveApi.getDetail(id)

      if (response.code === 200) {
        Object.assign(formData, response.data)

        // 处理附件数据
        if (formData.attachment) {
          if (typeof formData.attachment === 'string') {
            try {
              formData.attachment = JSON.parse(formData.attachment)
            } catch {
              formData.attachment = []
            }
          }

          // 构建文件列表用于显示
          if (Array.isArray(formData.attachment)) {
            fileList.value = formData.attachment.map((url, index) => ({
              name: `附件${index + 1}`,
              url: url,
              uid: Date.now() + index
            }))
          }
        }
      } else {
        ElMessage.error('加载数据失败：' + response.message)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
      ElMessage.error('加载数据失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备提交数据
      const submitData: HrLeaveFormData = {
        leave_type: formData.leave_type,
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact,
        emergency_phone: formData.emergency_phone,
        attachment: formData.attachment,
        remark: formData.remark
      }

      let response
      if (formData.id) {
        // 编辑
        response = await HrLeaveApi.edit(formData.id, submitData)
        if (response.code === 200) {
          ElMessage.success('保存成功')
          // 重新加载数据
          await loadFormData(formData.id)
          emit('save', formData as HrLeaveItem)
        } else {
          ElMessage.error('保存失败：' + response.message)
        }
      } else {
        // 新增
        response = await HrLeaveApi.add(submitData)
        if (response.code === 200) {
          ElMessage.success('保存成功')
          Object.assign(formData, response.data.data)
          emit('save', response.data.data)
        } else {
          ElMessage.error('保存失败：' + response.message)
        }
      }
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 先保存数据
      await handleSave()

      if (!formData.id) {
        ElMessage.error('请先保存数据')
        return
      }

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true
      const response = await HrLeaveApi.submit(formData.id)

      if (response.code === 200) {
        ElMessage.success('提交审批成功')
        // 重新加载数据
        await loadFormData(formData.id)
        emit('submit', response.data)
      } else {
        ElMessage.error('提交审批失败：' + response.message)
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交审批失败:', error)
        ElMessage.error('提交审批失败')
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 撤回申请
   */
  const handleWithdraw = async () => {
    try {
      const { value: reason } = await ElMessageBox.prompt('请输入撤回原因：', '确认撤回', {
        confirmButtonText: '确定撤回',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入撤回原因...',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '撤回原因不能为空'
          }
          return true
        }
      })

      withdrawing.value = true
      const response = await HrLeaveApi.withdraw(formData.id!, { reason: reason.trim() })

      if (response.code === 200) {
        ElMessage.success('撤回成功')
        // 重新加载数据
        await loadFormData(formData.id!)
        emit('withdraw')
      } else {
        ElMessage.error('撤回失败：' + response.message)
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('撤回失败:', error)
        ElMessage.error('撤回失败')
      }
    } finally {
      withdrawing.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    emit('cancel')
  }

  // ==================== 生命周期 ====================

  onMounted(async () => {
    // 加载请假类型选项
    try {
      const response = await HrLeaveApi.getLeaveTypes()
      if (response.code === 200) {
        leaveTypeOptions.value = response.data
      }
    } catch (error) {
      console.error('加载请假类型失败:', error)
    }

    // 如果有ID，加载数据
    if (props.id) {
      await loadFormData(props.id)
    }
  })

  // 监听ID变化
  watch(
    () => props.id,
    (newId) => {
      if (newId) {
        loadFormData(newId)
      }
    },
    { immediate: false }
  )
</script>

<style scoped lang="scss">
  .hr-leave-form {
    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #ebeef5;

      .form-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }

      .form-status {
        .el-tag {
          font-size: 14px;
          padding: 8px 16px;
        }
      }
    }

    .form-section {
      margin-bottom: 32px;

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }

      .field-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }

    .leave-form {
      .el-form-item {
        margin-bottom: 20px;
      }

      .el-textarea {
        .el-textarea__inner {
          resize: vertical;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #ebeef5;

      .el-button {
        min-width: 100px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .hr-leave-form {
      .form-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .form-actions {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
</style>
