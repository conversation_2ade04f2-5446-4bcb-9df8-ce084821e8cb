<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 客户共享表路由
/*Route::group('api/crm/crm_customer_share', function () {
    Route::get('index', 'app\crm\controller\CrmCustomerShareController@index');
    Route::get('detail/:id', 'app\crm\controller\CrmCustomerShareController@detail');
    Route::post('add', 'app\crm\controller\CrmCustomerShareController@add');
    Route::post('edit/:id', 'app\crm\controller\CrmCustomerShareController@edit');
    Route::post('delete/:id', 'app\crm\controller\CrmCustomerShareController@delete');
    Route::post('batchDelete', 'app\crm\controller\CrmCustomerShareController@batchDelete');
    Route::post('updateField', 'app\crm\controller\CrmCustomerShareController@updateField');
    Route::post('status/:id', 'app\crm\controller\CrmCustomerShareController@status');
    Route::post('import', 'app\crm\controller\CrmCustomerShareController@import');
    Route::get('importTemplate', 'app\crm\controller\CrmCustomerShareController@importTemplate');
    Route::get('downloadTemplate', 'app\crm\controller\CrmCustomerShareController@downloadTemplate');
    Route::get('export', 'app\crm\controller\CrmCustomerShareController@export');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);*/