# 组件映射与加载机制

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 设计理念

### 核心思想
通过`businessCode`实现组件的自动映射和加载，遵循"约定优于配置"的原则，实现零配置的组件管理。

### 设计原则
1. **约定优于配置**：通过命名约定自动映射组件
2. **渐进式增强**：支持从简单到复杂的多种表单类型
3. **向后兼容**：新增功能不影响现有组件
4. **优雅降级**：组件不存在时自动使用通用组件

## 🗺️ 映射规则体系

### 业务代码分类

```mermaid
graph TD
    A[businessCode] --> B{代码模式识别}
    
    B -->|fc_*| C[Form-Create表单]
    B -->|custom_form_*| D[自定义动态表单]
    B -->|业务模块_功能| E[静态业务表单]
    
    C --> C1[form-create-renderer.vue]
    D --> D1[dynamic-form.vue]
    E --> E1[business-forms/模块_功能.vue]
    
    C1 --> C2[加载Form-Create配置]
    D1 --> D2[加载动态表单配置]
    E1 --> E2[直接使用静态组件]
    
    C2 --> F[渲染表单]
    D2 --> F
    E2 --> F
```

### 映射规则表

| 业务代码模式 | 组件类型 | 申请表单路径 | 详情表单路径 | 配置来源 |
|-------------|----------|-------------|-------------|----------|
| `fc_001` | Form-Create | `form-create-renderer.vue` | `form-create-viewer.vue` | 数据库配置 |
| `custom_form_001` | 自定义动态 | `dynamic-form.vue` | `dynamic-detail.vue` | 数据库配置 |
| `crm_contract` | 静态业务 | `business-forms/crm_contract.vue` | `business-detail/crm_contract.vue` | 组件文件 |
| `hr_leave` | 静态业务 | `business-forms/hr_leave.vue` | `business-detail/hr_leave.vue` | 组件文件 |
| `daily_price_order` | 静态业务 | `business-forms/daily_price_order.vue` | `business-detail/daily_price_order.vue` | 组件文件 |

## 🔧 核心实现

### FormManager组件映射

```typescript
// FormManager.vue - 申请表单映射
interface ComponentMapping {
  component: AsyncComponent
  props?: Record<string, any>
  fallback?: AsyncComponent
}

class FormComponentMapper {
  private cache = new Map<string, ComponentMapping>()
  
  async getFormComponent(businessCode: string): Promise<ComponentMapping> {
    // 缓存检查
    if (this.cache.has(businessCode)) {
      return this.cache.get(businessCode)!
    }
    
    let mapping: ComponentMapping
    
    // Form-Create表单映射
    if (businessCode.startsWith('fc_')) {
      mapping = await this.mapFormCreateComponent(businessCode)
    }
    // 自定义动态表单映射
    else if (businessCode.startsWith('custom_form_')) {
      mapping = await this.mapCustomFormComponent(businessCode)
    }
    // 静态业务表单映射
    else {
      mapping = await this.mapBusinessFormComponent(businessCode)
    }
    
    // 缓存结果
    this.cache.set(businessCode, mapping)
    return mapping
  }
  
  private async mapFormCreateComponent(businessCode: string): Promise<ComponentMapping> {
    const formId = businessCode.replace('fc_', '')
    
    try {
      const formConfig = await CustomFormApi.getFormCreateConfig(formId)
      
      return {
        component: () => import('@/components/business-forms/form-create-renderer.vue'),
        props: { 
          formConfig,
          formId: parseInt(formId)
        }
      }
    } catch (error) {
      console.error(`加载Form-Create配置失败: ${businessCode}`, error)
      return this.getFallbackComponent()
    }
  }
  
  private async mapCustomFormComponent(businessCode: string): Promise<ComponentMapping> {
    const formId = businessCode.replace('custom_form_', '')
    
    try {
      const formConfig = await CustomFormApi.getConfig(formId)
      
      return {
        component: () => import('@/components/business-forms/dynamic-form.vue'),
        props: { 
          formConfig,
          formId: parseInt(formId)
        }
      }
    } catch (error) {
      console.error(`加载自定义表单配置失败: ${businessCode}`, error)
      return this.getFallbackComponent()
    }
  }
  
  private async mapBusinessFormComponent(businessCode: string): Promise<ComponentMapping> {
    return {
      component: () => import(`@/components/business-forms/${businessCode}.vue`)
        .catch(() => {
          console.warn(`静态表单组件不存在: ${businessCode}，使用通用表单`)
          return import('@/components/business-forms/generic-form.vue')
        })
    }
  }
  
  private getFallbackComponent(): ComponentMapping {
    return {
      component: () => import('@/components/business-forms/generic-form.vue')
    }
  }
}

// 使用示例
const mapper = new FormComponentMapper()

const currentFormComponent = computed(async () => {
  if (!formState.type) return null
  
  try {
    const mapping = await mapper.getFormComponent(formState.type)
    return markRaw(defineAsyncComponent(mapping.component))
  } catch (error) {
    console.error('组件映射失败:', error)
    return null
  }
})
```

### FormDataViewer详情映射

```typescript
// FormDataViewer.vue - 详情组件映射
class DetailComponentMapper {
  private cache = new Map<string, ComponentMapping>()
  
  async getDetailComponent(businessCode: string): Promise<ComponentMapping> {
    if (this.cache.has(businessCode)) {
      return this.cache.get(businessCode)!
    }
    
    let mapping: ComponentMapping
    
    // Form-Create详情映射
    if (businessCode.startsWith('fc_')) {
      mapping = await this.mapFormCreateDetail(businessCode)
    }
    // 自定义动态详情映射
    else if (businessCode.startsWith('custom_form_')) {
      mapping = await this.mapCustomFormDetail(businessCode)
    }
    // 静态业务详情映射
    else {
      mapping = await this.mapBusinessDetail(businessCode)
    }
    
    this.cache.set(businessCode, mapping)
    return mapping
  }
  
  private async mapFormCreateDetail(businessCode: string): Promise<ComponentMapping> {
    const formId = businessCode.replace('fc_', '')
    
    try {
      const formConfig = await CustomFormApi.getFormCreateConfig(formId)
      
      return {
        component: () => import('@/components/business-detail/form-create-viewer.vue'),
        props: { formConfig }
      }
    } catch (error) {
      return this.getFallbackDetailComponent()
    }
  }
  
  private async mapCustomFormDetail(businessCode: string): Promise<ComponentMapping> {
    const formId = businessCode.replace('custom_form_', '')
    
    try {
      const formConfig = await CustomFormApi.getConfig(formId)
      
      return {
        component: () => import('@/components/business-detail/dynamic-detail.vue'),
        props: { formConfig }
      }
    } catch (error) {
      return this.getFallbackDetailComponent()
    }
  }
  
  private async mapBusinessDetail(businessCode: string): Promise<ComponentMapping> {
    return {
      component: () => import(`@/components/business-detail/${businessCode}.vue`)
        .catch(() => {
          console.warn(`静态详情组件不存在: ${businessCode}，使用通用详情`)
          return import('@/components/business-detail/generic-detail.vue')
        })
    }
  }
  
  private getFallbackDetailComponent(): ComponentMapping {
    return {
      component: () => import('@/components/business-detail/generic-detail.vue')
    }
  }
}
```

## 🔄 动态加载流程

### 申请表单加载流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FM as FormManager
    participant CM as ComponentMapper
    participant API as ConfigAPI
    participant C as Component
    
    U->>FM: 打开表单(businessCode)
    FM->>CM: getFormComponent(businessCode)
    
    alt Form-Create表单
        CM->>API: getFormCreateConfig(formId)
        API->>CM: 返回配置
        CM->>FM: form-create-renderer + config
    else 自定义表单
        CM->>API: getConfig(formId)
        API->>CM: 返回配置
        CM->>FM: dynamic-form + config
    else 静态表单
        CM->>CM: 直接映射组件路径
        CM->>FM: business-form组件
    end
    
    FM->>C: 动态加载组件
    C->>U: 显示表单界面
```

### 详情组件加载流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FDV as FormDataViewer
    participant DCM as DetailComponentMapper
    participant API as ConfigAPI
    participant DC as DetailComponent
    
    U->>FDV: 查看详情(businessCode, formData)
    FDV->>DCM: getDetailComponent(businessCode)
    
    alt Form-Create详情
        DCM->>API: getFormCreateConfig(formId)
        API->>DCM: 返回配置
        DCM->>FDV: form-create-viewer + config
    else 自定义详情
        DCM->>API: getConfig(formId)
        API->>DCM: 返回配置
        DCM->>FDV: dynamic-detail + config
    else 静态详情
        DCM->>DCM: 直接映射组件路径
        DCM->>FDV: business-detail组件
    end
    
    FDV->>DC: 动态加载组件
    DC->>U: 显示详情界面
```

## 📁 目录结构规范

### 组件目录结构

```
src/components/
├── business-forms/                    # 申请表单目录
│   ├── crm_contract.vue              # 合同申请表单
│   ├── crm_contract_receivable.vue   # 回款申请表单
│   ├── daily_price_order.vue         # 每日报价申请表单
│   ├── hr_leave.vue                  # 请假申请表单
│   ├── hr_travel.vue                 # 出差申请表单
│   ├── form-create-renderer.vue      # Form-Create渲染器
│   ├── dynamic-form.vue              # 动态表单渲染器
│   └── generic-form.vue              # 通用表单（兜底）
├── business-detail/                   # 详情表单目录
│   ├── crm_contract.vue              # 合同详情展示
│   ├── crm_contract_receivable.vue   # 回款详情展示
│   ├── daily_price_order.vue         # 每日报价详情展示
│   ├── hr_leave.vue                  # 请假详情展示
│   ├── hr_travel.vue                 # 出差详情展示
│   ├── form-create-viewer.vue        # Form-Create详情查看器
│   ├── dynamic-detail.vue            # 动态详情查看器
│   └── generic-detail.vue            # 通用详情（兜底）
└── form-engine/                       # 表单引擎
    ├── component-mapper.ts            # 组件映射器
    ├── config-loader.ts               # 配置加载器
    └── cache-manager.ts               # 缓存管理器
```

### 命名约定

| 组件类型 | 命名规则 | 示例 |
|---------|----------|------|
| 申请表单 | `${businessCode}.vue` | `crm_contract.vue` |
| 详情表单 | `${businessCode}.vue` | `crm_contract.vue` |
| 渲染器 | `${type}-renderer.vue` | `form-create-renderer.vue` |
| 查看器 | `${type}-viewer.vue` | `form-create-viewer.vue` |
| 通用组件 | `generic-${type}.vue` | `generic-form.vue` |

## ⚡ 性能优化

### 组件缓存策略

```typescript
class ComponentCache {
  private componentCache = new Map<string, any>()
  private configCache = new Map<string, any>()
  private ttl = 5 * 60 * 1000 // 5分钟缓存

  // 组件缓存
  cacheComponent(key: string, component: any) {
    this.componentCache.set(key, {
      component,
      timestamp: Date.now()
    })
  }

  getCachedComponent(key: string): any | null {
    const cached = this.componentCache.get(key)
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.component
    }
    return null
  }

  // 配置缓存
  cacheConfig(key: string, config: any) {
    this.configCache.set(key, {
      config,
      timestamp: Date.now()
    })
  }

  getCachedConfig(key: string): any | null {
    const cached = this.configCache.get(key)
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.config
    }
    return null
  }

  // 清除过期缓存
  clearExpired() {
    const now = Date.now()
    
    for (const [key, value] of this.componentCache.entries()) {
      if (now - value.timestamp >= this.ttl) {
        this.componentCache.delete(key)
      }
    }
    
    for (const [key, value] of this.configCache.entries()) {
      if (now - value.timestamp >= this.ttl) {
        this.configCache.delete(key)
      }
    }
  }
}
```

### 懒加载优化

```typescript
// 组件懒加载配置
const lazyComponentOptions = {
  loadingComponent: ComponentSkeleton,
  errorComponent: ComponentError,
  delay: 200,
  timeout: 3000
}

// 创建懒加载组件
function createLazyComponent(loader: () => Promise<any>) {
  return defineAsyncComponent({
    loader,
    ...lazyComponentOptions
  })
}

// 使用示例
const businessFormComponent = computed(() => {
  if (!businessCode.value) return null
  
  return markRaw(createLazyComponent(() => 
    import(`@/components/business-forms/${businessCode.value}.vue`)
  ))
})
```

### 预加载策略

```typescript
class ComponentPreloader {
  private preloadQueue: string[] = []
  
  // 预加载常用组件
  async preloadCommonComponents() {
    const commonComponents = [
      'crm_contract',
      'crm_contract_receivable', 
      'hr_leave',
      'hr_travel'
    ]
    
    for (const businessCode of commonComponents) {
      this.preloadQueue.push(businessCode)
    }
    
    // 批量预加载
    await this.processPreloadQueue()
  }
  
  private async processPreloadQueue() {
    const batchSize = 3
    
    while (this.preloadQueue.length > 0) {
      const batch = this.preloadQueue.splice(0, batchSize)
      
      await Promise.allSettled(
        batch.map(businessCode => 
          import(`@/components/business-forms/${businessCode}.vue`)
        )
      )
      
      // 避免阻塞主线程
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }
}
```

## 🔍 错误处理

### 组件加载错误处理

```typescript
class ComponentErrorHandler {
  handleComponentLoadError(businessCode: string, error: Error) {
    console.error(`组件加载失败: ${businessCode}`, error)
    
    // 错误上报
    this.reportError({
      type: 'component_load_error',
      businessCode,
      error: error.message,
      timestamp: Date.now()
    })
    
    // 返回兜底组件
    return this.getFallbackComponent(businessCode)
  }
  
  handleConfigLoadError(businessCode: string, error: Error) {
    console.error(`配置加载失败: ${businessCode}`, error)
    
    // 错误上报
    this.reportError({
      type: 'config_load_error',
      businessCode,
      error: error.message,
      timestamp: Date.now()
    })
    
    // 返回默认配置
    return this.getDefaultConfig(businessCode)
  }
  
  private getFallbackComponent(businessCode: string) {
    if (businessCode.includes('form')) {
      return () => import('@/components/business-forms/generic-form.vue')
    } else {
      return () => import('@/components/business-detail/generic-detail.vue')
    }
  }
  
  private reportError(errorInfo: any) {
    // 发送错误信息到监控系统
    if (window.errorReporter) {
      window.errorReporter.report(errorInfo)
    }
  }
}
```

### 错误边界组件

```vue
<!-- ErrorBoundary.vue -->
<template>
  <div class="error-boundary">
    <slot v-if="!hasError" />
    <div v-else class="error-fallback">
      <el-alert
        title="组件加载失败"
        type="error"
        :description="errorMessage"
        show-icon
      />
      <el-button @click="retry" type="primary" style="margin-top: 10px">
        重试
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  fallback?: Component
}

const props = defineProps<Props>()

const hasError = ref(false)
const errorMessage = ref('')

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  // 触发重新加载
  emit('retry')
}

// 错误捕获
onErrorCaptured((error: Error) => {
  hasError.value = true
  errorMessage.value = error.message
  
  console.error('组件错误:', error)
  return false
})
</script>
```

## 📊 监控指标

### 组件加载性能监控

```typescript
class ComponentPerformanceMonitor {
  private metrics = new Map<string, any>()
  
  startTiming(businessCode: string) {
    this.metrics.set(businessCode, {
      startTime: performance.now(),
      businessCode
    })
  }
  
  endTiming(businessCode: string, success: boolean = true) {
    const metric = this.metrics.get(businessCode)
    if (!metric) return
    
    const endTime = performance.now()
    const duration = endTime - metric.startTime
    
    // 记录性能指标
    this.recordMetric({
      businessCode,
      duration,
      success,
      timestamp: Date.now()
    })
    
    this.metrics.delete(businessCode)
  }
  
  private recordMetric(metric: any) {
    // 发送到监控系统
    if (window.performanceMonitor) {
      window.performanceMonitor.record('component_load', metric)
    }
    
    // 本地存储（用于调试）
    if (process.env.NODE_ENV === 'development') {
      console.log('组件加载性能:', metric)
    }
  }
}
```

## 🧪 测试策略

### 组件映射测试

```typescript
// component-mapper.test.ts
describe('ComponentMapper', () => {
  let mapper: FormComponentMapper
  
  beforeEach(() => {
    mapper = new FormComponentMapper()
  })
  
  it('应该正确映射Form-Create组件', async () => {
    const businessCode = 'fc_001'
    const mapping = await mapper.getFormComponent(businessCode)
    
    expect(mapping.component).toBeDefined()
    expect(mapping.props).toHaveProperty('formConfig')
    expect(mapping.props).toHaveProperty('formId', 1)
  })
  
  it('应该正确映射静态业务组件', async () => {
    const businessCode = 'crm_contract'
    const mapping = await mapper.getFormComponent(businessCode)
    
    expect(mapping.component).toBeDefined()
  })
  
  it('应该在组件不存在时返回兜底组件', async () => {
    const businessCode = 'non_existent_component'
    const mapping = await mapper.getFormComponent(businessCode)
    
    expect(mapping.component).toBeDefined()
    // 应该是通用组件
  })
})
```

---

**注意：** 组件映射机制是整个架构的核心，确保所有新增组件都遵循既定的命名约定和目录结构。