import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

/**
 * 客户详情操作相关接口
 */
export class CrmCustomerDetailApi {
  // ==================== 联系人操作 ====================

  /**
   * 新增联系人
   * @param customerId 客户ID
   * @param data 联系人数据
   */
  static addContact(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/add_contact`,
      data: { ...data, customer_id: customerId }
    })
  }

  /**
   * 编辑联系人
   * @param contactId 联系人ID
   * @param data 联系人数据
   */
  static editContact(contactId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/edit_contact`,
      data: { ...data, id: contactId }
    })
  }

  /**
   * 删除联系人
   * @param contactId 联系人ID
   */
  static deleteContact(contactId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/delete_contact`,
      data: { id: contactId }
    })
  }

  /**
   * 获取联系人列表
   * @param customerId 客户ID
   * @param params 查询参数
   */
  static getContactList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `crm/crm_customer_my/contact_list`,
      params: { customer_id: customerId, ...params }
    })
  }

  /**
   * 联系人详情
   * @param contactId 联系人ID
   */
  static getContactDetail(contactId: number) {
    return request.get<BaseResult>({
      url: `crm/crm_customer_my/contact_detail`,
      params: { id: contactId }
    })
  }

  // ==================== 合同操作 ====================

  /**
   * 新增合同
   * @param customerId 客户ID
   * @param data 合同数据
   */
  static addContract(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/add_contract`,
      data: { ...data, customer_id: customerId }
    })
  }

  /**
   * 编辑合同
   * @param contractId 合同ID
   * @param data 合同数据
   */
  static editContract(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/edit_contract`,
      data: { ...data, id: contractId }
    })
  }

  /**
   * 删除合同
   * @param contractId 合同ID
   */
  static deleteContract(contractId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/delete_contract`,
      data: { id: contractId }
    })
  }

  /**
   * 合同详情
   * @param contractId 合同ID
   */
  static getContractDetail(contractId: number) {
    return request.get<BaseResult>({
      url: `crm/crm_customer_my/contract_detail`,
      params: { id: contractId }
    })
  }

  /**
   * 合同列表
   * @param customerId 客户ID
   * @param params 查询参数
   */
  static getContractList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `crm/crm_customer_my/contract_list`,
      params: { customer_id: customerId, ...params }
    })
  }

  /**
   * 提交审批
   * @param contractId 合同ID
   * @param data 审批数据
   */
  static submitApproval(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/submit_approval`,
      data: { contract_id: contractId, ...data }
    })
  }

  /**
   * 提交合同审批
   * @param contractId 合同ID
   */
  static submitContractApproval(contractId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/submit_contract_approval`,
      data: { id: contractId }
    })
  }

  /**
   * 撤回合同审批
   * @param contractId 合同ID
   */
  static withdrawContractApproval(contractId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/withdraw_contract_approval`,
      data: { id: contractId }
    })
  }

  /**
   * 提交收款审批
   * @param receivableId 收款ID
   */
  static submitReceivableApproval(receivableId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/submit_receivable_approval`,
      data: { id: receivableId }
    })
  }

  /**
   * 撤回收款审批
   * @param receivableId 收款ID
   */
  static withdrawReceivableApproval(receivableId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/withdraw_receivable_approval`,
      data: { id: receivableId }
    })
  }

  // ==================== 回款操作 ====================

  /**
   * 新增回款
   * @param contractId 合同ID
   * @param data 回款数据
   */
  static addReceivable(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/add_receivable`,
      data: { ...data, contract_id: contractId }
    })
  }

  /**
   * 编辑回款
   * @param receivableId 回款ID
   * @param data 回款数据
   */
  static editReceivable(receivableId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/edit_receivable`,
      data: { ...data, id: receivableId }
    })
  }

  /**
   * 删除回款
   * @param receivableId 回款ID
   */
  static deleteReceivable(receivableId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/delete_receivable`,
      data: { id: receivableId }
    })
  }

  /**
   * 回款详情
   * @param receivableId 回款ID
   */
  static getReceivableDetail(receivableId: number) {
    return request.get<BaseResult>({
      url: `crm/crm_customer_my/receivable_detail`,
      params: { id: receivableId }
    })
  }

  /**
   * 回款列表
   * @param contractId 合同ID
   * @param params 查询参数
   */
  static getReceivableList(contractId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `crm/crm_customer_my/receivable_list`,
      params: { contract_id: contractId, ...params }
    })
  }

  /**
   * 新增更多回款
   * @param contractId 合同ID
   * @param data 回款数据
   */
  static addReceivableMore(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/add_receivable_more`,
      data: { ...data, contract_id: contractId }
    })
  }

  // ==================== 跟进记录操作 ====================

  /**
   * 新增跟进
   * @param customerId 客户ID
   * @param data 跟进数据
   */
  static addFollow(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/add_follow`,
      data: { ...data, customer_id: customerId }
    })
  }

  /**
   * 编辑跟进
   * @param followId 跟进ID
   * @param data 跟进数据
   */
  static editFollow(followId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/edit_follow`,
      data: { ...data, id: followId }
    })
  }

  /**
   * 删除跟进
   * @param followId 跟进ID
   */
  static deleteFollow(followId: number) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/delete_follow`,
      data: { id: followId }
    })
  }

  /**
   * 跟进详情
   * @param followId 跟进ID
   */
  static getFollowDetail(followId: number) {
    return request.get<BaseResult>({
      url: `crm/crm_customer_my/follow_detail`,
      params: { id: followId }
    })
  }

  /**
   * 跟进记录列表
   * @param customerId 客户ID
   * @param params 查询参数
   */
  static getFollowList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `crm/crm_customer_my/follow_list`,
      params: { customer_id: customerId, ...params }
    })
  }

  // ==================== 客户操作 ====================

  /**
   * 回收客户
   * @param customerId 客户ID
   * @param reason 回收原因
   */
  static recycleCustomer(customerId: number | string, reason?: string) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/recycle_customer/${customerId}`,
      data: { reason }
    })
  }

  // ==================== 预留接口（暂不实施） ====================

  /**
   * 转移客户（预留）
   */
  /*
  static transferCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/transfer_customer`,
      data: { customer_id: customerId, ...data }
    })
  }
  */

  /**
   * 共享客户（预留）
   */
  /*
  static shareCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `crm/crm_customer_my/share_customer`,
      data: { customer_id: customerId, ...data }
    })
  }
  */
}
