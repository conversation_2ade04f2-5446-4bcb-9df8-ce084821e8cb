import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 工作报告表相关接口
 */
export class CrmWorkReportApi {
  /**
   * 获取工作报告表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_work_report/index',
      params
    })
  }

  /**
   * 获取工作报告表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/crm/crm_work_report/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加工作报告表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_work_report/add',
      data
    })
  }

  /**
   * 更新工作报告表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/crm/crm_work_report/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除工作报告表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_work_report/delete/${id}`
    })
  }

  /**
   * 批量删除工作报告表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/crm/crm_work_report/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_work_report/updateField',
      data
    })
  }

  /**
   * 复制工作报告
   * @param id 记录ID
   */
  static copy(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_work_report/copy/${id}`
    })
  }


  /**
   * 导出工作报告表数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/crm/crm_work_report/export',
      params,
      responseType: 'blob'
    })
  }
  


  /**
   * 导入工作报告表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/crm/crm_work_report/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/crm/crm_work_report/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/crm/crm_work_report/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}