<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑合同' : '新增合同'"
    width="800px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div v-loading="loading" class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contract_name">
              <el-input v-model="formData.contract_name" placeholder="请输入合同名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contract_number">
              <el-input v-model="formData.contract_number" placeholder="请输入合同编号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签约人姓名" prop="contact_name">
              <el-input v-model="formData.contact_name" placeholder="请输入签约人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="签约人电话" prop="contact_mobile">
              <el-input v-model="formData.contact_mobile" placeholder="请输入签约人电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contract_amount">
              <el-input-number
                v-model="formData.contract_amount"
                :min="0"
                :precision="2"
                placeholder="请输入合同金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12">
                      <el-form-item label="合同类型" prop="type">
                        <el-select v-model="formData.type" placeholder="请选择合同类型">
                          <el-option label="软件开发合同" value="软件开发合同" />
                          <el-option label="技术服务合同" value="技术服务合同" />
                          <el-option label="产品采购合同" value="产品采购合同" />
                          <el-option label="咨询服务合同" value="咨询服务合同" />
                          <el-option label="维护服务合同" value="维护服务合同" />
                        </el-select>
                      </el-form-item>
                    </el-col>-->
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期" prop="start_date">
              <el-date-picker
                v-model="formData.start_date"
                type="date"
                placeholder="请选择开始日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                v-model="formData.end_date"
                type="date"
                placeholder="请选择结束日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签署日期" prop="sign_date">
              <el-date-picker
                v-model="formData.sign_date"
                type="date"
                placeholder="请选择签署日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款期限" prop="payment_deadline">
              <el-date-picker
                v-model="formData.payment_deadline"
                type="date"
                placeholder="请选择付款期限"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!--        <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="付款方式" prop="payment_method">
                      <el-select v-model="formData.payment_method" placeholder="请选择付款方式">
                        <el-option label="银行转账" value="银行转账" />
                        <el-option label="支票" value="支票" />
                        <el-option label="现金" value="现金" />
                        <el-option label="支付宝" value="支付宝" />
                        <el-option label="微信" value="微信" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="已付金额">
                      <div
                        style="
                          padding: 8px 12px;
                          background-color: #f5f7fa;
                          border-radius: 4px;
                          border: 1px solid #dcdfe6;
                        "
                      >
                        <span style="font-size: 14px; color: #67c23a; font-weight: 500">
                          ¥{{ (formData.paid_amount || 0).toFixed(2) }}
                        </span>
                        <div style="font-size: 12px; color: #909399; margin-top: 2px">
                          通过回款记录自动计算
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>-->

        <el-form-item label="付款条件" prop="payment_terms">
          <el-input
            v-model="formData.payment_terms"
            type="textarea"
            :rows="2"
            placeholder="请输入付款条件，如：签约付30%，交付付50%，验收付20%"
          />
        </el-form-item>

        <el-form-item label="合同描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入合同描述"
          />
        </el-form-item>

        <el-form-item label="合同附件" prop="contract_files">
          <FormUploader
            v-model="formData.contract_files"
            fileType="file"
            :limit="5"
            :multiple="true"
            returnValueMode="string"
            buttonText="选择合同附件"
            tipText="支持上传图片、Word、Excel文件，最多5个。可按住Ctrl键选择多个文件"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.doc,.docx,.xls,.xlsx"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          v-if="!isEdit || formData.approval_status === 0"
          type="success"
          plain
          :loading="loading"
          @click="handleSave"
        >
          保存草稿
        </el-button>
        <el-button
          v-if="!isEdit || canSubmit"
          type="primary"
          :loading="loading"
          @click="handleSubmitApproval"
        >
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  import { FormUploader } from '@/components/custom/FormUploader'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number
    contractId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: 0,
    contractId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const isEdit = computed(() => !!props.contractId)

  // 表单数据
  const formData = reactive({
    contract_name: '',
    contract_number: '',
    contact_name: '',
    contact_mobile: '',
    contract_amount: 0,
    // type: '',
    start_date: '',
    end_date: '',
    sign_date: '',
    payment_deadline: '',
    // payment_method: '',
    payment_terms: '',
    description: '',
    contract_files: '',
    // paid_amount: 0,
    approval_status: 0
  })

  // 表单验证规则
  const formRules: FormRules = {
    contract_name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    contract_number: [{ required: true, message: '请输入合同编号', trigger: 'blur' }],
    contact_name: [{ required: true, message: '请输入签约人姓名', trigger: 'blur' }],
    contact_mobile: [{ required: true, message: '请输入签约人电话', trigger: 'blur' }],
    contract_amount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
    // type: [{ required: true, message: '请选择合同类型', trigger: 'change' }],
    start_date: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
    end_date: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
    // payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }]
  }

  // 监听合同ID变化，加载合同详情
  watch(
    () => props.contractId,
    (newId) => {
      if (newId && props.modelValue) {
        loadContractDetail()
      }
    },
    { immediate: true }
  )

  // 监听对话框显示状态
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal && props.contractId) {
        loadContractDetail()
      } else if (newVal && !props.contractId) {
        // 新增模式，重置表单
        resetFormData()
      }
    }
  )

  // 加载合同详情
  const loadContractDetail = async () => {
    if (!props.contractId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getContractDetail(props.contractId)

      if (res.code === ApiStatus.success) {
        const data = res.data
        Object.assign(formData, {
          contract_name: data.contract_name || '',
          contract_number: data.contract_number || '',
          contact_name: data.contact_name || '',
          contact_mobile: data.contact_mobile || '',
          contract_amount: data.contract_amount || 0,
          // type: data.type || '',
          start_date: data.start_date || '',
          end_date: data.end_date || '',
          sign_date: data.sign_date || '',
          payment_deadline: data.payment_deadline || '',
          // payment_method: data.payment_method || '',
          payment_terms: data.payment_terms || '',
          description: data.description || '',
          contract_files: data.contract_files || '',
          // paid_amount: data.paid_amount || 0,
          approval_status: data.approval_status || 0
        })
      }
    } catch (error) {
      console.error('加载合同详情失败:', error)
      ElMessage.error('加载合同详情失败')
    } finally {
      loading.value = false
    }
  }

  // 重置表单数据
  const resetFormData = () => {
    Object.assign(formData, {
      contract_name: '',
      contract_number: '',
      contact_name: '',
      contact_mobile: '',
      contract_amount: 0,
      // type: '',
      start_date: '',
      end_date: '',
      sign_date: '',
      payment_deadline: '',
      // payment_method: '',
      payment_terms: '',
      description: '',
      contract_files: '',
      // paid_amount: 0,
      approval_status: 0
    })
  }

  // 计算是否可以提交
  const canSubmit = computed(() => {
    // 新增时总是可以提交，编辑时需要检查状态
    if (!isEdit.value) return true
    // 编辑时只有草稿状态(0)才能提交审批
    return formData.approval_status === 0
  })

  // 保存表单（不提交审批）
  const handleSave = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editContract(props.contractId!, formData)
      } else {
        res = await CrmCustomerDetailApi.addContract(props.customerId!, formData)
      }

      if (res.code === ApiStatus.success) {
        ElMessage.success('保存成功')
        visible.value = false
        emit('success')
      }
    } catch (error) {
      console.error('保存表单失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 提交审批
  const handleSubmitApproval = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 先保存表单
      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editContract(props.contractId!, formData)
      } else {
        res = await CrmCustomerDetailApi.addContract(props.customerId!, formData)
      }

      if (res.code === ApiStatus.success) {
        // 保存成功后提交审批
        const contractId = isEdit.value ? props.contractId! : res.data
        const submitRes = await CrmCustomerDetailApi.submitContractApproval(contractId)

        if (submitRes.code === ApiStatus.success) {
          ElMessage.success('提交审批成功')
          visible.value = false
          emit('success')
        }
      }
    } catch (error) {
      console.error('提交审批失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭处理
  const handleClosed = () => {
    formRef.value?.resetFields()
    resetFormData()
  }
</script>

<style scoped lang="scss">
  .form-container {
    min-height: 200px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
