# CRM工作报告模块设计方案

## 📋 项目概述

### 项目背景
基于现有CRM系统，新增工作报告模块，为中小企业提供简洁实用的日报、周报、月报功能。设计理念参考飞书汇报功能，但保持功能简洁，专注核心汇报需求。

### 设计原则
- **功能简洁**: 专注核心汇报功能，避免过度复杂
- **界面现代**: 借鉴飞书视觉设计，提供现代化用户体验
- **操作便捷**: 适合中小企业快速上手使用
- **数据完整**: 满足基本的汇报数据存储和展示需求

### 技术架构
- **后端**: ThinkPHP 8 + MySQL
- **前端**: Vue 3 + TypeScript + Element Plus
- **组件**: 复用现有CRM组件体系（ArtTable、FormDialog等）
- **开发工具**: 使用现有CRUD生成器快速生成基础代码

### 开发策略
- **快速开发**: 使用CRUD生成器生成80%的基础代码
- **定制优化**: 在生成代码基础上进行UI和功能定制
- **设计升级**: 参考飞书设计风格进行界面优化

## 🗂️ 数据库设计

### 数据表结构
使用现有的 `crm_work_report` 表，需要优化字段注释以支持CRUD生成器：

```sql
CREATE TABLE IF NOT EXISTS `crm_work_report`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    `tenant_id`   bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `title`       varchar(200)        NOT NULL DEFAULT '' COMMENT '报告标题 @required @max:200 @search:like @exp @imp',
    `type`        varchar(20)         NOT NULL DEFAULT '' COMMENT '报告类型:daily=日报,weekly=周报,monthly=月报 @required @search:eq @exp @imp @component:tag',
    `report_date` date                         DEFAULT NULL COMMENT '报告日期 @required @search:date @exp @imp',
    `content`     text COMMENT '报告内容 @required @form:textarea @exp @imp',
    `summary`     text COMMENT '工作总结 @form:textarea @exp @imp',
    `plan`        text COMMENT '下期计划 @form:textarea @exp @imp',
    `attachments` text COMMENT '附件(JSON格式) @form:upload @component:file',
    `creator_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`  bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_creator` (`tenant_id`, `creator_id`),
    KEY `idx_tenant_type` (`tenant_id`, `type`),
    KEY `idx_report_date` (`report_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='工作报告表 @module:crm @exp:true @imp:true';
```

### CRUD生成器标记说明
- **@required**: 必填字段验证
- **@max:200**: 最大长度验证
- **@search:like**: 模糊搜索
- **@search:eq**: 精确搜索
- **@search:date**: 日期范围搜索
- **@form:textarea**: 表单使用文本域
- **@form:upload**: 表单使用上传组件
- **@component:tag**: 列表使用标签组件
- **@component:file**: 列表使用文件组件
- **@exp**: 包含在导出中
- **@imp**: 包含在导入中

### 表注释配置
- **@module:crm**: 指定CRM模块
- **@exp:true**: 启用导出功能
- **@imp:true**: 启用导入功能

### 字段说明
- **基础字段**: id、tenant_id、creator_id、时间戳字段
- **核心字段**: title（标题）、type（类型）、report_date（日期）
- **内容字段**: content（内容）、summary（总结）、plan（计划）
- **扩展字段**: attachments（附件JSON）

## 🎨 UI设计方案

### 整体设计风格
参考飞书设计语言，采用：
- **卡片式布局**: 信息层次清晰，视觉舒适
- **现代化配色**: 简洁的色彩系统
- **合理间距**: 充足的留白和组件间距
- **一致性**: 与现有CRM系统保持设计一致

### 色彩系统
```css
/* 主色调 */
--primary-color: #1f2329;      /* 深灰 */
--primary-blue: #3370ff;       /* 主蓝 */
--success-green: #00bc70;      /* 成功绿 */
--warning-orange: #ff8800;     /* 警告橙 */

/* 汇报类型色彩 */
--daily-color: #3370ff;        /* 日报-蓝色 */
--weekly-color: #00bc70;       /* 周报-绿色 */
--monthly-color: #ff8800;      /* 月报-橙色 */

/* 背景色 */
--bg-primary: #ffffff;         /* 主背景 */
--bg-secondary: #f7f8fa;       /* 次背景 */
--bg-card: #ffffff;            /* 卡片背景 */
```

### 字体规范
```css
/* 字体大小 */
--font-size-title: 18px;       /* 标题 */
--font-size-subtitle: 16px;    /* 副标题 */
--font-size-body: 14px;        /* 正文 */
--font-size-caption: 12px;     /* 说明文字 */

/* 字体权重 */
--font-weight-bold: 600;       /* 粗体 */
--font-weight-medium: 500;     /* 中等 */
--font-weight-normal: 400;     /* 常规 */
```

## 📱 页面设计详情

### 1. 列表页面设计

#### 1.1 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 📝 工作汇报                                    🔍 搜索框      │
├─────────────────────────────────────────────────────────────┤
│ 🔍 筛选栏                                                    │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│ │ 📅 本周 ▼   │ 📋 全部类型▼ │ 👤 全部人员▼ │ 🔄 刷新     │   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ ➕ 写汇报                                                   │
├─────────────────────────────────────────────────────────────┤
│ 📋 汇报列表（卡片式设计）                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📅 2025-01-15  🏷️ 日报                    👤 张三      │ │
│ │ 📝 客户拜访工作总结                                     │ │
│ │ 💬 今日完成3个客户拜访，新增2个潜在客户...              │ │
│ │ ┌─────────────┬─────────────┬─────────────────────────┐ │ │
│ │ │ 📎 2个附件   │ ⏰ 09:30    │ 👁️ 查看详情 ✏️ 编辑    │ │ │
│ │ └─────────────┴─────────────┴─────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 1.2 功能特性
- **搜索功能**: 支持标题关键词搜索
- **筛选功能**: 按时间范围、汇报类型、创建人筛选
- **卡片展示**: 每个汇报用卡片形式展示关键信息
- **快速操作**: 查看详情、编辑、删除等操作
- **分页展示**: 支持分页加载

#### 1.3 卡片信息层级
1. **头部信息**: 日期、类型标签、创建人
2. **标题**: 汇报标题，突出显示
3. **内容预览**: 显示部分内容，超出省略
4. **底部信息**: 附件数量、创建时间、操作按钮

### 2. 表单设计

#### 2.1 表单布局
```
┌─────────────────────────────────────────────────────────────┐
│ ✏️ 写工作汇报                                    ❌ 关闭     │
├─────────────────────────────────────────────────────────────┤
│ 📋 基本信息                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 汇报标题 *                                              │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 请输入汇报标题，如：2025年1月15日销售工作日报        │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────┬─────────────────────────────────────┐ │ │
│ │ │ 汇报类型 *   │ 汇报日期 *                           │ │ │
│ │ │ 📋 日报 ▼   │ 📅 2025-01-15                      │ │ │
│ │ └─────────────┴─────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📝 汇报内容                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 工作内容 *                                              │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 📝 富文本编辑器                                     │ │ │
│ │ │ • 支持基础格式化（粗体、斜体、列表）                │ │ │
│ │ │ • 支持插入图片                                      │ │ │
│ │ │ • 简洁的工具栏                                      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 工作总结                                                │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 📊 总结本期工作成果和遇到的问题                     │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 下期计划                                                │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 📅 下期工作计划和目标                               │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📎 附件上传                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 📁 点击上传或拖拽文件到此处                         │ │ │
│ │ │ 支持 Word、Excel、PDF、图片等格式                   │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ 📄 已上传文件：销售数据.xlsx  🗑️                       │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                                    💾 保存草稿  📤 提交汇报 │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2 表单特性
- **分区设计**: 基本信息、汇报内容、附件上传分区明确
- **必填标识**: 用红色*标识必填字段
- **智能默认**: 日期默认当天，类型可智能推荐
- **富文本编辑**: 支持基础格式化，不过度复杂
- **文件上传**: 支持拖拽上传，多种文件格式
- **草稿保存**: 支持保存草稿功能

### 3. 详情页面设计

#### 3.1 详情页布局
```
┌─────────────────────────────────────────────────────────────┐
│ 📄 汇报详情                                      ❌ 关闭     │
├─────────────────────────────────────────────────────────────┤
│ 📋 汇报信息                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📝 客户拜访工作总结                                     │ │
│ │ ┌─────────────┬─────────────┬─────────────┬─────────────┐ │ │
│ │ │ 🏷️ 日报     │ 📅 2025-01-15│ 👤 张三     │ ⏰ 09:30   │ │ │
│ │ └─────────────┴─────────────┴─────────────┴─────────────┘ │ │
│ │ ┌─────────────┬─────────────┬─────────────────────────┐ │ │
│ │ │ ✏️ 编辑     │ 📋 复制     │ 🗑️ 删除                │ │ │
│ │ └─────────────┴─────────────┴─────────────────────────┘ │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📝 工作内容                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 富文本内容展示区域                                      │ │
│ │ 保持原有格式和样式                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📊 工作总结                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 总结内容展示                                            │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📅 下期计划                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 计划内容展示                                            │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 📎 附件列表                                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📄 文档1.pdf                                📥 下载    │ │
│ │ 📊 数据.xlsx                                📥 下载    │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 3.2 详情页特性
- **信息完整**: 展示所有汇报信息
- **格式保持**: 富文本内容保持原有格式
- **操作便捷**: 编辑、复制、删除等操作
- **附件管理**: 附件列表和下载功能

## 🔧 交互设计

### 微交互效果
- **悬停效果**: 卡片悬停时轻微阴影变化
- **点击反馈**: 按钮点击时的缩放效果
- **加载状态**: 骨架屏加载效果
- **过渡动画**: 页面切换的平滑过渡

### 操作便捷性
- **快捷键**: Ctrl+S保存，Esc关闭
- **自动保存**: 表单内容实时保存草稿
- **智能提示**: 输入时的智能建议
- **一键操作**: 常用操作一键完成

## 📊 功能清单

### CRUD生成器自动生成功能
- ✅ 基础CRUD操作（增删改查）
- ✅ 搜索和筛选功能
- ✅ 分页展示
- ✅ 导入导出功能
- ✅ 表单验证
- ✅ 权限控制基础框架
- ✅ API接口封装
- ✅ TypeScript类型定义

### 定制开发功能
- ✅ 飞书风格UI设计
- ✅ 卡片式列表布局
- ✅ 富文本编辑器
- ✅ 附件上传下载
- ✅ 汇报复制功能
- ✅ 详情页面展示
- ✅ 草稿保存功能

## 🎯 用户体验目标

### 易用性
- 界面简洁直观，新用户5分钟内上手
- 操作流程简化，减少不必要的步骤
- 提供智能默认值和提示信息

### 效率性
- 快速创建和编辑汇报
- 支持模板和复制功能
- 提供搜索和筛选快速定位

### 一致性
- 与现有CRM系统保持设计一致
- 遵循统一的交互规范
- 保持视觉风格的统一性

## 📈 成功指标

### 功能指标
- 汇报创建成功率 > 95%
- 页面加载时间 < 2秒
- 文件上传成功率 > 98%

### 用户体验指标
- 用户满意度 > 4.5/5
- 新用户上手时间 < 5分钟
- 日活跃用户使用率 > 80%

## 🚀 开发优势

### CRUD生成器带来的优势
- **开发效率**: 自动生成80%的基础代码，大幅缩短开发周期
- **代码质量**: 生成器代码经过充分测试，质量有保障
- **功能完整**: 自动包含搜索、分页、导入导出等常用功能
- **规范统一**: 自动遵循项目代码规范和架构设计
- **维护便利**: 基于标准化代码结构，便于后续维护

### 定制开发重点
- **UI体验**: 重点实现飞书风格的现代化界面
- **功能增强**: 添加富文本编辑、附件管理等高级功能
- **交互优化**: 优化用户操作流程和体验细节

## 🔄 后续优化方向

### 短期优化（基于生成器代码）
- 增加汇报模板功能
- 优化富文本编辑器性能
- 完善附件管理功能

### 中期优化
- 数据统计和分析面板
- 与其他CRM模块深度集成
- 工作流审批集成

### 长期规划
- 移动端专用应用开发
- AI辅助汇报生成
- 智能数据分析
