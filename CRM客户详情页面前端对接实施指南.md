# CRM客户详情页面前端对接实施指南

## 📋 概述

本文档提供CRM客户详情页面权限系统前端对接的详细实施指南，包括具体的代码修改步骤、权限控制实现和API对接方案。

## 🎯 实施目标

1. 为客户详情页面的22个操作添加权限控制（21个核心功能 + 1个回收客户）
2. 替换模拟数据为真实API调用
3. 实现统一的错误处理和用户反馈
4. 优化用户体验和性能
5. 为转移客户和共享客户功能预留接口（暂不实施）

## 📁 第一步：创建新文件

### 1.1 创建客户详情API文件

**文件路径**：`frontend/src/api/crm/crmCustomerDetail.ts`

```typescript
import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

/**
 * 客户详情操作相关接口
 */
export class CrmCustomerDetailApi {
  // ==================== 联系人操作 ====================
  
  /**
   * 新增联系人
   */
  static addContact(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_contact/${customerId}`,
      data
    })
  }
  
  /**
   * 编辑联系人
   */
  static editContact(contactId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_contact/${contactId}`,
      data
    })
  }
  
  /**
   * 删除联系人
   */
  static deleteContact(contactId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_contact/${contactId}`
    })
  }
  
  /**
   * 获取联系人列表
   */
  static getContactList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/contact_list/${customerId}`,
      params
    })
  }
  
  // ==================== 合同操作 ====================
  
  /**
   * 新增合同
   */
  static addContract(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_contract/${customerId}`,
      data
    })
  }
  
  /**
   * 编辑合同
   */
  static editContract(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_contract/${contractId}`,
      data
    })
  }
  
  /**
   * 删除合同
   */
  static deleteContract(contractId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_contract/${contractId}`
    })
  }
  
  /**
   * 合同详情
   */
  static getContractDetail(contractId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/contract_detail/${contractId}`
    })
  }
  
  /**
   * 合同列表
   */
  static getContractList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/contract_list/${customerId}`,
      params
    })
  }
  
  /**
   * 提交审批
   */
  static submitApproval(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/submit_approval/${contractId}`,
      data
    })
  }
  
  // ==================== 回款操作 ====================
  
  /**
   * 新增回款
   */
  static addReceivable(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_receivable/${contractId}`,
      data
    })
  }
  
  /**
   * 编辑回款
   */
  static editReceivable(receivableId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_receivable/${receivableId}`,
      data
    })
  }
  
  /**
   * 删除回款
   */
  static deleteReceivable(receivableId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_receivable/${receivableId}`
    })
  }
  
  /**
   * 回款详情
   */
  static getReceivableDetail(receivableId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/receivable_detail/${receivableId}`
    })
  }
  
  /**
   * 回款列表
   */
  static getReceivableList(contractId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/receivable_list/${contractId}`,
      params
    })
  }
  
  /**
   * 提交回款审批
   */
  static submitReceivableApproval(receivableId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/submit_receivable_approval/${receivableId}`,
      data
    })
  }
  
  // ==================== 跟进记录操作 ====================
  
  /**
   * 新增跟进
   */
  static addFollow(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_follow/${customerId}`,
      data
    })
  }
  
  /**
   * 编辑跟进
   */
  static editFollow(followId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_follow/${followId}`,
      data
    })
  }
  
  /**
   * 删除跟进
   */
  static deleteFollow(followId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_follow/${followId}`
    })
  }
  
  /**
   * 跟进详情
   */
  static getFollowDetail(followId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/follow_detail/${followId}`
    })
  }
  
  // ==================== 客户操作 ====================

  /**
   * 回收客户
   */
  static recycleCustomer(customerId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/recycle_customer/${customerId}`
    })
  }

  // ==================== 预留接口（暂不实施） ====================

  /**
   * 转移客户（预留）
   */
  /*
  static transferCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/transfer_customer/${customerId}`,
      data
    })
  }
  */

  /**
   * 共享客户（预留）
   */
  /*
  static shareCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/share_customer/${customerId}`,
      data
    })
  }
  */
}
```

### 1.2 创建客户权限验证组合式函数

**文件路径**：`frontend/src/composables/useCustomerPermission.ts`

```typescript
import { useAuth } from '@/composables/useAuth'

/**
 * 客户权限验证组合式函数
 */
export const useCustomerPermission = () => {
  const { hasAuth } = useAuth()
  
  /**
   * 验证客户数据访问权限
   * @param customerId 客户ID
   * @param operation 操作类型 (view/edit/delete/transfer/share/recycle)
   */
  const hasCustomerAccess = (customerId: number, operation: string): boolean => {
    // TODO: 实现客户数据权限验证逻辑
    // 这里需要根据实际的权限验证逻辑来实现
    // 可能需要调用API验证当前用户对指定客户的操作权限
    
    // 临时实现：假设当前用户有权限
    return true
  }
  
  /**
   * 组合权限验证：功能权限 + 数据权限
   * @param permission 功能权限标识
   * @param customerId 客户ID（可选）
   * @param operation 操作类型（可选）
   */
  const hasPermissionAndAccess = (
    permission: string, 
    customerId?: number, 
    operation?: string
  ): boolean => {
    // 先验证功能权限
    if (!hasAuth(permission)) {
      return false
    }
    
    // 如果需要验证数据权限
    if (customerId && operation) {
      return hasCustomerAccess(customerId, operation)
    }
    
    return true
  }
  
  /**
   * 验证记录编辑权限
   * @param record 记录对象
   * @param userId 当前用户ID
   */
  const canEditRecord = (record: any, userId: number): boolean => {
    // 记录创建人可以编辑
    if (record.creator_id === userId) {
      return true
    }
    
    // 客户负责人可以编辑
    if (record.customer && record.customer.owner_user_id === userId) {
      return true
    }
    
    return false
  }
  
  /**
   * 验证记录删除权限
   * @param record 记录对象
   * @param userId 当前用户ID
   */
  const canDeleteRecord = (record: any, userId: number): boolean => {
    // 通常删除权限比编辑权限更严格
    return canEditRecord(record, userId)
  }
  
  return {
    hasCustomerAccess,
    hasPermissionAndAccess,
    canEditRecord,
    canDeleteRecord
  }
}
```

## 📝 第二步：修改现有组件

### 2.1 修改CustomerDetailDrawer主组件

**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/index.vue`

#### 2.1.1 添加导入和权限验证

```vue
<script setup lang="ts">
  import { ref, computed, markRaw } from 'vue'
  import { Phone, Message, Link } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import CrmDetailDrawer from '../CrmDetailDrawer/index.vue'
  import CustomerInfoPanel from './panels/CustomerInfoPanel.vue'
  import CustomerContactPanel from './panels/CustomerContactPanel.vue'
  import CustomerContractPanel from './panels/CustomerContractPanel.vue'
  import CustomerFollowPanel from './panels/CustomerFollowPanel.vue'

  // 新增导入
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'
  import { ApiStatus } from '@/enums/apiEnum'

  // 权限验证
  const { hasPermissionAndAccess } = useCustomerPermission()
</script>
```

#### 2.1.2 修改头部操作按钮

```vue
<template>
  <!-- 在customer-actions部分添加权限控制 -->
  <div class="customer-actions">
    <!-- 当前实施的功能 -->
    <el-button
      v-if="hasPermissionAndAccess('crm:crm_customer_my:recycle_customer', customerId, 'recycle')"
      @click="handleMoveToSea">
      回收
    </el-button>

    <!-- 预留功能（暂不显示） -->
    <!--
    <el-button
      v-if="hasPermissionAndAccess('crm:crm_customer_my:transfer_customer', customerId, 'transfer')"
      @click="handleTransfer">
      转移客户
    </el-button>
    <el-button
      v-if="hasPermissionAndAccess('crm:crm_customer_my:share_customer', customerId, 'share')"
      @click="handleShare">
      共享客户
    </el-button>
    -->
  </div>
</template>
```

#### 2.1.3 扩展事件处理方法

```typescript
// 扩展handleAction方法
const handleAction = async (action: string, data?: any) => {
  // 权限验证
  const permission = getPermissionByAction(action)
  const operation = getOperationByAction(action)

  if (!hasPermissionAndAccess(permission, props.customerId, operation)) {
    ElMessage.error('无权限执行此操作')
    return
  }

  try {
    switch (action) {
      // 联系人操作
      case 'add-contact':
        await handleAddContact(data)
        break
      case 'edit-contact':
        await handleEditContact(data)
        break
      case 'delete-contact':
        await handleDeleteContact(data)
        break

      // 合同操作
      case 'add-contract':
        await handleAddContract(data)
        break
      case 'edit-contract':
        await handleEditContract(data)
        break
      case 'delete-contract':
        await handleDeleteContract(data)
        break
      case 'view-contract':
        await handleViewContract(data)
        break

      // 回款操作
      case 'add-receivable':
        await handleAddReceivable(data)
        break
      case 'edit-receivable':
        await handleEditReceivable(data)
        break
      case 'delete-receivable':
        await handleDeleteReceivable(data)
        break
      case 'view-receivables':
        await handleViewReceivables(data)
        break

      // 跟进操作
      case 'add-follow':
        await handleAddFollow(data)
        break
      case 'edit-follow':
        await handleEditFollow(data)
        break
      case 'delete-follow':
        await handleDeleteFollow(data)
        break

      // 客户操作
      case 'moveToSea':
        await handleRecycleCustomer(data)
        break

      // 预留操作（暂不实施）
      /*
      case 'transfer':
        await handleTransferCustomer(data)
        break
      case 'share':
        await handleShareCustomer(data)
        break
      */

      default:
        console.log('未知操作:', action, data)
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  }
}

// 权限映射函数
const getPermissionByAction = (action: string): string => {
  const permissionMap: Record<string, string> = {
    // 联系人操作
    'add-contact': 'crm:crm_customer_my:add_contact',
    'edit-contact': 'crm:crm_customer_my:edit_contact',
    'delete-contact': 'crm:crm_customer_my:delete_contact',
    // 合同操作
    'add-contract': 'crm:crm_customer_my:add_contract',
    'edit-contract': 'crm:crm_customer_my:edit_contract',
    'delete-contract': 'crm:crm_customer_my:delete_contract',
    'view-contract': 'crm:crm_customer_my:contract_detail',
    // 回款操作
    'add-receivable': 'crm:crm_customer_my:add_receivable',
    'edit-receivable': 'crm:crm_customer_my:edit_receivable',
    'delete-receivable': 'crm:crm_customer_my:delete_receivable',
    'view-receivables': 'crm:crm_customer_my:receivable_list',
    // 跟进操作
    'add-follow': 'crm:crm_customer_my:add_follow',
    'edit-follow': 'crm:crm_customer_my:edit_follow',
    'delete-follow': 'crm:crm_customer_my:delete_follow',
    // 客户操作（当前实施）
    'moveToSea': 'crm:crm_customer_my:recycle_customer',
    // 预留操作（暂不实施）
    // 'transfer': 'crm:crm_customer_my:transfer_customer',
    // 'share': 'crm:crm_customer_my:share_customer'
  }
  return permissionMap[action] || ''
}

// 操作类型映射函数
const getOperationByAction = (action: string): string => {
  const operationMap: Record<string, string> = {
    'add-contact': 'edit',
    'edit-contact': 'edit',
    'delete-contact': 'edit',
    'add-contract': 'edit',
    'edit-contract': 'edit',
    'delete-contract': 'edit',
    'view-contract': 'view',
    'add-receivable': 'edit',
    'edit-receivable': 'edit',
    'delete-receivable': 'edit',
    'view-receivables': 'view',
    'add-follow': 'view',
    'edit-follow': 'edit',
    'delete-follow': 'edit',
    'transfer': 'transfer',
    'share': 'share',
    'moveToSea': 'recycle'
  }
  return operationMap[action] || 'view'
}
```

### 2.2 修改CustomerContactPanel联系人面板

**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerContactPanel.vue`

#### 2.2.1 添加权限控制和API调用

```vue
<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, Edit, Delete } from '@element-plus/icons-vue'

  // 新增导入
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'
  import { ApiStatus } from '@/enums/apiEnum'

  // 权限验证
  const { hasPermissionAndAccess } = useCustomerPermission()

  // 组件属性保持不变
  interface Props {
    businessData?: any
    businessType?: string
    businessId?: number | string
  }

  const props = defineProps<Props>()

  // 事件定义保持不变
  const emit = defineEmits<{
    refresh: []
    action: [action: string, data?: any]
  }>()

  // 响应式数据
  const loading = ref(false)
  const contacts = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 生命周期
  onMounted(() => {
    loadContacts()
  })

  // 加载联系人列表 - 替换模拟数据
  const loadContacts = async () => {
    if (!props.businessId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getContactList(props.businessId, {
        page: currentPage.value,
        limit: pageSize.value
      })

      if (res.code === ApiStatus.success) {
        contacts.value = res.list || []
        total.value = res.total || 0
      } else {
        ElMessage.error(res.message || '加载联系人失败')
      }
    } catch (error) {
      console.error('加载联系人失败:', error)
      ElMessage.error('加载联系人失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理方法
  const handleAddContact = () => {
    emit('action', 'add-contact', { customerId: props.businessId })
  }

  const handleEditContact = (contact: any) => {
    emit('action', 'edit-contact', contact)
  }

  const handleDeleteContact = async (contact: any) => {
    try {
      await ElMessageBox.confirm(`确定要删除联系人"${contact.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await CrmCustomerDetailApi.deleteContact(contact.id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        loadContacts() // 刷新列表
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除联系人失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }
</script>
```

#### 2.2.2 修改模板添加权限控制

```vue
<template>
  <div class="customer-contact-panel">
    <!-- 头部操作 -->
    <div class="panel-header">
      <div class="header-left">
        <h3>联系人</h3>
        <span class="count">({{ total }})</span>
      </div>
      <div class="header-right">
        <el-button
          v-if="hasPermissionAndAccess('crm:crm_customer_my:add_contact', businessId, 'edit')"
          type="primary"
          :icon="Plus"
          @click="handleAddContact">
          新增联系人
        </el-button>
      </div>
    </div>

    <!-- 联系人列表 -->
    <div class="contact-list" v-loading="loading">
      <div v-if="contacts.length === 0" class="empty-state">
        <el-empty description="暂无联系人" />
      </div>

      <div v-else class="contact-cards">
        <div v-for="contact in contacts" :key="contact.id" class="contact-card">
          <!-- 联系人信息展示 -->
          <div class="contact-info">
            <div class="contact-header">
              <h4>{{ contact.name }}</h4>
              <el-tag v-if="contact.is_primary" type="primary" size="small">主要联系人</el-tag>
            </div>
            <div class="contact-details">
              <p v-if="contact.position">职位：{{ contact.position }}</p>
              <p v-if="contact.department">部门：{{ contact.department }}</p>
              <p v-if="contact.mobile">手机：{{ contact.mobile }}</p>
              <p v-if="contact.email">邮箱：{{ contact.email }}</p>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="contact-actions">
            <el-button
              v-if="hasPermissionAndAccess('crm:crm_customer_my:edit_contact', businessId, 'edit')"
              type="primary"
              :icon="Edit"
              size="small"
              @click="handleEditContact(contact)">
              编辑
            </el-button>
            <el-button
              v-if="hasPermissionAndAccess('crm:crm_customer_my:delete_contact', businessId, 'edit')"
              type="danger"
              :icon="Delete"
              size="small"
              @click="handleDeleteContact(contact)">
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > pageSize" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>
```

## 📋 第三步：实施检查清单

### 3.1 文件创建检查
- [ ] 创建 `crmCustomerDetail.ts` API文件
- [ ] 创建 `useCustomerPermission.ts` 组合式函数
- [ ] 验证文件导入路径正确

### 3.2 权限控制检查
- [ ] 所有操作按钮添加权限指令
- [ ] 权限验证函数正确调用
- [ ] 权限映射关系正确配置

### 3.3 API对接检查
- [ ] 替换所有模拟数据为真实API调用
- [ ] API调用错误处理完善
- [ ] 加载状态正确显示

### 3.4 用户体验检查
- [ ] 操作成功/失败提示完善
- [ ] 确认对话框正确显示
- [ ] 数据刷新机制正常工作

## 🔧 第四步：测试验证

### 4.1 权限测试
1. 使用不同角色用户登录
2. 验证按钮显示/隐藏是否正确
3. 验证API调用权限拦截是否正常

### 4.2 功能测试
1. 测试所有CRUD操作
2. 验证数据加载和刷新
3. 测试错误处理机制

### 4.3 性能测试
1. 检查页面加载速度
2. 验证权限验证性能
3. 测试大数据量下的表现

## ⚠️ 注意事项

1. **向后兼容**：确保修改不影响现有功能
2. **错误处理**：所有API调用都要有错误处理
3. **用户反馈**：操作结果要有明确的用户提示
4. **权限验证**：前端权限控制不能替代后端验证
5. **性能优化**：避免不必要的API调用和权限验证

## 📚 相关文档

- [CRM客户详情页面权限系统设计说明文档](./CRM客户详情页面权限系统设计说明文档.md)
- [CRM客户详情页面权限系统开发计划任务文档](./CRM客户详情页面权限系统开发计划任务文档.md)
- [权限配置SQL文件](./crm_customer_detail_permissions.sql)

---

**文档版本**：v1.0
**创建时间**：2025-01-14
**维护人员**：前端开发团队
