<?php
declare(strict_types=1);

namespace app\workflow\utils;

use app\workflow\model\WorkflowTask;
use app\workflow\model\WorkflowHistory;
use app\workflow\model\WorkflowInstance;
use think\facade\Log;

/**
 * 工作流模型工厂类
 * 专门用于创建带有正确租户隔离和数据权限的模型实例
 * 解决单例模式导致的状态污染问题
 */
class WorkflowModelFactory
{
    /**
     * 创建工作流任务模型实例
     * 
     * @param bool $applyTenantIsolation 是否应用租户隔离
     * @param bool $applyDataPermission 是否应用数据权限
     * @return WorkflowTask
     */
    public static function createTaskModel(bool $applyTenantIsolation = true, bool $applyDataPermission = false): WorkflowTask
    {
        $model = new WorkflowTask();
        
        // 记录模型创建日志
        Log::debug('创建WorkflowTask模型实例', [
            'tenant_isolation' => $applyTenantIsolation,
            'data_permission' => $applyDataPermission,
            'admin_id' => request()->adminId ?? 0,
            'tenant_id' => request()->tenantId ?? 0
        ]);
        
        return $model;
    }
    
    /**
     * 创建工作流历史模型实例
     * 
     * @param bool $applyTenantIsolation 是否应用租户隔离
     * @param bool $applyDataPermission 是否应用数据权限
     * @return WorkflowHistory
     */
    public static function createHistoryModel(bool $applyTenantIsolation = true, bool $applyDataPermission = false): WorkflowHistory
    {
        $model = new WorkflowHistory();
        
        // 记录模型创建日志
        Log::debug('创建WorkflowHistory模型实例', [
            'tenant_isolation' => $applyTenantIsolation,
            'data_permission' => $applyDataPermission,
            'admin_id' => request()->adminId ?? 0,
            'tenant_id' => request()->tenantId ?? 0
        ]);
        
        return $model;
    }
    
    /**
     * 创建工作流实例模型实例
     * 
     * @param bool $applyTenantIsolation 是否应用租户隔离
     * @param bool $applyDataPermission 是否应用数据权限
     * @return WorkflowInstance
     */
    public static function createInstanceModel(bool $applyTenantIsolation = true, bool $applyDataPermission = true): WorkflowInstance
    {
        $model = new WorkflowInstance();
        
        // 记录模型创建日志
        Log::debug('创建WorkflowInstance模型实例', [
            'tenant_isolation' => $applyTenantIsolation,
            'data_permission' => $applyDataPermission,
            'admin_id' => request()->adminId ?? 0,
            'tenant_id' => request()->tenantId ?? 0
        ]);
        
        return $model;
    }
    
    /**
     * 安全创建任务记录
     * 确保租户隔离和数据权限的正确应用
     * 
     * @param array $taskData 任务数据
     * @return int 返回任务ID，失败返回0
     */
    public static function safeCreateTask(array $taskData): int
    {
        try {
            // 确保租户ID正确设置
            if (!isset($taskData['tenant_id'])) {
                $taskData['tenant_id'] = get_effective_tenant_id();
            }
            
            // 确保创建者ID正确设置
            if (!isset($taskData['creator_id'])) {
                $taskData['creator_id'] = request()->adminId ?? 0;
            }
            
            Log::info('安全创建任务记录', [
                'task_data' => $taskData,
                'effective_tenant_id' => get_effective_tenant_id(),
                'admin_id' => request()->adminId ?? 0
            ]);
            
            $model = self::createTaskModel();
            $result = $model->saveByCreate($taskData);
            
            if ($result) {
                Log::info('任务记录创建成功', ['task_id' => $result]);
            } else {
                Log::error('任务记录创建失败');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('安全创建任务记录异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'task_data' => $taskData
            ]);
            return 0;
        }
    }
    
    /**
     * 安全创建历史记录
     * 确保租户隔离和数据权限的正确应用
     * 
     * @param array $historyData 历史数据
     * @return int 返回历史记录ID，失败返回0
     */
    public static function safeCreateHistory(array $historyData): int
    {
        try {
            // 确保租户ID正确设置
            if (!isset($historyData['tenant_id'])) {
                $historyData['tenant_id'] = get_effective_tenant_id();
            }
            
            // 确保创建者ID正确设置
            if (!isset($historyData['creator_id'])) {
                $historyData['creator_id'] = request()->adminId ?? 0;
            }
            
            Log::info('安全创建历史记录', [
                'history_data' => $historyData,
                'effective_tenant_id' => get_effective_tenant_id(),
                'admin_id' => request()->adminId ?? 0
            ]);
            
            $model = self::createHistoryModel();
            $result = $model->saveByCreate($historyData);
            
            if ($result) {
                Log::info('历史记录创建成功', ['history_id' => $result]);
            } else {
                Log::error('历史记录创建失败');
            }
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('安全创建历史记录异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'history_data' => $historyData
            ]);
            return 0;
        }
    }
    
    /**
     * 安全查询任务记录
     * 自动应用租户隔离和数据权限
     * 
     * @param array $where 查询条件
     * @param bool $applyDataPermission 是否应用数据权限
     * @return WorkflowTask|null
     */
    public static function safeQueryTask(array $where, bool $applyDataPermission = false): ?WorkflowTask
    {
        try {
            $model = self::createTaskModel(true, $applyDataPermission);
            
            // 租户隔离由模型的全局范围自动处理
            $result = $model->where($where)->find();
            
            Log::debug('安全查询任务记录', [
                'where' => $where,
                'found' => $result ? true : false,
                'effective_tenant_id' => get_effective_tenant_id()
            ]);
            
            return $result ?: null;
            
        } catch (\Exception $e) {
            Log::error('安全查询任务记录异常', [
                'error' => $e->getMessage(),
                'where' => $where
            ]);
            return null;
        }
    }
    
    /**
     * 安全查询历史记录
     * 自动应用租户隔离和数据权限
     * 
     * @param array $where 查询条件
     * @param bool $applyDataPermission 是否应用数据权限
     * @return WorkflowHistory|null
     */
    public static function safeQueryHistory(array $where, bool $applyDataPermission = false): ?WorkflowHistory
    {
        try {
            $model = self::createHistoryModel(true, $applyDataPermission);
            
            // 租户隔离由模型的全局范围自动处理
            $result = $model->where($where)->find();
            
            Log::debug('安全查询历史记录', [
                'where' => $where,
                'found' => $result ? true : false,
                'effective_tenant_id' => get_effective_tenant_id()
            ]);
            
            return $result ?: null;
            
        } catch (\Exception $e) {
            Log::error('安全查询历史记录异常', [
                'error' => $e->getMessage(),
                'where' => $where
            ]);
            return null;
        }
    }
    
    /**
     * 获取当前上下文信息
     * 用于调试和日志记录
     * 
     * @return array
     */
    public static function getContextInfo(): array
    {
        return [
            'admin_id' => request()->adminId ?? 0,
            'tenant_id' => request()->tenantId ?? 0,
            'effective_tenant_id' => get_effective_tenant_id(),
            'should_apply_tenant_isolation' => should_apply_tenant_isolation(),
            'is_super_admin' => is_super_admin(),
            'is_tenant_super_admin' => is_tenant_super_admin()
        ];
    }
}
