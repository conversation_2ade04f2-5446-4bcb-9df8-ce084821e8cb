# 工作流业务集成架构设计方案

## 📋 方案概述

### 设计目标
- **高可维护性**：统一的架构模式，清晰的职责分离
- **可迭代性**：支持新业务快速接入，支持架构平滑演进
- **性能优化**：避免频繁关联查询，提升业务查询性能
- **工作流引擎稳定**：不对现有工作流引擎做大的改动

### 核心思路
采用**双轨制架构**：
1. **通用轨道**：标准化业务使用FormService + 工作流引擎
2. **业务轨道**：复杂业务使用WorkflowableService + 状态同步

## 🏗️ 架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端业务页面                              │
├─────────────────────────────────────────────────────────────┤
│                    控制器层                                  │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  通用工作流控制器  │    │      业务专用控制器              │ │
│  │  (请假、出差等)   │    │   (合同、回款、项目等)          │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    服务层                                    │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │ WorkflowInstance │    │    WorkflowableService         │ │
│  │    Service      │    │      (业务基类)                │ │
│  │  (通用工作流)    │    │                                │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
│           │                           │                     │
│           ▼                           ▼                     │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │ FormService     │    │   BusinessWorkflowService       │ │
│  │   Factory       │    │      (状态同步服务)             │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层                                    │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │   业务表        │    │      工作流表                   │ │
│  │ (冗余状态字段)   │    │   (workflow_instance)          │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据模型设计

#### 2.1 业务表标准字段
```sql
-- 所有业务表必须包含的工作流字段
`workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
`approval_status` tinyint(1) DEFAULT 0 COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回,6=已作废',
`submit_time` datetime DEFAULT NULL COMMENT '提交审批时间',
`approval_time` datetime DEFAULT NULL COMMENT '审批完成时间',
`submitter_id` bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

-- 必要索引
KEY `idx_workflow_instance_id` (`workflow_instance_id`),
KEY `idx_approval_status` (`approval_status`),
KEY `idx_submit_time` (`submit_time`)
```

#### 2.2 状态映射关系
```
业务表状态 ←→ 工作流实例状态
0 (草稿)     ←→ 0 (已保存)
1 (审批中)   ←→ 1 (审批中)
2 (已通过)   ←→ 2 (已通过)
3 (已驳回)   ←→ 3 (拒绝)
4 (已终止)   ←→ 4 (已终止)
5 (已撤回)   ←→ 5 (已撤回)
6 (已作废)   ←→ 6 (已作废)
```

## 🔄 业务流程设计

### 3. 通用业务流程（请假、出差等）

```mermaid
sequenceDiagram
    participant F as 前端
    participant WC as 工作流控制器
    participant WIS as WorkflowInstanceService
    participant FS as FormService
    participant BT as 业务表
    participant WT as 工作流表

    F->>WC: 提交申请
    WC->>WIS: processApplication()
    WIS->>FS: saveForm()
    FS->>BT: 保存业务数据
    BT-->>FS: 返回业务ID
    FS-->>WIS: 返回[businessId, formData]
    WIS->>WT: 创建工作流实例
    WT-->>WIS: 返回实例ID
    WIS->>BT: 更新workflow_instance_id
    WIS-->>WC: 返回结果
    WC-->>F: 返回响应
```

### 4. 业务专用流程（合同、回款等）

```mermaid
sequenceDiagram
    participant F as 前端
    participant BC as 业务控制器
    participant BS as 业务Service
    participant BWS as BusinessWorkflowService
    participant BT as 业务表
    participant WT as 工作流表

    F->>BC: 提交审批
    BC->>BS: submitApproval()
    BS->>BWS: createWorkflowForBusiness()
    BWS->>WT: 创建工作流实例
    WT-->>BWS: 返回实例ID
    BWS->>BT: 同步更新状态
    Note over BWS,BT: 事务保证一致性
    BWS-->>BS: 返回结果
    BS-->>BC: 返回结果
    BC-->>F: 返回响应
```

## 🔧 核心组件设计

### 5. BusinessWorkflowService（核心服务）

```php
class BusinessWorkflowService
{
    /**
     * 为业务创建工作流实例
     */
    public function createWorkflowForBusiness(array $params): array
    {
        Db::startTrans();
        try {
            // 1. 验证业务数据
            $this->validateBusinessData($params);
            
            // 2. 创建工作流实例
            $instanceId = $this->createWorkflowInstance($params);
            
            // 3. 同步业务表状态
            $this->syncBusinessStatus($params['business_code'], $params['business_id'], [
                'workflow_instance_id' => $instanceId,
                'approval_status' => WorkflowStatusConstant::APPROVING,
                'submit_time' => date('Y-m-d H:i:s'),
                'submitter_id' => $params['submitter_id']
            ]);
            
            Db::commit();
            return ['success' => true, 'instance_id' => $instanceId];
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 处理工作流状态变更回调
     */
    public function handleWorkflowStatusChange(int $instanceId, int $newStatus): bool
    {
        // 状态同步逻辑
    }
}
```

### 6. WorkflowableService（业务基类）

```php
abstract class WorkflowableService extends BaseService
{
    protected BusinessWorkflowService $workflowService;
    
    public function __construct()
    {
        parent::__construct();
        $this->workflowService = new BusinessWorkflowService();
    }
    
    /**
     * 提交审批
     */
    public function submitApproval(int $id, array $params = []): array
    {
        $record = $this->getDetail($id);
        
        // 业务验证
        $this->validateForApproval($record);
        
        // 创建工作流
        return $this->workflowService->createWorkflowForBusiness([
            'business_code' => $this->getBusinessCode(),
            'business_id' => $id,
            'definition_id' => $params['definition_id'],
            'submitter_id' => $params['submitter_id'],
            'title' => $this->getApprovalTitle($record)
        ]);
    }
    
    // 抽象方法，子类必须实现
    abstract protected function getBusinessCode(): string;
    abstract protected function getApprovalTitle($record): string;
    abstract protected function validateForApproval($record): void;
}
```

## 📊 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建业务数据
    草稿 --> 审批中: 提交审批
    草稿 --> [*]: 删除

    审批中 --> 已通过: 审批通过
    审批中 --> 已驳回: 审批驳回
    审批中 --> 已撤回: 申请人撤回
    审批中 --> 已终止: 管理员终止

    已驳回 --> 审批中: 重新提交
    已驳回 --> [*]: 删除

    已撤回 --> 审批中: 重新提交
    已撤回 --> [*]: 删除

    已通过 --> 已作废: 作废操作
    已通过 --> [*]: 归档

    已终止 --> [*]: 归档
    已作废 --> [*]: 归档
```

## 🚀 实施计划

### 阶段一：核心组件开发（1-2天）

#### 任务1.1：创建BusinessWorkflowService
- 实现createWorkflowForBusiness方法
- 实现状态同步机制
- 添加事务保护和错误处理

#### 任务1.2：完善WorkflowableService基类
- 实现submitApproval等核心方法
- 定义抽象方法规范
- 添加业务验证框架

#### 任务1.3：实现状态同步机制
- 创建状态映射配置
- 实现自动同步逻辑
- 添加同步失败重试

### 阶段二：业务模块改造（2-3天）

#### 任务2.1：改造CRM合同模块
- 添加标准工作流字段
- 继承WorkflowableService
- 实现具体业务方法
- 完善CustomerContractTrait

#### 任务2.2：改造CRM回款模块
- 按照合同模块模式改造
- 实现回款特有的业务逻辑

#### 任务2.3：保持通用工作流兼容
- 确保现有FormService机制正常工作
- 添加向后兼容处理

### 阶段三：测试和优化（1天）

#### 任务3.1：功能测试
- 新增/编辑/提交/撤回等完整流程测试
- 状态同步准确性测试
- 并发场景测试

#### 任务3.2：性能优化
- 查询性能测试
- 状态同步性能优化
- 添加必要的缓存机制

## 📋 开发规范

### 1. 业务Service开发规范

```php
// 示例：CrmContractService
class CrmContractService extends WorkflowableService
{
    public function __construct()
    {
        $this->model = new CrmContract();
        parent::__construct();
    }

    protected function getBusinessCode(): string
    {
        return 'crm_contract';
    }

    protected function getApprovalTitle($record): string
    {
        return "合同审批-{$record->contract_name}";
    }

    protected function validateForApproval($record): void
    {
        if ($record->approval_status !== 0) {
            throw new BusinessException('只有草稿状态的合同才能提交审批');
        }

        if (empty($record->contract_amount) || $record->contract_amount <= 0) {
            throw new BusinessException('合同金额必须大于0');
        }
    }

    protected function afterApprovalComplete($record, int $status, string $opinion): void
    {
        if ($status === WorkflowStatusConstant::APPROVED) {
            // 审批通过后的业务处理
            $this->handleContractApproved($record);
        }
    }
}
```

### 2. 控制器开发规范

```php
// 示例：CrmContractController
class CrmContractController extends BaseController
{
    /**
     * 提交审批
     */
    public function submitApproval(): Json
    {
        $params = $this->request->post();

        try {
            $contractService = CrmContractService::getInstance();
            $result = $contractService->submitApproval($params['id'], [
                'definition_id' => $params['definition_id'],
                'submitter_id' => get_user_id()
            ]);

            return $this->success('提交成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 撤回审批
     */
    public function withdrawApproval(): Json
    {
        $params = $this->request->post();

        try {
            $contractService = CrmContractService::getInstance();
            $result = $contractService->withdrawApproval($params['id']);

            return $this->success('撤回成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 3. 数据库设计规范

```sql
-- 业务表改造示例
ALTER TABLE `crm_contract`
ADD COLUMN `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID' AFTER `id`,
ADD COLUMN `approval_status` tinyint(1) DEFAULT 0 COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回,6=已作废' AFTER `workflow_instance_id`,
ADD COLUMN `submit_time` datetime DEFAULT NULL COMMENT '提交审批时间' AFTER `approval_status`,
ADD COLUMN `approval_time` datetime DEFAULT NULL COMMENT '审批完成时间' AFTER `submit_time`,
ADD COLUMN `submitter_id` bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID' AFTER `approval_time`,
ADD INDEX `idx_workflow_instance_id` (`workflow_instance_id`),
ADD INDEX `idx_approval_status` (`approval_status`),
ADD INDEX `idx_submit_time` (`submit_time`);
```

## 🔍 监控和维护

### 1. 状态同步监控
- 记录同步失败的实例
- 实现自动重试机制
- 设置告警阈值

### 2. 性能监控
- 监控查询响应时间
- 监控状态同步耗时
- 优化高频操作

### 3. 数据一致性检查
- 定期检查业务表与工作流表状态一致性
- 提供数据修复工具

## 📈 扩展指南

### 新业务接入步骤

1. **数据库改造**
   ```sql
   -- 添加标准工作流字段
   ALTER TABLE `new_business_table` ADD COLUMN ...
   ```

2. **创建Service类**
   ```php
   class NewBusinessService extends WorkflowableService
   {
       // 实现抽象方法
   }
   ```

3. **更新BusinessWorkflowService**
   ```php
   // 在syncBusinessStatus方法中添加新业务的处理逻辑
   case 'new_business':
       return $this->syncNewBusinessStatus($businessId, $statusData);
   ```

4. **创建控制器方法**
   ```php
   // 添加提交审批、撤回等标准方法
   ```

## 🎯 方案优势

1. **高可维护性**
   - 统一的架构模式
   - 清晰的职责分离
   - 标准化的开发规范

2. **可迭代性**
   - 新业务快速接入
   - 支持架构平滑演进
   - 向后兼容保证

3. **性能优化**
   - 避免频繁关联查询
   - 业务表直接查询状态
   - 合理的索引设计

4. **工作流引擎稳定**
   - 不对现有引擎做大改动
   - 通过适配层解耦
   - 保持引擎的通用性

---

**文档版本**: v1.0
**创建时间**: 2025-01-18
**适用范围**: 工作流业务集成开发
