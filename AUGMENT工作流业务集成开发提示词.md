# AUGMENT工作流业务集成开发提示词

## 📋 项目概述

**项目名称：** 工作流业务集成动态工厂实施  
**技术栈：** ThinkPHP8 + Vue3 + Element Plus  
**项目路径：** E:\项目\self_admin\base_admin  
**目标：** 实现基于workflow_type表的动态工厂，替代硬编码映射，支持新业务快速接入工作流

## 🎯 核心任务目标

### 主要目标
1. **实现动态工厂机制** - 基于workflow_type表动态创建Service和Model
2. **清理方案C代码** - 移除CRM模块的WorkflowableService相关代码
3. **集成动态工厂** - 在WorkflowInstanceService中集成动态工厂
4. **保持向后兼容** - 确保现有HR模块正常工作
5. **完整测试验证** - 确保所有工作流操作正常

### 关键约束
- ✅ 不新增workflow_business_config表
- ✅ FormServiceInterface已移除getFormType()方法（7个方法）
- ✅ 采用方案A：要求新业务完整实现FormServiceInterface
- ✅ 保持现有HR模块完全不变
- ✅ 基于workflow_type表实现真正的动态映射

## 📚 参考文档

**主要参考：** `工作流业务集成实施指南.md`  
**关键章节：**
- 🏭 动态工厂设计
- 🔄 代码改动清单  
- 🔍 FormServiceInterface接口分析
- 📚 开发文档

## 🚀 分阶段实施计划

### 阶段一：代码清理与准备 🧹

**任务1.1：清理方案C代码**
```
请根据实施指南中的"需要删除的文件"清单，删除以下4个文件：
- app/workflow/service/WorkflowableService.php
- app/crm/service/CrmContractWorkflowService.php  
- app/crm/service/CrmContractReceivableWorkflowService.php
- app/workflow/service/WorkflowSyncService.php

执行前请先备份这些文件，以防需要回滚。
```

**任务1.2：恢复CRM模型和服务**
```
请将以下CRM文件恢复为继承BaseModel和BaseService：
- app/crm/model/CrmContract.php - 恢复继承BaseModel
- app/crm/model/CrmContractReceivable.php - 恢复继承BaseModel  
- app/crm/service/CrmContractService.php - 恢复继承BaseService
- app/crm/service/CrmContractReceivableService.php - 恢复继承BaseService

移除所有WorkflowableService相关的继承和方法调用。
```

**任务1.3：清理控制器方法**
```
清理以下控制器中的方案C相关方法：
- app/crm/controller/CrmContractController.php
- app/crm/controller/CrmContractReceivableController.php

移除submitForApproval、getApprovalStatus等工作流相关方法。
```

### 阶段二：动态工厂核心开发 🏭

**任务2.1：创建动态工厂类**
```
创建文件：app/workflow/factory/DynamicWorkflowFactory.php

实现以下方法：
1. createServiceByBusinessCode(string $businessCode): ?BaseService
2. createModelByBusinessCode(string $businessCode): ?BaseModel  
3. createFormServiceByBusinessCode(string $businessCode): ?FormServiceInterface
4. getBusinessConfig(string $businessCode): ?array

核心逻辑：
- 基于workflow_type表查询module_code和business_code
- 动态构建类名：app\{module_code}\service\{Business}Service
- 使用class_exists()验证类是否存在
- 支持FormServiceInterface检查和回退机制

参考实施指南中的"核心动态工厂类"代码示例。
```

**任务2.2：验证workflow_type表数据**
```
检查workflow_type表是否包含必要的测试数据：
- hr_leave (HR请假)
- hr_travel (HR出差)  
- crm_contract (CRM合同)

如果缺少，请根据实施指南添加相应的测试数据。
```

### 阶段三：工作流引擎集成 🔧

**任务3.1：更新WorkflowInstanceService**
```
修改文件：app/workflow/service/WorkflowInstanceService.php

在以下方法中集成动态工厂：
1. createNewApplication() - 使用动态工厂获取FormService
2. updateExistingApplication() - 使用动态工厂获取FormService
3. recallApplication() - 使用动态工厂获取FormService
4. deleteApplication() - 使用动态工厂获取FormService

集成逻辑：
- 优先使用DynamicWorkflowFactory::createFormServiceByBusinessCode()
- 如果失败，回退到FormServiceFactory::create()
- 确保向后兼容性

参考实施指南中的"WorkflowInstanceService集成动态工厂"代码示例。
```

**任务3.2：更新BusinessWorkflowService**
```
修改文件：app/workflow/service/BusinessWorkflowService.php

更新以下方法使用动态工厂：
1. createWorkflowForBusiness() - 使用动态工厂创建Model
2. syncBusinessStatus() - 使用动态工厂创建Model

移除硬编码的businessModelMap数组，改为动态创建。
```

### 阶段四：FormServiceInterface适配 📝

**任务4.1：验证现有FormService实现**
```
检查以下Service是否正确实现FormServiceInterface（7个方法）：
- app/hr/service/HrLeaveService.php
- app/hr/service/HrTravelService.php (如果存在)
- app/crm/service/CrmContractService.php

确保移除了getFormType()方法，只实现7个核心方法：
1. getFormData(int $id): array
2. saveForm(array $data): array  
3. updateForm(int $id, array $data): bool
4. deleteForm(int $id): bool
5. updateFormStatus(int $id, int $status, array $extra = []): bool
6. getInstanceTitle($formData): string
7. validateFormData(array $data, string $scene = 'create'): array
```

**任务4.2：更新FormServiceInterface定义**
```
确认文件：app/workflow/interfaces/FormServiceInterface.php

接口应该只包含7个方法，已移除getFormType()方法。
如果接口还包含getFormType()，请移除该方法定义。
```

### 阶段五：测试验证 🧪

**任务5.1：单元测试**
```
创建测试文件：tests/Unit/workflow/factory/DynamicWorkflowFactoryTest.php

测试用例：
1. testCreateServiceByBusinessCode() - 测试Service创建
2. testCreateModelByBusinessCode() - 测试Model创建  
3. testCreateFormServiceByBusinessCode() - 测试FormService创建
4. testGetBusinessConfig() - 测试配置获取
5. testInvalidBusinessCode() - 测试无效业务代码处理

使用PHPUnit框架，确保所有测试通过。
```

**任务5.2：集成测试**
```
测试完整工作流流程：
1. HR请假申请 - 验证现有功能不受影响
2. CRM合同审批 - 验证动态工厂正常工作
3. 工作流状态变更 - 验证各种操作（同意、拒绝、撤回等）

测试场景：
- 新增申请 → 提交审批 → 审批通过
- 新增申请 → 提交审批 → 审批拒绝  
- 草稿状态 → 编辑 → 提交审批
- 审批中 → 撤回申请
```

**任务5.3：错误处理测试**
```
测试异常情况：
1. workflow_type表中不存在的business_code
2. Service类不存在的情况
3. Service未实现FormServiceInterface的情况
4. 数据库连接异常的处理

确保所有异常都有合适的错误处理和日志记录。
```

## 🔧 开发规范与约束

### 代码规范
- **命名规范：** 遵循ThinkPHP8命名约定
- **注释要求：** 所有public方法必须有完整的PHPDoc注释
- **异常处理：** 使用BusinessException处理业务异常
- **日志记录：** 关键操作必须记录日志

### 技术约束  
- **PHP版本：** >= 8.0
- **框架版本：** ThinkPHP 8.x
- **数据库：** MySQL（不使用SQLite）
- **架构原则：** 分层架构，职责分离

### 测试要求
- **单元测试覆盖率：** >= 80%
- **集成测试：** 覆盖所有主要业务流程
- **性能测试：** 确保动态工厂不影响性能
- **兼容性测试：** 确保现有HR模块正常工作

## 📋 验收标准

### 功能验收
- ✅ 动态工厂能正确创建Service和Model
- ✅ 工作流引擎正常使用动态工厂
- ✅ 现有HR模块功能完全正常
- ✅ CRM模块通过动态工厂正常工作
- ✅ 所有工作流操作（新增、编辑、审批等）正常

### 代码质量验收
- ✅ 所有单元测试通过
- ✅ 集成测试覆盖主要场景
- ✅ 代码符合PSR规范
- ✅ 无明显性能问题
- ✅ 异常处理完善

### 文档验收
- ✅ 代码注释完整
- ✅ 测试用例文档清晰
- ✅ 部署说明准确
- ✅ 问题排查指南完整

## 🚨 风险控制

### 回滚准备
- **代码备份：** 实施前完整备份现有代码
- **数据备份：** 备份workflow相关数据表
- **回滚脚本：** 准备快速回滚方案
- **监控指标：** 设置关键功能监控

### 分阶段验证
- **每个阶段完成后进行验证**
- **发现问题立即停止，分析解决**
- **确保每个阶段都可以独立回滚**
- **保持FormService机制始终可用**

## 📞 支持与协作

### 开发支持
- **参考文档：** 工作流业务集成实施指南.md
- **代码示例：** 文档中提供的完整代码示例
- **测试数据：** 使用现有的HR和CRM测试数据

### 问题反馈
- **技术问题：** 详细描述问题现象和错误日志
- **业务问题：** 说明具体的业务场景和期望结果
- **性能问题：** 提供性能测试数据和分析报告

## 🛠️ MCP工具使用指南

### 文件操作工具
```bash
# 查看项目结构
view E:\项目\self_admin\base_admin type:directory

# 查看具体文件
view app/workflow/service/WorkflowInstanceService.php type:file

# 搜索相关代码
codebase-retrieval "WorkflowInstanceService中的createNewApplication方法实现"

# 编辑文件
str-replace-editor path:app/workflow/factory/DynamicWorkflowFactory.php command:str_replace

# 创建新文件
save-file path:app/workflow/factory/DynamicWorkflowFactory.php
```

### 测试工具
```bash
# 运行单元测试
launch-process command:"php think test tests/Unit/workflow/factory/DynamicWorkflowFactoryTest.php" wait:true

# 检查代码语法
launch-process command:"php -l app/workflow/factory/DynamicWorkflowFactory.php" wait:true

# 运行完整测试套件
launch-process command:"php think test" wait:true
```

### 数据库操作
```bash
# 查看workflow_type表结构
execute_query "DESCRIBE workflow_type"

# 查看测试数据
execute_query "SELECT * FROM workflow_type WHERE status = 1"

# 插入测试数据
execute_query "INSERT INTO workflow_type (name, module_code, business_code, status) VALUES ('测试业务', 'test', 'test_business', 1)"
```

## 📋 关键代码模板

### 动态工厂核心代码
```php
<?php
declare(strict_types=1);

namespace app\workflow\factory;

use app\common\core\base\BaseService;
use app\common\core\base\BaseModel;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\model\WorkflowType;

class DynamicWorkflowFactory
{
    /**
     * 基于workflow_type表动态创建Service
     */
    public static function createServiceByBusinessCode(string $businessCode): ?BaseService
    {
        $workflowType = WorkflowType::where('business_code', $businessCode)->first();

        if (!$workflowType) {
            return null;
        }

        // 动态构建Service类名
        $businessName = ucfirst(str_replace($workflowType->module_code . '_', '', $businessCode));
        $serviceClass = "\\app\\{$workflowType->module_code}\\service\\{$businessName}Service";

        return class_exists($serviceClass) ? new $serviceClass() : null;
    }

    /**
     * 动态创建FormService（优先使用动态工厂，回退到FormServiceFactory）
     */
    public static function createFormServiceByBusinessCode(string $businessCode): ?FormServiceInterface
    {
        // 1. 优先使用动态工厂创建Service
        $service = self::createServiceByBusinessCode($businessCode);

        // 2. 检查是否实现了FormServiceInterface
        if ($service instanceof FormServiceInterface) {
            return $service;
        }

        // 3. 回退到原有FormServiceFactory
        return \app\workflow\factory\FormServiceFactory::create($businessCode);
    }
}
```

### 测试用例模板
```php
<?php
declare(strict_types=1);

namespace tests\Unit\workflow\factory;

use PHPUnit\Framework\TestCase;
use app\workflow\factory\DynamicWorkflowFactory;
use app\workflow\interfaces\FormServiceInterface;

class DynamicWorkflowFactoryTest extends TestCase
{
    public function testCreateServiceByBusinessCode(): void
    {
        // 测试HR请假Service创建
        $service = DynamicWorkflowFactory::createServiceByBusinessCode('hr_leave');

        $this->assertNotNull($service);
        $this->assertInstanceOf(\app\hr\service\HrLeaveService::class, $service);
    }

    public function testCreateFormServiceByBusinessCode(): void
    {
        // 测试FormService创建
        $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode('hr_leave');

        $this->assertNotNull($formService);
        $this->assertInstanceOf(FormServiceInterface::class, $formService);
    }

    public function testInvalidBusinessCode(): void
    {
        // 测试无效业务代码
        $service = DynamicWorkflowFactory::createServiceByBusinessCode('invalid_code');

        $this->assertNull($service);
    }
}
```

## 🔍 调试与排错指南

### 常见问题排查
1. **动态工厂创建失败**
   - 检查workflow_type表数据是否正确
   - 验证Service类是否存在
   - 确认类名拼接逻辑是否正确

2. **FormServiceInterface实现问题**
   - 确认Service实现了所有7个方法
   - 检查方法签名是否正确
   - 验证返回值类型是否匹配

3. **工作流集成问题**
   - 检查WorkflowInstanceService中的集成代码
   - 验证回退机制是否正常工作
   - 确认日志记录是否完整

### 日志查看
```bash
# 查看工作流相关日志
tail -f runtime/log/$(date +%Y%m%d).log | grep -i workflow

# 查看错误日志
tail -f runtime/log/error_$(date +%Y%m%d).log
```

---

**重要提醒：** 请严格按照阶段顺序执行，每个阶段完成后进行充分测试验证，确保系统稳定性。如遇到任何问题，请及时反馈并暂停后续开发。
