<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 产品分类表路由
Route::group('api/crm/crm_product_category', function () {
	Route::get('index', 'app\crm\controller\CrmProductCategoryController@index');
	Route::get('options', 'app\crm\controller\CrmProductCategoryController@options');
	Route::get('detail/:id', 'app\crm\controller\CrmProductCategoryController@detail');
	Route::post('add', 'app\crm\controller\CrmProductCategoryController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmProductCategoryController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmProductCategoryController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmProductCategoryController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmProductCategoryController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmProductCategoryController@status');
	Route::post('import', 'app\crm\controller\CrmProductCategoryController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmProductCategoryController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmProductCategoryController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmProductCategoryController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);