import { ElMessage, ElMessageBox } from 'element-plus'
import { WorkflowStatus, getInstanceStatusText, getTaskStatusText } from '@/constants/workflow'

/**
 * 简单的工作流操作拦截
 * 
 * 提供基本的操作前检查，防止对已结束流程的误操作
 */

export interface WorkflowTaskItem {
  task_id?: number
  status: number                    // 任务状态
  instance?: {
    status: number                  // 实例状态
    [key: string]: any
  }
  [key: string]: any
}

export function useWorkflowOperationGuard() {
  
  /**
   * 检查操作是否被允许
   */
  const checkOperationAllowed = (
    task: WorkflowTaskItem, 
    operation: 'approve' | 'reject' | 'transfer' | 'recall'
  ): { allowed: boolean; reason?: string } => {
    
    // 1. 检查实例状态
    if (task.instance?.status) {
      const instanceStatus = task.instance.status
      
      // 实例已结束的情况
      const endedStatuses = [
        WorkflowStatus.COMPLETED,  // 已通过
        WorkflowStatus.REJECTED,   // 已驳回
        WorkflowStatus.TERMINATED, // 已终止
        WorkflowStatus.RECALLED,   // 已撤回
        WorkflowStatus.VOID        // 已作废
      ]
      
      if (endedStatuses.includes(instanceStatus)) {
        return {
          allowed: false,
          reason: `流程已${getInstanceStatusText(instanceStatus)}，无法执行${getOperationText(operation)}操作`
        }
      }
    }
    
    // 2. 检查任务状态
    const taskStatus = task.status
    
    // 只有待处理的任务才能进行审批操作
    if (['approve', 'reject', 'transfer'].includes(operation)) {
      if (taskStatus !== WorkflowStatus.DRAFT) {
        return {
          allowed: false,
          reason: `任务已${getTaskStatusText(taskStatus)}，无法执行${getOperationText(operation)}操作`
        }
      }
    }
    
    // 已跳过的任务不能进行任何操作
    if (taskStatus === WorkflowStatus.SKIPPED) {
      return {
        allowed: false,
        reason: '任务已跳过，无法执行操作'
      }
    }
    
    return { allowed: true }
  }
  
  /**
   * 安全执行操作（带拦截和确认）
   */
  const safeExecuteOperation = async (
    task: WorkflowTaskItem,
    operation: 'approve' | 'reject' | 'transfer' | 'recall',
    operationFn: () => Promise<void>,
    skipConfirm: boolean = false
  ): Promise<boolean> => {
    
    try {
      // 1. 检查操作是否被允许
      const check = checkOperationAllowed(task, operation)
      
      if (!check.allowed) {
        ElMessage.error(check.reason || '操作不被允许')
        return false
      }
      
      // 2. 重要操作需要确认
      if (!skipConfirm && ['reject', 'recall'].includes(operation)) {
        try {
          await ElMessageBox.confirm(
            `确定要${getOperationText(operation)}此申请吗？此操作不可撤销。`,
            '操作确认',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
        } catch {
          // 用户取消
          return false
        }
      }
      
      // 3. 执行操作
      await operationFn()
      return true
      
    } catch (error: any) {
      ElMessage.error(error.message || '操作失败')
      return false
    }
  }
  
  /**
   * 获取操作文本
   */
  const getOperationText = (operation: string): string => {
    const operationMap: Record<string, string> = {
      approve: '同意',
      reject: '拒绝',
      transfer: '转交',
      recall: '撤回'
    }
    return operationMap[operation] || operation
  }
  
  /**
   * 检查任务是否可以操作
   */
  const isTaskOperable = (task: WorkflowTaskItem): boolean => {
    const check = checkOperationAllowed(task, 'approve')
    return check.allowed
  }
  
  /**
   * 获取不可操作的原因
   */
  const getInoperableReason = (task: WorkflowTaskItem): string => {
    const check = checkOperationAllowed(task, 'approve')
    return check.reason || '任务不可操作'
  }
  
  return {
    checkOperationAllowed,
    safeExecuteOperation,
    isTaskOperable,
    getInoperableReason
  }
}
