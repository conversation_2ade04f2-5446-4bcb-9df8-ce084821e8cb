# CRM客户页面表格列整合说明

## 修改概述

将"我的客户"页面参考"我的线索"页面的方式，从循环渲染改为声明式写法，提升代码可读性和维护性。

## 修改前后对比

### 修改前（循环渲染）
```vue
<!-- 普通列 -->
<template v-for="col in columns.filter((col) => !col.isSpecialColumn)" :key="col.prop">
  <ElTableColumn v-bind="col" />
</template>

<!-- 特殊组件列 -->
<template v-for="col in columns.filter((col) => col.component)" :key="col.prop">
  <component
    :is="col.component"
    :prop="col.prop"
    :label="col.label"
    :width="col.width"
    :align="col.align"
    v-bind="col.componentProps || {}"
  />
</template>
```

### 修改后（声明式）
```vue
<!-- 序号列 -->
<ElTableColumn type="index" label="序号" width="60" align="center" />

<!-- ID列 -->
<ElTableColumn prop="id" label="ID" width="80" />

<!-- 客户名称 -->
<ElTableColumn prop="customer_name" label="客户名称" />

<!-- 客户级别 -->
<TagColumn
  label="客户级别"
  prop="level"
  width="100"
  :options="[
    { label: '未知', value: 0, type: 'info' },
    { label: '普通', value: 1, type: 'info' },
    { label: '重要', value: 2, type: 'warning' },
    { label: '战略', value: 3, type: 'danger' }
  ]"
/>

<!-- 状态 -->
<SwitchColumn
  label="状态"
  prop="status"
  width="100"
  :active-value="1"
  :inactive-value="0"
  active-text="启用"
  inactive-text="禁用"
  :api-method="CrmCustomerMyApi.updateField"
/>
```

## 主要改进

### 1. 代码可读性提升
- **直观明了**: 每个列的配置一目了然
- **易于维护**: 修改某个列只需要找到对应的声明
- **类型安全**: 编译时可以检查属性配置

### 2. 组件使用优化
- **TagColumn**: 客户级别使用标签显示，不同级别用不同颜色
- **SwitchColumn**: 状态字段使用开关组件，支持直接切换
- **LongTextColumn**: 备注字段支持长文本显示和截断
- **LinkColumn**: 官网字段支持点击跳转

### 3. 操作列优化
参考线索页面，采用"详情、编辑、更多"的结构：
```vue
<ElTableColumn prop="operation" label="操作" fixed="right" width="260" align="center">
  <template #default="scope">
    <div class="operation-buttons">
      <!-- 详情按钮 -->
      <ArtButtonTable
        text="详情"
        :iconClass="BgColorEnum.SECONDARY"
        @click="showDetail(scope.row.id)"
      />
      
      <!-- 编辑按钮 -->
      <ArtButtonTable
        text="编辑"
        :iconClass="BgColorEnum.PRIMARY"
        @click="showFormDialog('edit', scope.row.id)"
      />
      
      <!-- 更多操作下拉菜单 -->
      <ElDropdown @command="(command) => handleMoreAction(command, scope.row)">
        <ArtButtonTable text="更多" type="more" />
        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem divided command="delete" :icon="Delete">
              删除
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>
  </template>
</ElTableColumn>
```

## 技术实现细节

### 1. 导入优化
```typescript
import {
  ElMessage,
  ElMessageBox,
  ElCard,
  ElButton,
  ElTableColumn,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElTag
} from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { LongTextColumn, SwitchColumn, LinkColumn, TagColumn } from '@/components/core/tables/columns'
```

### 2. 列配置简化
移除了复杂的列配置对象，直接在模板中声明：
```typescript
// 只保留必要的列检查配置
const { columnChecks } = useCheckedColumns(() => [
  // 配置项仅用于列显示/隐藏控制
])
```

### 3. 操作处理统一
```typescript
const handleMoreAction = (command: string, row: any) => {
  switch (command) {
    case 'delete':
      handleDelete(row.id)
      break
    default:
      console.warn('未知的操作命令:', command)
  }
}
```

## 列配置说明

### 基础列
- **序号列**: 自动编号，固定宽度60px
- **ID列**: 客户ID，宽度80px
- **客户名称**: 主要信息，自适应宽度
- **所属行业**: 行业分类信息
- **客户来源**: 来源渠道信息

### 特殊列
- **客户级别**: 使用TagColumn，不同级别显示不同颜色标签
- **官网**: 使用LinkColumn，支持点击跳转，新窗口打开
- **备注**: 使用LongTextColumn，长文本自动截断
- **状态**: 使用SwitchColumn，支持直接切换启用/禁用

### 地址信息
- **省份**: 省级行政区
- **城市**: 市级行政区  
- **区/县**: 区县级行政区
- **详细地址**: 具体地址信息

### 业务信息
- **统一社会信用代码**: 企业标识
- **年营业额**: 企业规模指标
- **员工人数**: 企业规模指标
- **注册资本**: 企业资本信息

### 时间信息
- **最后跟进时间**: 业务跟进记录，宽度180px
- **下次跟进时间**: 计划跟进时间，宽度180px
- **更新时间**: 数据更新时间，宽度180px

### 状态信息
- **锁定状态**: 使用ElTag显示，已锁定显示红色，未锁定显示绿色

## 样式优化

### 操作按钮样式
```scss
.operation-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;

  .el-button {
    margin: 0;
    padding: 4px 8px;
    font-size: 12px;
  }
}
```

### 响应式设计
```scss
@media (max-width: 768px) {
  .operation-buttons {
    flex-direction: column;
    gap: 2px;
  }
}
```

## 优势总结

1. **可维护性**: 声明式写法更直观，易于理解和修改
2. **类型安全**: 编译时检查，减少运行时错误
3. **性能优化**: 减少了动态组件渲染的开销
4. **一致性**: 与线索页面保持相同的操作列结构
5. **扩展性**: 新增列只需要在模板中添加对应声明

这种声明式的表格列配置方式可以作为标准模板应用到其他类似的列表页面。
