<?php
declare(strict_types=1);

namespace app\workflow\traits;

use app\workflow\constants\WorkflowStatusConstant;
use think\facade\Log;

/**
 * 默认工作流回调处理Trait
 * 
 * 为FormServiceInterface实现类提供afterWorkflowStatusChange方法的默认实现
 * 子类可以覆盖此方法实现具体的业务逻辑
 */
trait DefaultWorkflowCallbackTrait
{
    /**
     * 工作流状态变更后的默认业务处理
     * 
     * 提供通用的日志记录和基础处理逻辑
     * 子类可以覆盖此方法实现具体业务逻辑
     *
     * @param int $businessId 业务记录ID
     * @param int $status 新的工作流状态
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
    {
        // 获取状态文本描述
        $statusText = $this->getWorkflowStatusText($status);
        
        // 记录工作流状态变更日志
        Log::info('工作流状态变更处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'status' => $status,
            'status_text' => $statusText,
            'extra' => $extra,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        try {
            // 根据状态执行对应的默认处理
            switch ($status) {
                case WorkflowStatusConstant::STATUS_COMPLETED:
                    return $this->handleWorkflowCompleted($businessId, $extra);
                    
                case WorkflowStatusConstant::STATUS_REJECTED:
                    return $this->handleWorkflowRejected($businessId, $extra);
                    
                case WorkflowStatusConstant::STATUS_RECALLED:
                    return $this->handleWorkflowRecalled($businessId, $extra);
                    
                case WorkflowStatusConstant::STATUS_TERMINATED:
                    return $this->handleWorkflowTerminated($businessId, $extra);
                    
                case WorkflowStatusConstant::STATUS_VOID:
                    return $this->handleWorkflowVoided($businessId, $extra);
                    
                default:
                    return $this->handleWorkflowOtherStatus($businessId, $status, $extra);
            }
            
        } catch (\Exception $e) {
            Log::error('工作流状态变更处理失败', [
                'service_class' => static::class,
                'business_id' => $businessId,
                'status' => $status,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }
    
    /**
     * 处理工作流完成（审批通过）
     * 
     * @param int $businessId 业务记录ID
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowCompleted(int $businessId, array $extra = []): bool
    {
        Log::info('工作流审批通过 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的审批通过逻辑
        return true;
    }
    
    /**
     * 处理工作流拒绝
     * 
     * @param int $businessId 业务记录ID
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowRejected(int $businessId, array $extra = []): bool
    {
        Log::info('工作流审批拒绝 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的审批拒绝逻辑
        return true;
    }
    
    /**
     * 处理工作流撤回
     * 
     * @param int $businessId 业务记录ID
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowRecalled(int $businessId, array $extra = []): bool
    {
        Log::info('工作流撤回 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的撤回逻辑
        return true;
    }
    
    /**
     * 处理工作流终止
     * 
     * @param int $businessId 业务记录ID
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowTerminated(int $businessId, array $extra = []): bool
    {
        Log::info('工作流终止 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的终止逻辑
        return true;
    }
    
    /**
     * 处理工作流作废
     * 
     * @param int $businessId 业务记录ID
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowVoided(int $businessId, array $extra = []): bool
    {
        Log::info('工作流作废 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的作废逻辑
        return true;
    }
    
    /**
     * 处理其他工作流状态
     * 
     * @param int $businessId 业务记录ID
     * @param int $status 状态值
     * @param array $extra 额外数据
     * @return bool 处理结果
     */
    protected function handleWorkflowOtherStatus(int $businessId, int $status, array $extra = []): bool
    {
        Log::info('工作流其他状态变更 - 默认处理', [
            'service_class' => static::class,
            'business_id' => $businessId,
            'status' => $status,
            'extra' => $extra
        ]);
        
        // 子类可以覆盖此方法实现具体的其他状态处理逻辑
        return true;
    }
    
    /**
     * 获取工作流状态文本描述
     * 
     * @param int $status 状态值
     * @return string 状态文本
     */
    protected function getWorkflowStatusText(int $status): string
    {
        $statusMap = [
            WorkflowStatusConstant::STATUS_DRAFT => '草稿',
            WorkflowStatusConstant::STATUS_PROCESSING => '审批中',
            WorkflowStatusConstant::STATUS_COMPLETED => '已完成',
            WorkflowStatusConstant::STATUS_REJECTED => '已拒绝',
            WorkflowStatusConstant::STATUS_RECALLED => '已撤回',
            WorkflowStatusConstant::STATUS_TERMINATED => '已终止',
            WorkflowStatusConstant::STATUS_VOID => '已作废'
        ];
        
        return $statusMap[$status] ?? "未知状态({$status})";
    }
}
