# 统一审批通知模板设计方案

## 一、设计理念

基于用户友好性原则，采用中文变量名设计统一的审批通知模板，让用户能够直观理解每个变量的含义，提升消息模板的可维护性和用户体验。

## 二、中文变量名规范

### 2.1 变量命名原则

1. **直观性**：变量名直接反映其含义，如`${商品名称}`、`${当前库存}`
2. **一致性**：同类型变量使用统一的命名格式
3. **简洁性**：避免过长的变量名，保持简洁明了
4. **业务性**：贴近业务场景，便于业务人员理解

### 2.2 变量配置结构

```json
{
  "variables": [
    {
      "name": "流程标题",        // 显示名称（中文）
      "code": "流程标题",        // 变量代码（中文）
      "field": "title",         // 数据字段路径
      "required": true,         // 是否必填
      "description": "工作流程标题"  // 变量说明
    }
  ]
}
```

## 三、统一审批通知模板

### 3.1 模板标题设计

#### 基础格式
```
您有一个待审批任务：${流程标题}
```

#### 扩展格式（根据业务场景）
```
【${业务类型}】您有一个待审批任务：${流程标题}
```

#### 示例
- `您有一个待审批任务：张三的请假申请`
- `【人事审批】您有一个待审批任务：李四的转正申请`
- `【财务审批】您有一个待审批任务：王五的报销申请`

### 3.2 模板内容设计

#### 标准审批通知内容
```
您有一个新的待审批任务

流程标题：${流程标题}
当前环节：${任务名称}
提交人：${提交人}
提交时间：${提交时间}
紧急程度：${紧急程度}

请及时处理！

详情链接：${详情链接}
```

#### 审批结果通知内容
```
您的申请已审批完成

流程标题：${流程标题}
审批结果：${审批结果}
审批人：${审批人}
审批时间：${审批时间}
审批意见：${审批意见}

详情链接：${详情链接}
```

#### 抄送通知内容
```
您收到一个抄送通知

流程标题：${流程标题}
提交人：${提交人}
当前环节：${节点名称}
抄送时间：${抄送时间}

请知悉。

详情链接：${详情链接}
```

## 四、工作流统一模板配置

### 4.1 workflow_task_approval（审批任务通知）

```sql
INSERT INTO `notice_template` 
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`) 
VALUES 
('workflow_task_approval', '工作流审批任务通知', '您有一个待审批任务：${流程标题}', 
'您有一个新的待审批任务

流程标题：${流程标题}
当前环节：${任务名称}
提交人：${提交人}
提交时间：${提交时间}

请及时处理！

详情链接：${详情链接}', 
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "任务名称",
      "code": "任务名称",
      "field": "task_name",
      "required": true,
      "description": "当前审批环节名称"
    },
    {
      "name": "提交人",
      "code": "提交人",
      "field": "submitter_name",
      "required": true,
      "description": "流程提交人姓名"
    },
    {
      "name": "提交时间",
      "code": "提交时间",
      "field": "created_at",
      "required": true,
      "description": "流程提交时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "任务详情页链接"
    }
  ]
}', 
'workflow', 'site,wework', 1);
```

### 4.2 workflow_task_approved（审批结果通知）

```sql
INSERT INTO `notice_template` 
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`) 
VALUES 
('workflow_task_approved', '工作流审批结果通知', '您的申请已审批完成：${流程标题}', 
'您的申请已审批完成

流程标题：${流程标题}
审批结果：${审批结果}
审批人：${审批人}
审批时间：${审批时间}
审批意见：${审批意见}

详情链接：${详情链接}', 
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "审批结果",
      "code": "审批结果",
      "field": "result",
      "required": true,
      "description": "审批结果：已通过/已驳回"
    },
    {
      "name": "审批人",
      "code": "审批人",
      "field": "approver_name",
      "required": true,
      "description": "审批人姓名"
    },
    {
      "name": "审批时间",
      "code": "审批时间",
      "field": "approve_time",
      "required": true,
      "description": "审批完成时间"
    },
    {
      "name": "审批意见",
      "code": "审批意见",
      "field": "opinion",
      "required": false,
      "description": "审批人填写的意见"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}', 
'workflow', 'site,email', 1);
```

### 4.3 workflow_task_cc（抄送通知）

```sql
INSERT INTO `notice_template` 
(`code`, `name`, `title`, `content`, `variables_config`, `module_code`, `send_channels`, `status`) 
VALUES 
('workflow_task_cc', '工作流抄送通知', '您收到一个抄送：${流程标题}', 
'您收到一个抄送通知

流程标题：${流程标题}
提交人：${提交人}
当前环节：${节点名称}
抄送时间：${抄送时间}

请知悉。

详情链接：${详情链接}', 
'{
  "variables": [
    {
      "name": "流程标题",
      "code": "流程标题",
      "field": "title",
      "required": true,
      "description": "工作流程标题"
    },
    {
      "name": "提交人",
      "code": "提交人",
      "field": "submitter_name",
      "required": true,
      "description": "流程提交人姓名"
    },
    {
      "name": "节点名称",
      "code": "节点名称",
      "field": "node_name",
      "required": true,
      "description": "抄送节点名称"
    },
    {
      "name": "抄送时间",
      "code": "抄送时间",
      "field": "cc_time",
      "required": true,
      "description": "抄送时间"
    },
    {
      "name": "详情链接",
      "code": "详情链接",
      "field": "detail_url",
      "required": false,
      "description": "流程详情页链接"
    }
  ]
}', 
'workflow', 'site,wework', 1);
```

## 五、变量数据传递规范

### 5.1 审批任务通知数据结构

```php
$variables = [
    'title'          => $instance['title'],           // 流程标题
    'task_name'      => $node['nodeName'],           // 任务名称
    'submitter_name' => $instance['submitter_name'], // 提交人
    'created_at'     => $instance['created_at'],     // 提交时间
    'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id']
];
```

### 5.2 审批结果通知数据结构

```php
$variables = [
    'title'         => $instance['title'],                    // 流程标题
    'result'        => $isApproved ? '已通过' : '已驳回',      // 审批结果
    'approver_name' => $approverName,                        // 审批人
    'approve_time'  => date('Y-m-d H:i:s'),                 // 审批时间
    'opinion'       => $opinion ?? '',                       // 审批意见
    'detail_url'    => '/workflow/detail?id=' . $instance['id']
];
```

### 5.3 抄送通知数据结构

```php
$variables = [
    'title'          => $instance['title'],           // 流程标题
    'submitter_name' => $instance['submitter_name'], // 提交人
    'node_name'      => $node['nodeName'],           // 节点名称
    'cc_time'        => date('Y-m-d H:i:s'),         // 抄送时间
    'detail_url'     => '/workflow/task/detail?id=' . $taskId
];
```

## 六、实施步骤

### 6.1 数据库更新

1. 执行模板创建/更新SQL脚本
2. 验证模板配置正确性
3. 测试变量提取功能

### 6.2 代码适配

1. 修改工作流引擎中的消息发送逻辑
2. 确保传递的数据结构与模板变量配置匹配
3. 添加必要的异常处理

### 6.3 测试验证

1. 创建测试工作流实例
2. 验证各种通知场景
3. 检查消息内容的正确性

## 七、扩展性设计

### 7.1 业务模块扩展

其他业务模块可以参考此设计，创建统一的通知模板：

- `crm_customer_create`：CRM客户创建通知
- `approval_expense_submit`：费用报销提交通知
- `inventory_stock_warning`：库存预警通知

### 7.2 模板变量扩展

根据业务需要，可以扩展更多变量：

```json
{
  "name": "紧急程度",
  "code": "紧急程度",
  "field": "urgency_level",
  "required": false,
  "description": "任务紧急程度：普通/紧急/特急"
}
```

## 八、最佳实践

### 8.1 变量命名建议

1. **使用业务术语**：如"客户名称"而非"customer_name"
2. **保持简洁**：避免过长的变量名
3. **统一格式**：同类变量使用相同的命名模式

### 8.2 内容编写建议

1. **结构清晰**：使用换行和分段提高可读性
2. **信息完整**：包含用户关心的关键信息
3. **行动指引**：明确告知用户需要采取的行动

### 8.3 维护建议

1. **版本控制**：对模板变更进行版本管理
2. **测试验证**：每次修改后进行充分测试
3. **文档更新**：及时更新相关文档

## 九、总结

通过统一的审批通知模板设计，实现了：

1. **用户友好**：中文变量名直观易懂
2. **维护简便**：统一的模板结构便于管理
3. **扩展灵活**：支持多种业务场景的扩展
4. **体验一致**：所有审批通知保持统一的风格

这套设计方案为整个系统的消息通知功能提供了坚实的基础，确保了良好的用户体验和系统的可维护性。
