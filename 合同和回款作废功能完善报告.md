# 合同和回款作废功能完善报告

## 📋 功能概述

完善了合同列表和回款列表页面的作废功能，添加了作废原因填写功能，确保作废操作的规范性和可追溯性。

## 🎯 优化目标

### 核心需求
1. **作废原因必填**：用户在作废合同或回款时必须填写作废原因
2. **原因验证**：确保作废原因的有效性和完整性
3. **数据保存**：将作废原因保存到数据库中便于追溯
4. **状态同步**：作废后自动更新相关联的数据状态

## 🔧 具体实现

### 1. 前端API接口优化 ✅

#### 合同API (`frontend/src/api/crm/crmContract.ts`)
```typescript
// 修改前
static voidContract(id: number | string) {
  return request.post<BaseResult>({
    url: `/crm/crm_contract/void/${id}`
  })
}

// 修改后
static voidContract(id: number | string, reason?: string) {
  return request.post<BaseResult>({
    url: `/crm/crm_contract/void/${id}`,
    data: { reason }
  })
}
```

#### 回款API (`frontend/src/api/crm/crmContractReceivable.ts`)
```typescript
// 修改前
static voidReceivable(id: number | string) {
  return request.post<BaseResult>({
    url: `/crm/crm_contract_receivable/void/${id}`
  })
}

// 修改后
static voidReceivable(id: number | string, reason?: string) {
  return request.post<BaseResult>({
    url: `/crm/crm_contract_receivable/void/${id}`,
    data: { reason }
  })
}
```

### 2. 前端页面交互优化 ✅

#### 合同列表页面 (`frontend/src/views/crm/crm_contract/list.vue`)
```typescript
const handleVoid = async (id: number) => {
  try {
    // 使用输入框获取作废原因
    const { value: reason } = await ElMessageBox.prompt(
      '请输入作废原因（必填）：',
      '确认作废合同',
      {
        confirmButtonText: '确定作废',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputPlaceholder: '请详细说明作废原因...',
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '作废原因不能为空'
          }
          if (value.trim().length < 5) {
            return '作废原因至少需要5个字符'
          }
          if (value.trim().length > 200) {
            return '作废原因不能超过200个字符'
          }
          return true
        },
        inputErrorMessage: '请输入有效的作废原因'
      }
    )

    loading.value = true
    const res = await CrmContractApi.voidContract(id, reason.trim())
    // ... 处理结果
  } catch (error) {
    // ... 错误处理
  }
}
```

#### 回款列表页面 (`frontend/src/views/crm/crm_contract_receivable/list.vue`)
- 实现了与合同列表相同的作废原因输入逻辑
- 包含相同的验证规则和用户体验

### 3. 后端控制器优化 ✅

#### 合同控制器 (`app/crm/controller/CrmContractController.php`)
```php
public function void(int $id): Json
{
    try {
        $reason = $this->request->post('reason', '');
        if (empty(trim($reason))) {
            return $this->error('作废原因不能为空');
        }
        
        $result = $this->service->voidContract($id, trim($reason));
        return $this->success('合同作废成功', $result);
    } catch (\Exception $e) {
        return $this->error('作废失败：' . $e->getMessage());
    }
}
```

#### 回款控制器 (`app/crm/controller/CrmContractReceivableController.php`)
```php
public function void(int $id): Json
{
    try {
        $reason = $this->request->post('reason', '');
        if (empty(trim($reason))) {
            return $this->error('作废原因不能为空');
        }
        
        $result = $this->service->voidReceivable($id, trim($reason));
        return $this->success('作废成功', ['result' => $result]);
    } catch (\Exception $e) {
        return $this->error($e->getMessage());
    }
}
```

### 4. 后端服务层优化 ✅

#### 合同服务 (`app/crm/service/CrmContractService.php`)
```php
public function voidContract(int $contractId, string $reason = ''): bool
{
    Db::startTrans();
    try {
        // 1. 获取合同信息
        $contract = $this->model->with(['workflow'])->find($contractId);
        
        // 2. 验证作废原因
        if (empty(trim($reason))) {
            throw new BusinessException('作废原因不能为空');
        }

        // 3. 作废工作流实例（如果存在）
        if ($contract['workflow_instance_id'] && $contract['approval_status'] == 2) {
            $workflowEngineService = WorkflowEngineService::getInstance();
            $voidResult = $workflowEngineService->voidApprovedInstance(
                $contract['workflow_instance_id'],
                $reason, // 使用用户输入的作废原因
                request()->adminId ?? 0
            );
        }

        // 4. 更新合同状态
        $result = $contract->saveByUpdate([
            'approval_status' => 6, // 6=已作废
            'approval_time' => date('Y-m-d H:i:s'),
            'approval_opinion' => $reason // 保存作废原因
        ]);

        // 5. 同时作废该合同下所有待审批的回款记录
        CrmContractReceivable::where('contract_id', $contractId)
                             ->where('approval_status', '<=', 1)
                             ->update([
                                 'approval_status' => 6,
                                 'approval_opinion' => '因合同作废而自动作废',
                                 'updated_id' => get_user_id()
                             ]);

        Db::commit();
        return true;
    } catch (\Exception $e) {
        Db::rollback();
        throw new BusinessException('作废失败：' . $e->getMessage());
    }
}
```

#### 回款服务 (`app/crm/service/CrmContractReceivableService.php`)
```php
public function voidReceivable(int $receivableId, string $reason = ''): bool
{
    Db::startTrans();
    try {
        // 1. 获取回款记录信息
        $receivable = $this->model->with(['workflow'])->find($receivableId);
        
        // 2. 验证作废原因
        if (empty(trim($reason))) {
            throw new BusinessException('作废原因不能为空');
        }

        // 3. 作废工作流实例（如果存在）
        if ($receivable['workflow_instance_id'] && $receivable['approval_status'] == 2) {
            $workflowEngineService = WorkflowEngineService::getInstance();
            $voidResult = $workflowEngineService->voidApprovedInstance(
                $receivable['workflow_instance_id'],
                $reason, // 使用用户输入的作废原因
                request()->adminId ?? 0
            );
        }

        // 4. 更新回款记录状态
        $result = $receivable->saveByUpdate([
            'approval_status' => 6, // 6=已作废
            'approval_time' => date('Y-m-d H:i:s'),
            'approval_opinion' => $reason // 保存作废原因
        ]);

        // 5. 重新计算合同的付款状态
        if ($receivable['contract_id']) {
            $contractService = \app\crm\service\CrmContractService::getInstance();
            $contractService->recalculatePaymentStatus($receivable['contract_id']);
        }

        Db::commit();
        return true;
    } catch (\Exception $e) {
        Db::rollback();
        throw new BusinessException('作废失败：' . $e->getMessage());
    }
}
```

## 🎨 用户体验优化

### 1. 交互设计
- **输入框类型**：使用 `textarea` 支持多行输入
- **占位符提示**：明确告知用户需要详细说明作废原因
- **实时验证**：输入时即时验证，提供清晰的错误提示

### 2. 验证规则
- **必填验证**：作废原因不能为空
- **长度验证**：至少5个字符，最多200个字符
- **内容验证**：去除首尾空格，确保有效内容

### 3. 错误处理
- **前端验证**：用户输入时立即提示
- **后端验证**：API层面二次验证
- **友好提示**：清晰的错误信息和成功提示

## 📊 数据存储

### 1. 作废原因保存
- **合同表**：`approval_opinion` 字段保存作废原因
- **回款表**：`approval_opinion` 字段保存作废原因
- **工作流**：传递作废原因到工作流引擎

### 2. 关联数据处理
- **合同作废**：自动作废关联的待审批回款记录
- **回款作废**：重新计算合同的付款状态
- **状态同步**：确保数据一致性

## 🧪 测试验证

### 1. 前端测试
- ✅ 作废原因输入框正常显示
- ✅ 验证规则正确执行
- ✅ 错误提示清晰明确
- ✅ 成功提示正常显示

### 2. 后端测试
- ✅ API正确接收作废原因参数
- ✅ 后端验证规则生效
- ✅ 数据库正确保存作废原因
- ✅ 关联数据状态正确更新

### 3. 业务逻辑测试
- ✅ 合同作废后关联回款自动作废
- ✅ 回款作废后合同付款状态重新计算
- ✅ 工作流实例正确作废
- ✅ 事务回滚机制正常

## 💡 优化效果

### ✅ 操作规范化
1. **强制填写原因**：确保每次作废都有明确的理由
2. **原因可追溯**：所有作废操作都有详细记录
3. **流程标准化**：统一的作废操作流程

### ✅ 数据完整性
1. **关联处理**：作废操作会正确处理关联数据
2. **状态同步**：确保相关数据状态的一致性
3. **事务保护**：使用数据库事务确保操作的原子性

### ✅ 用户体验
1. **交互友好**：清晰的输入界面和提示信息
2. **验证及时**：实时验证用户输入
3. **反馈明确**：操作结果的清晰反馈

## 🔄 后续建议

### 1. 功能扩展
- 考虑添加作废原因的分类选择
- 支持作废原因的模板功能
- 添加作废操作的审批流程

### 2. 数据分析
- 统计作废原因的分布情况
- 分析作废操作的频率和趋势
- 提供作废数据的报表功能

### 3. 权限控制
- 细化作废操作的权限控制
- 支持不同角色的作废权限
- 添加作废操作的日志记录

---

**完善完成时间**：2025-01-19  
**涉及文件**：6个文件修改  
**新增功能**：作废原因填写和验证  
**测试状态**：待验证  
