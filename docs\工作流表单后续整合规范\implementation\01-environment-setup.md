# 环境配置与依赖

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 环境要求

### 系统要求
- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**：v16.14.0 或更高版本
- **npm**：v8.0.0 或更高版本
- **PHP**：v8.1 或更高版本
- **MySQL**：v8.0 或更高版本
- **Redis**：v6.0 或更高版本（可选，用于缓存）

### 开发工具推荐
- **IDE**：VS Code, PhpStorm, WebStorm
- **浏览器**：Chrome 90+, Firefox 88+, Safari 14+
- **版本控制**：Git 2.30+
- **API测试**：Postman, Insomnia

## 📦 依赖包管理

### 前端依赖

#### 核心依赖

```json
{
  "dependencies": {
    "vue": "^3.4.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.4.0",
    "@element-plus/icons-vue": "^2.3.0",
    "axios": "^1.6.0",
    "dayjs": "^1.11.0",
    "lodash-es": "^4.17.0",
    "@vueuse/core": "^10.5.0"
  }
}
```

#### Form-Create相关依赖

```json
{
  "dependencies": {
    "@form-create/element-ui": "^3.2.0",
    "@form-create/designer": "^3.2.11",
    "epic-designer": "^1.0.1"
  }
}
```

#### 开发依赖

```json
{
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "@vue/test-utils": "^2.4.0",
    "vitest": "^1.0.0",
    "typescript": "^5.2.0",
    "vue-tsc": "^1.8.0",
    "eslint": "^8.54.0",
    "@typescript-eslint/eslint-plugin": "^6.12.0",
    "@typescript-eslint/parser": "^6.12.0",
    "eslint-plugin-vue": "^9.18.0",
    "prettier": "^3.1.0",
    "sass": "^1.69.0",
    "vite": "^5.0.0"
  }
}
```

### 后端依赖

#### Composer依赖

```json
{
  "require": {
    "php": ">=8.1",
    "topthink/framework": "^8.0",
    "topthink/think-orm": "^3.0",
    "topthink/think-multi-app": "^1.0",
    "topthink/think-view": "^2.0",
    "topthink/think-captcha": "^3.0",
    "firebase/php-jwt": "^6.0",
    "phpoffice/phpspreadsheet": "^1.29",
    "intervention/image": "^2.7"
  },
  "require-dev": {
    "phpunit/phpunit": "^10.0",
    "mockery/mockery": "^1.6",
    "symfony/var-dumper": "^6.0"
  }
}
```

## 🔧 环境配置

### 前端环境配置

#### 1. 安装Node.js和npm

```bash
# 检查版本
node --version  # 应该 >= 16.14.0
npm --version   # 应该 >= 8.0.0

# 如果版本过低，请升级
npm install -g npm@latest
```

#### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd base_admin

# 安装前端依赖
cd frontend
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install
```

#### 3. 环境变量配置

创建 `frontend/.env.local` 文件：

```bash
# 开发环境配置
VITE_APP_TITLE=统一表单管理系统
VITE_APP_ENV=development
VITE_APP_BASE_API=http://localhost:8000
VITE_APP_UPLOAD_URL=http://localhost:8000/upload

# API配置
VITE_API_TIMEOUT=10000
VITE_API_RETRY_COUNT=3

# 调试配置
VITE_APP_DEBUG=true
VITE_APP_MOCK=false

# Form-Create配置
VITE_FORM_CREATE_VERSION=3.2.0
VITE_FORM_CREATE_THEME=element-ui
```

#### 4. TypeScript配置

`frontend/tsconfig.json`：

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/views/*": ["src/views/*"],
      "@/api/*": ["src/api/*"],
      "@/utils/*": ["src/utils/*"],
      "@/stores/*": ["src/stores/*"],
      "@/types/*": ["src/types/*"]
    }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

#### 5. Vite配置

`frontend/vite.config.ts`：

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/views': resolve(__dirname, 'src/views'),
      '@/api': resolve(__dirname, 'src/api'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/types': resolve(__dirname, 'src/types')
    }
  },
  server: {
    host: '0.0.0.0',
    port: 3006,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]'
      }
    }
  }
})
```

### 后端环境配置

#### 1. PHP环境检查

```bash
# 检查PHP版本
php --version  # 应该 >= 8.1

# 检查必要扩展
php -m | grep -E "(pdo|mysql|json|mbstring|openssl|fileinfo|curl)"
```

#### 2. 安装Composer

```bash
# 下载并安装Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# 检查版本
composer --version
```

#### 3. 项目依赖安装

```bash
# 进入后端目录
cd backend

# 安装依赖
composer install

# 生成自动加载文件
composer dump-autoload
```

#### 4. 环境变量配置

创建 `backend/.env` 文件：

```bash
# 应用配置
APP_DEBUG=true
APP_TRACE=false

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=base_admin
DATABASE_USERNAME=root
DATABASE_PASSWORD=
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4
DATABASE_PREFIX=

# Redis配置（可选）
REDIS_HOSTNAME=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_TTL=7200

# 文件上传配置
UPLOAD_PATH=uploads
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_EXT=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 邮件配置
MAIL_TYPE=smtp
MAIL_SMTP_HOST=smtp.example.com
MAIL_SMTP_PORT=587
MAIL_SMTP_USER=<EMAIL>
MAIL_SMTP_PASS=your-password

# 工作流配置
WORKFLOW_ENGINE=simple
WORKFLOW_TIMEOUT=3600
```

#### 5. 数据库配置

创建数据库配置文件 `backend/config/database.php`：

```php
<?php
return [
    // 默认数据库连接
    'default' => env('DATABASE_TYPE', 'mysql'),
    
    // 数据库连接配置
    'connections' => [
        'mysql' => [
            'type' => 'mysql',
            'hostname' => env('DATABASE_HOSTNAME', '127.0.0.1'),
            'database' => env('DATABASE_DATABASE', 'base_admin'),
            'username' => env('DATABASE_USERNAME', 'root'),
            'password' => env('DATABASE_PASSWORD', ''),
            'hostport' => env('DATABASE_HOSTPORT', '3306'),
            'charset' => env('DATABASE_CHARSET', 'utf8mb4'),
            'prefix' => env('DATABASE_PREFIX', ''),
            'deploy' => 0,
            'rw_separate' => false,
            'master_num' => 1,
            'slave_no' => '',
            'fields_strict' => true,
            'break_reconnect' => false,
            'fields_cache' => false,
        ],
    ],
];
```

## 🗄️ 数据库初始化

### 1. 创建数据库

```sql
-- 创建数据库
CREATE DATABASE base_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'base_admin'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON base_admin.* TO 'base_admin'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 导入基础表结构

```bash
# 导入数据库结构
mysql -u root -p base_admin < database/structure.sql

# 导入初始数据
mysql -u root -p base_admin < database/data.sql
```

### 3. 运行数据库迁移

```bash
# 进入后端目录
cd backend

# 运行迁移
php think migrate:run

# 填充测试数据（可选）
php think seed:run
```

## 🚀 启动项目

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 启动内置服务器（开发环境）
php think run -H 0.0.0.0 -p 8000

# 或使用Apache/Nginx（生产环境）
```

### 2. 启动前端服务

```bash
# 进入前端目录
cd frontend

# 启动开发服务器
npm run dev

# 或
yarn dev

# 或
pnpm dev
```

### 3. 访问应用

- **前端地址**：http://localhost:3006
- **后端地址**：http://localhost:8000
- **API文档**：http://localhost:8000/docs

## 🔧 开发工具配置

### VS Code配置

#### 1. 推荐扩展

创建 `.vscode/extensions.json`：

```json
{
  "recommendations": [
    "vue.volar",
    "vue.typescript-vue-plugin",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

#### 2. 工作区配置

创建 `.vscode/settings.json`：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "vue.codeActions.enabled": true,
  "vue.complete.casing.tags": "pascal",
  "vue.complete.casing.props": "camel",
  "files.associations": {
    "*.vue": "vue"
  },
  "emmet.includeLanguages": {
    "vue-html": "html"
  }
}
```

### ESLint配置

创建 `frontend/.eslintrc.js`：

```javascript
module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/typescript/recommended',
    'plugin:vue/vue3-recommended',
    'prettier'
  ],
  parserOptions: {
    ecmaVersion: 2021,
    parser: '@typescript-eslint/parser',
    sourceType: 'module'
  },
  plugins: ['vue', '@typescript-eslint'],
  rules: {
    // Vue规则
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase'],
    'vue/event-name-casing': ['error', 'camelCase'],
    'vue/multi-word-component-names': 'off',
    
    // TypeScript规则
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'off',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  }
}
```

### Prettier配置

创建 `frontend/.prettierrc.js`：

```javascript
module.exports = {
  semi: false,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'none',
  printWidth: 100,
  bracketSpacing: true,
  arrowParens: 'avoid',
  vueIndentScriptAndStyle: true,
  endOfLine: 'lf'
}
```

## 🧪 测试环境配置

### 前端测试配置

#### 1. Vitest配置

创建 `frontend/vitest.config.ts`：

```typescript
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.ts']
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
})
```

#### 2. 测试设置文件

创建 `frontend/src/test/setup.ts`：

```typescript
import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'

// 全局组件配置
config.global.plugins = [ElementPlus]

// 模拟全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})
```

### 后端测试配置

#### 1. PHPUnit配置

创建 `backend/phpunit.xml`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.0/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DATABASE_DATABASE" value="base_admin_test"/>
    </php>
</phpunit>
```

## 📊 性能监控配置

### 前端性能监控

```typescript
// src/utils/performance.ts
class PerformanceMonitor {
  static init() {
    // 页面加载性能监控
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      
      console.log('页面性能指标:', {
        DNS解析: navigation.domainLookupEnd - navigation.domainLookupStart,
        TCP连接: navigation.connectEnd - navigation.connectStart,
        请求响应: navigation.responseEnd - navigation.requestStart,
        DOM解析: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        页面加载: navigation.loadEventEnd - navigation.navigationStart
      })
    })
    
    // 资源加载监控
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 1000) {
          console.warn('慢资源:', entry.name, entry.duration)
        }
      })
    }).observe({ entryTypes: ['resource'] })
  }
}

// 在main.ts中初始化
PerformanceMonitor.init()
```

### 后端性能监控

```php
<?php
// app/middleware/PerformanceMonitor.php
namespace app\middleware;

class PerformanceMonitor
{
    public function handle($request, \Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $executionTime = ($endTime - $startTime) * 1000; // 毫秒
        $memoryUsage = $endMemory - $startMemory;
        
        // 记录性能日志
        if ($executionTime > 1000) { // 超过1秒的请求
            \think\facade\Log::warning('慢请求', [
                'url' => $request->url(),
                'method' => $request->method(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage
            ]);
        }
        
        return $response;
    }
}
```

## 🔒 安全配置

### HTTPS配置（生产环境）

```nginx
# Nginx配置示例
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 前端静态文件
    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 环境变量安全

```bash
# 生产环境.env文件权限设置
chmod 600 .env

# 确保.env文件不被版本控制
echo ".env" >> .gitignore
```

---

**注意：** 请根据实际部署环境调整配置参数，确保安全性和性能。