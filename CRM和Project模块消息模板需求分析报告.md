# CRM和Project模块消息模板需求分析报告

## 📊 分析概述

**分析时间**: 2025-07-16  
**分析目标**: 基于实际业务需求分析CRM和Project模块所需的消息模板  
**分析方法**: 代码分析 + 数据库结构分析 + 业务场景梳理

## 🔍 CRM模块消息模板需求分析

### 1. 现有模板分析

根据数据库分析，CRM模块已有5个模板，但**全部使用英文变量名**，需要修复：

| 模板编码 | 模板名称 | 当前状态 | 业务场景 |
|---------|----------|----------|----------|
| `crm_lead_convert` | 线索转化通知 | ❌ 英文变量 | 线索转化为客户时通知 |
| `crm_customer_assign` | 客户分配通知 | ❌ 英文变量 | 客户分配给销售时通知 |
| `crm_business_stage_change` | 商机阶段变更通知 | ❌ 英文变量 | 商机阶段变化时通知 |
| `crm_quotation_create` | 报价单创建通知 | ❌ 英文变量 | 创建报价单时通知 |
| `crm_contract_approval` | 合同审批通知 | ❌ 英文变量 | 合同提交审批时通知 |

### 2. 基于数据库结构的新增模板需求

#### 2.1 线索管理相关
- **`crm_lead_assign`** - 线索分配通知
- **`crm_lead_follow_remind`** - 线索跟进提醒
- **`crm_lead_pool_recycle`** - 线索回收到公海通知

#### 2.2 客户管理相关
- **`crm_customer_follow_remind`** - 客户跟进提醒
- **`crm_customer_share_add`** - 客户共享添加通知
- **`crm_customer_share_remove`** - 客户共享取消通知
- **`crm_customer_sea_recycle`** - 客户回收到公海通知

#### 2.3 合同管理相关
- **`crm_contract_approval_result`** - 合同审批结果通知
- **`crm_contract_payment_remind`** - 合同付款提醒
- **`crm_contract_expire_remind`** - 合同到期提醒

#### 2.4 回款管理相关
- **`crm_receivable_approval_submit`** - 回款审批提交通知
- **`crm_receivable_approval_result`** - 回款审批结果通知
- **`crm_receivable_overdue_remind`** - 回款逾期提醒

#### 2.5 跟进记录相关
- **`crm_follow_record_add`** - 新增跟进记录通知
- **`crm_follow_plan_remind`** - 跟进计划提醒

### 3. CRM模块代码实现分析

根据 `CrmNotificationService.php` 分析，已实现的通知功能：

```php
// 已实现的通知方法
- sendLeadConvertNotification()           // 线索转化通知 ✅
- sendBusinessConvertNotification()       // 商机转化通知 ✅
- sendCustomerAssignNotification()        // 客户分配通知 ✅
- sendBusinessStageChangeNotification()   // 商机阶段变更通知 ✅
- sendContractApprovalNotification()      // 合同审批通知 ✅
- sendQuotationApprovalSubmitNotification() // 报价单审批提交通知 ✅
- sendContractApprovalSubmitNotification()  // 合同审批提交通知 ✅

// 待实现的通知方法
- sendReceivableApprovalSubmitNotification() // 回款审批提交通知 ❌ TODO
```

## 🔍 Project模块消息模板需求分析

### 1. 数据库结构分析

基于 `simple_database.sql` 分析，Project模块包含：

| 表名 | 功能 | 消息通知场景 |
|------|------|-------------|
| `project_project` | 项目管理 | 项目创建、状态变更、成员变更 |
| `project_member` | 项目成员 | 成员加入、角色变更、成员移除 |
| `project_task` | 任务管理 | 任务分配、状态变更、逾期提醒 |
| `project_task_comment` | 任务评论 | 新增评论、@提醒 |

### 2. Project模块消息模板需求

#### 2.1 项目管理相关
- **`project_create`** - 项目创建通知
- **`project_status_change`** - 项目状态变更通知
- **`project_member_add`** - 项目成员添加通知
- **`project_member_remove`** - 项目成员移除通知
- **`project_deadline_remind`** - 项目截止日期提醒
- **`project_progress_update`** - 项目进度更新通知

#### 2.2 任务管理相关
- **`project_task_assign`** - 任务分配通知
- **`project_task_status_change`** - 任务状态变更通知
- **`project_task_deadline_remind`** - 任务截止日期提醒
- **`project_task_overdue`** - 任务逾期通知
- **`project_task_comment_add`** - 任务评论添加通知
- **`project_task_mention`** - 任务@提醒通知

#### 2.3 协作相关
- **`project_member_role_change`** - 成员角色变更通知
- **`project_workload_remind`** - 工作负载提醒
- **`project_milestone_complete`** - 里程碑完成通知

### 3. Project模块代码实现分析

根据现有代码分析，Project模块的通知功能**尚未实现**：

```php
// ProjectTask.php 中的关键方法
- updateStatus()  // 更新任务状态 - 需要添加通知
- assignTo()      // 分配任务 - 需要添加通知
- isOverdue()     // 检查逾期 - 需要添加通知

// ProjectTaskService.php 中的关键方法  
- assignTask()    // 分配任务 - 需要添加通知
```

## 📋 消息模板变量规范

### CRM模块标准变量

| 变量名 | 使用场景 | 示例值 |
|--------|----------|--------|
| `线索名称` | 线索相关 | "张三的咨询" |
| `客户名称` | 客户相关 | "上海某某公司" |
| `客户电话` | 客户联系 | "13800138000" |
| `商机名称` | 商机相关 | "张三的采购需求" |
| `原阶段` | 阶段变更 | "初步接触" |
| `新阶段` | 阶段变更 | "需求确认" |
| `变更原因` | 阶段变更 | "客户明确采购意向" |
| `合同编号` | 合同相关 | "CT202507160001" |
| `合同金额` | 合同相关 | "100000.00" |
| `报价单编号` | 报价单相关 | "QT202507160001" |
| `最终金额` | 报价单相关 | "50000.00" |
| `分配人` | 分配操作 | "李四" |
| `分配时间` | 分配操作 | "2025-07-16 15:30:00" |
| `跟进方式` | 跟进记录 | "电话" |
| `跟进内容` | 跟进记录 | "客户表示有采购意向" |
| `下次跟进时间` | 跟进计划 | "2025-07-18 09:00:00" |

### Project模块标准变量

| 变量名 | 使用场景 | 示例值 |
|--------|----------|--------|
| `项目名称` | 项目相关 | "官网改版项目" |
| `项目描述` | 项目相关 | "公司官网UI改版升级" |
| `项目状态` | 状态变更 | "进行中" |
| `项目负责人` | 项目管理 | "张三" |
| `任务标题` | 任务相关 | "首页设计稿制作" |
| `任务描述` | 任务相关 | "设计首页UI界面" |
| `任务状态` | 任务状态 | "进行中" |
| `任务负责人` | 任务分配 | "李四" |
| `原负责人` | 任务转移 | "王五" |
| `新负责人` | 任务转移 | "李四" |
| `截止日期` | 时间提醒 | "2025-07-20" |
| `逾期天数` | 逾期提醒 | "3" |
| `项目进度` | 进度更新 | "75%" |
| `成员姓名` | 成员管理 | "张三" |
| `成员角色` | 角色变更 | "开发者" |
| `评论内容` | 评论通知 | "这个设计很不错" |
| `提及用户` | @提醒 | "李四" |

## 🎯 优先级建议

### 🔴 高优先级 (立即实施)

#### CRM模块
1. **修复现有5个模板** - 统一使用中文变量名
2. **补充核心业务模板**:
   - `crm_lead_assign` - 线索分配通知
   - `crm_customer_follow_remind` - 客户跟进提醒
   - `crm_contract_approval_result` - 合同审批结果通知
   - `crm_receivable_approval_submit` - 回款审批提交通知

#### Project模块
1. **实现核心通知功能**:
   - `project_task_assign` - 任务分配通知
   - `project_task_status_change` - 任务状态变更通知
   - `project_task_deadline_remind` - 任务截止提醒
   - `project_member_add` - 项目成员添加通知

### 🟡 中优先级 (计划实施)

#### CRM模块
- 客户共享相关通知
- 跟进记录相关通知
- 公海回收相关通知

#### Project模块
- 项目进度更新通知
- 任务评论相关通知
- 工作负载提醒

### 🟢 低优先级 (后续完善)

#### CRM模块
- 数据统计相关通知
- 工作报告相关通知

#### Project模块
- 里程碑相关通知
- 高级协作功能通知

## 📊 实施建议

### 1. 分阶段实施
- **第一阶段**: 修复现有CRM模板 + 实现Project核心通知
- **第二阶段**: 补充CRM核心业务模板
- **第三阶段**: 完善所有模块的通知功能

### 2. 技术实施
- 统一使用中文变量名规范
- 建立模板变量标准词典
- 实现通知服务的统一调用
- 添加完整的测试验证

### 3. 质量保证
- 每个模板都要经过测试验证
- 确保变量替换正确
- 验证用户接收体验
- 建立监控和日志机制
