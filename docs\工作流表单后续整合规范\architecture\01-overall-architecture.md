# 统一表单架构总体设计

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 设计目标

### 核心目标
1. **统一性**：所有表单组件遵循统一的设计模式和接口规范
2. **复用性**：申请表单和详情表单可在多个场景复用
3. **扩展性**：支持静态表单、动态表单、Form-Create表单等多种形式
4. **维护性**：零配置的组件映射，降低维护成本
5. **性能**：高效的组件加载和渲染机制

### 业务价值
- **开发效率提升60%**：通过组件复用和统一规范
- **维护成本降低50%**：零配置的组件映射机制
- **用户体验一致性**：统一的表单交互和展示风格
- **快速业务响应**：支持可视化表单设计，业务人员可自助

## 🏗️ 整体架构

### 架构分层图

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        A1[业务页面] --> A2[工作流页面]
        A2 --> A3[表单设计器]
    end
    
    subgraph "组件层 (Component Layer)"
        B1[FormManager<br/>表单管理器] --> B2[FormDataViewer<br/>详情查看器]
        B2 --> B3[FormDesigner<br/>表单设计器]
    end
    
    subgraph "渲染层 (Render Layer)"
        C1[静态表单组件<br/>business-forms/] --> C2[详情展示组件<br/>business-detail/]
        C2 --> C3[Form-Create渲染器<br/>form-create-renderer]
        C3 --> C4[动态表单渲染器<br/>dynamic-form]
    end
    
    subgraph "引擎层 (Engine Layer)"
        D1[组件映射引擎<br/>Component Mapping] --> D2[数据管理引擎<br/>Data Manager]
        D2 --> D3[验证引擎<br/>Validation Engine]
        D3 --> D4[渲染引擎<br/>Render Engine]
    end
    
    subgraph "数据层 (Data Layer)"
        E1[业务数据<br/>Business Data] --> E2[表单配置<br/>Form Config]
        E2 --> E3[工作流数据<br/>Workflow Data]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
```

### 核心组件关系图

```mermaid
graph LR
    subgraph "统一入口"
        FM[FormManager<br/>表单管理器]
        FDV[FormDataViewer<br/>详情查看器]
    end
    
    subgraph "组件映射"
        CM[ComponentMapping<br/>组件映射引擎]
    end
    
    subgraph "表单组件"
        BF[business-forms/<br/>申请表单]
        BD[business-detail/<br/>详情表单]
        FCR[form-create-renderer<br/>FC渲染器]
        DF[dynamic-form<br/>动态表单]
    end
    
    subgraph "数据源"
        BC[businessCode<br/>业务代码]
        FD[formData<br/>表单数据]
        FC[formConfig<br/>表单配置]
    end
    
    FM --> CM
    FDV --> CM
    CM --> BF
    CM --> BD
    CM --> FCR
    CM --> DF
    
    BC --> CM
    FD --> FM
    FD --> FDV
    FC --> FCR
    FC --> DF
```

## 🔄 组件映射机制

### 映射规则

| 业务代码模式 | 组件类型 | 映射路径 | 示例 |
|-------------|----------|----------|------|
| `crm_contract` | 静态业务表单 | `business-forms/${businessCode}.vue` | `business-forms/crm_contract.vue` |
| `fc_001` | Form-Create表单 | `form-create-renderer.vue` + 配置 | 动态加载配置 |
| `custom_form_001` | 自定义动态表单 | `dynamic-form.vue` + 配置 | 动态加载配置 |
| `hr_leave` | 通用业务表单 | `business-forms/${businessCode}.vue` | `business-forms/hr_leave.vue` |

### 映射算法

```typescript
interface ComponentMapping {
  component: AsyncComponent
  props?: Record<string, any>
  fallback?: AsyncComponent
}

async function getFormComponent(businessCode: string): Promise<ComponentMapping> {
  // Form-Create表单
  if (businessCode.startsWith('fc_')) {
    const formId = businessCode.replace('fc_', '')
    const formConfig = await CustomFormApi.getFormCreateConfig(formId)
    
    return {
      component: () => import('@/components/business-forms/form-create-renderer.vue'),
      props: { formConfig, formId: parseInt(formId) }
    }
  }
  
  // 自定义动态表单
  if (businessCode.startsWith('custom_form_')) {
    const formId = businessCode.replace('custom_form_', '')
    const formConfig = await CustomFormApi.getConfig(formId)
    
    return {
      component: () => import('@/components/business-forms/dynamic-form.vue'),
      props: { formConfig, formId: parseInt(formId) }
    }
  }
  
  // 静态业务表单
  return {
    component: () => import(`@/components/business-forms/${businessCode}.vue`),
    fallback: () => import('@/components/business-forms/generic-form.vue')
  }
}
```

## 📊 数据流架构

### 申请表单数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant FM as FormManager
    participant BC as BusinessComponent
    participant API as 后端API
    participant WF as 工作流引擎
    
    U->>FM: 打开申请表单
    FM->>BC: 加载业务组件
    BC->>U: 显示表单界面
    
    U->>BC: 填写表单数据
    BC->>BC: 本地验证
    
    U->>BC: 提交申请
    BC->>FM: 触发提交事件
    FM->>API: 调用提交接口
    API->>WF: 启动工作流
    WF->>API: 返回实例ID
    API->>FM: 返回提交结果
    FM->>U: 显示提交成功
```

### 详情查看数据流

```mermaid
sequenceDiagram
    participant U as 用户
    participant FDV as FormDataViewer
    participant DC as DetailComponent
    participant API as 后端API
    
    U->>FDV: 查看详情
    FDV->>API: 获取详情数据
    API->>FDV: 返回form_data
    FDV->>DC: 加载详情组件
    DC->>DC: 渲染详情数据
    DC->>U: 显示详情界面
```

## 🎨 组件设计模式

### 统一接口设计

```typescript
// 申请表单统一接口
interface BusinessFormComponent {
  // Props
  data?: any                    // 表单数据（编辑时）
  mode?: 'create' | 'edit'     // 表单模式
  definitionId?: number        // 工作流定义ID
  
  // Events
  onSubmit: (data: any) => void    // 提交申请
  onSave: (data: any) => void      // 保存草稿
  onCancel: () => void             // 取消操作
  
  // Methods
  showForm: (id?: number) => void  // 显示表单
  resetForm: () => void            // 重置表单
  validate: () => Promise<boolean> // 表单验证
}

// 详情组件统一接口
interface DetailComponent {
  // Props
  data: any                    // 详情数据
  businessCode?: string        // 业务代码
  
  // Methods
  refresh: () => void          // 刷新数据
  export: () => void           // 导出数据
}
```

### 组件生命周期

```mermaid
stateDiagram-v2
    [*] --> Loading: 组件初始化
    Loading --> Mounted: 组件挂载完成
    Mounted --> DataLoaded: 数据加载完成
    DataLoaded --> Ready: 组件就绪
    
    Ready --> Validating: 表单验证
    Validating --> Valid: 验证通过
    Validating --> Invalid: 验证失败
    
    Valid --> Submitting: 提交数据
    Submitting --> Success: 提交成功
    Submitting --> Error: 提交失败
    
    Invalid --> Ready: 修正错误
    Error --> Ready: 重新提交
    Success --> [*]: 组件销毁
```

## 🔧 扩展机制

### 插件化架构

```typescript
// 表单插件接口
interface FormPlugin {
  name: string
  version: string
  install: (app: App, options?: any) => void
  components?: Record<string, Component>
  validators?: Record<string, Function>
}

// 字段组件注册
class FieldRegistry {
  private fields = new Map<string, Component>()
  
  register(type: string, component: Component) {
    this.fields.set(type, component)
  }
  
  get(type: string): Component | undefined {
    return this.fields.get(type)
  }
  
  use(plugin: FormPlugin) {
    plugin.install(this.app, this.options)
    if (plugin.components) {
      Object.entries(plugin.components).forEach(([type, component]) => {
        this.register(type, component)
      })
    }
  }
}
```

### 自定义组件扩展

```vue
<!-- 自定义业务组件示例 -->
<template>
  <div class="custom-business-field">
    <el-select v-model="modelValue" @change="handleChange">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
// 注册为表单字段组件
defineOptions({
  name: 'CustomBusinessField'
})

interface Props {
  modelValue?: any
  options?: Array<{label: string, value: any}>
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'change': [value: any]
}>()

const handleChange = (value: any) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 注册到字段注册表
import { fieldRegistry } from '@/utils/form-engine'
fieldRegistry.register('custom-business-field', CustomBusinessField)
</script>
```

## 📈 性能优化策略

### 组件懒加载

```typescript
// 动态组件懒加载
const lazyComponent = defineAsyncComponent({
  loader: () => import('./heavy-component.vue'),
  loadingComponent: ComponentSkeleton,
  errorComponent: ComponentError,
  delay: 200,
  timeout: 3000
})
```

### 虚拟滚动

```vue
<!-- 大表单虚拟滚动 -->
<template>
  <virtual-list
    :data-key="'id'"
    :data-sources="formFields"
    :data-component="fieldComponent"
    :estimate-size="60"
    :buffer="10"
  />
</template>
```

### 缓存策略

```typescript
// 组件配置缓存
class ConfigCache {
  private cache = new Map<string, any>()
  private ttl = 5 * 60 * 1000 // 5分钟

  async get(key: string): Promise<any> {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data
    }
    
    // 缓存过期，重新获取
    const data = await this.fetch(key)
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
    return data
  }
}
```

## 🔒 安全考虑

### 权限控制

```typescript
// 字段级权限控制
interface FieldPermission {
  read: boolean
  write: boolean
  required: boolean
}

class PermissionManager {
  checkFieldPermission(
    fieldName: string, 
    userRole: string, 
    businessCode: string
  ): FieldPermission {
    // 权限检查逻辑
    return {
      read: this.hasReadPermission(fieldName, userRole, businessCode),
      write: this.hasWritePermission(fieldName, userRole, businessCode),
      required: this.isFieldRequired(fieldName, businessCode)
    }
  }
}
```

### 数据验证

```typescript
// 多层数据验证
class ValidationChain {
  private validators: Array<(data: any) => boolean> = []
  
  add(validator: (data: any) => boolean) {
    this.validators.push(validator)
    return this
  }
  
  validate(data: any): boolean {
    return this.validators.every(validator => validator(data))
  }
}
```

## 📊 监控与指标

### 性能指标

| 指标名称 | 目标值 | 监控方式 |
|---------|--------|----------|
| 组件加载时间 | < 200ms | Performance API |
| 表单渲染时间 | < 100ms | Custom Timing |
| 内存使用量 | < 50MB | Memory API |
| 组件缓存命中率 | > 80% | Custom Metrics |

### 错误监控

```typescript
// 错误边界组件
class FormErrorBoundary extends Vue {
  errorCaptured(err: Error, instance: ComponentPublicInstance, info: string) {
    // 错误上报
    this.reportError({
      error: err.message,
      component: instance.$options.name,
      info,
      timestamp: Date.now()
    })
    
    return false
  }
}
```

## 🔮 未来规划

### 短期目标（1-3个月）
- [ ] 完成Form-Create集成
- [ ] 实现基础的表单设计器
- [ ] 优化组件加载性能
- [ ] 完善文档和示例

### 中期目标（3-6个月）
- [ ] 支持复杂的表单联动
- [ ] 实现表单模板管理
- [ ] 添加更多自定义组件
- [ ] 完善权限控制系统

### 长期目标（6-12个月）
- [ ] 支持多租户表单隔离
- [ ] 实现表单版本管理
- [ ] 添加表单分析功能
- [ ] 支持移动端表单

---

**注意：** 本架构设计是活文档，会随着业务需求和技术发展持续演进。