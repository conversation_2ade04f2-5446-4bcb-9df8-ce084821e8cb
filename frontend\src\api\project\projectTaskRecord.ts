import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 任务记录表（评论+跟进）相关接口
 */
export class ProjectTaskRecordApi {
  /**
   * 获取任务记录表（评论+跟进）列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_task_record/index',
      params
    })
  }

  /**
   * 获取任务记录表（评论+跟进）详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/project/project_task_record/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加任务记录表（评论+跟进）
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task_record/add',
      data
    })
  }

  /**
   * 更新任务记录表（评论+跟进）
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/project_task_record/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除任务记录表（评论+跟进）
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_task_record/delete/${id}`
    })
  }

  /**
   * 批量删除任务记录表（评论+跟进）
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/project/project_task_record/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task_record/updateField',
      data
    })
  }

  

  


  /**
   * 导入任务记录表（评论+跟进）数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/project/project_task_record/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/project/project_task_record/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/project/project_task_record/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}