# 工作流引擎单例使用优化方案

## 🎯 分析目标

基于代码深入分析工作流引擎中单例模式的使用情况，识别数据污染风险，并提供基于模型直接实例化和静态方法调用的优化方案。

## 📊 现状分析

### 1. 单例使用情况统计

#### 1.1 WorkflowEngine.php 中的单例调用
- **WorkflowDefinitionService::getInstance()**: 2次调用（第39、123行）
- **WorkflowInstanceService::getInstance()**: 6次调用（第76、271、326、348、462、542、611行）
- **WorkflowTaskService::getInstance()**: 4次调用（第215、280、374、398、619行）
- **WorkflowHistoryService::getInstance()**: 3次调用（第487、575、647行）
- **NoticeDispatcherService::getInstance()**: 3次调用（第429、682、749行）

#### 1.2 节点处理器中的单例调用
- **ApprovalNodeHandler.php**: 13次单例调用
- **CcNodeHandler.php**: 1次单例调用（已优化）
- **AbstractNodeHandler.php**: 1次单例调用

#### 1.3 服务层的单例调用
- **WorkflowInstanceService.php**: 4次内部单例调用
- **WorkflowTaskService.php**: 2次内部单例调用

### 2. 数据污染风险评估

#### 2.1 高风险场景 ❌
```php
// WorkflowEngine.php 第280行 - 顺序审批模式
$taskService = WorkflowTaskService::getInstance();
$result = $taskService->createTask($instance, $currentNode, $nextApprover);
```

**风险分析**：
- 在顺序审批中，可能连续创建多个审批任务
- 使用同一个服务实例，存在模型状态污染风险

#### 2.2 中等风险场景 ⚠️
```php
// ApprovalNodeHandler.php 第132、222、335行
$taskService = WorkflowTaskService::getInstance();
// 在循环中可能多次调用
foreach ($approvers as $approver) {
    $taskService->createTask(...);
}
```

**风险分析**：
- 在"任意一人通过"、"所有人通过"、"顺序审批"模式下
- 可能在循环中连续创建多个任务
- 存在潜在的状态污染风险

#### 2.3 低风险场景 ✅
```php
// WorkflowEngine.php 第39行 - 单次查询
$definition = WorkflowDefinitionService::getInstance()
                ->getModel()
                ->where(['id' => $instance['definition_id']])
                ->findOrEmpty();
```

**风险分析**：
- 单次查询操作，无状态污染风险
- 但可以优化为直接模型调用

### 3. 当前CrudService优化状态

#### 3.1 已实现的优化 ✅
```php
// app/common/core/crud/traits/CrudOperationsTrait.php 第91-101行
protected function createFreshModel(): \think\Model
{
    $modelClass = get_class($this->model);
    return new $modelClass();
}
```

**优化效果**：
- CrudService的add方法已使用createFreshModel()
- 每次操作创建新的模型实例
- 有效避免了状态污染问题

#### 3.2 测试验证结果 ✅
```
单例服务创建结果: 成功 3/3
直接实例化创建结果: 成功 3/3
✅ 当前测试场景下单例服务工作正常
```

**结论**：CrudService优化已生效，单例服务在当前场景下工作正常。

## 🔧 优化方案

### 方案1：保持现状 + 局部优化（推荐）

#### 1.1 优势分析
- **CrudService已优化**：底层已解决状态污染问题
- **BaseModel租户隔离完善**：自动处理租户数据隔离
- **风险可控**：实际测试显示单例服务工作正常

#### 1.2 局部优化建议
```php
// 仅在明确的高风险场景使用直接实例化
// WorkflowEngine.php 第280行优化
// 原代码：
$taskService = WorkflowTaskService::getInstance();
$result = $taskService->createTask($instance, $currentNode, $nextApprover);

// 优化代码：
$taskModel = new \app\workflow\model\WorkflowTask();
$result = $taskModel->saveByCreate($taskData);
```

### 方案2：全面模型直接实例化

#### 2.1 查询操作优化
```php
// 原代码：
$definition = WorkflowDefinitionService::getInstance()
                ->getModel()
                ->where(['id' => $instance['definition_id']])
                ->findOrEmpty();

// 优化代码：
$definition = \app\workflow\model\WorkflowDefinition::where(['id' => $instance['definition_id']])
                ->findOrEmpty();
```

#### 2.2 更新操作优化

**⚠️ 重要发现：静态update方法的租户隔离问题**

基于深入的代码分析，发现了一个关键问题：

```php
// ❌ 问题代码：静态update方法不会应用全局查询范围
\app\workflow\model\WorkflowInstance::where('id', $instance['id'])
    ->update(['current_node' => $nodeConfig['nodeId']]);
// 此方法不会触发模型的全局查询范围，可能绕过租户隔离
```

**根本原因分析**：
1. 静态`where()`方法虽然会创建模型实例，但返回的是`think\db\Query`对象
2. `Query`对象的`update()`方法直接操作数据库，不会应用模型的全局查询范围
3. 这可能导致跨租户数据更新的安全风险

**正确的更新方式**：

```php
// ✅ 方案1：先查询再更新（推荐 - 使用findOrEmpty）
$instance = \app\workflow\model\WorkflowInstance::where('id', $instanceId)->findOrEmpty();
if (!$instance->isEmpty()) {
    $instance->saveByUpdate(['current_node' => $nodeConfig['nodeId']]);
}

// ✅ 方案1备选：使用find方法
$instance = \app\workflow\model\WorkflowInstance::where('id', $instanceId)->find();
if ($instance !== null) {
    $instance->saveByUpdate(['current_node' => $nodeConfig['nodeId']]);
}

// ✅ 方案2：使用CrudService（已优化，安全）
$instanceService = \app\workflow\service\WorkflowInstanceService::getInstance();
$instanceService->edit(['current_node' => $nodeConfig['nodeId']], ['id' => $instanceId]);
```

#### 2.3 创建操作优化
```php
// 原代码：
WorkflowHistoryService::getInstance()->safeAddHistory($historyData);

// 优化代码：
$historyModel = new \app\workflow\model\WorkflowHistory();
$historyModel->saveByCreate($historyData);
```

### **⚠️ find() vs findOrEmpty() 重要区别**

```php
// ❌ 错误用法：find()返回null时调用isEmpty()会报错
$model = Model::where('id', $id)->find();
if (!$model->isEmpty()) { // 如果$model为null，这里会报错
    $model->saveByUpdate($data);
}

// ✅ 正确用法1：使用findOrEmpty()
$model = Model::where('id', $id)->findOrEmpty();
if (!$model->isEmpty()) { // 安全，findOrEmpty()始终返回模型实例
    $model->saveByUpdate($data);
}

// ✅ 正确用法2：使用find()配合null检查
$model = Model::where('id', $id)->find();
if ($model !== null) { // 正确的null检查
    $model->saveByUpdate($data);
}
```

### 方案3：封装常用查询方法

#### 3.1 工作流查询工具类
```php
// app/workflow/utils/WorkflowQueryHelper.php
class WorkflowQueryHelper
{
    /**
     * 查找工作流定义
     */
    public static function findDefinition(int $definitionId): ?\app\workflow\model\WorkflowDefinition
    {
        return \app\workflow\model\WorkflowDefinition::where('id', $definitionId)->findOrEmpty();
    }
    
    /**
     * 查找工作流实例
     */
    public static function findInstance(int $instanceId): ?\app\workflow\model\WorkflowInstance
    {
        return \app\workflow\model\WorkflowInstance::where('id', $instanceId)->findOrEmpty();
    }
    
    /**
     * 查找待处理任务
     */
    public static function findPendingTasks(int $instanceId, string $nodeId = ''): array
    {
        $query = \app\workflow\model\WorkflowTask::where([
            'instance_id' => $instanceId,
            'status' => 0
        ]);
        
        if (!empty($nodeId)) {
            $query->where('node_id', $nodeId);
        }
        
        return $query->select()->toArray();
    }
    
    /**
     * 更新实例状态
     */
    public static function updateInstanceStatus(int $instanceId, array $data): bool
    {
        return \app\workflow\model\WorkflowInstance::where('id', $instanceId)->update($data) > 0;
    }
    
    /**
     * 创建任务记录
     */
    public static function createTask(array $taskData): int
    {
        $taskModel = new \app\workflow\model\WorkflowTask();
        return $taskModel->saveByCreate($taskData);
    }
    
    /**
     * 创建历史记录
     */
    public static function createHistory(array $historyData): int
    {
        $historyModel = new \app\workflow\model\WorkflowHistory();
        return $historyModel->saveByCreate($historyData);
    }
}
```

#### 3.2 使用示例
```php
// 优化后的WorkflowEngine代码
class WorkflowEngine
{
    public function startWorkflow(array $instance): bool
    {
        // 获取流程定义
        $definition = WorkflowQueryHelper::findDefinition($instance['definition_id']);
        if ($definition->isEmpty()) {
            Log::error('流程定义不存在: ' . $instance['definition_id']);
            return false;
        }
        
        // 更新当前节点
        WorkflowQueryHelper::updateInstanceStatus($instance['id'], [
            'current_node' => $nodeConfig['nodeId']
        ]);
        
        return true;
    }
}
```

## 📋 实施建议

### 阶段1：立即实施（推荐方案1）
1. **保持现状**：CrudService优化已生效，风险可控
2. **局部优化**：仅在明确的高风险场景使用直接实例化
3. **重点关注**：ApprovalNodeHandler中的循环创建场景

### 阶段2：渐进优化（可选方案2）
1. **查询操作**：逐步替换为静态方法调用
2. **⚠️ 更新操作**：**必须使用先查询再更新的方式**，避免静态update方法
3. **创建操作**：直接实例化模型

### 阶段3：工具类封装（可选方案3）
1. **创建工具类**：封装常用的工作流查询和操作方法
2. **逐步迁移**：将现有代码迁移到工具类
3. **统一接口**：提供一致的API接口

## ⚠️ 重要安全提醒

### 静态update方法的安全风险

**发现的问题**：
```php
// ❌ 危险：可能绕过租户隔离
Model::where('id', $id)->update($data);
```

**安全的替代方案**：
```php
// ✅ 安全：应用租户隔离和权限保护
$model = Model::where('id', $id)->find();
if (!$model->isEmpty()) {
    $model->saveByUpdate($data);
}
```

## 🎯 最终建议

### 推荐方案：方案1（保持现状 + 局部优化）

**理由**：
1. **CrudService已优化**：底层状态污染问题已解决
2. **测试验证通过**：实际测试显示单例服务工作正常
3. **BaseModel完善**：自动处理租户隔离和数据权限
4. **风险可控**：当前架构稳定可靠

**具体行动**：
1. ✅ **保持现有架构**：不进行大规模重构
2. ⚠️ **关注高风险点**：ApprovalNodeHandler中的循环创建场景
3. 📊 **持续监控**：通过日志监控是否出现状态污染问题
4. 🔧 **按需优化**：仅在发现具体问题时进行局部优化

**技术保障**：
- BaseModel自动租户隔离：✅ 已验证（查询操作）
- CrudService状态污染防护：✅ 已实现
- 模型直接实例化支持：✅ 随时可用
- ⚠️ **静态方法调用限制**：仅用于查询，更新必须先查询再操作

### 🚨 关键安全原则

**更新操作的最佳实践**：

1. **✅ 推荐方式**：先查询再更新
   ```php
   // 使用findOrEmpty()（推荐）
   $model = Model::where('id', $id)->findOrEmpty();
   if (!$model->isEmpty()) {
       $model->saveByUpdate($data);
   }

   // 或使用find()配合null检查
   $model = Model::where('id', $id)->find();
   if ($model !== null) {
       $model->saveByUpdate($data);
   }
   ```

2. **✅ 备选方式**：使用CrudService
   ```php
   $service = Service::getInstance();
   $service->edit($data, ['id' => $id]);
   ```

3. **❌ 禁止方式**：直接静态update
   ```php
   Model::where('id', $id)->update($data); // 可能绕过租户隔离
   ```

这种方案既保持了系统的稳定性，又确保了数据安全性。

## 📚 附录：find() vs findOrEmpty() 详细对比

### **方法差异总结**

| 方法 | 找到记录 | 未找到记录 | 推荐判断方式 | 风险 |
|------|----------|------------|--------------|------|
| `find()` | 返回模型实例 | 返回`null` | `if ($model !== null)` | 对`null`调用方法会报错 |
| `findOrEmpty()` | 返回模型实例 | 返回空模型实例 | `if (!$model->isEmpty())` | 无风险 |

### **实际测试结果**

```php
// 存在的记录
$existing1 = Model::where('id', 1)->find();        // 返回模型实例
$existing2 = Model::where('id', 1)->findOrEmpty(); // 返回模型实例

// 不存在的记录
$notExist1 = Model::where('id', 999999)->find();        // 返回null
$notExist2 = Model::where('id', 999999)->findOrEmpty(); // 返回空模型实例

// 判断测试
if ($notExist1) { }                    // false（正确）
if ($notExist2) { }                    // true（注意！）
if (!$notExist2->isEmpty()) { }        // false（正确）
```

### **最佳实践建议**

1. **优先使用`findOrEmpty()`**：更安全，不会出现null引用错误
2. **使用`find()`时必须进行null检查**：避免对null调用方法
3. **统一判断方式**：
   - `findOrEmpty()`：使用`!$model->isEmpty()`
   - `find()`：使用`$model !== null`

### **常见错误示例**

```php
// ❌ 危险：可能对null调用isEmpty()
$model = Model::where('id', $id)->find();
if (!$model->isEmpty()) { // 如果记录不存在，$model为null，调用isEmpty()会报错
    $model->saveByUpdate($data);
}

// ✅ 安全：使用findOrEmpty()
$model = Model::where('id', $id)->findOrEmpty();
if (!$model->isEmpty()) { // 安全，findOrEmpty()始终返回模型实例
    $model->saveByUpdate($data);
}
```
