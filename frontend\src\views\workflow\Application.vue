<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import {
    ElDialog,
    ElTag,
    ElDescriptions,
    ElDescriptionsItem,
    ElTabs,
    ElTabPane,
    ElTimeline,
    ElTimelineItem,
    ElCard
  } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { useAuth } from '@/composables/useAuth'
  import { BgColorEnum } from '@/enums/appEnum'
  import WorkflowTypeSelector from './components/workflow-type-selector.vue'
  import FormManager from './components/form-manager.vue'
  import FormDataViewer from './components/form-data-viewer.vue'
  import {
    getInstanceStatusText,
    getInstanceStatusTagType,
    getInstanceStatusOptions
  } from '@/constants/workflow'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'

  // 导入工作流组件
  import WorkflowDesigner from '@/components/custom/workflow/index.vue'
  import { isEmpty } from '@/utils/utils'

  const { hasAuth } = useAuth()

  // 加载状态
  const loading = ref(false)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 详情数据加载状态
  const processGraphLoading = ref(false)
  const historyLoading = ref(false)
  const processGraphData = ref<{ nodeConfig: any; flowPermission: any; name: string } | null>(null)

  // 定义审批历史记录的类型接口
  interface ApprovalHistory {
    operation_time?: string
    operation?: string
    operator_name?: string
    node_name?: string
    opinion?: string

    [key: string]: any
  }

  const historyData = ref<ApprovalHistory[]>([])

  // 类型选择对话框
  const typeSelectVisible = ref(false)

  // 业务表单控制
  const businessFormVisible = ref(false)
  const selectedFormType = ref('')
  const formManagerRef = ref()

  // 定义表单搜索初始值
  const initialSearchState = {
    title: '',
    status: '',
    created_at: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    /* {
       label: '申请标题',
       prop: 'title',
       type: 'input',
       config: {
         clearable: true
       },
       onChange: handleFormChange
     },*/
    {
      label: '申请状态',
      prop: 'status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () =>
        getInstanceStatusOptions().map((item) => ({
          label: item.label,
          value: item.value.toString()
        })),
      onChange: handleFormChange
    },
    {
      label: '申请时间',
      prop: 'created_at',
      type: 'daterange',
      config: {
        clearable: true,
        type: 'daterange',
        shortcuts: [
          {
            text: '今天',
            value: () => {
              const today = new Date()
              return [today, today]
            }
          },
          {
            text: '最近一周',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDate() - 6)
              return [start, end]
            }
          },
          {
            text: '最近一个月',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              return [start, end]
            }
          }
        ]
      }
    }
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 获取状态标签类型 - 使用统一常量
  const getStatusTagType = getInstanceStatusTagType

  // 构建状态标签文本 - 使用统一常量
  const buildStatusTagText = getInstanceStatusText

  // 动态列配置
  const { columnChecks, columns } = useCheckedColumns(() => [
    { prop: 'title', label: '标题' },
    { prop: 'type_name', label: '类型' },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      formatter: (row) => {
        return h(ElTag, { type: getStatusTagType(row.status) as any }, () =>
          buildStatusTagText(row.status)
        )
      }
    },
    // { prop: 'department_name', label: '所属部门' },
    { prop: 'submitter_name', label: '申请人' },
    {
      prop: 'current_handler',
      label: '当前处理人',
      formatter: (row) => {
        return isEmpty(row.current_handler) ? '-' : row.current_handler
      }
    },
    {
      prop: 'created_at',
      label: '申请时间',
      sortable: true
    },
    {
      prop: 'operation',
      label: '操作',
      fixed: 'right',
      width: 220
    }
  ])

  // 表格数据
  const tableData = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 存储选中的工作流定义ID
  const selectedWorkflowId = ref<number | string>(0)

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    console.log('asdf', val)
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      // 调用API获取我的申请列表
      const res = await ApplicationApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      // HTTP拦截器已确保请求成功，直接处理数据
      total.value = res.data.total || 0
      currentPage.value = res.data.page || 1
      pageSize.value = res.data.limit || 10
      tableData.value = res.data.list || []
    } catch (error) {
      console.error('获取申请列表失败', error)
      // HTTP拦截器已处理错误提示
    } finally {
      loading.value = false
    }
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      processGraphLoading.value = true
      historyLoading.value = true

      const res = await ApplicationApi.detail(id, {
        includeHistory: true,
        includeProcessGraph: true
      })

      // HTTP拦截器已确保请求成功，直接处理数据
      detailData.value = res.data

      // 添加调试信息
      console.log('Application.vue - 详情数据:', res.data)
      console.log('Application.vue - business_code:', res.data.business_code)
      console.log('Application.vue - form_data:', res.data.form_data)

      // 修复流程图数据，确保格式与WorkflowDesigner期望的一致
      if (res.data.process_data) {
        processGraphData.value = {
          nodeConfig: res.data.process_data.nodeConfig,
          flowPermission: res.data.process_data.flowPermission || [],
          name: res.data.title || ''
        }
      } else {
        processGraphData.value = null
      }

      historyData.value = res.data.history || []
      detailDialogVisible.value = true
    } catch (error) {
      console.error('获取详情失败', error)
      // ElMessage.error('获取详情失败')
    } finally {
      loading.value = false
      processGraphLoading.value = false
      historyLoading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 撤回申请
  const withdrawApplication = (id: number) => {
    ElMessageBox.confirm('确定要撤回该申请吗？', '撤回确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await ApplicationApi.recall(id)

          if (res.code === 1) {
            ElMessage.success(res.message || '撤回成功')
            await getTableData()
          }
        } catch (error) {
          console.error('撤回失败', error)
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }

  // 提交申请
  const confirmApplication = (id: number) => {
    ElMessageBox.confirm('确定要提交该申请吗？', '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await ApplicationApi.confirm(id)

          if (res.code === 1) {
            ElMessage.success(res.message || '提交成功')
            await getTableData()
          }
        } catch (error) {
          console.error('提交失败', error)
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }

  // 提交申请
  /*const submitApplication = (id: number) => {
    ElMessageBox.confirm('确定要提交该申请吗？', '提交确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await ApplicationApi.submit({
            id
          })

          if (res.code === 1) {
            ElMessage.success(res.message || '提交成功')
            await getTableData()
          } else {
            ElMessage.error(res.message || '提交失败')
          }
        } catch (error) {
          console.error('提交失败', error)
          ElMessage.error('提交失败')
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }*/

  // 删除申请记录
  /*const deleteApplication = (id: number) => {
    ElMessageBox.confirm('确定要删除该申请记录吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
      .then(async () => {
        try {
          loading.value = true
          const res = await ApplicationApi.delete(id)

          if (res.code === 1) {
            ElMessage.success(res.message || '删除成功')
            await getTableData()
          } else {
            ElMessage.error(res.message || '删除失败')
          }
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }*/

  // 点击申请按钮
  const handleCreateApplication = () => {
    typeSelectVisible.value = true
  }

  // 处理类型选择结果
  const handleTypeSelected = (type: any) => {
    console.log('选择的流程类型:', type)
    typeSelectVisible.value = false

    // 根据模块类型打开对应的表单
    selectedFormType.value = type.business_code
    selectedWorkflowId.value = type.id

    // 添加调试日志，检查ID是否正确获取
    console.log('Application.vue - 选择的工作流定义ID:', type.id)
    console.log('Application.vue - 业务代码:', type.business_code)
    console.log('Application.vue - 传递给FormManager的参数:', {
      type: selectedFormType.value,
      workflowTypeId: selectedWorkflowId.value
    })

    // 显示业务表单
    if (selectedFormType.value) {
      businessFormVisible.value = true
    }
  }

  // 表单提交成功处理
  const handleFormSuccess = (result: any) => {
    console.log('表单提交成功:', result)
    /*let messageText = '申请提交成功'
    if (result.type === 'leave') {
      messageText = '请假申请提交成功'
    } else if (result.type === 'travel') {
      messageText = '出差申请提交成功'
    }
    ElMessage.success(messageText)*/
    businessFormVisible.value = false
    getTableData() // 刷新表格数据
  }

  // 表单取消处理
  const handleFormCancel = () => {
    businessFormVisible.value = false
  }

  // 编辑申请
  const editApplication = (id: number) => {
    // 先获取申请详情，确定业务类型
    loading.value = true

    ApplicationApi.detail(id)
      .then((res) => {
        // HTTP拦截器已确保请求成功，直接处理数据
        if (res.data) {
          const formData = res.data

          // 设置表单类型和工作流ID
          selectedFormType.value = formData.business_code || ''
          selectedWorkflowId.value = formData.definition_id || 0

          // 先存储加载的数据
          const editData = {
            id: formData.id,
            formData: formData.form_data || {}
          }

          // 使用setTimeout确保表单显示前先完成数据准备
          setTimeout(() => {
            // 显示表单
            businessFormVisible.value = true

            // 等待表单组件挂载完成后再加载数据
            setTimeout(() => {
              if (formManagerRef.value && formManagerRef.value.setEditData) {
                formManagerRef.value.setEditData(editData)
              }
            }, 100)
          }, 0)
        }
      })
      .catch((error) => {
        console.error('获取申请详情失败', error)
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 判断是否有更多操作
  const hasMoreActions = (row: any) => {
    // 草稿状态可以编辑
    const canEdit = row.status === 0 && hasAuth('workflow:application:edit')
    // 已通过状态可以作废
    const canVoid = row.status === 2 && hasAuth('workflow:application:void')

    return canEdit || canVoid
  }

  // 处理更多操作
  const handleMoreAction = (command: { action: string; id: number }) => {
    switch (command.action) {
      case 'edit':
        editApplication(command.id)
        break
      case 'void':
        voidApplication(command.id)
        break
      default:
        console.warn('未知的操作类型:', command.action)
    }
  }

  // 作废申请
  const voidApplication = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要作废此申请吗？作废后申请将无法恢复。', '确认作废', {
        confirmButtonText: '确定作废',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      // 调用通用的作废接口，后端会根据业务类型自动分发
      const response = await ApplicationApi.voidApplication(id)

      if (response.code === 1) {
        ElMessage.success('申请已作废')
        await getTableData() // 刷新列表
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('作废申请失败:', error)
      }
    } finally {
      loading.value = false
    }
  }

  // 获取时间线项目类型
  const getTimelineItemType = (operation: string) => {
    switch (operation) {
      case 'submit':
      case '8':
      case 'start':
        return 'primary' // 蓝色 - 表示流程开始
      case 'withdraw':
        return 'info' // 灰色 - 表示撤回
      case 'approve':
      case '1':
        return 'success' // 绿色 - 表示同意/通过
      case 'reject':
      case '2':
        return 'danger' // 红色 - 表示拒绝
      case 'terminate':
      case '4':
        return 'warning' // 黄色 - 表示终止
      case 'recall':
      case '5':
        return 'info' // 灰色 - 表示撤回
      case 'transfer':
      case '3':
        return 'warning' // 黄色 - 表示转交
      case 'urge':
      case '6':
        return 'primary' // 蓝色 - 表示催办
      case 'cc':
      case '7':
        return 'info' // 灰色 - 表示抄送
      case 'end':
      case '9':
        return 'info' // 灰色 - 表示流程结束
      default:
        return 'info' // 默认灰色
    }
  }

  // 处理详情对话框关闭
  const handleDetailDialogClose = () => {
    // 重置工作流数据
    processGraphData.value = null
    historyData.value = []
    detailData.value = {}
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="my_application-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left>
            <ElButton
              @click="handleCreateApplication"
              type="primary"
              v-auth="'workflow:application:create'"
              v-ripple
              >发起申请
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col">
            <template v-if="col.prop === 'operation'" #default="scope">
              <div class="operation-buttons">
                <!-- 主要操作按钮 -->
                <ArtButtonTable
                  v-if="hasAuth('workflow:application:detail')"
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(scope.row.id)"
                />
                <ArtButtonTable
                  v-if="scope.row.status === 0 && hasAuth('workflow:application:submit')"
                  text="提交"
                  :iconClass="BgColorEnum.SUCCESS"
                  @click="confirmApplication(scope.row.id)"
                />
                <ArtButtonTable
                  v-if="scope.row.status === 1 && hasAuth('workflow:application:withdraw')"
                  text="撤回"
                  :iconClass="BgColorEnum.WARNING"
                  @click="withdrawApplication(scope.row.id)"
                />

                <!-- 更多操作下拉菜单 -->
                <ElDropdown
                  v-if="hasMoreActions(scope.row)"
                  @command="handleMoreAction"
                  trigger="hover"
                >
                  <ArtButtonTable text="更多" />
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem
                        v-if="scope.row.status === 0 && hasAuth('workflow:application:edit')"
                        :command="{ action: 'edit', id: scope.row.id }"
                        icon="Edit"
                      >
                        编辑
                      </ElDropdownItem>
                      <ElDropdownItem
                        v-if="scope.row.status === 2 && hasAuth('workflow:application:void')"
                        :command="{ action: 'void', id: scope.row.id }"
                        icon="Delete"
                        divided
                      >
                        作废
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="申请详情"
          width="60%"
          destroy-on-close
          @close="handleDetailDialogClose"
        >
          <ElTabs>
            <ElTabPane label="详情">
              <ElDescriptions border :column="2">
                <ElDescriptionsItem label="标题">{{ detailData.title }}</ElDescriptionsItem>
                <ElDescriptionsItem label="类型">{{ detailData.type_name }}</ElDescriptionsItem>
                <!--                <ElDescriptionsItem label="编号">
                                  {{ detailData.process_id || '-' }}
                                </ElDescriptionsItem>-->
                <!--                <ElDescriptionsItem label="所属部门">
                                  {{ detailData.dept_name || '-' }}
                                </ElDescriptionsItem>-->
                <ElDescriptionsItem label="申请状态">
                  <ElTag :type="getStatusTagType(detailData.status) as any">
                    {{ buildStatusTagText(detailData.status) }}
                  </ElTag>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="申请时间">
                  {{ detailData.created_at }}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="提交人">
                  {{ detailData.submitter_name || '-' }}
                </ElDescriptionsItem>
                <!--                <ElDescriptionsItem label="当前处理人">
                                  {{ detailData.current_handler || '-' }}
                                </ElDescriptionsItem>-->
              </ElDescriptions>

              <div class="workflow-form-data" style="margin-top: 20px">
                <FormDataViewer
                  :formData="detailData.form_data || {}"
                  :businessCode="detailData.business_code || ''"
                />
              </div>
            </ElTabPane>

            <ElTabPane label="流程图">
              <div class="workflow-process-graph" v-loading="processGraphLoading">
                <WorkflowDesigner
                  v-if="processGraphData"
                  :workflow-data="processGraphData"
                  :read-only="true"
                  :current-executing-node-id="
                    detailData.status === 2 ? null : detailData.current_node
                  "
                  style="height: 400px"
                />
                <div v-else class="empty-placeholder">暂无流程图数据</div>
              </div>
            </ElTabPane>

            <ElTabPane label="审批历史">
              <div class="workflow-history" v-loading="historyLoading">
                <ElTimeline v-if="historyData && historyData.length > 0">
                  <ElTimelineItem
                    v-for="(item, index) in historyData"
                    :key="index"
                    :timestamp="item.operation_time || ''"
                    :type="getTimelineItemType(item.operation || '')"
                  >
                    <ElCard class="history-card">
                      <h4>{{ item.operation_text || '' }}</h4>
                      <p class="operation-info">
                        <span>操作人：{{ item.operator_name || '-' }}</span>
                        <!--                        <span>节点：{{ item.node_name || '-' }}</span>-->
                      </p>
                      <template v-if="item.opinion">
                        <p class="opinion-title">审批意见：</p>
                        <p class="opinion-content">{{ item.opinion }}</p>
                      </template>
                    </ElCard>
                  </ElTimelineItem>
                </ElTimeline>
                <div v-else class="empty-placeholder">暂无审批历史</div>
              </div>
            </ElTabPane>
          </ElTabs>

          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
              <!--              <ElButton
                              v-if="detailData.status === 1"
                              type="warning"
                              @click="withdrawApplication(detailData.id)"
                            >
                              撤回申请
                            </ElButton>-->
            </div>
          </template>
        </ElDialog>

        <!-- 流程类型选择对话框 -->
        <WorkflowTypeSelector
          v-model="typeSelectVisible"
          @select="handleTypeSelected"
          @cancel="typeSelectVisible = false"
        />

        <!-- 业务表单管理器 -->
        <FormManager
          v-model="businessFormVisible"
          :type="selectedFormType"
          :workflowTypeId="selectedWorkflowId"
          @success="handleFormSuccess"
          @cancel="handleFormCancel"
          ref="formManagerRef"
        />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .my_application-page {
    width: 100%;

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }

  .workflow-process-graph {
    width: 100%;
    height: 500px;
    overflow: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .workflow-history {
    padding: 10px;

    .history-card {
      margin-bottom: 10px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .operation-info {
        display: flex;
        justify-content: space-between;
        color: #666;
        font-size: 13px;
      }

      .opinion-title {
        margin-top: 10px;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .opinion-content {
        color: #666;
        white-space: pre-wrap;
      }
    }
  }

  .empty-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #909399;
    font-size: 14px;
    background-color: #f8f8f9;
    border-radius: 4px;
  }

  // 操作列样式
  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }
</style>
