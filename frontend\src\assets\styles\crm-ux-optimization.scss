/**
 * CRM系统UI/UX优化样式
 * 针对25-60岁中文用户群体的用户体验优化
 * 
 * 优化原则：
 * 1. 字体适度放大，提升可读性
 * 2. 增强颜色对比度，减少视觉疲劳
 * 3. 优化间距布局，提升操作舒适度
 * 4. 统一交互反馈，增强操作确定性
 */

// ==================== 字体优化 ====================

// 基础字体大小优化 - 针对中年用户群体
:root {
  // 基础字体大小从14px提升到15px
  --crm-base-font-size: 15px;
  
  // 表格字体大小
  --crm-table-font-size: 15px;
  --crm-table-header-font-size: 15px;
  
  // 表单字体大小
  --crm-form-label-font-size: 15px;
  --crm-form-input-font-size: 15px;
  
  // 按钮字体大小
  --crm-button-font-size: 15px;
  
  // 标题字体大小
  --crm-title-font-size: 18px;
  --crm-subtitle-font-size: 16px;
  
  // 小字体（备注、时间等）
  --crm-small-font-size: 13px;
}

// ==================== 颜色对比度优化 ====================

:root {
  // 主要文本颜色 - 增强对比度
  --crm-text-primary: #1f2329;
  --crm-text-secondary: #4e5969;  // 从#86909C调整为更深的颜色
  --crm-text-placeholder: #86909c;
  
  // 链接和重要信息颜色
  --crm-link-color: #1664ff;
  --crm-link-hover-color: #0052d9;
  
  // 状态颜色 - 增强可识别性
  --crm-success-color: #00a870;    // 成功状态
  --crm-warning-color: #ed7b2f;    // 警告状态  
  --crm-error-color: #d54941;      // 错误状态
  --crm-info-color: #1664ff;       // 信息状态
  
  // 金额显示颜色
  --crm-amount-color: #ed7b2f;     // 金额橙色
  --crm-amount-paid-color: #00a870; // 已付金额绿色
}

// ==================== 间距和布局优化 ====================

:root {
  // 表格行高优化
  --crm-table-row-height: 52px;    // 从默认提升到52px
  --crm-table-cell-padding: 14px 12px; // 单元格内边距
  
  // 表单间距优化
  --crm-form-item-margin: 20px;    // 表单项间距
  --crm-form-label-width: 120px;   // 标签宽度
  
  // 按钮间距优化
  --crm-button-margin: 12px;       // 按钮间距
  --crm-button-padding: 10px 20px; // 按钮内边距
  
  // 卡片和容器间距
  --crm-card-padding: 24px;        // 卡片内边距
  --crm-section-margin: 24px;      // 区块间距
}

// ==================== CRM表格优化 ====================

.crm-table-optimized {
  // 基础字体大小
  font-size: var(--crm-table-font-size);
  
  :deep(.el-table) {
    // 表头优化
    .el-table__header {
      font-size: var(--crm-table-header-font-size);
      font-weight: 600;
      color: var(--crm-text-primary);
      
      th {
        background-color: #f8f9fa !important;
        border-bottom: 2px solid #e9ecef;
      }
    }
    
    // 表格行优化
    .el-table__row {
      height: var(--crm-table-row-height);
      
      &:hover {
        background-color: #f8f9fa;
      }
    }
    
    // 单元格优化
    .el-table__cell {
      padding: var(--crm-table-cell-padding);
      font-size: var(--crm-table-font-size);
      color: var(--crm-text-primary);
      line-height: 1.5;
    }
    
    // 链接样式优化
    .el-link {
      font-size: var(--crm-table-font-size);
      font-weight: 500;
      
      &.el-link--primary {
        color: var(--crm-link-color);
        
        &:hover {
          color: var(--crm-link-hover-color);
        }
      }
    }
    
    // 标签样式优化
    .el-tag {
      font-size: var(--crm-small-font-size);
      padding: 4px 10px;
      border-radius: 4px;
      font-weight: 500;
    }
  }
  
  // 金额显示优化
  .amount-text {
    font-size: var(--crm-table-font-size);
    font-weight: 600;
    color: var(--crm-amount-color);
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  .paid-amount {
    font-size: var(--crm-table-font-size);
    font-weight: 600;
    color: var(--crm-amount-paid-color);
    font-family: 'Consolas', 'Monaco', monospace;
  }
  
  // 次要信息优化
  .secondary-info {
    color: var(--crm-text-secondary);
    font-size: var(--crm-small-font-size);
  }
  
  // 时间信息优化
  .time-info {
    color: var(--crm-text-secondary);
    font-size: var(--crm-small-font-size);
    font-family: 'Consolas', 'Monaco', monospace;
  }
}

// ==================== CRM表单优化 ====================

.crm-form-optimized {
  // 表单项间距
  .el-form-item {
    margin-bottom: var(--crm-form-item-margin);
    
    // 标签优化
    .el-form-item__label {
      font-size: var(--crm-form-label-font-size);
      color: var(--crm-text-primary);
      font-weight: 500;
      line-height: 1.5;
      width: var(--crm-form-label-width);
    }
    
    // 输入框优化
    .el-input {
      .el-input__inner {
        font-size: var(--crm-form-input-font-size);
        color: var(--crm-text-primary);
        height: 40px;
        line-height: 40px;
      }
      
      .el-input__placeholder {
        color: var(--crm-text-placeholder);
      }
    }
    
    // 选择器优化
    .el-select {
      .el-input__inner {
        font-size: var(--crm-form-input-font-size);
        height: 40px;
        line-height: 40px;
      }
    }
    
    // 文本域优化
    .el-textarea {
      .el-textarea__inner {
        font-size: var(--crm-form-input-font-size);
        color: var(--crm-text-primary);
        line-height: 1.5;
      }
    }
  }
  
  // 按钮组优化
  .form-buttons {
    margin-top: 32px;
    text-align: center;
    
    .el-button {
      font-size: var(--crm-button-font-size);
      padding: var(--crm-button-padding);
      margin: 0 var(--crm-button-margin);
      min-width: 100px;
      
      &.el-button--primary {
        background-color: var(--crm-link-color);
        border-color: var(--crm-link-color);
        
        &:hover {
          background-color: var(--crm-link-hover-color);
          border-color: var(--crm-link-hover-color);
        }
      }
    }
  }
}

// ==================== CRM详情页面优化 ====================

.crm-detail-optimized {
  // 详情标题
  .detail-title {
    font-size: var(--crm-title-font-size);
    color: var(--crm-text-primary);
    font-weight: 600;
    margin-bottom: 20px;
  }
  
  // 详情子标题
  .detail-subtitle {
    font-size: var(--crm-subtitle-font-size);
    color: var(--crm-text-primary);
    font-weight: 500;
    margin-bottom: 16px;
  }
  
  // 描述列表优化
  .el-descriptions {
    .el-descriptions__label {
      font-size: var(--crm-base-font-size);
      color: var(--crm-text-primary);
      font-weight: 500;
      width: 120px;
    }
    
    .el-descriptions__content {
      font-size: var(--crm-base-font-size);
      color: var(--crm-text-primary);
      line-height: 1.5;
    }
  }
  
  // 信息卡片优化
  .info-card {
    padding: var(--crm-card-padding);
    margin-bottom: var(--crm-section-margin);
    border-radius: 8px;
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    
    .card-title {
      font-size: var(--crm-subtitle-font-size);
      color: var(--crm-text-primary);
      font-weight: 600;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }
  }
}

// ==================== 操作按钮优化 ====================

.crm-operation-buttons {
  display: flex;
  gap: var(--crm-button-margin);
  align-items: center;
  
  .art-button-table {
    font-size: var(--crm-button-font-size);
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// ==================== 响应式优化 ====================

// 平板设备优化
@media (max-width: 1024px) {
  :root {
    --crm-table-row-height: 48px;
    --crm-form-label-width: 100px;
    --crm-card-padding: 20px;
  }
}

// 移动设备优化
@media (max-width: 768px) {
  :root {
    --crm-base-font-size: 16px;
    --crm-table-font-size: 16px;
    --crm-table-row-height: 56px;
    --crm-form-label-width: 80px;
    --crm-card-padding: 16px;
    --crm-button-padding: 12px 24px;
  }
}
