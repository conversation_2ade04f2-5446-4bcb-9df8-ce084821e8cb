import request from '@/utils/http'
import { BaseResult } from '@/types/axios'
import { calculateHoursWithHalfHourRule } from '@/utils/date'

/**
 * 请假申请API接口（新工作流集成版本）
 *
 * 功能说明：
 * 1. 提供标准的CRUD操作接口
 * 2. 集成工作流操作接口（提交审批、撤回等）
 * 3. 支持请假申请的业务查询
 * 4. 提供数据统计和选项接口
 */

// ==================== 数据类型定义 ====================

/**
 * 请假申请数据结构
 */
export interface HrLeaveItem {
  id: number
  tenant_id: number
  workflow_instance_id: number
  approval_status: number
  submit_time: string | null
  approval_time: string | null
  approval_opinion: string
  submitter_id: number
  leave_type: number
  start_time: string
  end_time: string
  duration: number
  reason: string
  attachment: string[] | string
  emergency_contact: string
  emergency_phone: string
  remark: string
  creator_id: number
  created_at: string
  updated_at: string
  deleted_at: string | null

  // 虚拟字段
  leave_type_text: string
  approval_status_text: string
  approval_status_class: string
  submitter_name: string
  submitter_dept_name: string
  creator_name: string
  can_edit: boolean
  can_submit: boolean
  can_withdraw: boolean
  can_delete: boolean
}

/**
 * 请假申请表单数据结构
 */
export interface HrLeaveFormData {
  id?: number
  leave_type: number
  start_time: string
  end_time: string
  duration: number
  reason: string
  emergency_contact: string
  emergency_phone: string
  attachment?: string[]
  remark?: string
}

/**
 * 请假申请列表查询参数
 */
export interface HrLeaveListParams {
  page?: number
  limit?: number
  leave_type?: number
  approval_status?: number | string
  start_time?: string
  end_time?: string
  submitter_name?: string
  submitter_id?: number
  keyword?: string
}

/**
 * 请假申请列表响应数据
 */
export interface HrLeaveListResponse {
  list: HrLeaveItem[]
  total: number
  page: number
  limit: number
}

/**
 * 工作流提交参数
 */
export interface WorkflowSubmitParams {
  definition_id?: number
  submitter_id?: number
}

/**
 * 工作流撤回参数
 */
export interface WorkflowWithdrawParams {
  reason?: string
}

/**
 * 请假统计数据
 */
export interface HrLeaveStatistics {
  total_count: number
  total_days: number
  by_type: Record<string, { count: number; days: number; name: string }>
  by_status: Record<string, { count: number; name: string }>
  by_month: Record<string, { count: number; days: number }>
}

/**
 * 选项数据结构
 */
export interface OptionItem {
  value: number | string
  label: string
  class?: string
}

// ==================== API接口类 ====================

export class HrLeaveApi {
  // ==================== 标准CRUD接口 ====================

  /**
   * 获取请假申请列表
   *
   * @param params 查询参数
   * @returns Promise<BaseResult<HrLeaveListResponse>>
   *
   * 请求参数说明：
   * - page: 页码，默认1
   * - limit: 每页数量，默认10
   * - leave_type: 请假类型筛选
   * - approval_status: 审批状态筛选
   * - start_time: 开始时间筛选
   * - end_time: 结束时间筛选
   * - submitter_name: 提交人姓名筛选
   * - keyword: 关键词搜索（搜索请假原因）
   */
  static getList(params: HrLeaveListParams = {}) {
    return request.get<BaseResult<HrLeaveListResponse>>({
      url: '/hr/hr_leave/list',
      params
    })
  }

  /**
   * 获取我的请假申请列表
   *
   * @param params 查询参数
   * @returns Promise<BaseResult<HrLeaveListResponse>>
   *
   * 只返回当前用户提交的请假申请
   */
  static getMyList(params: HrLeaveListParams = {}) {
    return request.get<BaseResult<HrLeaveListResponse>>({
      url: '/hr/hr_leave/myList',
      params
    })
  }

  /**
   * 获取请假申请详情
   *
   * @param id 请假申请ID
   * @returns Promise<BaseResult<HrLeaveItem>>
   *
   * 返回完整的请假申请数据，包括关联的用户、部门、工作流实例信息
   */
  static getDetail(id: number | string) {
    return request.get<BaseResult<HrLeaveItem>>({
      url: `/hr/hr_leave/detail/${id}`
    })
  }

  /**
   * 新增请假申请
   *
   * @param data 请假申请数据
   * @returns Promise<BaseResult<{ id: number; data: HrLeaveItem }>>
   *
   * 请求数据说明：
   * - leave_type: 请假类型（1=年假,2=事假,3=病假,4=婚假,5=产假,6=丧假,7=其他）
   * - start_time: 开始时间（格式：YYYY-MM-DD HH:mm:ss）
   * - end_time: 结束时间（格式：YYYY-MM-DD HH:mm:ss）
   * - duration: 请假天数（可自动计算）
   * - reason: 请假原因（必填，最多500字符）
   * - emergency_contact: 紧急联系人
   * - emergency_phone: 紧急联系电话
   * - attachment: 附件列表（可选）
   * - remark: 备注（可选）
   */
  static add(data: HrLeaveFormData) {
    return request.post<BaseResult<{ id: number; data: HrLeaveItem }>>({
      url: '/hr/hr_leave/add',
      data
    })
  }

  /**
   * 编辑请假申请
   *
   * @param id 请假申请ID
   * @param data 更新数据
   * @returns Promise<BaseResult>
   *
   * 注意：只有草稿和已驳回状态的申请可以编辑
   */
  static edit(id: number | string, data: Partial<HrLeaveFormData>) {
    return request.post<BaseResult>({
      url: `/hr/hr_leave/edit/${id}`,
      data
    })
  }

  /**
   * 删除请假申请
   *
   * @param id 请假申请ID
   * @returns Promise<BaseResult>
   *
   * 注意：只有草稿、已驳回、已撤回状态的申请可以删除
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/hr/hr_leave/delete/${id}`
    })
  }

  /**
   * 批量删除请假申请
   *
   * @param ids 请假申请ID数组
   * @returns Promise<BaseResult<{ success_count: number; failed_count: number; errors: string[] }>>
   */
  static batchDelete(ids: number[]) {
    return request.post<
      BaseResult<{ success_count: number; failed_count: number; errors: string[] }>
    >({
      url: '/hr/hr_leave/batchDelete',
      data: { ids }
    })
  }

  // ==================== 工作流操作接口 ====================

  /**
   * 提交审批
   *
   * @param id 请假申请ID
   * @param params 提交参数
   * @returns Promise<BaseResult<{ instance_id: number; leave_id: number }>>
   *
   * 提交参数说明：
   * - definition_id: 工作流定义ID（可选，默认使用系统配置）
   * - submitter_id: 提交人ID（可选，默认使用当前用户）
   *
   * 注意：只有草稿和已驳回状态的申请可以提交审批
   */
  static submit(id: number | string, params: WorkflowSubmitParams = {}) {
    return request.post<BaseResult<{ instance_id: number; leave_id: number }>>({
      url: `/hr/hr_leave/submit/${id}`,
      data: params
    })
  }

  /**
   * 撤回审批
   *
   * @param id 请假申请ID
   * @param params 撤回参数
   * @returns Promise<BaseResult>
   *
   * 撤回参数说明：
   * - reason: 撤回原因（可选）
   *
   * 注意：只有审批中状态的申请可以撤回
   */
  static withdraw(id: number | string, params: WorkflowWithdrawParams = {}) {
    return request.post<BaseResult>({
      url: `/hr/hr_leave/withdraw/${id}`,
      data: params
    })
  }

  /**
   * 作废申请
   *
   * @param id 请假申请ID
   * @param params 作废参数
   * @returns Promise<BaseResult>
   *
   * 作废参数说明：
   * - reason: 作废原因（可选）
   *
   * 注意：只有已通过状态的申请可以作废，且请假未开始
   */
  static void(id: number | string, params: { reason?: string } = {}) {
    return request.post<BaseResult>({
      url: `/hr/hr_leave/void/${id}`,
      data: params
    })
  }

  // ==================== 业务查询接口 ====================

  /**
   * 获取请假统计数据
   *
   * @param params 统计参数
   * @returns Promise<BaseResult<HrLeaveStatistics>>
   *
   * 统计参数说明：
   * - start_date: 开始日期（格式：YYYY-MM-DD）
   * - end_date: 结束日期（格式：YYYY-MM-DD）
   * - submitter_id: 提交人ID（可选，不传则统计所有人）
   */
  static getStatistics(
    params: {
      start_date?: string
      end_date?: string
      submitter_id?: number
    } = {}
  ) {
    return request.get<BaseResult<HrLeaveStatistics>>({
      url: '/hr/hr_leave/statistics',
      params
    })
  }

  /**
   * 获取请假类型选项
   *
   * @returns Promise<BaseResult<OptionItem[]>>
   *
   * 返回所有可用的请假类型选项
   */
  static getLeaveTypes() {
    return request.get<BaseResult<OptionItem[]>>({
      url: '/hr/hr_leave/leaveTypes'
    })
  }

  /**
   * 获取审批状态选项
   *
   * @returns Promise<BaseResult<OptionItem[]>>
   *
   * 返回所有可用的审批状态选项
   */
  static getApprovalStatuses() {
    return request.get<BaseResult<OptionItem[]>>({
      url: '/hr/hr_leave/approvalStatuses'
    })
  }

  // ==================== 辅助方法 ====================

  /**
   * 计算请假时长
   * 使用半小时向上取整规则
   *
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 请假时长（小时）
   *
   * 根据开始时间和结束时间计算请假时长
   * 注意：这是前端辅助计算，实际以后端计算为准
   */
  static calculateDuration(startTime: string, endTime: string): number {
    // 使用新的半小时规则计算
    return calculateHoursWithHalfHourRule(startTime, endTime)
  }

  /**
   * 验证请假时间
   *
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @returns 验证结果
   *
   * 验证规则：
   * 1. 结束时间必须晚于开始时间
   * 2. 开始时间不能早于当前时间（允许当天）
   * 3. 请假时间不能超过365天
   */
  static validateLeaveTime(
    startTime: string,
    endTime: string
  ): {
    valid: boolean
    message: string
  } {
    const start = new Date(startTime).getTime()
    const end = new Date(endTime).getTime()
    const now = new Date().getTime()
    const oneDayMs = 24 * 60 * 60 * 1000

    // 检查结束时间是否晚于开始时间
    if (end <= start) {
      return {
        valid: false,
        message: '结束时间必须晚于开始时间'
      }
    }

    // 检查开始时间是否过早
    if (start < now - oneDayMs) {
      return {
        valid: false,
        message: '开始时间不能早于昨天'
      }
    }

    // 检查请假时间是否过长
    const duration = this.calculateDuration(startTime, endTime)
    if (duration > 2920) { // 365天 * 8小时 = 2920小时
      return {
        valid: false,
        message: '请假时间不能超过2920小时（365天）'
      }
    }

    return {
      valid: true,
      message: '验证通过'
    }
  }

  /**
   * 格式化请假类型
   *
   * @param leaveType 请假类型值
   * @returns 请假类型文本
   */
  static formatLeaveType(leaveType: number): string {
    const typeMap: Record<number, string> = {
      1: '年假',
      2: '事假',
      3: '病假',
      4: '婚假',
      5: '产假',
      6: '丧假',
      7: '其他'
    }

    return typeMap[leaveType] || '未知'
  }

  /**
   * 格式化审批状态
   *
   * @param approvalStatus 审批状态值
   * @returns 审批状态信息
   */
  static formatApprovalStatus(approvalStatus: number): {
    text: string
    class: string
  } {
    const statusMap: Record<number, { text: string; class: string }> = {
      0: { text: '草稿', class: 'info' },
      1: { text: '审批中', class: 'warning' },
      2: { text: '已通过', class: 'success' },
      3: { text: '已驳回', class: 'danger' },
      4: { text: '已终止', class: 'danger' },
      5: { text: '已撤回', class: 'info' },
      6: { text: '已作废', class: 'danger' }
    }

    return statusMap[approvalStatus] || { text: '未知', class: 'default' }
  }

  /**
   * 检查操作权限
   *
   * @param leave 请假申请数据
   * @param action 操作类型
   * @returns 是否有权限
   */
  static checkPermission(
    leave: HrLeaveItem,
    action: 'edit' | 'submit' | 'withdraw' | 'delete'
  ): boolean {
    switch (action) {
      case 'edit':
        return leave.can_edit
      case 'submit':
        return leave.can_submit
      case 'withdraw':
        return leave.can_withdraw
      case 'delete':
        return leave.can_delete
      default:
        return false
    }
  }
}

// 导出默认实例
export default HrLeaveApi
