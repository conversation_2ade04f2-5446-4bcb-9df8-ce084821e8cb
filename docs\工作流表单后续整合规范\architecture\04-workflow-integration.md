# 工作流集成架构

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 集成目标

### 核心目标
1. **无缝集成**：表单组件与工作流系统无缝对接
2. **状态同步**：工作流状态与业务数据实时同步
3. **统一体验**：申请、审批、详情查看使用统一的组件
4. **灵活扩展**：支持各种业务场景的工作流需求

### 业务价值
- **提升效率**：统一的表单组件减少重复开发
- **保证一致性**：相同的数据展示逻辑确保信息一致
- **降低维护成本**：一套组件多场景复用
- **增强用户体验**：统一的交互模式

## 🏗️ 集成架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "前端表单层"
        A1[申请页面<br/>Application.vue] --> A2[审批页面<br/>WorkflowTask.vue]
        A2 --> A3[抄送页面<br/>WorkflowCc.vue]
        A3 --> A4[消息中心<br/>MessageCenter.vue]
    end
    
    subgraph "统一组件层"
        B1[FormManager<br/>表单管理器] --> B2[FormDataViewer<br/>详情查看器]
        B2 --> B3[ComponentMapper<br/>组件映射器]
    end
    
    subgraph "业务组件层"
        C1[business-forms/<br/>申请表单] --> C2[business-detail/<br/>详情表单]
        C2 --> C3[form-create-renderer<br/>动态表单]
    end
    
    subgraph "工作流引擎"
        D1[WorkflowInstance<br/>实例管理] --> D2[WorkflowTask<br/>任务管理]
        D2 --> D3[WorkflowHistory<br/>历史记录]
    end
    
    subgraph "数据层"
        E1[业务数据表] --> E2[工作流实例表]
        E2 --> E3[表单配置表]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B2
    A4 --> B2
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    B1 --> D1
    B2 --> D1
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
```

### 数据流集成图

```mermaid
sequenceDiagram
    participant U as 用户
    participant APP as 申请页面
    participant FM as FormManager
    participant WF as 工作流引擎
    participant BT as 业务表
    participant WT as 工作流表
    
    Note over U,WT: 申请提交流程
    U->>APP: 填写申请表单
    APP->>FM: 提交表单数据
    FM->>WF: 启动工作流
    WF->>BT: 保存业务数据
    WF->>WT: 创建工作流实例
    WT->>WF: 返回实例ID
    WF->>FM: 返回提交结果
    FM->>APP: 更新UI状态
    
    Note over U,WT: 审批查看流程
    U->>APP: 查看审批详情
    APP->>FM: 请求详情数据
    FM->>WT: 查询工作流实例
    WT->>FM: 返回form_data
    FM->>FM: 映射详情组件
    FM->>U: 显示详情界面
```

## 📊 数据结构设计

### 工作流实例表结构

```sql
-- 工作流实例表
CREATE TABLE workflow_instance (
  id int PRIMARY KEY AUTO_INCREMENT,
  definition_id int NOT NULL COMMENT '工作流定义ID',
  business_code varchar(50) NOT NULL COMMENT '业务代码',
  business_id int DEFAULT 0 COMMENT '业务数据ID',
  title varchar(200) NOT NULL COMMENT '实例标题',
  form_data JSON NOT NULL COMMENT '表单数据',
  status tinyint DEFAULT 1 COMMENT '状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回',
  submitter_id int NOT NULL COMMENT '提交人ID',
  submitter_name varchar(50) COMMENT '提交人姓名',
  current_node varchar(100) COMMENT '当前节点',
  cc_users JSON COMMENT '抄送用户',
  process_data JSON COMMENT '流程数据',
  started_at datetime COMMENT '开始时间',
  finished_at datetime COMMENT '完成时间',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  INDEX idx_business_code (business_code),
  INDEX idx_business_id (business_id),
  INDEX idx_submitter_id (submitter_id),
  INDEX idx_status (status),
  INDEX idx_tenant_id (tenant_id)
);
```

### 业务表标准字段

```sql
-- 业务表标准工作流字段
ALTER TABLE business_table ADD COLUMN (
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回',
  approval_node varchar(100) COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime COMMENT '提交时间',
  approved_at datetime COMMENT '审批完成时间',
  
  INDEX idx_instance_id (instance_id),
  INDEX idx_approval_status (approval_status)
);
```

## 🔧 核心组件实现

### FormManager工作流集成

```vue
<!-- FormManager.vue -->
<template>
  <div class="form-manager">
    <!-- 动态加载业务表单组件 -->
    <component
      :is="currentFormComponent"
      v-if="currentFormComponent"
      v-model="formState.visible"
      :formId="formState.currentFormId"
      :definitionId="formState.typeId"
      :mode="formState.mode"
      @submit="handleSubmitForm"
      @save="handleSaveForm"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
      ref="formComponentRef"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  modelValue: boolean
  type: string                    // 业务类型 (businessCode)
  workflowTypeId: number         // 工作流类型ID
  formId?: number                // 表单ID（编辑时）
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'success', 'cancel'])

// 表单状态
const formState = reactive({
  visible: false,
  type: '',
  typeId: 0,
  currentFormId: 0,
  mode: 'create' as 'create' | 'edit',
  loading: false,
  preloadedEditData: null as any
})

// 动态加载表单组件
const currentFormComponent = computed(() => {
  if (!formState.type) return null

  try {
    return markRaw(
      defineAsyncComponent(() =>
        import(`../components/business-forms/${formState.type}-form.vue`).catch((error) => {
          console.error(`加载表单组件失败: ${formState.type}-form.vue`, error)
          return import('../components/business-forms/generic-form.vue')
        })
      )
    )
  } catch (error) {
    console.error(`加载表单组件异常: ${formState.type}-form.vue`, error)
    return null
  }
})

// 统一处理表单提交
const handleSubmitForm = async (formData: any) => {
  console.log('FormManager.handleSubmitForm 被调用，数据:', formData)
  if (formState.loading) return
  formState.loading = true

  try {
    const res = await ApplicationApi.submit({
      definition_id: formState.typeId,
      business_code: formState.type,
      business_data: formData
    })

    ElMessage.success(res.message || '申请提交成功')
    handleFormSuccess({
      type: 'submit',
      data: res.data
    })
  } catch (error) {
    console.error('申请提交失败:', error)
  } finally {
    formState.loading = false
    // 重置子组件的loading状态
    if (formComponentRef.value && formComponentRef.value.submitting !== undefined) {
      formComponentRef.value.submitting = false
    }
  }
}

// 统一处理表单保存
const handleSaveForm = async (formData: any) => {
  console.log('FormManager.handleSaveForm 被调用，数据:', formData)
  if (formState.loading) return
  formState.loading = true

  try {
    let res
    if (formState.currentFormId) {
      // 更新草稿
      res = await ApplicationApi.update(formState.currentFormId, {
        definition_id: formState.typeId,
        business_code: formState.type,
        business_data: formData
      })
    } else {
      // 保存新草稿
      res = await ApplicationApi.save({
        definition_id: formState.typeId,
        business_code: formState.type,
        business_data: formData
      })
    }

    ElMessage.success(res.message || '保存成功')
    handleFormSuccess({
      type: 'save',
      data: res.data
    })
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    formState.loading = false
  }
}

// 表单提交成功
const handleFormSuccess = (data: any) => {
  emit('success', {
    type: formState.type,
    ...data
  })
}

// 表单取消
const handleFormCancel = () => {
  emit('cancel')
}

// 显示表单
const showForm = async (formIdParam?: number | string) => {
  const idToUse = formIdParam || props.formId || formState.currentFormId
  console.log('FormManager.showForm - ID:', idToUse, '类型:', formState.type)

  // 检查是否有预加载的编辑数据
  if (formState.preloadedEditData) {
    console.log('FormManager: 使用预加载数据，跳过API请求')
    return
  }

  // 如果有表单ID，则先获取表单数据
  if (idToUse) {
    try {
      formState.loading = true
      console.log('FormManager: 请求详情数据，ID:', idToUse)
      const res = await ApplicationApi.detail(Number(idToUse))
      console.log('FormManager获取到的表单数据:', res.data)

      if (res.data) {
        // 更新表单类型和工作流ID
        formState.type = res.data.business_code || formState.type
        formState.typeId = res.data.definition_id || formState.typeId
        formState.mode = 'edit'

        // 更新当前表单ID
        formState.currentFormId = Number(idToUse)

        // 等待表单组件挂载完成后加载数据
        setTimeout(() => {
          if (formComponentRef.value?.showForm) {
            console.log('调用业务表单showForm方法，ID:', formState.currentFormId)
            formComponentRef.value.showForm(formState.currentFormId)
          } else {
            console.error('表单组件未挂载或没有showForm方法')
          }
        }, 200)
      }
    } catch (error) {
      console.error('获取表单数据失败:', error)
    } finally {
      formState.loading = false
    }
  } else {
    // 新建表单
    formState.mode = 'create'
    formState.currentFormId = 0
    
    setTimeout(() => {
      if (formComponentRef.value?.showForm) {
        formComponentRef.value.showForm()
      }
    }, 100)
  }
}

// 设置编辑数据
const setEditData = (editData: any) => {
  console.log('FormManager.setEditData 被调用，数据:', editData)
  formState.preloadedEditData = editData
  formState.currentFormId = editData.id || 0
  formState.mode = 'edit'

  // 等待组件挂载后设置数据
  nextTick(() => {
    if (formComponentRef.value?.setEditData) {
      formComponentRef.value.setEditData(editData)
    }
  })
}

// 暴露方法
defineExpose({
  showForm,
  setEditData
})
</script>
```

### FormDataViewer工作流集成

```vue
<!-- FormDataViewer.vue -->
<template>
  <div class="form-data-viewer" v-loading="loading">
    <!-- 动态业务详情组件 -->
    <component
      :is="businessDetailComponent"
      v-if="businessDetailComponent"
      :data="formData"
      :business-code="businessCode"
    />

    <!-- 通用表单数据展示（兜底） -->
    <div v-else-if="formData" class="generic-form-display">
      <el-descriptions border :column="2" size="small">
        <template v-for="(value, key) in formData" :key="key">
          <el-descriptions-item :label="formatLabel(key)" v-if="shouldDisplayField(key)">
            {{ formatValue(key, value) }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>

    <!-- 空数据提示 -->
    <div v-else class="empty-data">
      <el-empty description="暂无表单数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  formData: Record<string, any>
  businessCode: string
}

const props = defineProps<Props>()

// 加载状态
const loading = ref(false)

// 动态加载业务详情组件
const businessDetailComponent = computed(() => {
  if (!props.businessCode) return null
  
  return markRaw(defineAsyncComponent(() => 
    import(`@/components/business-detail/${props.businessCode}.vue`)
      .catch(() => {
        console.log(`未找到 ${props.businessCode} 专用详情组件，使用通用展示`)
        return null
      })
  ))
})

// 格式化字段标签
const formatLabel = (key: string): string => {
  const labelMap: Record<string, string> = {
    'contract_number': '合同编号',
    'contract_name': '合同名称',
    'contract_amount': '合同金额',
    'customer_name': '客户名称',
    'sign_date': '签署日期',
    'start_date': '开始日期',
    'end_date': '结束日期',
    'receivable_amount': '回款金额',
    'received_date': '回款日期',
    'payment_method': '付款方式',
    'leave_type': '请假类型',
    'start_time': '开始时间',
    'end_time': '结束时间',
    'duration': '请假时长',
    'reason': '请假原因'
  }
  
  return labelMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// 格式化字段值
const formatValue = (key: string, value: any): string => {
  if (value === null || value === undefined) return '-'
  
  // 金额格式化
  if (key.includes('amount') && typeof value === 'number') {
    return `¥${value.toLocaleString()}`
  }
  
  // 日期格式化
  if (key.includes('date') || key.includes('time')) {
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
  }
  
  // 布尔值格式化
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return String(value)
}

// 判断是否应该显示字段
const shouldDisplayField = (key: string): boolean => {
  const hiddenFields = ['id', 'created_at', 'updated_at', 'deleted_at', 'tenant_id']
  return !hiddenFields.includes(key) && !key.startsWith('_')
}
</script>
```

## 🔄 状态同步机制

### 工作流状态回调

```php
<?php
// app/workflow/service/WorkflowCallbackService.php

namespace app\workflow\service;

use app\common\service\BaseService;

class WorkflowCallbackService extends BaseService
{
    /**
     * 处理工作流状态变更回调
     */
    public function handleStatusChange(array $data): bool
    {
        $instanceId = $data['instance_id'];
        $status = $data['status'];
        $businessCode = $data['business_code'];
        $businessId = $data['business_id'];
        
        try {
            // 更新工作流实例状态
            $this->updateInstanceStatus($instanceId, $status, $data);
            
            // 同步业务表状态
            $this->syncBusinessStatus($businessCode, $businessId, $status, $data);
            
            // 发送状态变更通知
            $this->sendStatusNotification($data);
            
            return true;
        } catch (\Exception $e) {
            $this->logError('工作流状态同步失败', [
                'instance_id' => $instanceId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 更新工作流实例状态
     */
    private function updateInstanceStatus(int $instanceId, int $status, array $data): void
    {
        $updateData = [
            'status' => $status,
            'current_node' => $data['current_node'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status == 2) { // 已通过
            $updateData['finished_at'] = date('Y-m-d H:i:s');
        }
        
        db('workflow_instance')
            ->where('id', $instanceId)
            ->update($updateData);
    }
    
    /**
     * 同步业务表状态
     */
    private function syncBusinessStatus(string $businessCode, int $businessId, int $status, array $data): void
    {
        if (!$businessId) return;
        
        // 获取业务表名
        $tableName = $this->getBusinessTableName($businessCode);
        if (!$tableName) return;
        
        $updateData = [
            'approval_status' => $status,
            'approval_node' => $data['current_node'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status == 2) { // 已通过
            $updateData['approved_at'] = date('Y-m-d H:i:s');
        }
        
        db($tableName)
            ->where('id', $businessId)
            ->update($updateData);
    }
    
    /**
     * 发送状态变更通知
     */
    private function sendStatusNotification(array $data): void
    {
        // WebSocket推送
        if (class_exists('\Workerman\Worker')) {
            $this->pushWebSocketMessage([
                'type' => 'workflow_status_change',
                'data' => $data
            ]);
        }
        
        // 邮件通知
        $this->sendEmailNotification($data);
        
        // 站内消息
        $this->sendInternalMessage($data);
    }
    
    /**
     * 获取业务表名
     */
    private function getBusinessTableName(string $businessCode): ?string
    {
        $tableMap = [
            'crm_contract' => 'crm_contract',
            'crm_contract_receivable' => 'crm_contract_receivable',
            'daily_price_order' => 'daily_price_order',
            'hr_leave' => 'hr_leave',
            'hr_travel' => 'hr_travel'
        ];
        
        return $tableMap[$businessCode] ?? null;
    }
}
```

### 前端状态监听

```typescript
// composables/useWorkflowStatus.ts
export function useWorkflowStatus(instanceId: number) {
  const status = ref<number>(0)
  const statusText = ref<string>('')
  const currentNode = ref<string>('')
  
  // 状态映射
  const statusMap = {
    0: '草稿',
    1: '审批中',
    2: '已通过',
    3: '已驳回',
    4: '已终止',
    5: '已撤回'
  }
  
  // WebSocket监听
  const { data } = useWebSocket('ws://localhost:8080/ws')
  
  watch(data, (newData) => {
    if (!newData) return
    
    try {
      const message = JSON.parse(newData)
      if (message.type === 'workflow_status_change' && 
          message.data.instance_id === instanceId) {
        updateStatus(message.data.status, message.data.current_node)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  })
  
  // 更新状态
  const updateStatus = (newStatus: number, node: string = '') => {
    status.value = newStatus
    statusText.value = statusMap[newStatus] || '未知状态'
    currentNode.value = node
  }
  
  // 获取状态颜色
  const getStatusColor = computed(() => {
    const colorMap = {
      0: 'info',     // 草稿
      1: 'warning',  // 审批中
      2: 'success',  // 已通过
      3: 'danger',   // 已驳回
      4: 'info',     // 已终止
      5: 'warning'   // 已撤回
    }
    return colorMap[status.value] || 'info'
  })
  
  // 初始化状态
  const initStatus = async () => {
    try {
      const res = await ApplicationApi.detail(instanceId)
      if (res.data) {
        updateStatus(res.data.status, res.data.current_node)
      }
    } catch (error) {
      console.error('获取工作流状态失败:', error)
    }
  }
  
  onMounted(() => {
    initStatus()
  })
  
  return {
    status: readonly(status),
    statusText: readonly(statusText),
    currentNode: readonly(currentNode),
    statusColor: getStatusColor,
    updateStatus,
    initStatus
  }
}
```

## 🎨 工作流页面集成

### 审批页面集成

```vue
<!-- WorkflowTask.vue -->
<template>
  <div class="workflow-task-page">
    <!-- 任务列表 -->
    <ArtTable
      :columns="columns"
      :fetch-data="fetchData"
      :search-form-items="searchFormItems"
      @row-click="handleRowClick"
    />
    
    <!-- 审批详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      :title="detailTitle"
      width="80%"
      destroy-on-close
    >
      <!-- 使用统一的详情查看器 -->
      <FormDataViewer
        :form-data="currentTaskData.instance?.form_data || {}"
        :business-code="currentTaskData.instance?.business_code || ''"
      />
      
      <!-- 审批操作 -->
      <template #footer>
        <div class="approval-actions">
          <el-button @click="detailVisible = false">关闭</el-button>
          <el-button type="success" @click="handleApprove">通过</el-button>
          <el-button type="danger" @click="handleReject">驳回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { WorkflowTaskApi } from '@/api/workflow/workflowTask'
import FormDataViewer from '@/views/workflow/components/form-data-viewer.vue'

// 详情对话框状态
const detailVisible = ref(false)
const detailTitle = ref('')
const currentTaskData = ref<any>({})

// 处理行点击
const handleRowClick = async (row: any) => {
  try {
    // 获取任务详情
    const res = await WorkflowTaskApi.detail(row.id)
    if (res.code === 1) {
      currentTaskData.value = res.data
      detailTitle.value = `审批详情 - ${res.data.instance?.title || ''}`
      detailVisible.value = true
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  }
}

// 处理审批通过
const handleApprove = async () => {
  try {
    const res = await WorkflowTaskApi.approve(currentTaskData.value.id, {
      comment: '审批通过'
    })
    
    if (res.code === 1) {
      ElMessage.success('审批成功')
      detailVisible.value = false
      // 刷新列表
      await fetchData()
    }
  } catch (error) {
    console.error('审批失败:', error)
    ElMessage.error('审批失败')
  }
}

// 处理审批驳回
const handleReject = async () => {
  try {
    const res = await WorkflowTaskApi.reject(currentTaskData.value.id, {
      comment: '审批驳回'
    })
    
    if (res.code === 1) {
      ElMessage.success('驳回成功')
      detailVisible.value = false
      // 刷新列表
      await fetchData()
    }
  } catch (error) {
    console.error('驳回失败:', error)
    ElMessage.error('驳回失败')
  }
}
</script>
```

### 抄送页面集成

```vue
<!-- WorkflowCc.vue -->
<template>
  <div class="workflow-cc-page">
    <!-- 抄送列表 -->
    <ArtTable
      :columns="columns"
      :fetch-data="fetchData"
      :search-form-items="searchFormItems"
      @row-click="handleRowClick"
    />
    
    <!-- 抄送详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      :title="detailTitle"
      width="80%"
      destroy-on-close
    >
      <el-tabs v-model="activeTab">
        <!-- 表单数据标签页 -->
        <el-tab-pane label="表单数据" name="form">
          <FormDataViewer
            :form-data="detailData.form_data || {}"
            :business-code="detailData.business_code || ''"
          />
        </el-tab-pane>
        
        <!-- 审批历史标签页 -->
        <el-tab-pane label="审批历史" name="history">
          <WorkflowHistory :instance-id="detailData.id" />
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { WorkflowCcApi } from '@/api/workflow/workflowCc'
import FormDataViewer from '@/views/workflow/components/form-data-viewer.vue'
import WorkflowHistory from '@/views/workflow/components/workflow-history.vue'

// 详情对话框状态
const detailVisible = ref(false)
const detailTitle = ref('')
const detailData = ref<any>({})
const activeTab = ref('form')

// 处理行点击
const handleRowClick = async (row: any) => {
  try {
    // 获取抄送详情
    const res = await WorkflowCcApi.detail(row.instance_id)
    if (res.code === 1) {
      detailData.value = res.data
      detailTitle.value = `抄送详情 - ${res.data.title || ''}`
      detailVisible.value = true
      activeTab.value = 'form'
    }
  } catch (error) {
    console.error('获取抄送详情失败:', error)
    ElMessage.error('获取抄送详情失败')
  }
}
</script>
```

## 📊 性能优化

### 组件缓存优化

```typescript
// utils/workflowCache.ts
class WorkflowComponentCache {
  private cache = new Map<string, any>()
  private ttl = 10 * 60 * 1000 // 10分钟缓存
  
  // 缓存工作流详情
  cacheWorkflowDetail(instanceId: number, data: any) {
    const key = `workflow_detail_${instanceId}`
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  // 获取缓存的工作流详情
  getCachedWorkflowDetail(instanceId: number): any | null {
    const key = `workflow_detail_${instanceId}`
    const cached = this.cache.get(key)
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data
    }
    
    this.cache.delete(key)
    return null
  }
  
  // 清除相关缓存
  clearWorkflowCache(instanceId: number) {
    const key = `workflow_detail_${instanceId}`
    this.cache.delete(key)
  }
  
  // 批量清除缓存
  clearAllCache() {
    this.cache.clear()
  }
}

export const workflowCache = new WorkflowComponentCache()
```

### 懒加载优化

```typescript
// composables/useLazyWorkflowData.ts
export function useLazyWorkflowData(instanceId: Ref<number>) {
  const data = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 懒加载数据
  const loadData = async () => {
    if (!instanceId.value) return
    
    // 检查缓存
    const cached = workflowCache.getCachedWorkflowDetail(instanceId.value)
    if (cached) {
      data.value = cached
      return cached
    }
    
    loading.value = true
    error.value = null
    
    try {
      const res = await ApplicationApi.detail(instanceId.value)
      data.value = res.data
      
      // 缓存数据
      workflowCache.cacheWorkflowDetail(instanceId.value, res.data)
      
      return res.data
    } catch (err) {
      error.value = err.message || '加载失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 监听instanceId变化
  watch(instanceId, (newId) => {
    if (newId) {
      loadData()
    }
  }, { immediate: true })
  
  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    loadData,
    refresh: loadData
  }
}
```

## 🔍 监控与调试

### 工作流集成监控

```typescript
// utils/workflowMonitor.ts
class WorkflowIntegrationMonitor {
  private metrics = new Map<string, any>()
  
  // 监控组件加载性能
  monitorComponentLoad(businessCode: string, startTime: number) {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.recordMetric('component_load', {
      businessCode,
      duration,
      timestamp: Date.now()
    })
  }
  
  // 监控数据加载性能
  monitorDataLoad(operation: string, duration: number, success: boolean) {
    this.recordMetric('data_load', {
      operation,
      duration,
      success,
      timestamp: Date.now()
    })
  }
  
  // 监控状态同步
  monitorStatusSync(instanceId: number, oldStatus: number, newStatus: number) {
    this.recordMetric('status_sync', {
      instanceId,
      oldStatus,
      newStatus,
      timestamp: Date.now()
    })
  }
  
  // 记录指标
  private recordMetric(type: string, data: any) {
    // 发送到监控系统
    if (window.monitoringService) {
      window.monitoringService.record(`workflow_${type}`, data)
    }
    
    // 开发环境日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`工作流监控 - ${type}:`, data)
    }
  }
}

export const workflowMonitor = new WorkflowIntegrationMonitor()
```

---

**注意：** 工作流集成是系统的核心功能，确保所有集成点都有适当的错误处理和监控机制。