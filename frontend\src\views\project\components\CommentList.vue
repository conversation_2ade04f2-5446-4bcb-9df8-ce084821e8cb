<template>
  <div class="comment-list">
    <div v-loading="loading" class="comment-container">
      <!-- 空状态 -->
      <div v-if="!loading && commentList.length === 0" class="empty-state">
        <el-empty description="暂无评论" :image-size="120" />
      </div>

      <!-- 评论列表 -->
      <div v-else class="comment-items">
        <div v-for="comment in commentList" :key="comment.id" class="comment-item">
          <div class="comment-header">
            <div class="user-info">
              <el-avatar :size="32" class="user-avatar">
                {{ comment.creator?.nickname?.charAt(0) || '用' }}
              </el-avatar>
              <div class="user-details">
                <span class="user-name">{{ comment.creator?.nickname || '未知用户' }}</span>
                <span class="comment-time">{{ formatDateTime(comment.created_at) }}</span>
              </div>
            </div>

            <div class="comment-actions" v-if="canEdit(comment) || canDelete(comment)">
              <el-dropdown trigger="click" placement="bottom-end">
                <el-button text size="small" class="action-btn">
                  <el-icon>
                    <MoreFilled />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="canEdit(comment)" @click="handleEdit(comment)">
                      <el-icon>
                        <Edit />
                      </el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-if="canDelete(comment)"
                      @click="handleDelete(comment)"
                      class="danger-item"
                    >
                      <el-icon>
                        <Delete />
                      </el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="comment-content">
            <p class="content-text">{{ comment.content }}</p>

            <!-- 附件列表 -->
            <div v-if="comment.attachments && comment.attachments.length > 0" class="attachments">
              <div
                v-for="(file, index) in parseAttachments(comment.attachments)"
                :key="index"
                class="attachment-item"
              >
                <el-icon>
                  <Paperclip />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { MoreFilled, Edit, Delete, Paperclip } from '@element-plus/icons-vue'
  import { TaskApi } from '@/api/project/projectApi'
  import { formatDateTime } from '@/utils/date'
import { useAuth } from '@/composables/useAuth'

  // Props
  interface Props {
    taskId: number
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    edit: [comment: any]
  }>()

  // 响应式数据
  const loading = ref(false)
  const commentList = ref<any[]>([])
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 权限验证
  const { hasAuth } = useAuth()

  // 权限判断方法
  const canEdit = (comment: any) => {
    // 检查编辑评论权限
    return hasAuth('project:task:comment:edit')
  }

  const canDelete = (comment: any) => {
    // 检查删除评论权限
    return hasAuth('project:task:comment:delete')
  }

  // 方法
  const loadComments = async () => {
    try {
      loading.value = true
      const response = await TaskApi.getComments(props.taskId, {
        page: currentPage.value,
        limit: pageSize.value
      })

      if (response.code === 200) {
        commentList.value = response.data.list || []
        total.value = response.data.total || 0
      }
    } catch (error) {
      console.error('加载评论失败:', error)
      ElMessage.error('加载评论失败')
    } finally {
      loading.value = false
    }
  }

  const parseAttachments = (attachments: string | any[]) => {
    if (!attachments) return []

    try {
      if (typeof attachments === 'string') {
        return JSON.parse(attachments)
      }
      return attachments
    } catch (error) {
      console.error('解析附件失败:', error)
      return []
    }
  }

  const handleEdit = (comment: any) => {
    emit('edit', comment)
  }

  const handleDelete = async (comment: any) => {
    try {
      await ElMessageBox.confirm('确定要删除这条评论吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await TaskApi.deleteRecord(comment.id)
      ElMessage.success('评论删除成功')

      // 重新加载评论列表
      await loadComments()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('删除评论失败:', error)
        ElMessage.error(error.message || '删除评论失败')
      }
    }
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    loadComments()
  }

  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    loadComments()
  }

  // 暴露方法给父组件
  const refresh = () => {
    currentPage.value = 1
    loadComments()
  }

  defineExpose({
    refresh
  })

  // 生命周期
  onMounted(() => {
    loadComments()
  })
</script>

<style lang="scss" scoped>
  .comment-list {
    .comment-container {
      min-height: 200px;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }

    .comment-items {
      .comment-item {
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .comment-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .user-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .user-avatar {
              background-color: #409eff;
              color: white;
              font-weight: 500;
            }

            .user-details {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .user-name {
                font-weight: 500;
                color: #303133;
                font-size: 14px;
              }

              .comment-time {
                font-size: 12px;
                color: #909399;
              }
            }
          }

          .comment-actions {
            .action-btn {
              color: #909399;

              &:hover {
                color: #409eff;
              }
            }
          }
        }

        .comment-content {
          margin-left: 44px;

          .content-text {
            margin: 0 0 12px 0;
            line-height: 1.6;
            color: #606266;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .attachments {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .attachment-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 12px;
              background-color: #f5f7fa;
              border-radius: 6px;
              font-size: 13px;
              color: #606266;

              .el-icon {
                color: #909399;
              }

              .file-name {
                flex: 1;
              }
            }
          }
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 24px;
    }
  }

  :deep(.el-dropdown-menu__item.danger-item) {
    color: #f56c6c;

    &:hover {
      background-color: #fef0f0;
      color: #f56c6c;
    }
  }
</style>
