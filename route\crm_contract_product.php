<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 合同产品明细表路由
Route::group('api/crm/crm_contract_product', function () {
	Route::get('index', 'app\crm\controller\CrmContractProductController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmContractProductController@detail');
	Route::post('add', 'app\crm\controller\CrmContractProductController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmContractProductController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmContractProductController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmContractProductController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmContractProductController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmContractProductController@status');
	Route::post('import', 'app\crm\controller\CrmContractProductController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmContractProductController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmContractProductController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmContractProductController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);