<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElMessageBox, ElDescriptions, ElDescriptionsItem, ElTag } from 'element-plus'
  import { BgColorEnum } from '@/enums/appEnum'
  import { CrmContactApi } from '@/api/crm/crmContact'
  import { CrmCustomerMyApi } from '@/api/crm/crmCustomerMy'
  import { ApiStatus } from '@/utils/http/status'

  import { TagColumn } from '@/components/core/tables/columns'

  import FormDialog from './form-dialog.vue'

  import ImportExportDialog from './import-export-dialog.vue'

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 客户搜索选项
  const customerSearchOptions = ref<Array<{ label: string; value: number }>>([])
  const loadingCustomerOptions = ref(false)

  // 定义表单搜索初始值
  const initialSearchState = {}

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      prop: 'customer_id',
      label: '客户',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择客户'
      },
      options: () => customerSearchOptions.value,
      onChange: handleFormChange
    },
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入联系人姓名关键词'
      },
      onChange: handleFormChange
    },
    {
      prop: 'gender',
      label: '性别',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择性别'
      },
      options: () => [
        { label: '男', value: 1 },
        { label: '女', value: 2 }
      ],
      onChange: handleFormChange
    },
    {
      prop: 'mobile',
      label: '手机号',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入手机号关键词'
      },
      onChange: handleFormChange
    },
    {
      prop: 'importance',
      label: '重要程度',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择重要程度'
      },
      options: () => [
        { label: '普通', value: 0 },
        { label: '重要', value: 1 },
        { label: '核心', value: 2 }
      ],
      onChange: handleFormChange
    },
    {
      prop: 'role_type',
      label: '角色类型',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请输入角色类型'
      },
      options: () => [
        { label: '决策者', value: 'decision' },
        { label: '使用者', value: 'user' },
        { label: '影响者', value: 'influence' }
      ],
      onChange: handleFormChange
    }
  ]

  // 获取性别标签信息
  const getGenderTag = (value: number) => {
    const options = [
      { label: '未知', value: 0, type: 'info' as const },
      { label: '男', value: 1, type: 'primary' as const },
      { label: '女', value: 2, type: 'danger' as const }
    ]
    return options.find((option) => option.value === value) || { label: '-', type: 'info' as const }
  }

  // 获取重要程度标签信息
  const getImportanceTag = (value: number) => {
    const options = [
      { label: '普通', value: 0, type: 'info' as const },
      { label: '重要', value: 1, type: 'warning' as const },
      { label: '核心', value: 2, type: 'danger' as const }
    ]
    return options.find((option) => option.value === value) || { label: '-', type: 'info' as const }
  }

  // 获取角色类型标签信息
  const getRoleTypeTag = (value: string) => {
    const options = [
      { label: '决策者', value: 'decision', type: 'danger' as const },
      { label: '使用者', value: 'user', type: 'primary' as const },
      { label: '影响者', value: 'influence', type: 'warning' as const }
    ]
    return options.find((option) => option.value === value) || { label: '-', type: 'info' as const }
  }

  // 获取主要联系人标签信息
  const getPrimaryTag = (value: number) => {
    const options = [
      { label: '否', value: 0, type: 'info' as const },
      { label: '是', value: 1, type: 'success' as const }
    ]
    return options.find((option) => option.value === value) || { label: '-', type: 'info' as const }
  }

  // 获取性别CSS类
  const getGenderClass = (value: number) => {
    switch (value) {
      case 1:
        return 'gender-male'
      case 2:
        return 'gender-female'
      default:
        return 'gender-unknown'
    }
  }

  // 加载客户搜索选项
  const loadCustomerSearchOptions = async () => {
    try {
      loadingCustomerOptions.value = true
      const res = await CrmCustomerMyApi.options()
      if (res.code === ApiStatus.success) {
        // API返回的数据已经是 {label, value} 格式，直接使用
        customerSearchOptions.value = res.data
      }
    } catch (error) {
      console.error('加载客户选项失败:', error)
    } finally {
      loadingCustomerOptions.value = false
    }
  }

  onMounted(() => {
    getTableData()
    loadCustomerSearchOptions()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmContactApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await CrmContactApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  // 这个函数已经在下面的条件块中定义了

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmContactApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } finally {
      loading.value = false
    }
  }

  // 导入导出对话框引用
  const importExportDialogRef = ref()

  // 显示导入对话框
  const showImportDialog = () => {
    importExportDialogRef.value?.showDialog('import')
  }

  // 导入导出成功回调
  const handleImportExportSuccess = () => {
    getTableData()
  }

  // 显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }

  // 表单对话框引用
  const formDialogRef = ref()

  // 显示表单对话框
  const showFormDialog = (type: string, id?: number) => {
    formDialogRef.value?.showDialog(type, id)
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-crmContact-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columns="[]" @refresh="handleRefresh">
          <template #left>
            <ElButton type="primary" @click="showFormDialog('add')">新增</ElButton>

            <ElButton type="success" @click="showImportDialog">导入</ElButton>

            <ElButton type="warning" @click="showExportDialog">导出</ElButton>
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- 序号列 -->
          <ElTableColumn type="index" label="序号" width="60" />

          <!-- 客户名称列 -->
          <ElTableColumn prop="customer_name" label="客户名称" width="150" />

          <!-- 重要程度列 - 使用TagColumn -->
          <TagColumn
            prop="importance"
            label="重要程度"
            width="100"
            :options="[
              { label: '普通', value: 0, type: 'info' },
              { label: '重要', value: 1, type: 'warning' },
              { label: '核心', value: 2, type: 'danger' }
            ]"
          />

          <!-- 角色类型列 - 使用TagColumn -->
          <TagColumn
            prop="role_type"
            label="角色类型"
            width="100"
            :options="[
              { label: '决策者', value: 'decision', type: 'danger' },
              { label: '使用者', value: 'user', type: 'primary' },
              { label: '影响者', value: 'influence', type: 'warning' }
            ]"
          />

          <!-- 主要联系人列 -->
          <ElTableColumn prop="is_primary" label="主要联系人" width="100">
            <template #default="scope">
              <ElTag
                v-if="scope.row.is_primary !== undefined && scope.row.is_primary !== null"
                :type="getPrimaryTag(scope.row.is_primary).type"
                size="small"
              >
                {{ getPrimaryTag(scope.row.is_primary).label }}
              </ElTag>
              <span v-else>-</span>
            </template>
          </ElTableColumn>

          <!-- 基础信息合并列 -->
          <ElTableColumn label="基础信息" width="200">
            <template #default="scope">
              <div class="info-column">
                <div class="info-item">
                  <span class="info-label primary">姓名：</span>
                  <span class="info-value primary">{{ scope.row.name || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label tertiary">性别：</span>
                  <span class="info-value" :class="getGenderClass(scope.row.gender)">{{
                    getGenderTag(scope.row.gender).label
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label secondary">职位：</span>
                  <span class="info-value secondary">{{ scope.row.position || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label tertiary">部门：</span>
                  <span class="info-value tertiary">{{ scope.row.department || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 联系信息合并列 -->
          <ElTableColumn label="联系信息" width="200">
            <template #default="scope">
              <div class="info-column">
                <div class="info-item">
                  <span class="info-label primary">手机：</span>
                  <span class="info-value primary">{{ scope.row.mobile || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label tertiary">电话：</span>
                  <span class="info-value tertiary">{{ scope.row.phone || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label secondary">微信：</span>
                  <span class="info-value secondary">{{ scope.row.wechat || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label tertiary">QQ：</span>
                  <span class="info-value tertiary">{{ scope.row.qq || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 详细信息合并列 -->
          <ElTableColumn label="详细信息" width="200">
            <template #default="scope">
              <div class="info-column">
                <div class="info-item">
                  <span class="info-label tertiary">生日：</span>
                  <span class="info-value tertiary">{{ scope.row.birthday || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label tertiary">地址：</span>
                  <span class="info-value tertiary">{{ scope.row.address || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 邮箱列 -->
          <ElTableColumn prop="email" label="邮箱" width="180" />

          <!-- 备注列 -->
          <ElTableColumn prop="remark" label="备注" width="150">
            <template #default="scope">
              <span :title="scope.row.remark">
                {{
                  scope.row.remark && scope.row.remark.length > 20
                    ? scope.row.remark.substring(0, 20) + '...'
                    : scope.row.remark || '-'
                }}
              </span>
            </template>
          </ElTableColumn>

          <!-- 创建人列 -->
          <ElTableColumn prop="creator_name" label="创建人" width="100" />

          <!-- 创建时间列 -->
          <ElTableColumn prop="created_at" label="创建时间" width="180" />

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="240">
            <template #default="scope">
              <div>
                <ArtButtonTable text="详情" @click="showDetail(scope.row.id)" />
                <ArtButtonTable
                  text="编辑"
                  :iconClass="BgColorEnum.PRIMARY"
                  @click="showFormDialog('edit', scope.row.id)"
                />
                <ArtButtonTable
                  text="删除"
                  :iconClass="BgColorEnum.DANGER"
                  @click="handleDelete(scope.row.id)"
                />
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="联系人表详情"
          width="700px"
          destroy-on-close
          class="detail-dialog"
        >
          <div class="detail-content" style="height: 500px; overflow-y: auto; padding-right: 10px">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="ID">
                {{ detailData.id || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="客户名称">
                {{ detailData.customer_name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="联系人姓名">
                {{ detailData.name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="性别">
                <ElTag
                  v-if="detailData.gender !== undefined && detailData.gender !== null"
                  :type="getGenderTag(detailData.gender).type"
                  size="small"
                >
                  {{ getGenderTag(detailData.gender).label }}
                </ElTag>
                <span v-else>-</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="职位">
                {{ detailData.position || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="部门">
                {{ detailData.department || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="手机号">
                {{ detailData.mobile || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="电话">
                {{ detailData.phone || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="邮箱">
                {{ detailData.email || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="微信">
                {{ detailData.wechat || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="QQ">
                {{ detailData.qq || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="重要程度">
                <ElTag
                  v-if="detailData.importance !== undefined && detailData.importance !== null"
                  :type="getImportanceTag(detailData.importance).type"
                  size="small"
                >
                  {{ getImportanceTag(detailData.importance).label }}
                </ElTag>
                <span v-else>-</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="角色类型">
                <ElTag
                  v-if="detailData.role_type"
                  :type="getRoleTypeTag(detailData.role_type).type"
                  size="small"
                >
                  {{ getRoleTypeTag(detailData.role_type).label }}
                </ElTag>
                <span v-else>-</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="生日">
                {{ detailData.birthday || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="地址">
                {{ detailData.address || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="备注">
                {{ detailData.remark || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="主要联系人">
                <ElTag
                  v-if="detailData.is_primary !== undefined && detailData.is_primary !== null"
                  :type="getPrimaryTag(detailData.is_primary).type"
                  size="small"
                >
                  {{ getPrimaryTag(detailData.is_primary).label }}
                </ElTag>
                <span v-else>-</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建人">
                {{ detailData.creator_name || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建时间">
                {{ detailData.created_at || '-' }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 表单组件 -->
        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />

        <!-- 导入导出对话框 -->
        <ImportExportDialog ref="importExportDialogRef" @success="handleImportExportSuccess" />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-crmContact-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    /* 合并列样式 */
    .info-column {
      line-height: 1.6;
    }

    .info-item {
      margin-bottom: 6px;
      font-size: 14px;
    }

    .info-item:last-child {
      margin-bottom: 0;
    }

    .info-label {
      min-width: 45px;
      display: inline-block;
    }

    /* 核心信息 - 使用系统颜色变量，强烈突出 */
    .info-label.primary {
      color: var(--art-text-gray-900);
      font-weight: 700;
    }

    .info-value.primary {
      color: var(--art-text-gray-800);
      font-weight: 600;
    }

    /* 次要信息 - 使用系统颜色变量，适度区分 */
    .info-label.secondary {
      color: var(--art-text-gray-600);
      font-weight: 600;
    }

    .info-value.secondary {
      color: var(--art-text-gray-700);
      font-weight: 500;
    }

    /* 补充信息 - 使用系统颜色变量，明显降级 */
    .info-label.tertiary {
      color: var(--art-text-gray-500);
      font-weight: 400;
    }

    .info-value.tertiary {
      color: var(--art-text-gray-600);
      font-weight: 400;
    }

    /* 性别颜色样式 - 使用系统主题色 */
    .info-value.gender-male {
      color: rgb(var(--art-info)); /* 系统信息色（蓝色） */
      font-weight: 500;
    }

    .info-value.gender-female {
      color: rgb(var(--art-danger)); /* 系统危险色（红色） */
      font-weight: 500;
    }

    .info-value.gender-unknown {
      color: var(--art-text-gray-600); /* 系统中灰色 */
      font-weight: 400;
    }

    /* 默认值样式 */
    .info-value {
      word-break: break-all;
    }

    /* 详情对话框固定高度样式 */
    :deep(.detail-dialog) {
      .el-dialog__body {
        height: 500px !important;
        padding: 20px !important;
        overflow: hidden !important;
      }

      .detail-content {
        height: 100%;
        overflow-y: auto;
        padding-right: 10px;
      }

      /* 滚动条样式优化 */
      .detail-content::-webkit-scrollbar {
        width: 6px;
      }

      .detail-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      .detail-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
      }
    }
  }
</style>
