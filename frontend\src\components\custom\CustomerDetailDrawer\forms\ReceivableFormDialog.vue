<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑回款记录' : '新增回款记录'"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
      v-loading="formLoading"
    >
      <!--      <el-form-item label="回款编号" prop="receivable_number">
              <el-input v-model="formData.receivable_number" placeholder="请输入回款编号" />
            </el-form-item>-->

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="回款金额" prop="amount">
            <el-input-number
              v-model="formData.amount"
              :min="0"
              :precision="2"
              placeholder="请输入回款金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="回款日期" prop="received_date">
            <el-date-picker
              v-model="formData.received_date"
              type="date"
              placeholder="请选择回款日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="付款方式" prop="payment_method">
        <el-select v-model="formData.payment_method" placeholder="请选择付款方式">
          <el-option label="银行转账" value="银行转账" />
          <el-option label="支票" value="支票" />
          <el-option label="现金" value="现金" />
          <el-option label="支付宝" value="支付宝" />
          <el-option label="微信" value="微信" />
        </el-select>
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="收款银行" prop="bank_name">
            <el-input
              v-model="formData.bank_name"
              placeholder="请输入收款银行名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="银行账号" prop="bank_account">
            <el-input
              v-model="formData.bank_account"
              placeholder="请输入银行账号"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="回款凭证" prop="voucher_files">
        <FormUploader
          v-model="formData.voucher_files"
          fileType="image"
          :limit="3"
          :multiple="true"
          returnValueMode="string"
          buttonText="选择回款凭证"
          tipText="支持上传图片格式，最多3个。可按住Ctrl键选择多个文件"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="success" plain :loading="loading" @click="handleSave"> 保存草稿</el-button>
        <el-button
          v-if="!isEdit || canSubmit"
          type="primary"
          :loading="loading"
          @click="handleSubmitApproval"
        >
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  import { FormUploader } from '@/components/custom/FormUploader'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number
    contractId?: number
    receivableData?: any
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: 0,
    contractId: 0,
    receivableData: null
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const formLoading = ref(false)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const isEdit = computed(() => !!props.receivableData?.id)

  // 表单数据
  const formData = reactive({
    // receivable_number: '',
    amount: 0,
    received_date: '',
    payment_method: '',
    bank_name: '',
    bank_account: '',
    voucher_files: '',
    remark: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    // receivable_number: [{ required: true, message: '请输入回款编号', trigger: 'blur' }],
    amount: [
      { required: true, message: '请输入回款金额', trigger: 'blur' },
      {
        validator: (rule: any, value: any, callback: any) => {
          if (value === '' || value === null || value === undefined) {
            callback(new Error('请输入回款金额'))
          } else if (isNaN(Number(value))) {
            callback(new Error('回款金额必须是数字'))
          } else if (Number(value) <= 0) {
            callback(new Error('回款金额必须大于0'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    received_date: [{ required: true, message: '请选择回款日期', trigger: 'change' }],
    payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
    bank_name: [{ max: 100, message: '收款银行名称长度不能超过100个字符', trigger: 'blur' }],
    bank_account: [{ max: 50, message: '银行账号长度不能超过50个字符', trigger: 'blur' }]
  }

  // 监听回款数据变化
  watch(
    () => props.receivableData,
    (newData) => {
      if (newData) {
        formLoading.value = true
        // 模拟数据加载延迟，实际中数据已经通过API获取
        setTimeout(() => {
          Object.assign(formData, {
            // receivable_number: newData.receivable_number || '',
            amount:
              typeof newData.amount === 'string' ? parseFloat(newData.amount) : newData.amount || 0,
            received_date: newData.received_date || '',
            payment_method: newData.payment_method || '',
            bank_name: newData.bank_name || '',
            bank_account: newData.bank_account || '',
            voucher_files: newData.voucher_files || '',
            remark: newData.remark || ''
          })
          formLoading.value = false
        }, 100)
      } else {
        formLoading.value = false
      }
    },
    { immediate: true }
  )

  // 计算是否可以提交
  const canSubmit = computed(() => {
    // 新增时总是可以提交，编辑时需要检查状态
    if (!isEdit.value) return true
    // 这里可以根据收款的workflow状态来判断是否可以提交
    return true
  })

  // 保存表单（不提交审批）
  const handleSave = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      const submitData = {
        ...formData,
        customer_id: props.customerId,
        contract_id: props.contractId
      }

      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editReceivable(props.receivableData.id, submitData)
      } else {
        res = await CrmCustomerDetailApi.addReceivable(props.contractId!, submitData)
      }

      if (res.code === ApiStatus.success) {
        ElMessage.success('保存成功')
        visible.value = false
        emit('success')
      }
    } catch (error) {
      console.error('保存表单失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 提交审批
  const handleSubmitApproval = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      const submitData = {
        ...formData,
        customer_id: props.customerId,
        contract_id: props.contractId
      }

      // 先保存表单
      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editReceivable(props.receivableData.id, submitData)
      } else {
        res = await CrmCustomerDetailApi.addReceivable(props.contractId!, submitData)
      }

      if (res.code === ApiStatus.success) {
        // 保存成功后提交审批
        const receivableId = isEdit.value ? props.receivableData.id : res.data
        const submitRes = await CrmCustomerDetailApi.submitReceivableApproval(receivableId)

        if (submitRes.code === ApiStatus.success) {
          ElMessage.success('提交审批成功')
          visible.value = false
          emit('success')
        }
      }
    } catch (error) {
      console.error('提交审批失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭处理
  const handleClosed = () => {
    formRef.value?.resetFields()
    formLoading.value = false
    Object.assign(formData, {
      // receivable_number: '',
      amount: 0,
      received_date: '',
      payment_method: '',
      voucher_files: '',
      remark: ''
    })
  }
</script>

<style scoped lang="scss">
  .dialog-footer {
    text-align: right;
  }
</style>
