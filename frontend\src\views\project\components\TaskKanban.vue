<template>
  <div class="task-kanban">
    <div class="kanban-columns">
      <div v-for="column in kanbanColumns" :key="column.status" class="kanban-column">
        <div class="column-header">
          <div class="column-title">
            <span class="title-text">{{ column.title }}</span>
            <span class="task-count">({{ column.tasks.length }})</span>
          </div>
          <el-button type="primary" text size="small" @click="handleAddTask(column.status)">
            <el-icon>
              <Plus />
            </el-icon>
          </el-button>
        </div>

        <div class="column-content">
          <div
            v-for="task in column.tasks"
            :key="task.id"
            class="task-card"
            @click="handleTaskClick(task.id)"
          >
            <div class="task-header">
              <div class="task-priority">
                <el-tag :type="getPriorityType(task.priority)" size="small" effect="plain">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-actions" @click.stop>
                <el-dropdown trigger="hover">
                  <el-button text circle size="small">
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleTaskClick(task.id)">
                        <el-icon>
                          <View />
                        </el-icon>
                        详情
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleEditTask(task)">
                        <el-icon>
                          <Edit />
                        </el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="handleDeleteTask(task)">
                        <el-icon>
                          <Delete />
                        </el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <div class="task-title">{{ task.title }}</div>

            <div class="task-description" v-if="task.description">
              {{ task.description }}
            </div>

            <div class="task-meta">
              <div class="task-assignee" v-if="task.assignee_name">
                <el-icon>
                  <User />
                </el-icon>
                <span>{{ task.assignee_name }}</span>
              </div>
              <div class="task-deadline" v-if="task.end_date">
                <el-icon>
                  <Calendar />
                </el-icon>
                <span :class="{ overdue: isOverdue(task.end_date) }">
                  {{ formatDate(task.end_date) }}
                </span>
              </div>
            </div>

            <!-- TODO: 任务工时进度暂时注释，后续需要完善工时估算准确性和管理机制 -->
            <!--
            <div class="task-progress" v-if="task.estimated_hours">
              <div class="progress-info">
                <span class="progress-label">工时</span>
                <span class="progress-value">
                  {{ task.actual_hours || 0 }}h / {{ task.estimated_hours }}h
                </span>
              </div>
              <el-progress
                :percentage="getProgressPercentage(task)"
                :stroke-width="4"
                :show-text="false"
                :color="getProgressColor(task)"
              />
            </div>
            -->

            <div class="task-footer">
              <div class="task-comments" v-if="task.comment_count">
                <el-icon>
                  <ChatDotRound />
                </el-icon>
                <span>{{ task.comment_count }}</span>
              </div>
              <div class="task-attachments" v-if="task.attachment_count">
                <el-icon>
                  <Paperclip />
                </el-icon>
                <span>{{ task.attachment_count }}</span>
              </div>
            </div>
          </div>

          <div
            v-if="column.tasks.length === 0"
            class="empty-column"
            @click="handleAddTask(column.status)"
          >
            <el-icon>
              <Plus />
            </el-icon>
            <span>添加任务</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    Plus,
    MoreFilled,
    View,
    Edit,
    Delete,
    User,
    Calendar,
    ChatDotRound,
    Paperclip
  } from '@element-plus/icons-vue'
  import { formatDate, isOverdue } from '@/utils/date'

  // Props
  interface Props {
    projectId: number
    kanbanData: any
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    'task-moved': [taskId: number, newStatus: number]
    'task-click': [taskId: number]
    'add-task': [status: number]
    'edit-task': [task: any]
    'delete-task': [taskId: number]
  }>()

  // 计算属性
  const kanbanColumns = computed(() => {
    const columns = [
      { status: 1, title: '待办', tasks: [] },
      { status: 2, title: '进行中', tasks: [] },
      { status: 3, title: '已完成', tasks: [] },
      { status: 4, title: '已关闭', tasks: [] }
    ]

    // 将任务分配到对应的列
    if (props.kanbanData && props.kanbanData.tasks && props.kanbanData.tasks.length > 0) {
      console.log('看板数据加载:', props.kanbanData.tasks.length, '个任务')

      props.kanbanData.tasks.forEach((task) => {
        const column = columns.find((col) => col.status === task.status)
        if (column) {
          column.tasks.push(task)
        } else {
          console.warn(`任务 "${task.title}" 的状态 ${task.status} 无法匹配到任何列`)
        }
      })
    }

    return columns
  })

  // 方法
  const handleTaskClick = (taskId: number) => {
    emit('task-click', taskId)
  }

  const handleAddTask = (status: number) => {
    emit('add-task', status)
  }

  const handleEditTask = (task: any) => {
    console.log('看板编辑任务:', task)
    emit('edit-task', task)
  }

  const handleDeleteTask = (task: any) => {
    console.log('看板删除任务:', task)
    emit('delete-task', task.id)
  }

  const getPriorityType = (priority: number) => {
    const typeMap = {
      1: 'info', // 低
      2: 'primary', // 中
      3: 'warning', // 高
      4: 'danger' // 紧急
    }
    return typeMap[priority] || 'info'
  }

  const getPriorityText = (priority: number) => {
    const textMap = {
      1: '低',
      2: '中',
      3: '高',
      4: '紧急'
    }
    return textMap[priority] || '未知'
  }

  const getProgressPercentage = (task: any) => {
    if (!task.estimated_hours || task.estimated_hours === 0) return 0
    const actual = task.actual_hours || 0
    return Math.min(Math.round((actual / task.estimated_hours) * 100), 100)
  }

  const getProgressColor = (task: any) => {
    const percentage = getProgressPercentage(task)
    if (percentage >= 100) return '#67c23a'
    if (percentage >= 80) return '#e6a23c'
    if (percentage >= 50) return '#409eff'
    return '#f56c6c'
  }
</script>

<style scoped lang="scss">
  .task-kanban {
    height: 100%;
    overflow: hidden;

    .kanban-columns {
      display: flex;
      gap: 16px;
      height: 100%;
      overflow-x: auto;
      padding-bottom: 16px;

      .kanban-column {
        flex: 1;
        min-width: 280px;
        background: #f7f8fa;
        border-radius: 8px;
        display: flex;
        flex-direction: column;

        // 黑暗模式适配
        html.dark & {
          background: var(--art-main-bg-color);
          border: 1px solid var(--art-border-color);
        }

        .column-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          border-bottom: 1px solid #e5e6eb;

          // 黑暗模式适配
          html.dark & {
            border-bottom-color: var(--art-border-color);
          }

          .column-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .title-text {
              font-weight: 600;
              color: #1f2329;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-900);
              }
            }

            .task-count {
              color: #86909c;
              font-size: 12px;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-600);
              }
            }
          }
        }

        .column-content {
          flex: 1;
          padding: 16px;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 12px;

          .task-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
              transform: translateY(-1px);
            }

            // 黑暗模式适配
            html.dark & {
              background: var(--art-color);
              box-shadow: var(--art-box-shadow-xs);
              border: 1px solid var(--art-border-color);

              &:hover {
                box-shadow: var(--art-box-shadow-sm);
              }
            }
          }

          .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .task-actions {
              // 为30岁以上用户群体设计的友好配色
              :deep(.el-button) {
                // 使用温和的蓝灰色，提供足够的对比度但不刺眼
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                color: #64748b;

                // 移除hover效果，使用更稳定的视觉状态
                &:hover {
                  background-color: #f1f5f9;
                  border-color: #cbd5e1;
                  color: #475569;
                  transform: none; // 移除动画效果
                }

                // 点击状态使用更明显但温和的反馈
                &:active {
                  background-color: #e2e8f0;
                  border-color: #94a3b8;
                  color: #334155;
                }

                // 图标样式优化
                .el-icon {
                  font-size: 14px;
                  font-weight: 500;
                }
              }

              // 下拉菜单样式优化
              :deep(.el-dropdown-menu) {
                border: 1px solid #e2e8f0;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                border-radius: 8px;
                padding: 4px 0;

                .el-dropdown-menu__item {
                  padding: 8px 16px;
                  font-size: 14px;
                  color: #475569;
                  line-height: 1.5;

                  &:hover {
                    background-color: #f8fafc;
                    color: #334155;
                  }

                  &:active {
                    background-color: #e2e8f0;
                  }

                  .el-icon {
                    margin-right: 8px;
                    font-size: 14px;
                    color: #64748b;
                  }

                  // 删除项特殊样式
                  &:last-child {
                    color: #dc2626;

                    &:hover {
                      background-color: #fef2f2;
                      color: #b91c1c;
                    }

                    .el-icon {
                      color: #dc2626;
                    }
                  }
                }
              }
            }
          }

          .task-title {
            font-weight: 500;
            color: #1f2329;
            margin-bottom: 6px;
            line-height: 1.4;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }

          .task-description {
            font-size: 12px;
            color: #86909c;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }

          .task-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;

            .task-assignee,
            .task-deadline {
              display: flex;
              align-items: center;
              gap: 4px;
              color: #86909c;

              .el-icon {
                font-size: 12px;
              }

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-600);
              }
            }

            .overdue {
              color: #ff4d4f;

              // 黑暗模式适配
              html.dark & {
                color: rgb(var(--art-danger));
              }
            }
          }

          .task-progress {
            margin-bottom: 8px;

            .progress-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;
              font-size: 12px;

              .progress-label {
                color: #86909c;

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-600);
                }
              }

              .progress-value {
                color: #1f2329;
                font-weight: 500;

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-900);
                }
              }
            }
          }

          .task-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            font-size: 12px;

            .task-comments,
            .task-attachments {
              display: flex;
              align-items: center;
              gap: 4px;
              color: #86909c;

              .el-icon {
                font-size: 12px;
              }

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-600);
              }
            }
          }
        }

        .empty-column {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 32px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          color: #86909c;

          &:hover {
            border-color: #1664ff;
            color: #1664ff;
          }

          // 黑暗模式适配
          html.dark & {
            border-color: var(--art-border-dashed-color);
            color: var(--art-text-gray-600);

            &:hover {
              border-color: rgb(var(--art-primary));
              color: rgb(var(--art-primary));
            }
          }

          .el-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }

          span {
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
