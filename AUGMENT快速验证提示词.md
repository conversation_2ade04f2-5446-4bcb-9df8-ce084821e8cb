# AUGMENT快速验证提示词 - CRM工作报告模块

## 🎯 快速验证当前状态

### 验证提示词：检查项目当前状态

```
请帮我快速验证CRM工作报告模块的当前状态。

背景信息：
- 项目路径：e:\项目\self_admin\base_admin
- CRUD生成器已执行完成
- MCP工具已配置并可用
- 需要确认生成的文件状态

请执行以下验证：

1. 检查后端生成文件：
   - 使用view工具查看：app/crm/controller/CrmWorkReportController.php
   - 使用view工具查看：app/crm/model/CrmWorkReport.php
   - 使用view工具查看：app/crm/service/CrmWorkReportService.php
   - 检查这些文件的基础CRUD功能是否完整

2. 检查前端生成文件：
   - 使用view工具查看：frontend/src/views/crm/crm_work_report/list.vue
   - 使用view工具查看：frontend/src/views/crm/crm_work_report/form-dialog.vue
   - 使用view工具查看：frontend/src/views/crm/crm_work_report/import-export-dialog.vue
   - 检查API接口文件：frontend/src/api/crm/crmWorkReportApi.ts

3. 检查数据库表结构：
   - 使用codebase-retrieval工具查询crm_work_report表的字段注释
   - 确认字段注释包含CRUD生成器标记

4. 验证MCP工具功能：
   - 测试view工具查看文件
   - 测试codebase-retrieval工具
   - 确认str-replace-editor工具可用

5. 检查项目结构：
   - 确认CRM模块路径正确
   - 确认前端路由配置
   - 检查是否有编译错误

请提供详细的验证报告，包括：
- 文件存在状态
- 代码结构是否完整
- 是否有明显的错误或缺失
- 下一步开发建议
```

## 🔧 MCP工具测试验证

### 验证提示词：测试MCP工具功能

```
请帮我测试所有MCP工具的功能是否正常。

请逐一测试以下工具：

1. **view工具测试**：
   - 查看目录：app/crm/controller
   - 查看文件：app/crm/controller/CrmWorkReportController.php（前20行）
   - 查看前端目录：frontend/src/views/crm/crm_work_report

2. **codebase-retrieval工具测试**：
   - 搜索：CRM工作报告相关的代码和配置
   - 搜索：crm_work_report表的相关信息

3. **str-replace-editor工具测试**：
   - 在CrmWorkReportController.php中添加一个测试注释
   - 然后删除这个测试注释

4. **launch-process工具测试**：
   - 尝试启动前端开发服务器（如果需要）
   - 检查进程状态

5. **diagnostics工具测试**：
   - 检查后端PHP文件是否有语法错误
   - 检查前端Vue文件是否有TypeScript错误

请报告每个工具的测试结果，如果有问题请说明具体错误信息。
```

## 🚀 开发环境验证

### 验证提示词：检查开发环境

```
请帮我验证CRM工作报告模块的开发环境是否就绪。

请检查以下环境：

1. **后端环境**：
   - 检查ThinkPHP框架是否正常
   - 检查数据库连接是否正常
   - 检查CRM模块路由是否配置正确

2. **前端环境**：
   - 检查Vue 3项目结构
   - 检查TypeScript配置
   - 检查Element Plus组件库
   - 检查现有CRM组件是否可用

3. **数据库环境**：
   - 确认crm_work_report表存在
   - 确认字段注释已优化
   - 确认索引配置正确

4. **开发服务器**：
   - 前端服务器：localhost:3006
   - 后端服务器：www.bs.com
   - 检查服务器是否可访问

5. **依赖检查**：
   - 检查后端依赖是否完整
   - 检查前端node_modules
   - 检查是否需要安装额外依赖（如富文本编辑器）

请提供环境检查报告，如果发现问题请提供解决建议。
```

## 📋 快速功能测试

### 验证提示词：测试基础CRUD功能

```
请帮我快速测试CRM工作报告模块的基础CRUD功能。

请执行以下测试：

1. **启动服务**：
   - 使用launch-process启动前端开发服务器
   - 确认服务器正常运行

2. **访问页面**：
   - 使用open-browser打开：http://localhost:3006/crm/work-report
   - 检查页面是否正常加载
   - 检查是否有JavaScript错误

3. **测试列表功能**：
   - 检查列表是否正常显示
   - 测试搜索功能
   - 测试分页功能

4. **测试表单功能**：
   - 点击"新增"按钮
   - 检查表单对话框是否正常打开
   - 测试表单验证

5. **测试API接口**：
   - 检查网络请求是否正常
   - 检查API响应格式
   - 检查错误处理

6. **检查控制台**：
   - 查看浏览器控制台是否有错误
   - 查看网络请求状态
   - 检查Vue DevTools

请提供测试结果报告，包括：
- 功能正常的部分
- 发现的问题和错误
- 需要修复的优先级
```

## ✅ 验收清单

### 快速验收检查点

- [ ] **后端文件完整性**
  - [ ] CrmWorkReportController.php 存在且结构正确
  - [ ] CrmWorkReport.php 模型存在且配置正确
  - [ ] CrmWorkReportService.php 服务类存在且功能完整

- [ ] **前端文件完整性**
  - [ ] list.vue 列表页面存在
  - [ ] form-dialog.vue 表单页面存在
  - [ ] import-export-dialog.vue 导入导出页面存在
  - [ ] crmWorkReportApi.ts API接口文件存在

- [ ] **MCP工具可用性**
  - [ ] view工具正常工作
  - [ ] codebase-retrieval工具正常工作
  - [ ] str-replace-editor工具正常工作
  - [ ] launch-process工具正常工作
  - [ ] open-browser工具正常工作
  - [ ] diagnostics工具正常工作

- [ ] **开发环境就绪**
  - [ ] 前端开发服务器可启动
  - [ ] 后端API可访问
  - [ ] 数据库连接正常
  - [ ] 页面可正常访问

- [ ] **基础功能可用**
  - [ ] 列表页面正常显示
  - [ ] 表单对话框正常打开
  - [ ] API接口正常响应
  - [ ] 无明显JavaScript错误

## 🎯 验证完成后的下一步

验证完成后，如果一切正常，可以开始执行主要的开发任务：

1. **阶段一**：后端功能定制（添加复制功能等）
2. **阶段二**：前端UI优化（飞书风格、富文本编辑器）
3. **阶段三**：功能完善和测试

如果验证中发现问题，请先解决基础环境问题，再进行后续开发。
