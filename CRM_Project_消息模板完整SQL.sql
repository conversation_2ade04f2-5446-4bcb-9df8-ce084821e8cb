-- CRM和Project模块消息模板完整SQL脚本
-- 执行时间: 2025-07-16
-- 目标: 1. 修复现有CRM模板使用中文变量名 2. 新增Project模块消息模板

SET NAMES utf8mb4;

-- ===== 第一部分: 修复现有CRM模板使用中文变量名 =====

-- 1. 修复 crm_lead_convert 模板
UPDATE notice_template SET 
    title = '线索转化通知',
    content = '您的线索"${线索名称}"已成功转化为客户"${客户名称}"，请及时跟进。',
    updated_at = NOW()
WHERE code = 'crm_lead_convert';

-- 2. 修复 crm_customer_assign 模板
UPDATE notice_template SET 
    title = '客户分配通知',
    content = '客户"${客户名称}"已分配给您，请及时联系客户并建立良好关系。
联系电话：${客户电话}
分配时间：${分配时间}',
    updated_at = NOW()
WHERE code = 'crm_customer_assign';

-- 3. 修复 crm_business_stage_change 模板
UPDATE notice_template SET 
    title = '商机阶段变更通知',
    content = '商机"${商机名称}"的阶段已从"${原阶段}"变更为"${新阶段}"
变更原因：${变更原因}
变更时间：${变更时间}',
    updated_at = NOW()
WHERE code = 'crm_business_stage_change';

-- 4. 修复 crm_quotation_create 模板
UPDATE notice_template SET 
    title = '报价单创建通知',
    content = '报价单"${报价单编号}"已创建
客户：${客户名称}
金额：¥${最终金额}
请及时发送给客户。',
    updated_at = NOW()
WHERE code = 'crm_quotation_create';

-- 5. 修复 crm_contract_approval 模板
UPDATE notice_template SET 
    title = '合同审批通知',
    content = '合同"${合同编号}"需要您的审批
客户：${客户名称}
金额：¥${合同金额}
提交时间：${提交时间}
请及时处理。',
    updated_at = NOW()
WHERE code = 'crm_contract_approval';

-- ===== 第二部分: 新增CRM模块核心消息模板 =====

-- 6. 线索分配通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'crm_lead_assign',
    '线索分配通知',
    '您有新的线索分配',
    '线索"${线索名称}"已分配给您
客户信息：${客户信息}
分配人：${分配人}
分配时间：${分配时间}
请及时跟进。',
    'crm',
    1,
    'site,email',
    '{"variables":[{"name":"线索名称","code":"lead_name","field":"lead_name","required":true},{"name":"客户信息","code":"customer_info","field":"customer_info","required":false},{"name":"分配人","code":"assigner_name","field":"assigner_name","required":true},{"name":"分配时间","code":"assign_time","field":"assign_time","required":true}]}',
    NOW(),
    NOW()
);

-- 7. 客户跟进提醒
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'crm_customer_follow_remind',
    '客户跟进提醒',
    '客户跟进提醒：${客户名称}',
    '客户"${客户名称}"需要跟进
上次跟进：${上次跟进时间}
计划跟进：${计划跟进时间}
跟进方式：${跟进方式}
请及时联系客户。',
    'crm',
    1,
    'site,email',
    '{"variables":[{"name":"客户名称","code":"customer_name","field":"customer_name","required":true},{"name":"上次跟进时间","code":"last_follow_time","field":"last_follow_time","required":false},{"name":"计划跟进时间","code":"plan_follow_time","field":"plan_follow_time","required":true},{"name":"跟进方式","code":"follow_method","field":"follow_method","required":false}]}',
    NOW(),
    NOW()
);

-- 8. 合同审批结果通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'crm_contract_approval_result',
    '合同审批结果通知',
    '合同审批结果：${审批结果}',
    '您提交的合同"${合同编号}"审批已完成
审批结果：${审批结果}
审批人：${审批人}
审批时间：${审批时间}
审批意见：${审批意见}',
    'crm',
    1,
    'site,email',
    '{"variables":[{"name":"合同编号","code":"contract_no","field":"contract_no","required":true},{"name":"审批结果","code":"approval_result","field":"approval_result","required":true},{"name":"审批人","code":"approver_name","field":"approver_name","required":true},{"name":"审批时间","code":"approval_time","field":"approval_time","required":true},{"name":"审批意见","code":"approval_opinion","field":"approval_opinion","required":false}]}',
    NOW(),
    NOW()
);

-- 9. 回款审批提交通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'crm_receivable_approval_submit',
    '回款审批提交通知',
    '回款审批申请：${合同编号}',
    '回款审批申请已提交，请及时处理
合同编号：${合同编号}
客户名称：${客户名称}
回款金额：¥${回款金额}
计划回款日期：${计划回款日期}
提交人：${提交人}
提交时间：${提交时间}',
    'crm',
    1,
    'site,email',
    '{"variables":[{"name":"合同编号","code":"contract_no","field":"contract_no","required":true},{"name":"客户名称","code":"customer_name","field":"customer_name","required":true},{"name":"回款金额","code":"receivable_amount","field":"receivable_amount","required":true},{"name":"计划回款日期","code":"plan_date","field":"plan_date","required":true},{"name":"提交人","code":"submitter_name","field":"submitter_name","required":true},{"name":"提交时间","code":"submit_time","field":"submit_time","required":true}]}',
    NOW(),
    NOW()
);

-- ===== 第三部分: 新增Project模块消息模板 =====

-- 10. 任务分配通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_task_assign',
    '任务分配通知',
    '您有新的任务分配：${任务标题}',
    '任务"${任务标题}"已分配给您
项目：${项目名称}
任务描述：${任务描述}
优先级：${任务优先级}
截止日期：${截止日期}
分配人：${分配人}
分配时间：${分配时间}
请及时处理。',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"任务标题","code":"task_title","field":"task_title","required":true},{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"任务描述","code":"task_description","field":"task_description","required":false},{"name":"任务优先级","code":"task_priority","field":"task_priority","required":false},{"name":"截止日期","code":"due_date","field":"due_date","required":false},{"name":"分配人","code":"assigner_name","field":"assigner_name","required":true},{"name":"分配时间","code":"assign_time","field":"assign_time","required":true}]}',
    NOW(),
    NOW()
);

-- 11. 任务状态变更通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_task_status_change',
    '任务状态变更通知',
    '任务状态更新：${任务标题}',
    '任务"${任务标题}"状态已更新
项目：${项目名称}
原状态：${原状态}
新状态：${新状态}
更新人：${更新人}
更新时间：${更新时间}',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"任务标题","code":"task_title","field":"task_title","required":true},{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"原状态","code":"old_status","field":"old_status","required":true},{"name":"新状态","code":"new_status","field":"new_status","required":true},{"name":"更新人","code":"updater_name","field":"updater_name","required":true},{"name":"更新时间","code":"update_time","field":"update_time","required":true}]}',
    NOW(),
    NOW()
);

-- 12. 任务截止提醒
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_task_deadline_remind',
    '任务截止提醒',
    '任务即将到期：${任务标题}',
    '任务"${任务标题}"即将到期，请注意
项目：${项目名称}
截止日期：${截止日期}
剩余时间：${剩余时间}
任务状态：${任务状态}
请及时完成任务。',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"任务标题","code":"task_title","field":"task_title","required":true},{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"截止日期","code":"due_date","field":"due_date","required":true},{"name":"剩余时间","code":"remaining_time","field":"remaining_time","required":true},{"name":"任务状态","code":"task_status","field":"task_status","required":true}]}',
    NOW(),
    NOW()
);

-- 13. 项目成员添加通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_member_add',
    '项目成员添加通知',
    '您已加入项目：${项目名称}',
    '您已被添加到项目"${项目名称}"
项目描述：${项目描述}
您的角色：${成员角色}
项目负责人：${项目负责人}
加入时间：${加入时间}
欢迎加入团队！',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"项目描述","code":"project_description","field":"project_description","required":false},{"name":"成员角色","code":"member_role","field":"member_role","required":true},{"name":"项目负责人","code":"project_owner","field":"project_owner","required":true},{"name":"加入时间","code":"join_time","field":"join_time","required":true}]}',
    NOW(),
    NOW()
);

-- 14. 任务逾期通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_task_overdue',
    '任务逾期通知',
    '任务已逾期：${任务标题}',
    '任务"${任务标题}"已逾期，请尽快处理
项目：${项目名称}
截止日期：${截止日期}
逾期天数：${逾期天数}天
任务状态：${任务状态}
请立即处理此任务。',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"任务标题","code":"task_title","field":"task_title","required":true},{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"截止日期","code":"due_date","field":"due_date","required":true},{"name":"逾期天数","code":"overdue_days","field":"overdue_days","required":true},{"name":"任务状态","code":"task_status","field":"task_status","required":true}]}',
    NOW(),
    NOW()
);

-- 15. 项目状态变更通知
INSERT INTO notice_template (
    code, name, title, content, module_code, status, 
    send_channels, variables_config, created_at, updated_at
) VALUES (
    'project_status_change',
    '项目状态变更通知',
    '项目状态更新：${项目名称}',
    '项目"${项目名称}"状态已更新
原状态：${原状态}
新状态：${新状态}
项目进度：${项目进度}%
更新人：${更新人}
更新时间：${更新时间}',
    'project',
    1,
    'site,email',
    '{"variables":[{"name":"项目名称","code":"project_name","field":"project_name","required":true},{"name":"原状态","code":"old_status","field":"old_status","required":true},{"name":"新状态","code":"new_status","field":"new_status","required":true},{"name":"项目进度","code":"project_progress","field":"project_progress","required":false},{"name":"更新人","code":"updater_name","field":"updater_name","required":true},{"name":"更新时间","code":"update_time","field":"update_time","required":true}]}',
    NOW(),
    NOW()
);

-- ===== 验证和统计 =====

-- 查看所有CRM模板
SELECT id, code, name, title, module_code, status, created_at 
FROM notice_template 
WHERE module_code = 'crm' 
ORDER BY id;

-- 查看所有Project模板
SELECT id, code, name, title, module_code, status, created_at 
FROM notice_template 
WHERE module_code = 'project' 
ORDER BY id;

-- 统计各模块模板数量
SELECT 
    module_code as 模块,
    COUNT(*) as 模板数量,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as 启用数量
FROM notice_template 
WHERE module_code IN ('crm', 'project')
GROUP BY module_code;

-- 检查是否还有英文变量名的模板
SELECT 
    id, code, name, content
FROM notice_template 
WHERE module_code IN ('crm', 'project')
    AND status = 1
    AND (
        content LIKE '%${title}%' 
        OR content LIKE '%${name}%'
        OR content LIKE '%${customer_name}%'
        OR content LIKE '%${business_name}%'
        OR content LIKE '%${contract_no}%'
        OR content LIKE '%${task_title}%'
        OR content LIKE '%${project_name}%'
    );

-- 执行完成提示
SELECT 
    '执行完成时间' as 项目,
    NOW() as 值
UNION ALL
SELECT 
    'CRM模板修复' as 项目,
    '5个模板已修复为中文变量名' as 值
UNION ALL
SELECT 
    'CRM新增模板' as 项目,
    '4个核心业务模板已添加' as 值
UNION ALL
SELECT 
    'Project新增模板' as 项目,
    '6个核心功能模板已添加' as 值
UNION ALL
SELECT 
    '下一步' as 项目,
    '请更新相关业务代码使用中文变量键名' as 值;
