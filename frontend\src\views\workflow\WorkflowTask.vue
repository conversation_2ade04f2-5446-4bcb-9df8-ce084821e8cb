<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import {
    ElDialog,
    FormInstance,
    ElTag,
    ElDescriptions,
    ElDescriptionsItem,
    ElTabs,
    ElTabPane,
    ElTimeline,
    ElTimelineItem,
    ElCard,
    ElForm,
    ElInput,
    ElInputNumber,
    ElButton
  } from 'element-plus'
  import { ElMessage } from 'element-plus'
  import { Refresh } from '@element-plus/icons-vue'
  import type { FormRules } from 'element-plus'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { WorkflowTaskApi } from '@/api/workflow/WorkflowTaskApi'
  import { ApiStatus } from '@/utils/http/status'
  import { BgColorEnum } from '@/enums/appEnum'
  import FormDataViewer from '../workflow/components/form-data-viewer.vue'
  import {
    WorkflowStatus,
    WorkflowOperation,
    getInstanceStatusText,
    getTaskStatusText,
    getOperationText,
    getInstanceStatusTagType,
    getTaskStatusTagType,
    getOperationColorType,
    getTaskStatusOptions,
    getOperationOptions
  } from '@/constants/workflow'
  import { useWorkflowStatusDisplay } from '@/composables/useWorkflowStatusDisplay'
  import { useWorkflowOperationGuard } from '@/composables/useWorkflowOperationGuard'

  // 修改默认标签为"待审批"
  const activeTab = ref('all')
  const loading = ref(false)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 审批操作面板
  const actionPanelVisible = ref(false)
  const actionPanelData = ref<any>({})
  const actionFormRef = ref<FormInstance>()
  const actionType = ref('') // approve, reject, transfer, terminate
  const actionForm = reactive({
    task_id: 0,
    instance_id: 0,
    opinion: '',
    reason: '',
    to_user_id: 0,
    to_user_name: '',
    form_data: {}
  })

  // 定义表单搜索初始值
  const initialSearchState = {
    keyword: '', // 搜索关键词
    definition_id: '', // 流程定义ID
    date_range: [], // 日期范围
    operation: '' // 操作类型（已办任务中使用）
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    /*{
      label: '标题',
      prop: 'keyword',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入流程标题关键词'
      },
      onChange: handleFormChange
    },*/
    {
      label: '类型',
      prop: 'definition_id',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择流程类型'
      },
      options: () => [
        // todo 这里需要从接口获取流程类型
        { label: '请假流程', value: '1' },
        { label: '报销流程', value: '2' },
        { label: '采购流程', value: '3' }
      ],
      onChange: handleFormChange
    },
    {
      label: '处理时间',
      prop: 'date_range',
      type: 'daterange',
      config: {
        clearable: true,
        type: 'daterange',
        shortcuts: [
          {
            text: '今天',
            value: () => {
              const today = new Date()
              return [today, today]
            }
          },
          {
            text: '最近一周',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDate() - 6)
              return [start, end]
            }
          },
          {
            text: '最近一个月',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              return [start, end]
            }
          }
        ]
      },
      onChange: handleFormChange
    }
  ]

  // 已办任务额外的搜索项
  const doneSearchItems: SearchFormItem[] = [
    {
      label: '状态',
      prop: 'operation',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择'
      },
      options: () =>
        getTaskStatusOptions()
          .filter((item) => item.value !== 0)
          .map((item) => ({
            label: item.label,
            value: item.value.toString()
          })),
      onChange: handleFormChange
    }
  ]

  // 当前使用的表单配置项
  const currentFormItems = computed(() => {
    if (activeTab.value === 'done' || activeTab.value === 'all') {
      return [...formItems, ...doneSearchItems]
    }
    return formItems
  })

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 使用状态显示优化
  const { getAccurateStatus, isTaskOperable, getOperationButtonState, shouldRefresh } =
    useWorkflowStatusDisplay()

  // 使用操作拦截
  const { safeExecuteOperation, checkOperationAllowed } = useWorkflowOperationGuard()

  // 获取准确的状态显示
  const getTaskDisplayStatus = (task: any) => {
    return getAccurateStatus(task)
  }

  // 检查任务是否需要刷新提示
  const checkRefreshNeeded = (task: any) => {
    return shouldRefresh(task)
  }

  // 动态列配置
  const { columnChecks, columns } = useCheckedColumns(() => [
    {
      prop: 'instance.type_name',
      label: '类型',
      width: 120
    },
    {
      prop: 'instance.title',
      label: '标题'
    },
    /*{
      prop: 'node_name',
      label: '当前节点',
      width: 120
    },*/
    {
      prop: 'instance.submitter_name',
      label: '提交人'
    },
    {
      prop: 'instance.dept_name',
      label: '提交部门',
      width: 120
    },
    {
      prop: 'instance.start_time',
      label: '发起时间'
    },
    {
      prop: 'status',
      label: '状态',
      width: 100
    },
    /*{
      prop: 'due_time',
      label: '截止时间',
      width: 160
    },*/
    {
      prop: 'handle_time',
      label: '处理时间'
    },
    {
      prop: 'operation',
      label: '操作',
      fixed: 'right',
      width: 180
    }
  ])

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  // 监听标签页变化
  watch(activeTab, () => {
    currentPage.value = 1
    getTableData()
  })

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      let res
      res = await WorkflowTaskApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        active: activeTab.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await WorkflowTaskApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data

        // 确保历史记录数据存在
        if (!detailData.value.instance.flowLogs) {
          detailData.value.instance.flowLogs = []
        }

        detailDialogVisible.value = true
      } else {
        ElMessage.error(res.message || '获取详情失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 显示审批操作面板
  const showActionPanel = async (row: any, type: string) => {
    // 重置表单
    Object.assign(actionForm, {
      task_id: row.id,
      instance_id: row.instance_id,
      opinion: '',
      reason: '',
      to_user_id: 0,
      to_user_name: '',
      form_data: {}
    })

    actionType.value = type

    try {
      loading.value = true
      // 获取详情数据以展示表单
      const res = await WorkflowTaskApi.detail(row.id)
      if (res.code === ApiStatus.success) {
        actionPanelData.value = res.data
        // 如果有表单数据，加载到表单中
        if (res.data.instance?.form_data) {
          actionForm.form_data = res.data.instance.form_data || {}
        }
        actionPanelVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  // 审批表单验证规则
  const actionRules = computed<FormRules>(() => {
    // 根据当前操作类型动态返回不同的验证规则
    const baseRules: FormRules = {
      opinion: [{ max: 500, message: '审批意见不能超过500个字符', trigger: 'blur' }]
    }

    // 根据操作类型添加不同验证规则
    if (actionType.value === 'reject' || actionType.value === 'terminate') {
      baseRules.reason = [
        {
          required: true,
          message: actionType.value === 'reject' ? '拒绝原因不能为空' : '终止原因不能为空',
          trigger: 'blur'
        },
        { max: 500, message: '原因不能超过500个字符', trigger: 'blur' }
      ]
    } else {
      baseRules.reason = [{ max: 500, message: '原因不能超过500个字符', trigger: 'blur' }]
    }

    if (actionType.value === 'transfer') {
      baseRules.to_user_id = [{ required: true, message: '请选择转交人', trigger: 'change' }]
      baseRules.to_user_name = [{ required: true, message: '转交人姓名不能为空', trigger: 'blur' }]
    }

    return baseRules
  })

  // 提交审批操作（带安全检查）
  const handleSubmitAction = async () => {
    if (!actionFormRef.value) return

    await actionFormRef.value.validate(async (valid) => {
      if (valid) {
        // 使用安全执行操作
        const success = await safeExecuteOperation(
          actionPanelData.value,
          actionType.value as any,
          async () => {
            loading.value = true
            let res
            let successMsg = ''

            switch (actionType.value) {
              case 'approve':
                res = await WorkflowTaskApi.approve({
                  task_id: actionForm.task_id,
                  opinion: actionForm.opinion,
                  form_data: actionForm.form_data
                })
                successMsg = '审批通过成功'
                break
              case 'reject':
                res = await WorkflowTaskApi.reject({
                  task_id: actionForm.task_id,
                  opinion: actionForm.opinion
                })
                successMsg = '拒绝成功'
                break
              case 'transfer':
                res = await WorkflowTaskApi.transfer({
                  task_id: actionForm.task_id,
                  to_user_id: actionForm.to_user_id,
                  to_user_name: actionForm.to_user_name,
                  reason: actionForm.reason
                })
                successMsg = '任务转交成功'
                break
              case 'terminate':
                res = await WorkflowTaskApi.terminate({
                  instance_id: actionForm.instance_id,
                  reason: actionForm.reason
                })
                successMsg = '流程终止成功'
                break
            }

            if (res && res.code === ApiStatus.success) {
              ElMessage.success(successMsg)
              actionPanelVisible.value = false
              await getTableData()
            } else {
              throw new Error(res?.msg || '操作失败')
            }
          },
          true // 跳过确认，因为我们有自己的表单验证
        )

        if (!success) {
          // 操作被拦截或失败，不需要额外处理
          loading.value = false
          return
        }

        loading.value = false
      }
    })
  }

  const gotoCheck = () => {
    detailDialogVisible.value = false
    // 显示审批操作面板，默认为同意操作
    if (detailData.value && detailData.value.id) {
      showActionPanel(detailData.value, 'approve')
    }
  }

  // 处理详情对话框关闭
  const handleDetailDialogClose = () => {
    // 重置数据
    detailData.value = {}
  }

  // 处理操作对话框关闭
  const handleActionDialogClose = () => {
    // 重置数据
    actionPanelData.value = {}
  }

  // 获取时间线项目类型
  const getTimelineItemType = (operation: string | number) => {
    const operationType = typeof operation === 'string' ? operation : String(operation)

    switch (operationType) {
      case '1':
      case 'approve':
        return 'success' // 绿色 - 表示同意/通过
      case '2':
      case 'reject':
        return 'danger' // 红色 - 表示拒绝
      case '3':
      case 'transfer':
        return 'warning' // 黄色 - 表示转交
      case '4':
      case 'terminate':
        return 'info' // 灰色 - 表示终止
      case '5':
      case 'recall':
        return 'info' // 灰色 - 表示撤回
      case '6':
      case 'urge':
        return 'primary' // 蓝色 - 表示催办
      case '7':
      case 'cc':
        return 'info' // 灰色 - 表示抄送
      case '8':
      case 'start':
        return 'primary' // 蓝色 - 表示流程开始（原来是绿色success）
      case '9':
      case 'end':
        return 'info' // 灰色 - 表示流程结束
      default:
        return 'info' // 默认灰色
    }
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="workflow_task-page" id="table-full-screen">
      <!-- 标签页切换 - 修改为五个标签页 -->
      <ElTabs v-model="activeTab" class="workflow-tabs">
        <ElTabPane label="全部" name="all"></ElTabPane>
        <ElTabPane label="待审批" name="todo"></ElTabPane>
        <ElTabPane label="已处理" name="done"></ElTabPane>
        <ElTabPane label="已撤销" name="canceled"></ElTabPane>
      </ElTabs>

      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="currentFormItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <template #left></template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col">
            <!-- 状态列显示标签 -->
            <template v-if="col.prop === 'status'" #default="scope">
              <div class="status-display">
                <ElTag :type="getTaskDisplayStatus(scope.row).tagType as any">
                  {{ getTaskDisplayStatus(scope.row).text }}
                </ElTag>
                <!-- 如果需要刷新，显示提示 -->
                <ElTooltip
                  v-if="checkRefreshNeeded(scope.row)"
                  content="状态可能已更新，建议刷新页面"
                  placement="top"
                >
                  <ElIcon class="refresh-hint" color="#e6a23c" size="14">
                    <Refresh />
                  </ElIcon>
                </ElTooltip>
              </div>
            </template>
            <!-- 操作列显示按钮 -->
            <template v-else-if="col.prop === 'operation'" #default="scope">
              <div>
                <ArtButtonTable
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(scope.row.id)"
                />
                <!-- 使用操作检查来决定是否显示操作按钮 -->
                <template v-if="checkOperationAllowed(scope.row, 'approve').allowed">
                  <ArtButtonTable
                    text="操作"
                    type="more"
                    @click="showActionPanel(scope.row, 'approve')"
                  />
                </template>
                <!-- 如果不可操作，显示原因提示 -->
                <template v-else>
                  <ElTooltip
                    :content="checkOperationAllowed(scope.row, 'approve').reason"
                    placement="top"
                  >
                    <ArtButtonTable text="操作" disabled />
                  </ElTooltip>
                </template>
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 审批操作面板 -->
        <ElDialog
          v-model="actionPanelVisible"
          :title="
            actionType === 'approve'
              ? '审批任务'
              : actionType === 'reject'
                ? '拒绝申请'
                : actionType === 'transfer'
                  ? '转交任务'
                  : '终止流程'
          "
          width="70%"
          class="action-panel-dialog fixed-content-dialog"
          destroy-on-close
          @close="handleActionDialogClose"
        >
          <div class="dialog-wrapper">
            <ElTabs>
              <!-- 表单数据与审批操作面板合并 -->
              <ElTabPane label="详情">
                <div v-if="actionPanelData?.instance">
                  <h3 class="form-title">{{ actionPanelData.instance.title }}</h3>

                  <!-- 基本信息 -->
                  <div class="form-data-scroll-area">
                    <ElDescriptions border :column="2" class="mb-4">
                      <ElDescriptionsItem label="申请编号">
                        {{ actionPanelData.instance?.process_id }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem label="提交人">
                        {{ actionPanelData.instance?.submitter_name }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem label="提交部门">
                        {{ actionPanelData.instance?.submitter_dept_name }}
                      </ElDescriptionsItem>
                      <ElDescriptionsItem label="提交时间">
                        {{ actionPanelData.instance?.start_time }}
                      </ElDescriptionsItem>
                      <!--                      <ElDescriptionsItem label="当前节点">
                                              {{ actionPanelData.node_name }}
                                            </ElDescriptionsItem>-->
                      <ElDescriptionsItem label="当前状态">
                        <ElTag
                          :type="
                            getInstanceStatusTagType(actionPanelData.instance?.status || 0) as any
                          "
                        >
                          {{ getInstanceStatusText(actionPanelData.instance?.status || 0) }}
                        </ElTag>
                      </ElDescriptionsItem>
                    </ElDescriptions>

                    <!-- 使用FormDataViewer组件展示表单数据 -->
                    <FormDataViewer
                      :formData="actionPanelData.instance?.form_data || {}"
                      :businessCode="actionPanelData.instance?.business_code || ''"
                    />
                  </div>

                  <div class="approval-form-section">
                    <h3 class="section-title">审批操作</h3>
                    <ElForm
                      ref="actionFormRef"
                      :model="actionForm"
                      :rules="actionRules"
                      label-width="100px"
                    >
                      <!-- 根据不同操作类型展示不同表单项 -->

                      <!-- 审批意见 - 同意和拒绝时显示 -->
                      <ElFormItem
                        v-if="actionType === 'approve' || actionType === 'reject'"
                        :label="actionType === 'approve' ? '审批意见' : '拒绝原因'"
                        prop="opinion"
                      >
                        <ElInput
                          v-model="actionForm.opinion"
                          type="textarea"
                          :rows="4"
                          :placeholder="
                            actionType === 'approve'
                              ? '请输入审批意见（选填）'
                              : '请输入拒绝原因（必填）'
                          "
                        />
                      </ElFormItem>

                      <!-- todo 暂时不实现 转交信息 - 转交时显示 -->
                      <!--                      <template v-if="actionType === 'transfer'">
                                              <ElFormItem label="转交给" prop="to_user_id">
                                                <div class="user-select-box">
                                                  <ElInputNumber
                                                    v-model="actionForm.to_user_id"
                                                    :min="1"
                                                    style="width: 120px"
                                                  />
                                                  <ElButton type="primary" plain size="small">选择用户</ElButton>
                                                </div>
                                              </ElFormItem>
                                              <ElFormItem label="接收人" prop="to_user_name">
                                                <ElInput v-model="actionForm.to_user_name" />
                                              </ElFormItem>
                                              <ElFormItem label="转交原因" prop="reason">
                                                <ElInput
                                                  v-model="actionForm.reason"
                                                  type="textarea"
                                                  :rows="3"
                                                  placeholder="请输入转交原因（选填）"
                                                />
                                              </ElFormItem>
                                            </template>-->

                      <!-- 终止原因 - 终止时显示 -->
                      <ElFormItem v-if="actionType === 'terminate'" label="终止原因" prop="reason">
                        <ElInput
                          v-model="actionForm.reason"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入终止原因（必填）"
                        />
                      </ElFormItem>
                    </ElForm>

                    <!-- 操作类型选择按钮 -->
                    <div class="action-type-selector">
                      <div class="button-group">
                        <ElButton
                          :type="actionType === 'approve' ? 'primary' : 'default'"
                          @click="actionType = 'approve'"
                          size="large"
                        >
                          同意
                        </ElButton>
                        <ElButton
                          :type="actionType === 'reject' ? 'danger' : 'default'"
                          @click="actionType = 'reject'"
                          size="large"
                        >
                          拒绝
                        </ElButton>
                        <!--                        <ElButton
                                                  :type="actionType === 'transfer' ? 'warning' : 'default'"
                                                  @click="actionType = 'transfer'"
                                                  size="large"
                                                >
                                                  转交
                                                </ElButton>-->
                        <ElButton
                          :type="actionType === 'terminate' ? 'danger' : 'default'"
                          @click="actionType = 'terminate'"
                          size="large"
                        >
                          终止
                        </ElButton>
                      </div>
                    </div>
                  </div>
                </div>
              </ElTabPane>

              <!-- 审批记录面板 -->
              <ElTabPane label="记录">
                <div class="workflow-history">
                  <ElTimeline
                    v-if="
                      actionPanelData.instance?.flowLogs &&
                      actionPanelData.instance?.flowLogs.length > 0
                    "
                  >
                    <ElTimelineItem
                      v-for="(item, index) in actionPanelData.instance.flowLogs"
                      :key="index"
                      :timestamp="item.operation_time || ''"
                      :type="getTimelineItemType(item.operation || '')"
                    >
                      <ElCard class="history-card">
                        <h4>{{ item.operation_text || item.content || '' }}</h4>
                        <p class="operation-info">
                          <span>操作人：{{ item.operator_name || '-' }}</span>
                        </p>
                        <template v-if="item.opinion">
                          <p class="opinion-title">审批意见：</p>
                          <p class="opinion-content">{{ item.opinion }}</p>
                        </template>
                      </ElCard>
                    </ElTimelineItem>
                  </ElTimeline>
                  <div v-else class="empty-placeholder">暂无审批记录</div>
                </div>
              </ElTabPane>
            </ElTabs>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="actionPanelVisible = false">取消</ElButton>
              <ElButton
                :type="
                  actionType === 'approve'
                    ? 'primary'
                    : actionType === 'reject'
                      ? 'danger'
                      : actionType === 'transfer'
                        ? 'warning'
                        : 'danger'
                "
                @click="handleSubmitAction"
              >
                {{
                  actionType === 'approve'
                    ? '确认通过'
                    : actionType === 'reject'
                      ? '确认拒绝'
                      : actionType === 'transfer'
                        ? '确认转交'
                        : '确认终止'
                }}
              </ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>

      <!-- 详情对话框 -->
      <ElDialog
        v-model="detailDialogVisible"
        title="任务详情"
        width="70%"
        class="task-detail-dialog fixed-content-dialog"
        destroy-on-close
        @close="handleDetailDialogClose"
      >
        <div class="dialog-wrapper" v-if="detailData?.instance">
          <h3 class="detail-title">{{ detailData.instance.title }}</h3>

          <ElTabs>
            <ElTabPane label="详情">
              <div class="details-scroll-area">
                <ElDescriptions border :column="2">
                  <ElDescriptionsItem label="类型">
                    {{ detailData.instance?.definition_name }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="编号">
                    {{ detailData.instance?.process_id }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="提交人">
                    {{ detailData.instance?.submitter_name }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="提交部门">
                    {{ detailData.instance?.submitter_dept_name }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="提交时间">
                    {{ detailData.instance?.start_time }}
                  </ElDescriptionsItem>
                  <ElDescriptionsItem label="当前状态">
                    <ElTag :type="getInstanceStatusTagType(detailData.instance.status) as any">
                      {{ getInstanceStatusText(detailData.instance.status) }}
                    </ElTag>
                  </ElDescriptionsItem>
                  <!--                  <ElDescriptionsItem label="当前节点">
                                      {{ detailData.node_name }}
                                    </ElDescriptionsItem>-->
                  <!--                  <ElDescriptionsItem label="到达时间">
                                      {{ detailData.created_at }}
                                    </ElDescriptionsItem>-->
                </ElDescriptions>
              </div>
              <div class="details-scroll-area">
                <FormDataViewer
                  :formData="detailData.instance?.form_data || {}"
                  :businessCode="detailData.instance?.business_code || ''"
                />
              </div>
            </ElTabPane>

            <ElTabPane label="审批历史">
              <div class="details-scroll-area">
                <div class="workflow-history">
                  <ElTimeline
                    v-if="detailData.instance?.flowLogs && detailData.instance?.flowLogs.length > 0"
                  >
                    <ElTimelineItem
                      v-for="(item, index) in detailData.instance.flowLogs"
                      :key="index"
                      :timestamp="item.operation_time || ''"
                      :type="getTimelineItemType(item.operation || '')"
                    >
                      <ElCard class="history-card">
                        <h4>{{ item.operation_text || item.content || '' }}</h4>
                        <p class="operation-info">
                          <span>操作人：{{ item.operator_name || '-' }}</span>
                        </p>
                        <template v-if="item.opinion">
                          <p class="opinion-title">审批意见：</p>
                          <p class="opinion-content">{{ item.opinion }}</p>
                        </template>
                      </ElCard>
                    </ElTimelineItem>
                  </ElTimeline>
                  <div v-else class="empty-placeholder">暂无审批历史</div>
                </div>
              </div>
            </ElTabPane>
          </ElTabs>
        </div>

        <template #footer>
          <div class="dialog-footer">
            <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            <ElButton type="primary" v-if="activeTab === 'todo'" @click="gotoCheck">
              去审批
            </ElButton>
          </div>
        </template>
      </ElDialog>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .workflow_task-page {
    width: 100%;

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }

    .workflow-tabs {
      margin-bottom: 16px;
    }

    .task-detail-dialog {
      .detail-title {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
      }

      .form-data-container {
        min-height: 200px;
        padding: 20px;
        border: 1px dashed #ccc;

        .placeholder-text {
          text-align: center;
          color: #999;
        }
      }

      .flow-logs {
        .log-item {
          display: flex;
          margin-bottom: 15px;

          .log-date {
            width: 160px;
            color: #666;
          }

          .log-content {
            flex: 1;

            .log-user {
              font-weight: bold;
              margin-bottom: 5px;
            }

            .log-action {
              color: #333;
            }
          }
        }
      }

      .no-data {
        text-align: center;
        color: #999;
        padding: 30px 0;
      }
    }

    .user-select-box {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }

  .workflow_task-page :deep(.el-table) {
    .el-table__inner-wrapper:before {
      display: none;
    }
  }

  .action-panel-dialog {
    .form-title {
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }

    .form-data-container {
      min-height: 200px;
      padding: 20px;
      border: 1px dashed #ccc;
      margin-bottom: 20px;

      .placeholder-text {
        text-align: center;
        color: #999;
      }
    }

    .action-type-selector {
      margin-top: 20px;
      text-align: center;

      .button-group {
        display: flex;
        justify-content: center;
        gap: 10px;
      }
    }

    .mb-4 {
      margin-bottom: 16px;
    }

    .approval-form-section {
      padding-top: 20px;
      border-top: 1px dashed #dcdfe6;

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 10px !important;
  }

  :deep(.el-dialog__header.show-close) {
    padding-bottom: 0 !important;
  }

  .fixed-content-dialog {
    .dialog-wrapper {
      width: 100%;
    }

    .form-data-scroll-area,
    .details-scroll-area {
      max-height: 40vh;
      overflow-y: auto;
      padding: 5px;
    }
  }

  .workflow-history {
    padding: 10px;

    .history-card {
      margin-bottom: 10px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .operation-info {
        display: flex;
        justify-content: space-between;
        color: #666;
        font-size: 13px;
      }

      .opinion-title {
        margin-top: 10px;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .opinion-content {
        color: #666;
        white-space: pre-wrap;
      }
    }
  }

  .empty-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #909399;
    font-size: 14px;
    background-color: #f8f8f9;
    border-radius: 4px;
  }

  /* 状态显示优化 */
  .status-display {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .refresh-hint {
    cursor: pointer;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
</style>
