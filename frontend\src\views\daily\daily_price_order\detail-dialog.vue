<template>
  <ElDialog v-model="visible" title="" width="60%" :close-on-click-modal="false" destroy-on-close>
    <div v-loading="loading" class="detail-container">
      <!-- 报价单头部 -->
      <div class="quote-header">
        <div class="company-name">{{ companyName }}</div>
        <div class="quote-title">报价单</div>
      </div>

      <!-- 基本信息 -->
      <div class="basic-info">
        <div class="info-row">
          <span class="label">日期：</span>
          <span class="value">{{ formatDate(detailData.price_date) }}</span>
          <span class="label unit-label">单位：元/吨</span>
        </div>
      </div>

      <!-- 报价表格 -->
      <div class="quote-table-container">
        <table class="quote-table">
          <thead>
            <tr>
              <th rowspan="2" class="sequence-col">序号</th>
              <th rowspan="2" class="factory-col">厂家</th>
              <th rowspan="2" class="model-col">规格型号</th>
              <th rowspan="2" class="price-col">单价</th>
              <th rowspan="2" class="freight-col">涨幅</th>
              <th rowspan="2" class="storage-col">库存价格</th>
              <th rowspan="2" class="pickup-col">优惠政策/备注</th>
              <th class="region-col">厂区库存（吨）</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in sortedPriceItems" :key="index">
              <td class="text-center">{{ index + 1 }}</td>
              <td
                v-if="shouldShowSupplierCell(index)"
                class="text-center supplier-cell"
                :rowspan="getSupplierRowspan(index)"
              >
                {{ item.supplier?.name || '' }}
              </td>
              <td class="text-center">{{ item.product?.spec || item.product?.name || '' }}</td>
              <td class="text-center">{{ formatPrice(item.unit_price) }}</td>
              <td class="text-center" :class="getPriceChangeClass(item.price_change)">
                {{ formatPriceChange(item.price_change) }}
              </td>
              <td class="text-center">{{ formatPrice(item.stock_price) }}</td>
              <td class="text-center">{{ item.policy_remark || '/' }}</td>
              <td class="text-center">{{ item.stock_qty || '0' }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 备注信息 -->
      <div class="remarks-section">
        <div class="remarks-content">
          <div class="remark-item">1、 以上价格为出厂净价，每天上午 10 点-11 点间发布。</div>
          <div class="remark-item">2、 库存价格含散装充装费</div>
          <div class="remark-item">3、 充装费:吨桶装 50、铁桶装 200</div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">关闭</ElButton>
        <ElButton type="primary" @click="handlePrint">打印</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { ElDialog, ElButton } from 'element-plus'

  interface PriceItem {
    id?: number
    supplier_id?: number
    product_id?: number
    unit_price?: number
    stock_price?: number
    stock_qty?: number
    policy_remark?: string
    supplier?: any
    product?: any
  }

  interface DetailData {
    id?: number
    title?: string
    price_date?: string
    approval_time?: string
    approver_name?: string
    items?: PriceItem[]
  }

  const props = defineProps<{
    modelValue: boolean
    detailData: DetailData
    loading?: boolean
  }>()

  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
  }>()

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 公司名称 - 后续可以从配置中获取
  const companyName = ref('您的公司名称')

  // 报价项目列表
  const priceItems = computed(() => {
    return props.detailData?.items || []
  })

  // 按厂家排序的报价项目列表
  const sortedPriceItems = computed(() => {
    const items = [...priceItems.value]
    return items.sort((a, b) => {
      const supplierA = a.supplier?.name || ''
      const supplierB = b.supplier?.name || ''
      return supplierA.localeCompare(supplierB)
    })
  })

  // 计算每个供应商的rowspan和是否显示
  const supplierRowSpans = computed(() => {
    const items = sortedPriceItems.value
    const spans: { [key: number]: { rowspan: number; show: boolean } } = {}

    let currentSupplier = ''
    let currentSpanStart = 0
    let currentSpanCount = 0

    items.forEach((item, index) => {
      const supplierName = item.supplier?.name || ''

      if (supplierName !== currentSupplier) {
        // 结束上一个供应商的span计算
        if (currentSpanCount > 0) {
          spans[currentSpanStart] = { rowspan: currentSpanCount, show: true }
          for (let i = currentSpanStart + 1; i < currentSpanStart + currentSpanCount; i++) {
            spans[i] = { rowspan: 0, show: false }
          }
        }

        // 开始新的供应商span计算
        currentSupplier = supplierName
        currentSpanStart = index
        currentSpanCount = 1
      } else {
        currentSpanCount++
      }
    })

    // 处理最后一个供应商
    if (currentSpanCount > 0) {
      spans[currentSpanStart] = { rowspan: currentSpanCount, show: true }
      for (let i = currentSpanStart + 1; i < currentSpanStart + currentSpanCount; i++) {
        spans[i] = { rowspan: 0, show: false }
      }
    }

    return spans
  })

  // 获取供应商单元格的rowspan
  const getSupplierRowspan = (index: number) => {
    return supplierRowSpans.value[index]?.rowspan || 1
  }

  // 判断是否应该显示供应商单元格
  const shouldShowSupplierCell = (index: number) => {
    return supplierRowSpans.value[index]?.show !== false
  }

  // 格式化价格显示（不四舍五入，保留两位小数）
  const formatPrice = (price?: number) => {
    if (!price || price === 0) return ''
    // 不四舍五入，直接截取两位小数
    const truncated = Math.floor(price * 100) / 100
    return truncated.toFixed(2)
  }

  // 格式化涨幅显示
  const formatPriceChange = (rate?: number) => {
    // if (!rate || rate === 0) return ''
    if (!rate || rate <= 0) return ''
    const sign = rate > 0 ? '+' : ''
    return `${sign}${rate.toFixed(2)}`
  }

  // 获取价格变化的样式类
  const getPriceChangeClass = (rate?: number) => {
    if (!rate || rate <= 0) return ''
    return rate > 0 ? 'price-up' : 'price-down'
  }

  // 格式化日期
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
  }

  // 关闭对话框
  const handleClose = () => {
    visible.value = false
  }

  // 打印功能
  const handlePrint = () => {
    const printContent = document.querySelector('.detail-container')?.innerHTML
    if (!printContent) return

    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>报价单详情</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .quote-header { text-align: center; margin-bottom: 20px; }
            .company-name { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            .quote-title { font-size: 24px; font-weight: bold; }
            .basic-info { margin-bottom: 20px; }
            .info-row { display: flex; align-items: center; gap: 20px; }
            .label { font-weight: bold; }
            .unit-label { margin-left: auto; }
            .quote-table-container { margin-bottom: 20px; border: 2px solid #000; }
            .quote-table { width: 100%; border-collapse: collapse; }
            .quote-table th, .quote-table td { border: 1px solid #000; padding: 8px 4px; text-align: center; font-size: 12px; }
            .quote-table th { background-color: #f5f5f5; font-weight: bold; }
            .supplier-cell { vertical-align: middle; font-weight: bold; background-color: #fafafa; }
            .price-up { color: #f56c6c !important; font-weight: bold; }
            .price-down { color: #67c23a !important; font-weight: bold; }
            .remarks-section { display: flex; justify-content: space-between; align-items: flex-end; }
            .remarks-content { flex: 1; }
            .remark-item { margin-bottom: 5px; font-size: 12px; }
            .signature-section { text-align: center; }
            .approver-name { font-weight: bold; margin-bottom: 5px; }
            .approval-date { font-size: 12px; }
          </style>
        </head>
        <body>
          ${printContent}
        </body>
      </html>
    `)

    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
  }
</script>

<style scoped lang="scss">
  .detail-container {
    //padding: 20px;
    background: white;
    max-height: 80vh;
    overflow-y: auto;

    .quote-header {
      text-align: center;
      margin-bottom: 20px;

      .company-name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .quote-title {
        font-size: 24px;
        font-weight: bold;
      }
    }

    .basic-info {
      margin-bottom: 20px;

      .info-row {
        display: flex;
        align-items: center;
        gap: 20px;

        .label {
          font-weight: bold;
        }

        .unit-label {
          margin-left: auto;
        }
      }
    }

    .quote-table-container {
      margin-bottom: 20px;
      border: 2px solid #000;

      .quote-table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          border: 1px solid #000;
          padding: 8px 4px;
          text-align: center;
          font-size: 12px;
        }

        th {
          background-color: #f5f5f5;
          font-weight: bold;
        }

        .supplier-cell {
          vertical-align: middle;
          font-weight: bold;
          background-color: #fafafa;
        }

        .price-up {
          color: #f56c6c !important;
          font-weight: bold;
        }

        .price-down {
          color: #67c23a !important;
          font-weight: bold;
        }

        .sequence-col {
          width: 40px;
        }

        .factory-col {
          width: 80px;
        }

        .model-col {
          width: 100px;
        }

        .price-col {
          width: 80px;
        }

        .freight-col {
          width: 60px;
        }

        .storage-col {
          width: 80px;
        }

        .pickup-col {
          width: 80px;
        }

        .region-col {
          width: 60px;
        }
      }
    }

    .remarks-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .remarks-content {
        flex: 1;

        .remark-item {
          margin-bottom: 5px;
          font-size: 12px;
        }
      }

      .signature-section {
        text-align: center;

        .signature-info {
          .approver-name {
            font-weight: bold;
            margin-bottom: 5px;
          }

          .approval-date {
            font-size: 12px;
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }

  // 打印样式
  @media print {
    .detail-container {
      padding: 0;
    }

    .dialog-footer {
      display: none;
    }
  }
</style>
