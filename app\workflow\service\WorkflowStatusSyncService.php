<?php

namespace app\workflow\service;

use app\workflow\model\WorkflowInstance;
use app\workflow\model\WorkflowTask;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\factory\DynamicWorkflowFactory;
use think\facade\Db;
use think\facade\Log;

/**
 * 工作流状态统一同步服务
 *
 * 解决双轨架构下的状态不一致问题：
 * 1. 统一同步workflow_instance、workflow_task、业务表的状态
 * 2. 处理多人审批模式下的任务状态
 * 3. 清理审批配置和历史数据
 */
class WorkflowStatusSyncService
{
	/**
	 * 统一同步所有工作流相关状态
	 *
	 * @param int    $instanceId 工作流实例ID
	 * @param int    $newStatus  新状态
	 * @param string $reason     操作原因
	 * @param int    $operatorId 操作人ID
	 * @return bool
	 */
	public function syncAllWorkflowStatus(int $instanceId, int $newStatus, string $reason = '', int $operatorId = 0
	): bool
	{
		Db::startTrans();
		try {
			Log::info("开始同步工作流状态", [
				'instance_id' => $instanceId,
				'new_status'  => $newStatus,
				'reason'      => $reason,
				'operator_id' => $operatorId
			]);
			
			// 1. 获取工作流实例信息
			$instance = WorkflowInstance::find($instanceId);
			if (!$instance) {
				throw new \Exception("工作流实例不存在: {$instanceId}");
			}
			
			// 2. 更新workflow_instance状态
			$this->syncInstanceStatus($instance, $newStatus);
			
			// 3. 更新workflow_task状态（处理多人审批）
			$this->syncTaskStatus($instanceId, $newStatus, $reason, $instance);
			
			// 4. 更新业务表状态
			$this->syncBusinessStatus($instance, $newStatus);
			
			
			// 6. 记录操作历史
			$this->recordOperationHistory($instance, $newStatus, $reason, $operatorId);
			
			Db::commit();
			
			Log::info("工作流状态同步成功", [
				'instance_id' => $instanceId,
				'new_status'  => $newStatus
			]);
			
			return true;
			
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error("工作流状态同步失败: " . $e->getMessage(), [
				'instance_id' => $instanceId,
				'new_status'  => $newStatus,
				'error'       => $e->getMessage(),
				'trace'       => $e->getTraceAsString()
			]);
			return false;
		}
	}
	
	/**
	 * 同步工作流实例状态
	 */
	private function syncInstanceStatus($instance, int $newStatus): void
	{
		$updateData = [
			'status'     => $newStatus,
			'updated_at' => date('Y-m-d H:i:s')
		];
		
		// 如果是结束状态，设置结束时间
		if (in_array($newStatus, [
			WorkflowStatusConstant::STATUS_COMPLETED,
			WorkflowStatusConstant::STATUS_REJECTED,
			WorkflowStatusConstant::STATUS_TERMINATED,
			WorkflowStatusConstant::STATUS_RECALLED,
			WorkflowStatusConstant::STATUS_VOID
		])) {
			$updateData['end_time'] = date('Y-m-d H:i:s');
		}
		
		$res = $instance->saveByUpdate($updateData);
		
		Log::info("更新实例状态成功", [
			'instance_id' => $instance->id,
			'status'      => $newStatus
		]);
	}
	
	/**
	 * 同步任务状态（处理多人审批模式）
	 */
	private function syncTaskStatus(int $instanceId, int $newStatus, string $reason, $instance): void
	{
		// 获取实例的审批模式配置（如果字段不存在则使用默认值）
		$nodeConfig = [];
		if (isset($instance->node_config)) {
			$nodeConfig = json_decode($instance->node_config ?? '{}', true);
		}
		$approvalMode = $nodeConfig['approvalMode'] ?? WorkflowStatusConstant::APPROVAL_MODE_ALL;
		
		$defaultReason = $this->getDefaultReason($newStatus);
		$finalReason   = $reason
			?: $defaultReason;
		
		if (in_array($newStatus, [
			WorkflowStatusConstant::STATUS_RECALLED,
			WorkflowStatusConstant::STATUS_TERMINATED
		])) {
			// 撤回和终止：更新所有待处理任务
			$affectedRows = WorkflowTask::where('instance_id', $instanceId)
			                            ->where('status', 0)
			                            ->update([
				                            'status'      => $newStatus,
				                            'handle_time' => date('Y-m-d H:i:s'),
				                            'opinion'     => $finalReason
			                            ]);
			
			Log::info("更新待处理任务状态", [
				'instance_id'   => $instanceId,
				'affected_rows' => $affectedRows,
				'new_status'    => $newStatus
			]);
			
		}
		elseif ($newStatus === WorkflowStatusConstant::STATUS_REJECTED) {
			// 拒绝：根据审批模式处理其他任务
			if (in_array($approvalMode, [
				WorkflowStatusConstant::APPROVAL_MODE_ANY,
				WorkflowStatusConstant::APPROVAL_MODE_ALL
			])) {
				// 任意一人通过或所有人通过：标记其他任务为已跳过
				$affectedRows = WorkflowTask::where('instance_id', $instanceId)
				                            ->where('status', 0)
				                            ->update([
					                            'status'      => WorkflowStatusConstant::STATUS_SKIPPED,
					                            // 需要新增此状态
					                            'handle_time' => date('Y-m-d H:i:s'),
					                            'opinion'     => '流程已被拒绝，自动跳过'
				                            ]);
				
				Log::info("标记其他任务为已跳过", [
					'instance_id'   => $instanceId,
					'affected_rows' => $affectedRows,
					'approvalMode' => $approvalMode
				]);
			}
		}
	}
	
	/**
	 * 同步业务表状态
	 */
	private function syncBusinessStatus($instance, int $newStatus): void
	{
		try {
			// 判断是通用轨道还是业务轨道
			$businessCode = $instance->business_code ?? '';
			
			if (empty($businessCode)) {
				Log::warning("业务代码为空，跳过业务表状态同步", [
					'instance_id' => $instance->id
				]);
				return;
			}
			
			$this->syncFormServiceStatus($instance, $newStatus);
			
		}
		catch (\Exception $e) {
			Log::error("同步业务表状态失败: " . $e->getMessage(), [
				'instance_id'   => $instance->id,
				'business_code' => $instance->business_code ?? '',
				'new_status'    => $newStatus
			]);
			// 不抛出异常，避免影响主流程
		}
	}
	
	/**
	 * 同步业务状态（统一使用FormService）
	 */
	private function syncFormServiceStatus($instance, int $newStatus): void
	{
		$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance->business_code);

		if ($formService) {
			// 1. 更新业务表状态
			$result = $formService->updateFormStatus(intval($instance->business_id), $newStatus);
			if (!$result) {
				throw new \Exception("FormService状态更新失败");
			}

			Log::info("FormService状态同步成功", [
				'business_code' => $instance->business_code,
				'business_id'   => $instance->business_id,
				'status'        => $newStatus
			]);

			// 2. 触发业务后处理
			$formService->afterWorkflowStatusChange(intval($instance->business_id), $newStatus, [
				'instance_id' => $instance->id,
				'business_code' => $instance->business_code,
				'timestamp' => date('Y-m-d H:i:s')
			]);

			Log::info("业务后处理完成", [
				'business_code' => $instance->business_code,
				'business_id'   => $instance->business_id,
				'status'        => $newStatus
			]);
		}
		else {
			// 测试环境或找不到FormService时，记录日志但不抛出异常
			Log::info("FormService未找到，跳过业务表状态同步", [
				'business_code' => $instance->business_code,
				'business_id'   => $instance->business_id,
				'status'        => $newStatus
			]);
		}
	}
	

	
	/**
	 * 记录操作历史
	 */
	private function recordOperationHistory($instance, int $newStatus, string $reason, int $operatorId): void
	{
		// 这里可以记录到workflow_history表
		// 暂时使用日志记录
		Log::info("记录操作历史", [
			'instance_id' => $instance->id,
			'operation'   => $this->getOperationByStatus($newStatus),
			'operator_id' => $operatorId,
			'reason'      => $reason
		]);
	}
	
	/**
	 * 根据状态获取默认原因
	 */
	private function getDefaultReason(int $status): string
	{
		$reasonMap = [
			WorkflowStatusConstant::STATUS_RECALLED   => '申请已撤回',
			WorkflowStatusConstant::STATUS_TERMINATED => '流程已终止',
			WorkflowStatusConstant::STATUS_REJECTED   => '流程已拒绝',
			WorkflowStatusConstant::STATUS_COMPLETED  => '流程已通过',
			WorkflowStatusConstant::STATUS_VOID       => '流程已作废',
		];
		
		return $reasonMap[$status] ?? '状态已更新';
	}
	
	/**
	 * 根据状态获取操作类型
	 */
	private function getOperationByStatus(int $status): int
	{
		$operationMap = [
			WorkflowStatusConstant::STATUS_RECALLED   => WorkflowStatusConstant::OPERATION_RECALL,
			WorkflowStatusConstant::STATUS_TERMINATED => WorkflowStatusConstant::OPERATION_TERMINATE,
			WorkflowStatusConstant::STATUS_REJECTED   => WorkflowStatusConstant::OPERATION_REJECT,
			WorkflowStatusConstant::STATUS_COMPLETED  => WorkflowStatusConstant::OPERATION_AGREE,
			WorkflowStatusConstant::STATUS_VOID       => WorkflowStatusConstant::OPERATION_CANCEL,
		];
		
		return $operationMap[$status] ?? WorkflowStatusConstant::OPERATION_AGREE;
	}
}
