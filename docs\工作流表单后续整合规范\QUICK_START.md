# 快速开始指南

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 5分钟快速上手

### 1. 创建业务表单组件

```vue
<!-- business-forms/your_business.vue -->
<template>
  <div class="business-form-container">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入名称" />
      </el-form-item>
      <!-- 更多表单项... -->
    </el-form>
    
    <div class="form-actions" v-if="showActions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSave" :loading="saving">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交申请
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件名称（必须）
defineOptions({
  name: 'YourBusinessForm'
})

// 统一Props接口
interface Props {
  data?: any
  mode?: 'create' | 'edit'
  definitionId?: number
  formId?: number
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  showActions: true,
  data: () => ({})
})

// 统一Events接口
const emit = defineEmits<{
  submit: [data: any]
  save: [data: any]
  cancel: []
  change: [data: any]
}>()

// 表单数据
const formData = reactive({
  name: '',
  // 其他字段...
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ]
}

// 表单提交
const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (valid) {
    emit('submit', { ...formData })
  }
}

// 保存草稿
const handleSave = () => {
  emit('save', { ...formData })
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 必须暴露的方法
defineExpose({
  showForm: (id?: number) => {
    // 显示表单逻辑
  },
  resetForm: () => {
    // 重置表单逻辑
  },
  validate: () => formRef.value?.validate(),
  getFormData: () => ({ ...formData }),
  setFormData: (data: any) => Object.assign(formData, data)
})
</script>
```

### 2. 创建详情展示组件

```vue
<!-- business-detail/your_business.vue -->
<template>
  <div class="business-detail-container">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="名称">
        {{ data.name || '-' }}
      </el-descriptions-item>
      <!-- 更多详情项... -->
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
// 组件名称（必须）
defineOptions({
  name: 'YourBusinessDetail'
})

// 统一Props接口
interface Props {
  data: any
  businessCode?: string
  loading?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: false,
  data: () => ({})
})

// 统一Events接口
const emit = defineEmits<{
  refresh: []
  export: []
}>()

// 必须暴露的方法
defineExpose({
  refresh: () => emit('refresh'),
  export: () => emit('export')
})
</script>
```

### 3. 在工作流中使用

```vue
<!-- 申请页面 -->
<template>
  <FormManager
    v-model="formVisible"
    type="your_business"
    :workflow-type-id="workflowTypeId"
    @success="handleFormSuccess"
    @cancel="handleFormCancel"
  />
</template>

<!-- 详情查看 -->
<template>
  <FormDataViewer
    :form-data="workflowData.form_data"
    :business-code="workflowData.business_code"
  />
</template>
```

## 🚀 完整开发流程

### Step 1: 环境准备

```bash
# 1. 检查环境
node --version  # >= 16.14.0
npm --version   # >= 8.0.0

# 2. 安装依赖
cd frontend
npm install

# 3. 启动开发服务器
npm run dev
```

### Step 2: 数据库设计

```sql
-- 业务表设计（必须包含标准字段）
CREATE TABLE your_business_table (
  id int PRIMARY KEY AUTO_INCREMENT,
  name varchar(100) NOT NULL COMMENT '名称',
  -- 其他业务字段...
  
  -- 工作流字段（必须）
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态',
  approval_node varchar(100) DEFAULT '' COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime DEFAULT NULL COMMENT '提交时间',
  approved_at datetime DEFAULT NULL COMMENT '审批完成时间',
  
  -- 标准字段（必须）
  created_id int NOT NULL DEFAULT 0,
  updated_id int NOT NULL DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int NOT NULL DEFAULT 0
);
```

### Step 3: TypeScript类型定义

```typescript
// types/your-business.ts
export interface YourBusiness extends WorkflowEntity {
  id?: number
  name: string
  // 其他字段...
}

export interface WorkflowEntity extends BaseEntity {
  instance_id?: number
  approval_status: ApprovalStatus
  approval_node?: string
  submitter_id?: number
  submitted_at?: string | null
  approved_at?: string | null
}
```

### Step 4: API接口开发

```typescript
// api/your-business.ts
export const YourBusinessApi = {
  list(params: any): Promise<PaginatedResponse<YourBusiness>> {
    return request.get('/your-business/list', { params })
  },

  detail(id: number): Promise<ApiResponse<YourBusiness>> {
    return request.get(`/your-business/detail/${id}`)
  },

  create(data: Partial<YourBusiness>): Promise<ApiResponse<{ id: number }>> {
    return request.post('/your-business/create', data)
  }
}
```

### Step 5: 后端控制器

```php
<?php
// app/your-module/controller/YourBusiness.php
namespace app\your_module\controller;

use app\common\controller\BaseController;

class YourBusiness extends BaseController
{
    public function list()
    {
        $params = $this->request->param();
        $result = $this->service->getPageList($params);
        return $this->success('获取成功', $result);
    }

    public function detail($id)
    {
        $result = $this->service->getDetail($id);
        return $this->success('获取成功', $result);
    }

    public function create()
    {
        $data = $this->request->param();
        $result = $this->service->create($data);
        return $this->success('创建成功', ['id' => $result]);
    }
}
```

## 🔧 常用工具和方法

### 组件映射验证

```javascript
// 验证组件是否正确映射
const testComponentMapping = async (businessCode) => {
  try {
    const component = await import(`@/components/business-forms/${businessCode}.vue`)
    console.log('✅ 申请表单组件映射成功:', businessCode)
    
    const detailComponent = await import(`@/components/business-detail/${businessCode}.vue`)
    console.log('✅ 详情组件映射成功:', businessCode)
    
    return true
  } catch (error) {
    console.error('❌ 组件映射失败:', businessCode, error)
    return false
  }
}

// 使用示例
testComponentMapping('your_business')
```

### 表单数据验证

```typescript
// 表单数据验证工具
export const validateFormData = (data: any, rules: any): boolean => {
  for (const [field, fieldRules] of Object.entries(rules)) {
    const value = data[field]
    
    for (const rule of fieldRules as any[]) {
      if (rule.required && (!value || value === '')) {
        console.error(`字段 ${field} 不能为空`)
        return false
      }
      
      if (rule.min && value.length < rule.min) {
        console.error(`字段 ${field} 长度不能少于 ${rule.min}`)
        return false
      }
      
      // 更多验证规则...
    }
  }
  
  return true
}
```

### 工作流状态监听

```typescript
// 工作流状态监听
export const useWorkflowStatus = (instanceId: number) => {
  const status = ref(0)
  const statusText = ref('')
  
  // WebSocket监听状态变化
  const { data } = useWebSocket('ws://localhost:8080/ws')
  
  watch(data, (newData) => {
    if (newData) {
      const message = JSON.parse(newData)
      if (message.type === 'workflow_status_change' && 
          message.data.instance_id === instanceId) {
        status.value = message.data.status
        statusText.value = getStatusText(message.data.status)
      }
    }
  })
  
  return { status, statusText }
}
```

## 🐛 常见问题解决

### Q1: 组件映射失败

**问题**：组件无法正确加载
**解决**：
1. 检查文件名是否与businessCode一致
2. 检查组件是否正确导出
3. 检查路径是否正确

```javascript
// 调试组件映射
console.log('businessCode:', businessCode)
console.log('组件路径:', `@/components/business-forms/${businessCode}.vue`)
```

### Q2: 表单数据不同步

**问题**：表单数据与后端不一致
**解决**：
1. 检查表单字段名是否正确
2. 检查数据类型是否匹配
3. 检查数据转换逻辑

```javascript
// 调试表单数据
console.log('前端表单数据:', formData)
console.log('后端返回数据:', apiResponse.data)
```

### Q3: 工作流集成问题

**问题**：工作流状态不同步
**解决**：
1. 检查工作流回调配置
2. 检查状态映射逻辑
3. 检查WebSocket连接

```javascript
// 调试工作流状态
console.log('工作流实例ID:', instanceId)
console.log('当前状态:', approvalStatus)
console.log('状态文本:', statusText)
```

## 📚 进阶学习

### 1. 深入了解架构
- 阅读 [统一表单架构总体设计](./architecture/01-overall-architecture.md)
- 学习 [组件映射与加载机制](./architecture/02-component-mapping.md)

### 2. 掌握开发规范
- 遵循 [组件开发规范](./specifications/01-component-standards.md)
- 理解 [数据结构规范](./specifications/02-data-structure.md)

### 3. 实践高级功能
- 尝试 [Form-Create集成](./architecture/05-form-create-integration.md)
- 实现 [复杂表单示例](./examples/02-complex-form-example.md)

### 4. 性能优化
- 学习 [性能优化指南](./implementation/07-performance-optimization.md)
- 实施监控和调试

## 🎯 下一步行动

1. **立即开始**：按照本指南创建你的第一个业务表单
2. **深入学习**：阅读完整的架构文档
3. **实践应用**：在实际项目中应用统一架构
4. **持续改进**：根据使用反馈优化和完善

## 📞 获取帮助

- **文档问题**：查阅完整文档体系
- **技术问题**：联系架构团队
- **Bug反馈**：提交问题跟踪系统
- **功能建议**：参与技术讨论会议

---

**祝你开发愉快！** 🚀