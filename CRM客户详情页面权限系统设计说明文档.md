# CRM客户详情页面权限系统设计说明文档

## 📋 项目概述

### 项目背景
基于现有CRM系统，为客户详情页面的联系人、合同、回款、跟进记录等模块添加细粒度的按钮权限控制，实现安全可控的客户数据管理。

### 设计目标
1. **权限精细化**：实现按钮级别的权限控制
2. **数据安全性**：确保客户数据访问的安全性
3. **代码可维护性**：避免单文件过长，保持代码结构清晰
4. **扩展性**：为未来的客户共享功能预留完整框架

## 🏗️ 系统架构设计

### 权限模型架构
```
权限验证层次：
├── 功能权限 (菜单权限)
│   └── 基于system_menu表的按钮权限
├── 数据权限 (归属权限)  
│   ├── 客户负责人权限 (owner_user_id)
│   ├── 数据范围权限 (部门/角色)
│   └── 共享权限 (预留)
└── 业务权限 (状态权限)
    └── 基于业务状态的操作限制
```

### 数据表关联关系
```
crm_customer (客户表)
├── owner_user_id = creator_id (负责人=创建人)
├── crm_contact (联系人表)
│   └── customer_id → crm_customer.id
├── crm_contract (合同表)  
│   ├── customer_id → crm_customer.id
│   └── crm_contract_receivable (回款表)
│       └── contract_id → crm_contract.id
├── crm_follow_record (跟进记录表)
│   ├── related_type = 'customer'
│   └── related_id → crm_customer.id
└── crm_customer_share (客户共享表) [预留]
    ├── customer_id → crm_customer.id
    └── shared_user_id (被共享用户)
```

## 🔐 权限设计详解

### 客户归属权限模型

#### 当前实施阶段 (简化模型)
```php
/**
 * 客户权限验证规则
 */
class CustomerPermissionRules 
{
    // 1. 超级管理员：全部权限
    // 2. 客户负责人：全部操作权限 (owner_user_id = current_user_id)
    // 3. 数据权限范围：仅查看和跟进权限
    // 4. 其他用户：无权限
}
```

#### 未来扩展阶段 (共享模型)
```php
/**
 * 共享权限扩展规则
 */
class CustomerSharePermissionRules
{
    // 只读权限 (share_scope=1)：查看 + 跟进
    // 读写权限 (share_scope=2)：查看 + 跟进 + 联系人操作 + 部分合同回款操作
    // 限制权限：客户基础操作、删除操作、审批操作
}
```

### 按钮权限映射表

#### 联系人模块权限 (4个)
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 |
|---------|---------|--------|----------|----------|----------|
| `crm:crm_customer_my:add_contact` | 新增联系人 | ✅ | ✅ | ❌ | ❌ |
| `crm:crm_customer_my:contact_list` | 联系人列表 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:edit_contact` | 编辑联系人 | ✅ | ✅ | ❌ | ❌ |
| `crm:crm_customer_my:delete_contact` | 删除联系人 | ✅ | ✅ | ❌ | ❌ |

#### 合同模块权限 (6个)
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 |
|---------|---------|--------|----------|----------|----------|
| `crm:crm_customer_my:add_contract` | 新增合同 | ✅ | ✅ | ❌ | ❌ |
| `crm:crm_customer_my:contract_list` | 合同列表 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:contract_detail` | 合同详情 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:edit_contract` | 编辑合同 | ✅ | ✅* | ❌ | ❌ |
| `crm:crm_customer_my:delete_contract` | 删除合同 | ✅ | ❌ | ❌ | ❌ |
| `crm:crm_customer_my:submit_approval` | 提交审批 | ✅ | ❌ | ❌ | ❌ |

*注：共享用户只能编辑自己创建的合同

#### 回款模块权限 (7个)
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 |
|---------|---------|--------|----------|----------|----------|
| `crm:crm_customer_my:add_receivable` | 新增回款 | ✅ | ✅ | ❌ | ❌ |
| `crm:crm_customer_my:receivable_list` | 回款列表 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:receivable_detail` | 回款详情 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:edit_receivable` | 编辑回款 | ✅ | ✅* | ❌ | ❌ |
| `crm:crm_customer_my:delete_receivable` | 删除回款 | ✅ | ❌ | ❌ | ❌ |
| `crm:crm_customer_my:submit_receivable_approval` | 提交审批 | ✅ | ❌ | ❌ | ❌ |
| `crm:crm_customer_my:add_receivable_more` | 新增回款(更多) | ✅ | ✅ | ❌ | ❌ |

#### 跟进记录权限 (4个)
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 |
|---------|---------|--------|----------|----------|----------|
| `crm:crm_customer_my:add_follow` | 新增跟进 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:follow_detail` | 跟进详情 | ✅ | ✅ | ✅ | ✅ |
| `crm:crm_customer_my:edit_follow` | 编辑跟进 | ✅ | ✅* | ✅* | ✅* |
| `crm:crm_customer_my:delete_follow` | 删除跟进 | ✅ | ✅* | ✅* | ✅* |

*注：只能编辑/删除自己创建的跟进记录

#### 客户操作权限 (2个) - 暂缓实施
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 | 实施状态 |
|---------|---------|--------|----------|----------|----------|----------|
| `crm:crm_customer_my:transfer_customer` | 转移客户 | ✅ | ❌ | ❌ | ❌ | 🔄 预留 |
| `crm:crm_customer_my:recycle_customer` | 回收客户 | ✅ | ❌ | ❌ | ❌ | ✅ 实施 |

#### 共享功能权限 (1个) - 预留设计
| 权限标识 | 按钮名称 | 负责人 | 共享读写 | 共享只读 | 数据范围 | 实施状态 |
|---------|---------|--------|----------|----------|----------|----------|
| `crm:crm_customer_my:share_customer` | 共享客户 | ✅ | ❌ | ❌ | ❌ | 🔄 预留 |

## 🏛️ 技术实现架构

### 控制器架构设计
```
CrmCustomerMyController.php (主控制器)
├── 基础CRUD操作
├── 客户管理操作 (转移、共享、回收)
└── 公海相关操作

CrmCustomerDetailController.php (详情操作控制器)
├── CustomerContactTrait (联系人操作)
├── CustomerContractTrait (合同操作)  
├── CustomerReceivableTrait (回款操作)
└── CustomerFollowTrait (跟进操作)
```

### 权限验证架构
```
权限验证中间件链：
├── TokenAuthMiddleware (身份验证)
├── PermissionMiddleware (功能权限)
└── CustomerAccessMiddleware (数据权限) [新增]

权限服务层：
├── CustomerPermissionService (客户权限验证)
├── CustomerShareService (共享权限验证) [预留]
└── DataPermissionService (数据范围权限)
```

### 前端权限控制
```vue
<!-- 权限指令使用 -->
<el-button v-permission="'crm:crm_customer_my:add_contact'">
  新增联系人
</el-button>

<!-- 组合权限验证 -->
<el-button v-if="hasCustomerAccess(customerId, 'edit')" 
           v-permission="'crm:crm_customer_my:edit_contact'">
  编辑联系人
</el-button>
```

## 📊 数据库设计

### 权限表结构
```sql
-- system_menu 表新增24个按钮权限
-- parent_id = 192 (crm:crm_customer_my:index)
-- type = 2 (按钮权限)
-- 权限标识格式：crm:crm_customer_my:{action}
```

### 共享表结构 (预留)
```sql
-- crm_customer_share 表
-- 支持按客户、按用户的共享权限管理
-- share_scope: 1=只读, 2=读写
-- share_deadline: 共享截止时间
```

## 🔧 配置说明

### 权限配置流程
1. **执行SQL文件**：添加24个按钮权限到system_menu表
2. **角色权限配置**：在角色管理中分配相应权限
3. **前端权限控制**：使用v-permission指令控制按钮显示
4. **后端权限验证**：使用权限中间件验证API访问

### 性能优化考虑
1. **权限缓存**：缓存用户权限信息，减少数据库查询
2. **批量验证**：支持批量客户权限验证，避免N+1查询
3. **索引优化**：在关键字段上建立适当索引

## ⚠️ 注意事项

### 安全考虑
1. **权限继承**：详情页面权限需要先有客户访问权限
2. **数据隔离**：严格的租户数据隔离
3. **操作日志**：记录敏感操作的权限验证过程

### 扩展性设计
1. **共享功能预留**：完整的共享权限框架设计
2. **审批流程**：为合同、回款审批预留权限扩展点
3. **批量操作**：支持批量操作的权限控制

## 📈 后续规划

### 第一阶段：核心权限实施 (当前版本)
- 实现21个按钮权限 (联系人4个 + 合同6个 + 回款7个 + 跟进4个)
- 完成负责人权限模型
- 基础的数据权限验证
- 实现回收客户功能

### 第二阶段：预留功能实施 (未来版本)
- 客户转移功能
- 客户共享功能及权限验证
- 商机管理功能
- 共享操作日志记录

### 第三阶段：高级功能扩展 (未来版本)
- 审批流程权限
- 批量操作权限
- 权限统计分析
- 数据权限范围扩展

---

**文档版本**：v1.0  
**创建时间**：2025-01-14  
**更新时间**：2025-01-14  
**维护人员**：CRM开发团队
