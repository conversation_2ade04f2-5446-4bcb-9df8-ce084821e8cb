<template>
  <el-dialog
    v-model="visible"
    title="添加跟进"
    width="700px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="follow-dialog-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="跟进方式" prop="follow_type">
          <el-select v-model="formData.follow_type" placeholder="请选择跟进方式" style="width: 100%">
            <el-option label="电话" value="phone" />
            <el-option label="会议" value="meeting" />
            <el-option label="邮件" value="email" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="跟进时间" prop="follow_date">
          <el-date-picker
            v-model="formData.follow_date"
            type="datetime"
            placeholder="选择跟进时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="跟进内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="4"
            placeholder="请输入跟进内容..."
            maxlength="1000"
            show-word-limit
            resize="none"
          />
        </el-form-item>
        
        <el-form-item label="下次计划" prop="next_plan">
          <el-input
            v-model="formData.next_plan"
            type="textarea"
            :rows="3"
            placeholder="请输入下次跟进计划..."
            maxlength="500"
            show-word-limit
            resize="none"
          />
        </el-form-item>
        
        <el-form-item label="下次跟进" prop="next_date">
          <el-date-picker
            v-model="formData.next_date"
            type="datetime"
            placeholder="选择下次跟进时间"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="default">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="loading"
          size="default"
        >
          保存跟进
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElSelect, ElOption, ElDatePicker } from 'element-plus'
import { ProjectApi } from '@/api/project/projectApi'

// Props
interface Props {
  modelValue: boolean
  taskId: number | string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 响应式数据
const visible = ref(props.modelValue)
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  follow_type: '',
  follow_date: '',
  content: '',
  next_plan: '',
  next_date: '',
  priority: 'medium'
})

// 表单验证规则
const rules = {
  follow_type: [
    { required: true, message: '请选择跟进方式', trigger: 'change' }
  ],
  follow_date: [
    { required: true, message: '请选择跟进时间', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入跟进内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '跟进内容长度在 1 到 1000 个字符', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 设置默认跟进时间为当前时间
    const now = new Date()
    formData.follow_date = now.toISOString().slice(0, 19).replace('T', ' ')
  }
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.follow_type = ''
  formData.follow_date = ''
  formData.content = ''
  formData.next_plan = ''
  formData.next_date = ''
  formData.priority = 'medium'
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      task_id: props.taskId,
      follow_type: formData.follow_type,
      follow_date: formData.follow_date,
      content: formData.content,
      next_plan: formData.next_plan,
      next_date: formData.next_date,
      priority: formData.priority
    }
    
    await ProjectApi.addTaskFollow(submitData)
    
    ElMessage.success('跟进记录保存成功')
    emit('success')
    visible.value = false
    
  } catch (error) {
    console.error('保存跟进记录失败:', error)
    ElMessage.error('保存跟进记录失败')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.follow-dialog-content {
  padding: 0;
  
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .el-textarea {
      :deep(.el-textarea__inner) {
        font-family: inherit;
        line-height: 1.5;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
