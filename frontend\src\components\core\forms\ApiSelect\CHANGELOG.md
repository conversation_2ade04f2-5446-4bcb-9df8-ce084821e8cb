# ApiSelect 组件更新日志

## v2.1.0 (2025-07-13) 🚀

### 🎯 重大优化：智能加载策略

#### ✨ 新增特性
- **精确监听机制**：只监听API URL变化，避免响应式数据变化导致的误触发
- **零额外请求**：选择值和关闭下拉框时无额外API请求
- **完善状态管理**：增加初始化状态管理，确保数据只在需要时加载
- **防重复请求优化**：增强防重复请求机制，支持多种场景

#### 🔧 技术改进
- 将API配置的深度监听改为只监听URL变化
- 优化loadDefaultData方法，增加已初始化检查
- 移除不必要的聚焦和可见性变化时的加载逻辑
- 增强缓存机制，避免重复数据加载

#### 📊 性能提升
- **减少API请求**：表单场景下从多次请求优化为单次预加载
- **提升响应速度**：用户点击下拉框立即显示选项
- **降低服务器压力**：避免不必要的重复请求

#### 🎯 用户体验改进
- **即时响应**：下拉框打开时立即显示选项，无等待时间
- **流畅交互**：选择值时无卡顿，无额外网络请求
- **智能缓存**：重复打开表单时使用缓存数据

### 📋 配置建议更新

#### 推荐配置（表单场景）
```vue
<ApiSelect
  v-model="formData.category_id"
  :api="{ url: '/api/categories/options' }"
  placeholder="请选择分类"
  clearable
  :auto-load="true"        <!-- 预加载 -->
  :load-on-focus="false"   <!-- 避免重复请求 -->
/>
```

#### 初始值规范
```javascript
// ✅ 正确：使用 null 显示 placeholder
const formData = reactive({
  category_id: null,
  unit_id: null
})

// ❌ 错误：使用 0 会被认为是已选择状态
const formData = reactive({
  category_id: 0,
  unit_id: 0
})
```

### 🐛 修复问题
- 修复选择值后可能触发额外请求的问题
- 修复API配置深度监听导致的误触发问题
- 修复初始值为0时不显示placeholder的问题
- 修复重复打开表单时的重复请求问题

### 📚 文档更新
- 更新README.md，添加最新优化说明
- 更新USAGE.md，增加性能优化成果说明
- 更新demo.vue，展示推荐配置
- 新增CHANGELOG.md，记录版本变更

### 🔄 兼容性
- 保持向后兼容，现有代码无需修改
- 推荐更新配置以获得最佳性能
- 所有现有API和事件保持不变

### 🎉 升级建议
1. 将表单中的ApiSelect配置为 `autoLoad: true, loadOnFocus: false`
2. 确保初始值使用 `null` 而不是 `0`
3. 移除不必要的事件监听器
4. 启用缓存机制以获得最佳性能

---

## v2.0.0 (2025-07-03)

### ✨ 新增特性
- 支持自定义字段映射
- 增加数据转换函数
- 支持远程搜索
- 增加智能缓存机制

### 🔧 技术改进
- 重构组件架构
- 优化API调用逻辑
- 增强错误处理
- 改进TypeScript类型定义

---

## v1.0.0 (2025-06-01)

### 🎉 首次发布
- 基础API选择器功能
- 支持单选/多选模式
- Element Plus集成
- 基础缓存机制
