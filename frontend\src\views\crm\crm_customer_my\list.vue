<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import {
    ElMessage,
    ElMessageBox,
    ElCard,
    ElButton,
    ElTableColumn,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElTag
  } from 'element-plus'
  import { Delete } from '@element-plus/icons-vue'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import { BgColorEnum } from '@/enums/appEnum'
  import { CrmCustomerMyApi } from '@/api/crm/crmCustomerMy'
  import { ApiStatus } from '@/utils/http/status'

  import {
    LongTextColumn,
    SwitchColumn,
    LinkColumn,
    TagColumn
  } from '@/components/core/tables/columns'

  import FormDialog from './form-dialog.vue'
  import ImportExportDialog from './import-export-dialog.vue'
  import CustomerDetailDrawer from '@/components/custom/CustomerDetailDrawer/index.vue'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'

  // 权限验证
  const { hasButtonPermission } = useCustomerPermission()

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 客户详情抽屉
  const customerDetailVisible = ref(false)
  const currentCustomerId = ref<number | string>('')

  // 定义表单搜索初始值
  const initialSearchState = {}

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      prop: 'customer_name',
      label: '客户名称',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入客户名称'
      },
      onChange: handleFormChange
    },
    {
      prop: 'level',
      label: '客户级别',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择客户级别'
      },
      options: () => [
        { label: '未知', value: '0' },
        { label: '普通', value: '1' },
        { label: '重要', value: '2' },
        { label: '战略', value: '3' }
      ],
      onChange: handleFormChange
    },
    {
      prop: 'phone',
      label: '电话',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入电话号码'
      },
      onChange: handleFormChange
    },
    /*{
      prop: 'region_province',
      label: '省份',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入省份'
      },
      onChange: handleFormChange
    },
    {
      prop: 'region_city',
      label: '城市',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入城市'
      },
      onChange: handleFormChange
    },*/
    {
      prop: 'status',
      label: '客户状态',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择客户状态'
      },
      options: () => [
        { label: '启用', value: '1' },
        { label: '禁用', value: '0' }
      ],
      onChange: handleFormChange
    }
    /*{
      prop: 'annual_revenue',
      label: '年营业额',
      type: 'numberrange',
      config: {
        clearable: true,
        placeholder: '请输入年营业额范围',
        minPlaceholder: '最小年营业额',
        maxPlaceholder: '最大年营业额'
      },
      onChange: handleFormChange
    },
    {
      prop: 'last_followed_at',
      label: '最后跟进时间',
      type: 'date',
      config: {
        clearable: true,
        placeholder: '请选择最后跟进时间',
        type: 'date'
      },
      onChange: handleFormChange
    },
    {
      prop: 'lock_status',
      label: '锁定状态',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择锁定状态'
      },
      options: () => [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      onChange: handleFormChange
    }*/
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 动态列配置
  const { columnChecks } = useCheckedColumns(() => [
    {
      prop: 'id',
      label: 'ID',
      width: 80
    },
    {
      prop: 'customer_name',
      label: '名称'
    },
    {
      prop: 'industry',
      label: '所属行业'
    },
    {
      prop: 'level',
      label: '客户级别',
      width: 100,
      component: TagColumn,
      componentProps: {
        options: [
          { label: '普通', value: 1, type: 'info' },
          { label: '重要', value: 2, type: 'warning' },
          { label: '战略', value: 3, type: 'danger' }
        ]
      },
      isSpecialColumn: 1
    },
    {
      prop: 'source',
      label: '客户来源'
    },
    {
      prop: 'phone',
      label: '电话'
    },
    {
      prop: 'website',
      label: '官网',
      component: LinkColumn,
      componentProps: {
        openInNewTab: true,
        maxLength: 30
      },
      isSpecialColumn: 1
    },
    {
      prop: 'region_province',
      label: '省份'
    },
    {
      prop: 'region_city',
      label: '城市'
    },
    {
      prop: 'region_district',
      label: '区/县'
    },
    {
      prop: 'address',
      label: '详细地址'
    },
    {
      prop: 'remark',
      label: '备注',
      component: LongTextColumn,
      componentProps: {
        maxLength: 50
      },
      isSpecialColumn: 1
    },
    {
      prop: 'owner_user_id',
      label: '负责人ID'
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      component: SwitchColumn,
      componentProps: {
        activeValue: 1,
        inactiveValue: 0,
        activeText: '启用',
        inactiveText: '禁用',
        updateApi: CrmCustomerMyApi.updateField
      },
      isSpecialColumn: 1
    },
    /*{
      prop: 'in_sea',
      label: '是否在公海',
      component: SwitchColumn,
      componentProps: {
        activeValue: 1,
        inactiveValue: 0,
        activeText: '是',
        inactiveText: '否',
        updateApi: CrmCustomerMyApi.updateField
      },
      isSpecialColumn: 1
    },*/
    {
      prop: 'credit_code',
      label: '统一社会信用代码'
    },
    {
      prop: 'annual_revenue',
      label: '年营业额'
    },
    {
      prop: 'employee_count',
      label: '员工人数'
    },
    {
      prop: 'registered_capital',
      label: '注册资本'
    },
    {
      prop: 'last_followed_at',
      label: '最后跟进时间',
      width: 180
    },
    {
      prop: 'next_followed_at',
      label: '下次跟进时间',
      width: 180
    },
    /*{
      prop: 'creator_name',
      label: '创建人'
    },*/
    /*
        {
          prop: 'updated_by',
          label: '更新人'
        },*/
    /*{
      prop: 'created_at',
      label: '创建时间',
      width: 180
    },*/
    {
      prop: 'lock_status',
      label: '锁定',
      component: SwitchColumn,
      componentProps: {
        activeValue: 1,
        inactiveValue: 0,
        activeText: '是',
        inactiveText: '否',
        updateApi: CrmCustomerMyApi.updateField
      },
      isSpecialColumn: 1
    },
    {
      prop: 'lock_expire_time',
      label: '锁定到期时间',
      width: 180
    }
  ])

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmCustomerMyApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示客户详情抽屉
  const showDetail = (id: number) => {
    currentCustomerId.value = id
    customerDetailVisible.value = true
  }

  // 处理客户详情刷新
  const handleCustomerRefresh = () => {
    getTableData()
  }

  // 这个函数已经在下面的条件块中定义了

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmCustomerMyApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } catch {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 导入导出对话框引用
  const importExportDialogRef = ref()

  // 显示导入对话框
  const showImportDialog = () => {
    importExportDialogRef.value?.showDialog('import')
  }

  // 导入导出成功回调
  const handleImportExportSuccess = () => {
    getTableData()
  }

  // 显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }

  // 表单对话框引用
  const formDialogRef = ref()

  // 显示表单对话框
  const showFormDialog = (type: string, id?: number) => {
    formDialogRef.value?.showDialog(type, id)
  }

  // 处理更多操作下拉菜单
  const handleMoreAction = (command: string, row: any) => {
    // 权限二次验证
    const permissionMap: Record<string, string> = {
      delete: 'crm:crm_customer_my:delete'
    }

    const requiredPermission = permissionMap[command]
    if (requiredPermission && !hasButtonPermission(requiredPermission)) {
      ElMessage.error('您没有执行此操作的权限')
      return
    }

    switch (command) {
      case 'delete':
        handleDelete(row.id)
        break
      default:
        console.warn('未知的操作命令:', command)
    }
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }

  // 客户级别映射对象
  const levelTypeMap: Record<number, 'info' | 'warning' | 'danger'> = {
    0: 'info', // 未知
    1: 'info', // 普通
    2: 'warning', // 重要
    3: 'danger' // 战略
  }

  const levelLabelMap: Record<number, string> = {
    0: '未知',
    1: '普通',
    2: '重要',
    3: '战略'
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-crmCustomer-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <ElButton
              v-if="hasButtonPermission('crm:crm_customer_my:add')"
              type="primary"
              @click="showFormDialog('add')"
            >
              新增
            </ElButton>

            <!--            <ElButton type="success" @click="showImportDialog">导入</ElButton>-->

            <!--            <ElButton type="warning" @click="showExportDialog">导出</ElButton>-->
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- 序号列 -->
          <!-- <ElTableColumn type="index" label="序号" width="60" align="center" /> -->

          <!-- ID列 -->
          <ElTableColumn prop="id" label="ID" width="80" align="center">
            <template #default="scope">
              <span class="number-field">{{ scope.row.id }}</span>
            </template>
          </ElTableColumn>

          <!-- 基础信息 -->
          <ElTableColumn label="基础信息" min-width="350">
            <template #default="scope">
              <div class="basic-info-container">
                <!-- 客户名称 -->
                <div class="customer-name">
                  <span class="name-text">{{ scope.row.customer_name || '-' }}</span>
                </div>

                <!-- 级别和行业 -->
                <div class="info-row">
                  <span class="info-label">级别:</span>
                  <ElTag
                    :type="levelTypeMap[scope.row.level] || 'info'"
                    size="small"
                    class="level-tag"
                  >
                    {{ levelLabelMap[scope.row.level] || '未知' }}
                  </ElTag>
                  <span class="info-separator">|</span>
                  <span class="info-label">行业:</span>
                  <span class="info-value">{{ scope.row.industry || '-' }}</span>
                </div>

                <!-- 来源和电话 -->
                <div class="info-row">
                  <span class="info-label">来源:</span>
                  <ElTag v-if="scope.row.source" type="primary" size="small" class="source-tag">
                    {{ scope.row.source }}
                  </ElTag>
                  <span v-else class="info-value">-</span>
                  <span class="info-separator">|</span>
                  <span class="info-label">电话:</span>
                  <span class="info-value phone-field">{{ scope.row.phone || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 业务信息 -->
          <ElTableColumn label="业务信息" min-width="260">
            <template #default="scope">
              <div class="info-group">
                <div class="info-item" v-if="scope.row.website">
                  <span class="label">官网:</span>
                  <a :href="scope.row.website" target="_blank" class="link">
                    {{
                      scope.row.website.length > 30
                        ? scope.row.website.substring(0, 30) + '...'
                        : scope.row.website
                    }}
                  </a>
                </div>
                <div class="info-item">
                  <span class="label">信用代码:</span>
                  <span class="value number-field">{{ scope.row.credit_code || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">年营业额:</span>
                  <span class="value number-field">{{ scope.row.annual_revenue || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">员工人数:</span>
                  <span class="value number-field">{{ scope.row.employee_count || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">注册资本:</span>
                  <span class="value number-field">{{ scope.row.registered_capital || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 备注 -->
          <!-- <LongTextColumn
            label="备注"
            prop="remark"
            :max-length="50"
          /> -->

          <!-- 负责人 -->
          <!--          <ElTableColumn prop="owner_user_id" label="负责人" align="center">
                      <template #default="scope">
                        <span class="number-field">{{ scope.row.owner_user_id || '-' }}</span>
                      </template>
                    </ElTableColumn>-->

          <!-- 状态 -->
          <SwitchColumn
            label="状态"
            prop="status"
            width="100"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
            :api-method="CrmCustomerMyApi.updateField"
          />

          <!-- 跟进时间 -->
          <!--          <ElTableColumn label="跟进时间" width="180">
                      <template #default="scope">
                        <div class="info-group">
                          <div class="info-item">
                            <span class="label">最后跟进:</span>
                            <span class="value time-info">{{ scope.row.last_followed_at || '-' }}</span>
                          </div>
                          <div class="info-item">
                            <span class="label">下次跟进:</span>
                            <span class="value time-info">{{ scope.row.next_followed_at || '-' }}</span>
                          </div>
                        </div>
                      </template>
                    </ElTableColumn>-->

          <!-- 锁定状态 -->
          <ElTableColumn prop="lock_status" label="锁定状态" width="100" align="center">
            <template #default="scope">
              <ElTag :type="scope.row.lock_status === 1 ? 'danger' : 'success'" size="small">
                {{ scope.row.lock_status === 1 ? '已锁定' : '未锁定' }}
              </ElTag>
            </template>
          </ElTableColumn>

          <!-- 创建时间 -->
          <ElTableColumn prop="created_at" label="创建时间" width="120" align="center">
            <template #default="scope">
              <span class="time-info">{{ scope.row.created_at }}</span>
            </template>
          </ElTableColumn>

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="230" align="center">
            <template #default="scope">
              <div class="operation-buttons">
                <!-- 详情按钮 -->
                <ArtButtonTable
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(scope.row.id)"
                />

                <!-- 编辑按钮 -->
                <ArtButtonTable
                  v-if="hasButtonPermission('crm:crm_customer_my:edit')"
                  text="编辑"
                  :iconClass="BgColorEnum.PRIMARY"
                  @click="showFormDialog('edit', scope.row.id)"
                />

                <!-- 更多操作下拉菜单 -->
                <ElDropdown
                  v-if="hasButtonPermission('crm:crm_customer_my:delete')"
                  @command="(command) => handleMoreAction(command, scope.row)"
                >
                  <ArtButtonTable text="更多" type="more" />
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem
                        v-if="hasButtonPermission('crm:crm_customer_my:delete')"
                        divided
                        command="delete"
                        :icon="Delete"
                      >
                        删除
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 客户详情抽屉 -->
        <CustomerDetailDrawer
          v-model="customerDetailVisible"
          :customer-id="currentCustomerId"
          @refresh="handleCustomerRefresh"
        />

        <!-- 表单组件 -->

        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />

        <!-- 导入导出对话框 -->

        <ImportExportDialog ref="importExportDialogRef" @success="handleImportExportSuccess" />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-crmCustomer-page {
    width: 100%;

    // ==================== 表格基础样式 ====================
    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }

      // 表格字体统一设置
      .el-table__body-wrapper,
      .el-table__header-wrapper,
      .el-table__body,
      .el-table__header {
        font-size: 15px;

        .el-table__cell,
        td,
        th {
          font-size: 15px;
        }
      }

      // 数字字段等宽字体
      .time-info,
      .number-field,
      .phone-field {
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 15px;
      }

      // 次要信息字体
      .secondary-info {
        font-size: 13px;
      }
    }

    // ==================== 基础信息组样式 ====================
    .basic-info-container {
      padding: 8px 0;

      .customer-name {
        margin-bottom: 8px;

        .name-text {
          font-size: 16px;
          font-weight: 600;
          color: var(--art-text-gray-800);
          line-height: 1.4;
        }
      }

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        font-size: 13px;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          color: var(--art-text-gray-600);
          font-size: 13px;
          margin-right: 4px;
          font-weight: 500;
        }

        .info-value {
          color: var(--art-text-gray-800);
          font-size: 13px;
          margin-right: 8px;

          &.phone-field {
            font-family: 'Consolas', 'Monaco', monospace;
          }
        }

        .info-separator {
          color: #dcdfe6;
          margin: 0 8px;
          font-weight: normal;
        }

        .level-tag,
        .source-tag {
          margin-right: 8px;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }

    // ==================== 通用信息组样式 ====================
    .info-group {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
        font-size: 15px;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: var(--art-text-gray-600);
          margin-right: 6px;
          min-width: 60px;
          font-weight: 500;
          font-size: 13px;
        }

        .value {
          color: var(--art-text-gray-800);
          flex: 1;
          word-break: break-all;
          font-size: 13px;

          &.phone-field,
          &.number-field,
          &.time-info {
            font-family: 'Consolas', 'Monaco', monospace;
          }
        }

        .link {
          color: #409eff;
          text-decoration: none;
          flex: 1;
          word-break: break-all;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    // ==================== 标签样式 ====================
    .source-tag {
      font-size: 13px;
      font-weight: bold;
    }

    // ==================== 其他样式 ====================
    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    // ==================== 操作按钮样式 ====================
    .operation-buttons {
      display: flex;
      align-items: center;
      gap: 6px;
      justify-content: center;

      .el-button {
        margin: 0;
        padding: 4px 8px;
        font-size: 12px;
      }

      :deep(.el-dropdown) {
        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    // ==================== 响应式设计 ====================
    @media (max-width: 768px) {
      .operation-buttons {
        flex-direction: column;
        gap: 2px;

        .el-button {
          font-size: 11px;
          padding: 2px 6px;
        }

        :deep(.el-dropdown) {
          .el-button {
            font-size: 11px;
            padding: 2px 6px;
          }
        }
      }
    }
  }
</style>
