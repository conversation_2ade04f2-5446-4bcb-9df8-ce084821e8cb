<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import {
    ElDialog,
    ElTag,
    ElDescriptions,
    ElDescriptionsItem,
    ElTabs,
    ElTabPane,
    ElTimeline,
    ElTimelineItem,
    ElCard
  } from 'element-plus'
  // import { ElMessage } from 'element-plus'
  import { useCheckedColumns } from '@/composables/useCheckedColumns'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  // import { BgColorEnum } from '@/enums/appEnum'
  import FormDataViewer from './components/form-data-viewer.vue'

  // 导入工作流组件
  import WorkflowDesigner from '@/components/custom/workflow/index.vue'
  import { WorkflowCcApi } from '@/api/workflow/WorkflowCcApi'
  /*import {
    getInstanceStatusText,
    getInstanceStatusTagType
  } from '@/constants/workflow'*/

  // 使用h函数渲染标签
  import { h } from 'vue'
  import { ApiStatus } from '@/utils/http/status'
  import { BgColorEnum } from '@/enums/appEnum'

  // 加载状态
  const loading = ref(false)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 详情数据加载状态
  const processGraphLoading = ref(false)
  const historyLoading = ref(false)
  const processGraphData = ref<{ nodeConfig: any; flowPermission: any; name: string } | null>(null)

  // 定义审批历史记录的类型接口
  interface ApprovalHistory {
    operation_time?: string
    operation?: string
    operator_name?: string
    node_name?: string
    opinion?: string

    [key: string]: any
  }

  const historyData = ref<ApprovalHistory[]>([])

  // 定义表单搜索初始值
  const initialSearchState = {
    title: '',
    status: '',
    created_at: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    /*{
      label: '标题',
      prop: 'title',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },*/
    {
      label: '类型',
      prop: 'definition_id',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '请假流程', value: '1' },
        { label: '报销流程', value: '2' },
        { label: '采购流程', value: '3' }
      ],
      onChange: handleFormChange
    },
    /*{
      label: '抄送时间',
      prop: 'created_at',
      type: 'daterange',
      config: {
        clearable: true,
        type: 'daterange',
        shortcuts: [
          {
            text: '今天',
            value: () => {
              const today = new Date()
              return [today, today]
            }
          },
          {
            text: '最近一周',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setDate(start.getDate() - 6)
              return [start, end]
            }
          },
          {
            text: '最近一个月',
            value: () => {
              const end = new Date()
              const start = new Date()
              start.setMonth(start.getMonth() - 1)
              return [start, end]
            }
          }
        ]
      }
    }*/
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 获取状态标签类型
  /*const getStatusTagType = (status: number) => {
    switch (status) {
      case 0:
        return 'info' // 未读
      case 1:
        return 'success' // 已读
      default:
        return 'info' // 默认使用info类型
    }
  }*/

  // 构建状态标签文本
  /*const buildStatusTagText = (status: number) => {
    switch (status) {
      case 0:
        return '未读'
      case 1:
        return '已读'
      default:
        return '未知状态'
    }
  }*/

  // 构建流程状态标签文本
  const buildProcessStatusTagText = (status: number) => {
    switch (status) {
      case 0:
        return '草稿'
      case 1:
        return '审批中'
      case 2:
        return '已通过'
      case 3:
        return '已拒绝'
      case 4:
        return '已终止'
      case 5:
        return '已撤回'
      default:
        return '未知状态'
    }
  }

  // 获取流程状态标签类型
  const getProcessStatusTagType = (status: number) => {
    switch (status) {
      case 0:
        return 'info' // 草稿
      case 1:
        return 'warning' // 审批中
      case 2:
        return 'success' // 已通过
      case 3:
        return 'danger' // 已拒绝
      case 4:
        return 'info' // 已终止
      case 5:
        return 'info' // 已撤回
      default:
        return 'info' // 默认使用info类型
    }
  }

  // 动态列配置
  const { columnChecks, columns } = useCheckedColumns(() => [
    { prop: 'instance.title', label: '标题' },
    { prop: 'instance.type_name', label: '类型' },
    {
      prop: 'instance.status',
      label: '状态',
      width: 100,
      formatter: (row) => {
        return h(ElTag, { type: getProcessStatusTagType(row.instance?.status) }, () =>
          buildProcessStatusTagText(row.instance?.status)
        )
      }
    },
    { prop: 'instance.submitter_name', label: '发起人' },
    // { prop: 'node_name', label: '抄送节点' },
    /*{
      prop: 'status',
      label: '状态',
      width: 80,
      formatter: (row) => {
        return h(ElTag, { type: getStatusTagType(row.status) }, () =>
          buildStatusTagText(row.status)
        )
      }
    },*/
    {
      prop: 'created_at',
      label: '抄送时间',
      sortable: true
    },
    {
      prop: 'operation',
      label: '操作',
      width: 120
    }
  ])

  // 表格数据
  const tableData = ref<any[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      // 调用API获取抄送列表
      const res = await WorkflowCcApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } catch (error) {
      console.error('获取抄送列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 显示详情
  const showDetail = async (instance_id: number) => {
    try {
      loading.value = true
      processGraphLoading.value = true
      historyLoading.value = true

      const res = await WorkflowCcApi.detail(instance_id)

      if (res.code === ApiStatus.success) {
        detailData.value = res.data

        // 修复流程图数据，确保格式与WorkflowDesigner期望的一致
        if (res.data.process_data) {
          processGraphData.value = {
            nodeConfig: res.data.process_data.nodeConfig,
            flowPermission: res.data.process_data.flowPermission || [],
            name: res.data.title || ''
          }
        } else {
          processGraphData.value = null
        }

        historyData.value = res.data.history || []
        detailDialogVisible.value = true

        // 如果是未读状态，自动标记为已读
        /*if (res.data.status === 0) {
          markAsRead(id)
        }*/
      }
    } catch (error) {
      console.error('获取详情失败', error)
    } finally {
      loading.value = false
      processGraphLoading.value = false
      historyLoading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 标记为已读
  /*const markAsRead = async (id: number) => {
    try {
      const res = await WorkflowCcApi.read(id)
      if (res.code === 1) {
        // 更新本地数据状态
        const index = tableData.value.findIndex((item) => item.id === id)
        if (index !== -1) {
          tableData.value[index].status = 1
        }
        ElMessage.success('已标记为已读')
      }
    } catch (error) {
      console.error('标记已读失败', error)
    }
  }*/

  // 批量标记为已读
  /*const batchMarkAsRead = () => {
    if (tableData.value.length === 0) {
      ElMessage.warning('没有可标记的数据')
      return
    }

    ElMessageBox.confirm('确定要将所有未读抄送标记为已读吗？', '批量标记', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        try {
          loading.value = true
          // 获取所有未读的任务ID
          const unreadTaskIds = tableData.value
            .filter((item) => item.status === 0)
            .map((item) => item.id)

          if (unreadTaskIds.length === 0) {
            ElMessage.info('没有未读的抄送')
            loading.value = false
            return
          }

          const res = await WorkflowCcApi.batchRead(unreadTaskIds)
          if (res.code === 1) {
            // 更新本地数据状态
            tableData.value.forEach((item) => {
              if (item.status === 0) {
                item.status = 1
              }
            })
            ElMessage.success(res.message || '批量标记已读成功')
          } else {
            ElMessage.error(res.message || '批量标记已读失败')
          }
        } catch (error) {
          console.error('批量标记已读失败', error)
          ElMessage.error('批量标记已读失败')
        } finally {
          loading.value = false
        }
      })
      .catch(() => {})
  }
*/
  // 获取时间线项目类型
  const getTimelineItemType = (operation: string) => {
    switch (operation) {
      case 'submit':
        return 'success'
      case 'withdraw':
        return 'info'
      case 'approve':
        return 'primary'
      case 'reject':
        return 'danger'
      case 'terminate':
        return 'warning'
      case 'recall':
        return 'info'
      default:
        return 'info'
    }
  }

  // 处理详情对话框关闭
  const handleDetailDialogClose = () => {
    // 重置工作流数据
    processGraphData.value = null
    historyData.value = []
    detailData.value = {}
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="workflow_cc-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader
          :columnList="columnOptions"
          v-model:columns="columnChecks"
          @refresh="handleRefresh"
        >
          <!--          <template #left>
                      <ElButton @click="batchMarkAsRead" type="primary" v-ripple> 全部标为已读</ElButton>
                    </template>-->
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :data="tableData"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop || col.type" v-bind="col">
            <template v-if="col.prop === 'operation'" #default="scope">
              <div>
                <ArtButtonTable
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(scope.row.instance_id)"
                />
                <!--                <ArtButtonTable
                                  v-if="scope.row.status === 0"
                                  text="标记已读"
                                  :iconClass="BgColorEnum.SUCCESS"
                                  @click="markAsRead(scope.row.id)"
                                />-->
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="抄送详情"
          width="70%"
          destroy-on-close
          @close="handleDetailDialogClose"
        >
          <ElTabs>
            <ElTabPane label="基本信息">
              <ElDescriptions border :column="2">
                <ElDescriptionsItem label="标题">{{ detailData.title }}</ElDescriptionsItem>
                <ElDescriptionsItem label="类型">{{ detailData.type_name }}</ElDescriptionsItem>
                <ElDescriptionsItem label="编号">
                  {{ detailData.process_id || '-' }}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="所属部门">
                  {{ detailData.dept_name || '-' }}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="状态">
                  <ElTag :type="getProcessStatusTagType(detailData.status)">
                    {{ buildProcessStatusTagText(detailData.status) }}
                  </ElTag>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="发起时间">
                  {{ detailData.created_at }}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="提交人">
                  {{ detailData.submitter_name || '-' }}
                </ElDescriptionsItem>
                <ElDescriptionsItem label="当前处理人">
                  {{ detailData.current_handler || '-' }}
                </ElDescriptionsItem>
              </ElDescriptions>
            </ElTabPane>

            <ElTabPane label="流程图">
              <div class="workflow-process-graph" v-loading="processGraphLoading">
                <WorkflowDesigner
                  v-if="processGraphData"
                  :workflow-data="processGraphData"
                  :read-only="true"
                  :current-executing-node-id="
                    detailData.status === 2 ? null : detailData.current_node
                  "
                  style="height: 400px"
                />
                <div v-else class="empty-placeholder">暂无流程图数据</div>
              </div>
            </ElTabPane>

            <ElTabPane label="审批历史">
              <div class="workflow-history" v-loading="historyLoading">
                <ElTimeline v-if="historyData && historyData.length > 0">
                  <ElTimelineItem
                    v-for="(item, index) in historyData"
                    :key="index"
                    :timestamp="item.operation_time || ''"
                    :type="getTimelineItemType(item.operation || '')"
                  >
                    <ElCard class="history-card">
                      <h4>{{ item.operation_text || '' }}</h4>
                      <p class="operation-info">
                        <span>操作人：{{ item.operator_name || '-' }}</span>
                      </p>
                      <template v-if="item.opinion">
                        <p class="opinion-title">审批意见：</p>
                        <p class="opinion-content">{{ item.opinion }}</p>
                      </template>
                    </ElCard>
                  </ElTimelineItem>
                </ElTimeline>
                <div v-else class="empty-placeholder">暂无审批历史</div>
              </div>
            </ElTabPane>

            <ElTabPane label="表单数据">
              <FormDataViewer
                :formData="detailData.form_data || {}"
                :businessCode="detailData.business_code || ''"
              />
            </ElTabPane>
          </ElTabs>

          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .workflow_cc-page {
    width: 100%;

    :deep(.small-btn) {
      height: 30px !important;
      padding: 0 10px !important;
      font-size: 12px !important;
    }
  }

  .workflow-process-graph {
    width: 100%;
    height: 500px;
    overflow: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .workflow-history {
    padding: 10px;

    .history-card {
      margin-bottom: 10px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 500;
      }

      .operation-info {
        display: flex;
        justify-content: space-between;
        color: #666;
        font-size: 13px;
      }

      .opinion-title {
        margin-top: 10px;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .opinion-content {
        color: #666;
        white-space: pre-wrap;
      }
    }
  }

  .empty-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #909399;
    font-size: 14px;
    background-color: #f8f8f9;
    border-radius: 4px;
  }
</style>
