<?php

use app\system\middleware\CheckLoginAttempts;
use think\facade\Route;

Route::group('api', function () {
	
	
	// 系统管理接口，需要Token验证
	Route::group('system', function () {
		
		// 系统-认证相关路由
		$nameSpace = '\app\system\controller';
		// 系统-认证相关路由
		Route::post('login', $nameSpace . '\AuthController@login')
		     ->middleware([CheckLoginAttempts::class]);;
		Route::get('captcha', $nameSpace . '\AuthController@captcha');
		
		Route::get('config', $nameSpace . '\ConfigController@index');
		
	});
	
});

