# 工作流统一对接开发指南

## 📋 文档概述

**文档版本：** v2.0  
**创建日期：** 2025-01-24  
**适用范围：** 所有需要集成工作流审批的业务模块  
**技术栈：** ThinkPHP8 + UnifiedWorkflowService + FormServiceInterface  

## 🏗️ 架构概览

### 统一工作流架构图

```mermaid
graph TD
    A[业务Controller] --> B[UnifiedWorkflowService]
    B --> C[DynamicWorkflowFactory]
    C --> D[FormServiceInterface实现]
    D --> E[业务Model]
    
    B --> F[WorkflowStatusSyncService]
    F --> G[工作流引擎]
    G --> H[审批回调]
    H --> D
    
    I[workflow_type配置表] --> C
```

### 核心组件说明

| 组件 | 职责 | 实现方式 |
|------|------|----------|
| **UnifiedWorkflowService** | 统一工作流操作入口 | submit/withdraw/void/terminate操作 |
| **DynamicWorkflowFactory** | 动态Service创建 | 基于workflow_type表映射 |
| **FormServiceInterface** | 统一业务表单接口 | 7个标准方法 |
| **WorkflowStatusSyncService** | 状态同步服务 | 统一的状态更新逻辑 |

## 🎯 两种对接模式

### 模式一：独立业务场景对接

**适用业务：** 合同、每日报价、回款等独立业务模块  
**特点：** 在各自的Controller/Trait中直接调用UnifiedWorkflowService

### 模式二：通用页面集成对接

**适用业务：** 请假等通用审批业务  
**特点：** 集成在ApplicationController通用页面内

---

## 🔧 模式一：独立业务场景对接

### 1. 基础配置

#### 1.1 workflow_type表配置
```sql
INSERT INTO workflow_type (name, module_code, business_code, status, created_id, updated_id) 
VALUES 
('合同审批', 'crm', 'crm_contract', 1, 1, 1),
('每日报价审批', 'daily', 'daily_price_order', 1, 1, 1),
('回款审批', 'crm', 'crm_contract_receivable', 1, 1, 1);
```

#### 1.2 业务表字段要求
```sql
-- 必需字段
approval_status INT DEFAULT 0 COMMENT '审批状态：0草稿 1审批中 2已通过 3已驳回 4已终止 5已撤回 6已作废',
workflow_instance_id INT DEFAULT NULL COMMENT '工作流实例ID',
submit_time DATETIME DEFAULT NULL COMMENT '提交时间',
approval_time DATETIME DEFAULT NULL COMMENT '审批完成时间',

-- 权限字段（可选）
creator_id INT DEFAULT 0 COMMENT '创建人ID',
tenant_id INT DEFAULT 0 COMMENT '租户ID',
deleted_at DATETIME DEFAULT NULL COMMENT '软删除时间'
```

### 2. FormService实现

#### 2.1 创建FormService类
```php
<?php
namespace app\crm\service;

use app\common\core\base\BaseService;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\traits\DefaultWorkflowCallbackTrait;
use app\crm\model\CrmContract;

class CrmContractService extends BaseService implements FormServiceInterface
{
    use DefaultWorkflowCallbackTrait;
    
    protected string $modelClass = CrmContract::class;
    
    /**
     * 获取表单数据
     */
    public function getFormData(int $id): array
    {
        $model = $this->getModel()->find($id);
        return $model ? $model->toArray() : [];
    }
    
    /**
     * 保存表单数据
     */
    public function saveForm(array $data): array
    {
        if (isset($data['id']) && $data['id'] > 0) {
            $model = $this->getModel()->find($data['id']);
            $result = $model->saveByUpdate($data);
            return [$data['id'], $model->toArray()];
        } else {
            $id = $this->getModel()->saveByCreate($data);
            $model = $this->getModel()->find($id);
            return [$id, $model->toArray()];
        }
    }
    
    /**
     * 更新表单状态
     */
    public function updateFormStatus(int $id, int $status): bool
    {
        $statusData = ['approval_status' => $status];
        
        // 根据状态添加时间字段
        if ($status === 1) { // 审批中
            $statusData['submit_time'] = date('Y-m-d H:i:s');
        } elseif (in_array($status, [2, 3, 4, 5, 6])) { // 已完成状态
            $statusData['approval_time'] = date('Y-m-d H:i:s');
        }
        
        return $this->getModel()->where('id', $id)->update($statusData) > 0;
    }
    
    /**
     * 获取实例标题
     */
    public function getInstanceTitle(array $formData): string
    {
        return "合同审批-{$formData['contract_name']}";
    }
    
    /**
     * 验证表单数据
     */
    public function validateFormData(array $data): bool
    {
        return !empty($data['contract_name']) && !empty($data['contract_amount']);
    }
    
    /**
     * 删除表单数据
     */
    public function deleteFormData(int $id): bool
    {
        return $this->getModel()->destroy($id);
    }
    
    /**
     * 工作流状态变更后处理
     */
    public function afterWorkflowStatusChange(int $id, int $status, array $context = []): bool
    {
        // 合同特定的业务逻辑
        if ($status === 2) { // 审批通过
            // 可以在这里添加合同通过后的业务逻辑
            // 例如：生成合同编号、发送通知等
        }
        
        return true;
    }
}
```

### 3. Controller/Trait集成

#### 3.1 在Controller或Trait中使用
```php
<?php
namespace app\crm\controller\traits;

use app\workflow\service\UnifiedWorkflowService;
use think\response\Json;

trait CustomerContractTrait
{
    /**
     * 提交合同审批
     */
    public function submitContractApproval(): Json
    {
        $data = $this->request->post();
        $contractId = $data['id'] ?? 0;
        
        if (!$contractId) {
            return $this->error('合同ID不能为空');
        }
        
        try {
            // 使用统一工作流服务提交审批
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
                'business_code' => 'crm_contract',
                'business_id' => $contractId,
                'operator_id' => get_user_id()
            ]);
            
            if ($result['success']) {
                return $this->success('合同已提交审批', $result);
            } else {
                return $this->error($result['message']);
            }
            
        } catch (\Exception $e) {
            return $this->error('提交审批失败：' . $e->getMessage());
        }
    }
    
    /**
     * 撤回合同审批
     */
    public function withdrawContractApproval(): Json
    {
        $data = $this->request->post();
        $contractId = $data['id'] ?? 0;
        
        if (!$contractId) {
            return $this->error('合同ID不能为空');
        }
        
        try {
            // 使用统一工作流服务撤回审批
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $result = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
                'business_code' => 'crm_contract',
                'business_id' => $contractId,
                'operator_id' => get_user_id(),
                'reason' => '用户撤回申请'
            ]);
            
            return $this->success($result['message'], $result);
            
        } catch (\Exception $e) {
            return $this->error('撤回失败：' . $e->getMessage());
        }
    }
    
    /**
     * 作废合同
     */
    public function voidContract(): Json
    {
        $data = $this->request->post();
        $contractId = $data['id'] ?? 0;
        $reason = $data['reason'] ?? '业务作废';
        
        if (!$contractId) {
            return $this->error('合同ID不能为空');
        }
        
        try {
            // 使用统一工作流服务作废
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $result = $unifiedWorkflowService->executeWorkflowOperation('void', [
                'business_code' => 'crm_contract',
                'business_id' => $contractId,
                'operator_id' => get_user_id(),
                'reason' => $reason
            ]);
            
            return $this->success($result['message'], $result);
            
        } catch (\Exception $e) {
            return $this->error('作废失败：' . $e->getMessage());
        }
    }
}
```

### 4. 业务特定配置

#### 4.1 每日报价单配置示例
```php
// business_code: 'daily_price_order'
// 特殊验证逻辑
public function validateFormData(array $data): bool
{
    // 验证报价日期不能重复
    $exists = DailyPriceOrder::where('price_date', $data['price_date'])
        ->where('approval_status', 2)
        ->where('id', '<>', $data['id'] ?? 0)
        ->count();
        
    return $exists === 0;
}

// 特殊标题格式
public function getInstanceTitle(array $formData): string
{
    return "每日报价审批-{$formData['price_date']}";
}
```

#### 4.2 回款配置示例
```php
// business_code: 'crm_contract_receivable'
// 特殊验证逻辑
public function validateFormData(array $data): bool
{
    return !empty($data['contract_id']) && $data['receivable_amount'] > 0;
}

// 特殊标题格式
public function getInstanceTitle(array $formData): string
{
    return "回款审批-{$formData['receivable_amount']}元";
}
```

---

## 🔧 模式二：通用页面集成对接

### 1. ApplicationController集成

#### 1.1 请假业务配置
```sql
INSERT INTO workflow_type (name, module_code, business_code, status, created_id, updated_id) 
VALUES ('请假审批', 'hr', 'hr_leave', 1, 1, 1);
```

#### 1.2 FormService实现
```php
<?php
namespace app\hr\service;

use app\common\core\base\BaseService;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\traits\DefaultWorkflowCallbackTrait;
use app\hr\model\HrLeave;

class HrLeaveService extends BaseService implements FormServiceInterface
{
    use DefaultWorkflowCallbackTrait;
    
    protected string $modelClass = HrLeave::class;
    
    // 实现FormServiceInterface的7个方法
    // ... (与模式一相同的实现)
    
    /**
     * 获取实例标题
     */
    public function getInstanceTitle(array $formData): string
    {
        return "请假申请-{$formData['leave_type_name']}-{$formData['start_date']}至{$formData['end_date']}";
    }
}
```

#### 1.3 ApplicationController中的处理
```php
<?php
namespace app\workflow\controller;

class ApplicationController extends BaseController
{
    /**
     * 通用提交审批
     */
    public function submit(): Json
    {
        $data = $this->request->post();
        $businessCode = $data['business_code'] ?? '';
        $businessId = $data['business_id'] ?? 0;
        
        if (!$businessCode || !$businessId) {
            return $this->error('参数不完整');
        }
        
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
                'business_code' => $businessCode,
                'business_id' => $businessId,
                'operator_id' => get_user_id(),
                'title' => $data['title'] ?? null
            ]);
            
            return $this->success($result['message'], $result);
            
        } catch (\Exception $e) {
            return $this->error('提交失败：' . $e->getMessage());
        }
    }
    
    /**
     * 通用撤回审批
     */
    public function withdraw(): Json
    {
        $data = $this->request->post();
        $businessCode = $data['business_code'] ?? '';
        $businessId = $data['business_id'] ?? 0;
        
        try {
            $unifiedWorkflowService = new UnifiedWorkflowService();
            $result = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
                'business_code' => $businessCode,
                'business_id' => $businessId,
                'operator_id' => get_user_id(),
                'reason' => $data['reason'] ?? '用户撤回申请'
            ]);
            
            return $this->success($result['message'], $result);
            
        } catch (\Exception $e) {
            return $this->error('撤回失败：' . $e->getMessage());
        }
    }
}
```

---

## 📊 状态常量说明

### 工作流状态定义
```php
// app/workflow/constants/WorkflowStatusConstant.php
class WorkflowStatusConstant
{
    const STATUS_DRAFT = 0;        // 草稿
    const STATUS_PROCESSING = 1;   // 审批中
    const STATUS_COMPLETED = 2;    // 已通过
    const STATUS_REJECTED = 3;     // 已驳回
    const STATUS_TERMINATED = 4;   // 已终止
    const STATUS_RECALLED = 5;     // 已撤回
    const STATUS_VOID = 6;         // 已作废
}
```

### 状态流转规则
```
草稿(0) → 审批中(1) → 已通过(2)
                  ↓
                已驳回(3)
                  ↓
                已终止(4)

审批中(1) → 已撤回(5)
已通过(2) → 已作废(6)
```

---

## 🧪 测试验证

### 1. 功能测试命令
```bash
# 测试所有工作流功能
php think test:phase3-complete

# 测试WorkflowableService迁移
php think test:workflowable-service-migration
```

### 2. 手动测试步骤

#### 2.1 独立业务场景测试
1. 创建合同/报价单/回款记录
2. 提交审批 → 验证状态变为"审批中"
3. 撤回审批 → 验证状态变为"已撤回"
4. 重新提交 → 审批通过 → 验证状态变为"已通过"
5. 作废记录 → 验证状态变为"已作废"

#### 2.2 通用页面集成测试
1. 在ApplicationController页面创建请假申请
2. 提交审批 → 验证工作流启动
3. 在审批页面进行审批操作
4. 验证状态同步和回调处理

---

## ⚠️ 注意事项

### 1. 开发规范
- ✅ 所有业务Service必须实现FormServiceInterface的7个方法
- ✅ 使用统一的状态常量WorkflowStatusConstant
- ✅ 业务表必须包含标准的工作流字段
- ✅ 统一使用UnifiedWorkflowService进行工作流操作

### 2. 错误处理
- 使用BusinessException抛出业务异常
- 记录详细的操作日志
- 提供友好的错误提示信息

### 3. 性能优化
- 使用事务保证数据一致性
- 合理使用缓存机制
- 避免重复的数据库查询

### 4. 安全考虑
- 验证用户权限
- 防止状态篡改
- 记录操作审计日志

---

## 📚 相关文档

- [FormServiceInterface统一化实施完成报告](./FormServiceInterface统一化实施完成报告.md)
- [工作流业务对接统一技术文档](./工作流业务对接统一技术文档.md)
- [统一工作流对接详细技术文档](./统一工作流对接详细技术文档.md)

## 🔧 高级配置

### 1. 自定义工作流定义ID

```php
// 在UnifiedWorkflowService中会自动获取默认定义ID
// 如需指定特定定义ID，可在参数中传递
$result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
    'business_code' => 'crm_contract',
    'business_id' => $contractId,
    'operator_id' => get_user_id(),
    'definition_id' => 2  // 指定特定的工作流定义
]);
```

### 2. 复杂业务验证

```php
// 在FormService中实现复杂验证逻辑
public function validateFormData(array $data): bool
{
    // 基础验证
    if (empty($data['contract_name']) || $data['contract_amount'] <= 0) {
        return false;
    }

    // 业务规则验证
    if ($data['contract_amount'] > 1000000) {
        // 大额合同需要额外审批
        $this->setRequiredApprovalLevel('high');
    }

    // 重复性检查
    $exists = $this->getModel()
        ->where('contract_number', $data['contract_number'])
        ->where('id', '<>', $data['id'] ?? 0)
        ->count();

    return $exists === 0;
}
```

### 3. 批量操作支持

```php
/**
 * 批量提交审批
 */
public function batchSubmitApproval(array $ids, string $businessCode): array
{
    $results = [];
    $unifiedWorkflowService = new UnifiedWorkflowService();

    foreach ($ids as $id) {
        try {
            $result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
                'business_code' => $businessCode,
                'business_id' => $id,
                'operator_id' => get_user_id()
            ]);
            $results[$id] = $result;
        } catch (\Exception $e) {
            $results[$id] = ['success' => false, 'message' => $e->getMessage()];
        }
    }

    return $results;
}
```

---

## 📋 快速参考

### 业务代码映射表

| 业务模块 | business_code | module_code | Service类 |
|---------|---------------|-------------|-----------|
| CRM合同 | crm_contract | crm | CrmContractService |
| CRM回款 | crm_contract_receivable | crm | CrmContractReceivableService |
| 每日报价 | daily_price_order | daily | DailyPriceOrderService |
| HR请假 | hr_leave | hr | HrLeaveService |

### 常用操作代码片段

#### 提交审批
```php
$unifiedWorkflowService = new UnifiedWorkflowService();
$result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
    'business_code' => 'crm_contract',
    'business_id' => $contractId,
    'operator_id' => get_user_id()
]);
```

#### 撤回审批
```php
$result = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
    'business_code' => 'crm_contract',
    'business_id' => $contractId,
    'operator_id' => get_user_id(),
    'reason' => '用户撤回申请'
]);
```

#### 作废记录
```php
$result = $unifiedWorkflowService->executeWorkflowOperation('void', [
    'business_code' => 'crm_contract',
    'business_id' => $contractId,
    'operator_id' => get_user_id(),
    'reason' => '业务作废'
]);
```

### 状态检查代码片段

```php
// 检查是否可以提交审批
if ($record->approval_status === 0) {
    // 可以提交
}

// 检查是否可以撤回
if ($record->approval_status === 1 && !empty($record->workflow_instance_id)) {
    // 可以撤回
}

// 检查是否可以作废
if (in_array($record->approval_status, [1, 2]) && !empty($record->workflow_instance_id)) {
    // 可以作废
}
```

---

## 🐛 常见问题排查

### 1. FormService创建失败
**错误信息**: "不支持的业务类型: xxx"
**解决方案**:
1. 检查workflow_type表是否有对应配置
2. 检查Service类是否存在且实现了FormServiceInterface
3. 检查DynamicWorkflowFactory的映射配置

### 2. 状态更新失败
**错误信息**: "FormService状态更新失败"
**解决方案**:
1. 检查业务表是否有approval_status字段
2. 检查updateFormStatus方法实现
3. 检查数据库连接和权限

### 3. 工作流实例创建失败
**错误信息**: "definition_id参数问题"
**解决方案**:
1. 检查workflow_definition表是否有可用定义
2. 检查workflow_type表的definition_id配置
3. 使用getDefaultDefinitionId方法获取默认定义

### 4. 权限验证失败
**错误信息**: "当前状态不允许此操作"
**解决方案**:
1. 检查业务记录的approval_status
2. 检查workflow_instance_id是否存在
3. 验证操作权限和业务规则

---

## 📈 性能优化建议

### 1. 数据库优化
```sql
-- 为工作流相关字段添加索引
ALTER TABLE crm_contract ADD INDEX idx_approval_status (approval_status);
ALTER TABLE crm_contract ADD INDEX idx_workflow_instance (workflow_instance_id);
ALTER TABLE crm_contract ADD INDEX idx_submit_time (submit_time);
```

### 2. 缓存策略
```php
// 缓存workflow_type配置
$workflowTypes = Cache::remember('workflow_types', 3600, function() {
    return WorkflowType::where('status', 1)->select();
});
```

### 3. 批量处理
```php
// 使用事务处理批量操作
Db::startTrans();
try {
    foreach ($operations as $operation) {
        $unifiedWorkflowService->executeWorkflowOperation($operation['type'], $operation['params']);
    }
    Db::commit();
} catch (\Exception $e) {
    Db::rollback();
    throw $e;
}
```

---

**文档维护人员**: 开发团队
**最后更新时间**: 2025-01-24
**文档状态**: 正式版本
