<!-- ArtTableHeader 测试组件 - 用于验证空数组时的隐藏逻辑 -->
<template>
  <div class="test-container">
    <h3>ArtTableHeader 列设置按钮测试</h3>
    
    <div class="test-case">
      <h4>测试用例1: 空数组 columns (应该隐藏列设置按钮)</h4>
      <ArtTableHeader 
        v-model:columns="emptyColumns"
        @refresh="handleRefresh"
      >
        <template #left>
          <ElButton type="primary">测试按钮</ElButton>
        </template>
      </ArtTableHeader>
      <p>当前 columns 长度: {{ emptyColumns.length }}</p>
    </div>

    <div class="test-case">
      <h4>测试用例2: 有数据的 columns (应该显示列设置按钮)</h4>
      <ArtTableHeader 
        v-model:columns="normalColumns"
        @refresh="handleRefresh"
      >
        <template #left>
          <ElButton type="primary">测试按钮</ElButton>
        </template>
      </ArtTableHeader>
      <p>当前 columns 长度: {{ normalColumns.length }}</p>
    </div>

    <div class="test-case">
      <h4>测试用例3: 未传入 columns (应该隐藏列设置按钮)</h4>
      <ArtTableHeader @refresh="handleRefresh">
        <template #left>
          <ElButton type="primary">测试按钮</ElButton>
        </template>
      </ArtTableHeader>
    </div>

    <div class="controls">
      <ElButton @click="toggleColumns">切换 columns 状态</ElButton>
      <ElButton @click="addColumn">添加列</ElButton>
      <ElButton @click="clearColumns">清空列</ElButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElButton } from 'element-plus'
import ArtTableHeader from './ArtTableHeader.vue'

interface ColumnOption {
  label?: string
  prop?: string
  type?: string
  width?: string | number
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  filters?: any[]
  filterMethod?: (value: any, row: any) => boolean
  filterPlacement?: string
  disabled?: boolean
  checked?: boolean
}

// 空数组测试
const emptyColumns = ref<ColumnOption[]>([])

// 正常数据测试
const normalColumns = ref<ColumnOption[]>([
  {
    prop: 'id',
    label: 'ID',
    width: 80,
    checked: true
  },
  {
    prop: 'name',
    label: '名称',
    checked: true
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    checked: true
  }
])

const handleRefresh = () => {
  console.log('刷新按钮被点击')
}

const toggleColumns = () => {
  if (emptyColumns.value.length === 0) {
    emptyColumns.value = [
      {
        prop: 'test',
        label: '测试列',
        checked: true
      }
    ]
  } else {
    emptyColumns.value = []
  }
}

const addColumn = () => {
  normalColumns.value.push({
    prop: `col_${Date.now()}`,
    label: `新列 ${normalColumns.value.length + 1}`,
    checked: true
  })
}

const clearColumns = () => {
  normalColumns.value = []
}
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h3 {
    color: #333;
    margin-bottom: 20px;
  }

  .test-case {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fafafa;

    h4 {
      color: #606266;
      margin-bottom: 15px;
      font-size: 14px;
    }

    p {
      margin-top: 10px;
      font-size: 12px;
      color: #909399;
    }
  }

  .controls {
    margin-top: 20px;
    padding: 15px;
    background-color: #f0f9ff;
    border-radius: 8px;

    .el-button {
      margin-right: 10px;
    }
  }
}
</style>
