# Workflow模块消息中心使用指南

## 📋 概述

本指南详细说明如何在Workflow模块中正确使用消息中心功能，包括发送各种类型的工作流通知。

## 🚀 快速开始

### 基本用法
```php
use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;

// 发送审批通知
$result = NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,           // 'workflow'
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, // 'task_approval'
    $variables,                                    // 变量数据
    [$userId]                                      // 接收人ID数组
);
```

## 📊 支持的消息类型

### 1. 审批通知 (task_approval)
**用途**: 发送给审批人，通知有新的审批任务
**触发时机**: 工作流流转到审批节点时

```php
// 在 ApprovalNodeHandler.php 中使用
$variables = [
    'task_name'      => $node['nodeName'] ?? '审批任务',
    'title'          => $instance['title'] ?? '未命名流程',
    'submitter_name' => $instance['submitter_name'] ?? '系统',
    'created_at'     => $instance['created_at'] ?? date('Y-m-d H:i:s'),
    'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL,
    $variables,
    [$approverId]
);
```

### 2. 审批结果通知 (task_approved)
**用途**: 发送给提交人，通知审批结果
**触发时机**: 审批任务完成时

```php
// 在 WorkflowTaskService.php 中使用
$variables = [
    'title'         => $instance['title'],
    'result'        => $isApproved ? '通过' : '拒绝',
    'opinion'       => $task['opinion'] ?? '',
    'approver_name' => $currentUserName,
    'completed_at'  => $task['handle_time'] ?? date('Y-m-d H:i:s')
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_APPROVED,
    $variables,
    [$submitterId]
);
```

### 3. 抄送通知 (task_cc)
**用途**: 发送给抄送人，通知流程进展
**触发时机**: 工作流流转到抄送节点时

```php
// 在 WorkflowInstanceService.php 中使用
$data = [
    'title'          => $instance['title'],
    'submitter_name' => $instance['submitter_name'],
    'node_name'      => '抄送',
    'cc_time'        => date('Y-m-d H:i:s'),
    'detail_url'     => '/workflow/detail?id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_CC,
    $data,
    [$userId]
);
```

### 4. 催办通知 (task_urge)
**用途**: 发送给审批人，催促处理超时任务
**触发时机**: 手动催办或系统自动催办时

```php
// 在 WorkflowTaskService.php 中使用
$variables = [
    'title'      => $instance['title'],
    'task_name'  => $task['node_name'],
    'urger_name' => $urgerName,
    'created_at' => date('Y-m-d H:i:s'),
    'reason'     => $urgeReason
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_URGE,
    $variables,
    [$approverId]
);
```

### 5. 转交通知 (task_transfer)
**用途**: 发送给接收人，通知任务已转交
**触发时机**: 审批任务转交时

```php
// 在 WorkflowTaskService.php 中使用
$variables = [
    'title'         => $instance['title'],
    'node_name'     => $task['node_name'],
    'from_user'     => AdminModel::where('id', $fromUserId)->value('realname'),
    'to_user'       => $toUser['realname'],
    'transfer_time' => date('Y-m-d H:i:s'),
    'detail_url'    => '/workflow/task/detail?id=' . $task['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_TRANSFER,
    $variables,
    [$toUserId]
);
```

### 6. 终止通知 (task_terminated)
**用途**: 发送给提交人，通知流程已终止
**触发时机**: 工作流被终止时

```php
// 在 WorkflowEngine.php 中使用
$variables = [
    'title'          => $instance['title'],
    'result'         => '已终止',
    'submit_time'    => $instance['created_at'],
    'terminate_time' => date('Y-m-d H:i:s'),
    'terminate_by'   => $operatorName,
    'reason'         => $reason,
    'detail_url'     => '/workflow/detail?id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_TERMINATED,
    $variables,
    [$submitterId]
);
```

### 7. 作废通知 (task_void) 🆕
**用途**: 发送给提交人，通知流程已作废
**触发时机**: 工作流被作废时

```php
// 在 WorkflowEngine.php 中使用
$variables = [
    'title'       => $instance['title'],
    'result'      => '已作废',
    'submit_time' => $instance['created_at'],
    'void_time'   => date('Y-m-d H:i:s'),
    'void_by'     => $operatorName,
    'reason'      => $reason,
    'detail_url'  => '/workflow/detail?id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_VOID,
    $variables,
    [$submitterId]
);
```

## 🔧 变量说明

### 通用变量
| 变量名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | ✅ | 工作流程标题 |
| detail_url | string | ❌ | 详情页链接 |

### 审批相关变量
| 变量名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| task_name | string | ✅ | 审批任务名称 |
| submitter_name | string | ✅ | 流程提交人姓名 |
| created_at | datetime | ✅ | 提交时间 |
| approver_name | string | ✅ | 审批人姓名 |
| result | string | ✅ | 审批结果(通过/拒绝) |
| completed_at | datetime | ✅ | 审批完成时间 |
| opinion | string | ❌ | 审批意见 |

### 操作相关变量
| 变量名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| node_name | string | ✅ | 当前节点名称 |
| urger_name | string | ✅ | 催办人姓名 |
| from_user | string | ✅ | 转交人姓名 |
| to_user | string | ✅ | 接收人姓名 |
| transfer_time | datetime | ✅ | 转交时间 |
| reason | string | ❌ | 操作原因 |

### 状态变更变量
| 变量名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| submit_time | datetime | ✅ | 流程提交时间 |
| terminate_time | datetime | ✅ | 终止时间 |
| terminate_by | string | ✅ | 终止人姓名 |
| void_time | datetime | ✅ | 作废时间 |
| void_by | string | ✅ | 作废人姓名 |

## 🆕 新增作废功能

### 作废工作流实例
```php
use app\workflow\service\WorkflowEngine;

// 作废工作流实例
$result = WorkflowEngine::getInstance()->voidInstance(
    $instanceId,    // 实例ID
    $reason,        // 作废原因
    $operatorId     // 操作人ID
);

if ($result) {
    echo "作废成功";
} else {
    echo "作废失败";
}
```

### 作废功能特点
1. **状态管理**: 实例状态更新为 `WorkflowStatusConstant::STATUS_VOID`
2. **任务处理**: 自动作废所有待处理的任务
3. **通知机制**: 自动发送作废通知给提交人
4. **操作记录**: 记录作废人和作废原因

## 🚨 注意事项

### 1. 变量命名规范
- **代码传入**: 必须使用英文键名（如 `title`、`task_name`）
- **模板内容**: 使用中文变量名（如 `${流程标题}`、`${任务名称}`）

### 2. 错误处理
```php
try {
    $result = NoticeDispatcherService::getInstance()->send(
        'workflow',
        'task_approval',
        $variables,
        $recipients
    );
    
    if (!$result) {
        Log::error('消息发送失败');
        // 处理失败情况，但不影响主业务流程
    }
} catch (\Exception $e) {
    Log::error('消息发送异常: ' . $e->getMessage());
    // 异常处理，确保主业务流程继续
}
```

### 3. 数据格式
- **时间字段**: 统一使用 `Y-m-d H:i:s` 格式
- **布尔值**: 转换为中文描述（如：通过/拒绝）
- **必填字段**: 确保所有必填变量都有值

## 🔍 调试指南

### 检查消息发送状态
```sql
-- 查看最近的workflow消息
SELECT id, title, status, created_at 
FROM notice_message 
WHERE module_code = 'workflow' 
ORDER BY created_at DESC LIMIT 10;
```

### 检查模板配置
```sql
-- 查看模板变量配置
SELECT code, name, variables_config 
FROM notice_template 
WHERE module_code = 'workflow';
```

### 常见问题排查
1. **消息发送失败**: 检查必填变量是否都有值
2. **变量未替换**: 检查变量键名是否为英文
3. **模板不存在**: 确认模板已创建且状态为启用

## 📈 性能优化

### 批量发送
```php
// 批量发送给多个用户
$recipients = [1, 2, 3, 4, 5];
NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    $variables,
    $recipients  // 一次发送给多个用户
);
```

### 异步处理
消息发送已集成队列机制，支持异步处理，不会阻塞主业务流程。

---

**版本**: 1.0  
**更新时间**: 2025-07-16  
**维护人**: Augment Agent
