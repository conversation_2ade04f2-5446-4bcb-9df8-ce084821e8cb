@echo off
echo 清除 Vite 缓存和 node_modules 缓存...

echo 1. 删除 node_modules/.vite 目录...
if exist "node_modules\.vite" (
    rmdir /s /q "node_modules\.vite"
    echo    ✓ 已删除 node_modules/.vite
) else (
    echo    ℹ node_modules/.vite 目录不存在
)

echo 2. 删除 dist 目录...
if exist "dist" (
    rmdir /s /q "dist"
    echo    ✓ 已删除 dist
) else (
    echo    ℹ dist 目录不存在
)

echo 3. 清除 npm 缓存...
npm cache clean --force
echo    ✓ npm 缓存已清除

echo 4. 重新安装依赖...
npm install
echo    ✓ 依赖重新安装完成

echo.
echo 缓存清除完成！现在可以重新启动开发服务器：
echo npm run dev
echo.
pause
