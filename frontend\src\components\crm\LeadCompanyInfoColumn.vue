<template>
  <div class="lead-company-info">
    <!-- 第一行：公司名称 -->
    <div class="company-name" :title="data.company">
      <ElIcon class="company-icon">
        <OfficeBuilding />
      </ElIcon>
      <span class="company-text">{{ data.company || '-' }}</span>
    </div>

    <!-- 第二行：标签组 -->
    <div class="company-tags" v-if="hasCompanyTags">
      <ElTag
        v-if="data.industry"
        type="info"
        size="small"
        class="tag-item"
        :title="`所属行业: ${data.industry}`"
      >
        <ElIcon class="tag-icon">
          <Shop />
        </ElIcon>
        {{ formatText(data.industry, 8) }}
      </ElTag>

      <ElTag
        v-if="data.source"
        type="primary"
        size="small"
        class="tag-item"
        :title="`线索来源: ${data.source}`"
      >
        <ElIcon class="tag-icon">
          <Link />
        </ElIcon>
        {{ formatText(data.source, 8) }}
      </ElTag>
    </div>

    <!-- 第三行：地址 (可选) -->
    <div class="company-address" v-if="data.address">
      <ElIcon class="address-icon">
        <Location />
      </ElIcon>
      <span class="address-text" :title="data.address">
        {{ formatText(data.address, 18) }}
      </span>
    </div>

    <!-- 第四行：备注 (可选) -->
    <div class="company-remark" v-if="data.remark">
      <ElIcon class="remark-icon">
        <Document />
      </ElIcon>
      <span class="remark-text" :title="data.remark">
        {{ formatText(data.remark, 20) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { ElIcon, ElTag } from 'element-plus'
  import { OfficeBuilding, Shop, Link, Location, Document } from '@element-plus/icons-vue'

  interface CompanyData {
    company?: string
    industry?: string
    source?: string
    address?: string
    remark?: string
  }

  interface Props {
    data: CompanyData
  }

  const props = defineProps<Props>()

  // 检查是否有公司标签
  const hasCompanyTags = computed(() => {
    return props.data.industry || props.data.source
  })

  // 格式化文本显示
  const formatText = (text: string, maxLength: number): string => {
    if (!text) return '-'
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }
</script>

<style scoped lang="scss">
  .lead-company-info {
    padding: 8px 0;
    font-size: 15px;

    .company-name {
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
      font-size: 15px;
      line-height: 1.4;
      word-break: break-word;
      display: flex;
      align-items: flex-start;

      .company-icon {
        margin-right: 4px;
        margin-top: 1px;
        font-size: 14px;
        color: #409eff;
        flex-shrink: 0;
      }

      .company-text {
        flex: 1;
        // 处理长公司名称
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .company-tags {
      margin-bottom: 6px;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .tag-item {
        display: flex;
        align-items: center;
        max-width: 100px;

        .tag-icon {
          margin-right: 2px;
          font-size: 10px;
        }
      }
    }

    .company-address,
    .company-remark {
      display: flex;
      align-items: flex-start;
      font-size: 13px;
      color: #909399;
      margin-top: 8px;
      margin-bottom: 3px;
      line-height: 1.3;

      .address-icon,
      .remark-icon {
        margin-right: 4px;
        font-size: 12px;
        margin-top: 1px;
        flex-shrink: 0;
      }

      .address-text,
      .remark-text {
        flex: 1;
        word-break: break-word;
      }
    }

    .company-remark {
      color: #303133;
      font-style: italic;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .lead-company-info {
      .company-name {
        font-size: 13px;
      }

      .company-tags {
        .tag-item {
          max-width: 80px;
          font-size: 11px;
        }
      }

      .company-address,
      .company-remark {
        font-size: 11px;
      }
    }
  }

  // 深色主题适配
  @media (prefers-color-scheme: dark) {
    .lead-company-info {
      .company-name {
        color: #e5eaf3;
      }
    }
  }
</style>
