<?php
declare(strict_types=1);

namespace app\workflow\service;

use app\common\core\base\BaseService;
use app\common\core\crud\traits\CrudServiceTrait;
use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\factory\DynamicWorkflowFactory;
use app\workflow\model\WorkflowInstance;
use app\common\exception\BusinessException;
use think\facade\Db;
use think\facade\Log;
use think\Validate;

// WorkflowOperationConstant已合并到WorkflowStatusConstant

/**
 * 工作流实例服务类
 */
class WorkflowInstanceService extends BaseService
{
	use CrudServiceTrait;
	
	/**`
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new WorkflowInstance();
		parent::__construct();
	}
	
	/**
	 * 获取工作流实例数据并校验
	 *
	 * @param array $params
	 * @return array
	 * @throws \Exception
	 */
	protected function getInstanceData(array $params): array
	{
		// 使用ThinkPHP验证类进行校验
		$validate = new Validate();
		$validate->rule([
			'process_id'   => 'max:100',
			'admin_id'     => 'require|egt:0',
			'submitter_id' => 'require|gt:0',
		]);
		
		$validate->message([
			'process_id.max'       => '流程ID最大长度为100个字符',
			'admin_id.require'     => '管理员不能为空',
			'admin_id.egt'         => '管理员参数必须大于等于0',
			'submitter_id.require' => '提交人不能为空',
			'submitter_id.gt'      => '提交人参数必须大于0',
		]);
		
		// 执行验证
		if (!$validate->check($params)) {
			throw new BusinessException($validate->getError());
		}
		
		return [
			'process_id'        => $params['process_id'],
			'admin_id'          => (int)$params['admin_id'],
			'submitter_id'      => (int)$params['submitter_id'],
			'submitter_dept_id' => (int)$params['submitter_dept_id'],
		];
	}
	
	/**
	 * 处理工作流申请（新增或编辑）
	 *
	 * @param array $params 请求参数
	 * @param int   $status 申请状态（草稿或提交）
	 * @return array 结果
	 * @throws \Exception
	 */
	protected function processApplication(array $params, int $status): array
	{
		// 参数预处理
		$params = $this->prepareParams($params, $status);
		
		// 判断是新增还是编辑
		$id     = intval($params['id'] ?? 0);
		$isEdit = $id > 0;
		
		if ($isEdit) {
			// 处理编辑申请
			return $this->updateExistingApplication($id, $params, $status);
		}
		else {
			// 处理新增申请
			return $this->createNewApplication($params, $status);
		}
	}
	
	/**
	 * 更新现有申请
	 *
	 * @param int   $id     申请ID
	 * @param array $params 请求参数
	 * @param int   $status 申请状态
	 * @return array 结果
	 * @throws \Exception
	 */
	protected function updateExistingApplication(int $id, array $params, int $status): array
	{
		Db::startTrans();
		try {
			// 获取实例信息
			$instanceInfo = $this->getCrudService()
			                     ->getOne([
				                     'id' => $id,
			                     ]);
			if ($instanceInfo->isEmpty()) {
				throw new BusinessException('当前申请不存在');
			}
			
			// 检查状态，只有草稿状态才能更新
			if ($status == WorkflowStatusConstant::APPROVING && $instanceInfo->getData('status') != WorkflowStatusConstant::SAVED) {
				throw new BusinessException('当前申请已提交');
			}
			
			
			// 更新表单状态 - 使用动态工厂
			$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instanceInfo->business_code);
			if (!$formService) {
				throw new BusinessException("不支持的业务类型：{$instanceInfo->business_code}");
			}
			
			$formData                    = $params['business_data'];
			$formData['approval_status'] = $status;
			$business_id                 = intval($instanceInfo->getData('business_id'));
			$res                         = $formService->updateForm($business_id, $formData);
			
			if (!$res) {
				throw new BusinessException('更新表单失败');
			}
			
			$formAllData = $formService->getFormData($business_id);
			// 更新状态
			$updateData = [
				'title'     => $formService->getInstanceTitle($formAllData),
				'status'    => $status,
				'form_data' => $formAllData
			];
			if ($status == WorkflowStatusConstant::APPROVING) {
				$updateData['start_time'] = date('Y-m-d H:i:s');
			}
			
			$res = $instanceInfo->save($updateData);
			
			if (!$res) {
				throw new BusinessException('更新申请状态失败');
			}
			
			Db::commit();
			
			return [
				'instance_id' => $instanceInfo->id,
				'business_id' => $instanceInfo->business_id
			];
		}
		catch (BusinessException $e) {
			Db::rollback();
			throw new BusinessException($e->getMessage());
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new \Exception($status == WorkflowStatusConstant::SAVED
				? '保存失败'
				: '提交失败');
		}
	}
	
	/**
	 * 创建新申请
	 *
	 * @param array $params 请求参数 todo 需完善参数数据结构
	 * @param int   $status 申请状态
	 * @return array 结果
	 * @throws \Exception
	 */
	protected function createNewApplication(array $params, int $status): array
	{
		try {
			// 获取业务表单类型
			$businessCode = $params['business_code'] ?? '';
			if (empty($businessCode)) {
				throw new BusinessException('业务类型不能为空');
			}
			
			// 查询流程定义是否存在
			$workflowDefinitionService = WorkflowDefinitionService::getInstance();
			$definitionInfo            = $workflowDefinitionService->getCrudService()
			                                                       ->getOne([
				                                                       'id' => $params['definition_id'],
			                                                       ], [], false);
			if ($definitionInfo->isEmpty()) {
				throw new BusinessException('当前流程不存在');
			}
			if ($definitionInfo->status != 1) {
				throw new BusinessException('当前流程已停用');
			}
			
			// 获取表单服务适配器 - 使用动态工厂
			$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
			if (!$formService) {
				throw new BusinessException("不支持的业务类型：{$businessCode}");
			}
			
			$process_id           = $this->generateProcessId($businessCode);
			$params['process_id'] = $process_id;
			[
				$businessId,
				$formData
			] = $formService->saveForm($params);
			
			if (empty($businessId)) {
				throw new BusinessException('保存表单数据失败');
			}
			
			// 构建实例数据
			$instanceData                  = $this->getInstanceData($params);
			$instanceData['process_data']  = $definitionInfo->flow_config;
			$instanceData['process_id']    = $process_id;
			$instanceData['business_code'] = $businessCode;
			$instanceData['business_id']   = $businessId;
			$instanceData['status']        = $status;
			$instanceData['definition_id'] = $definitionInfo->id;
			$instanceData['type_id']       = $definitionInfo->type_id;
			$instanceData['form_data']     = $formData;
			$instanceData['title']         = $formService->getInstanceTitle($formData);
			$instanceData['cc_users']      = $workflowDefinitionService->getCcUsers($definitionInfo->flow_config);
			
			// 如果是提交状态，设置开始时间
			if ($status == WorkflowStatusConstant::APPROVING) {
				$instanceData['start_time'] = date('Y-m-d H:i:s');
			}
			
			$instanceId = $this->crudService->add($instanceData);
			
			// 更新业务表单的工作流实例ID
			$formService->edit(['workflow_instance_id' => $instanceId], ['id' => $businessId]);
			
			// 记录流程历史
			WorkflowHistoryService::getInstance()
			                      ->getCrudService()
			                      ->add([
				                      'instance_id'    => $instanceId,
				                      'process_id'     => $process_id,
				                      'task_id'        => '',
				                      'node_id'        => '',
				                      'node_name'      => '提交申请',
				                      'node_type'      => 'start',
				                      'prev_node_id'   => '',
				                      'operator_id'    => $params['submitter_id'],
				                      'operation'      => WorkflowStatusConstant::OPERATION_PROCESS_START,
				                      'operation_time' => date('Y-m-d H:i:s')
			                      ]);
			
			return [
				'instance_id' => $instanceId,
				'business_id' => $businessId
			];
		}
		catch (BusinessException $e) {
			throw new BusinessException($e->getMessage());
		}
		catch (\Exception $e) {
			throw new \Exception($status == WorkflowStatusConstant::SAVED
				? '保存失败'
				: '提交失败');
		}
	}
	
	/**
	 * 保存申请草稿
	 *
	 * @param array $params 请求参数
	 * @return array 结果
	 * @throws \Exception
	 */
	public function saveApplicationDraft(array $params): array
	{
		return $this->processApplication($params, WorkflowStatusConstant::SAVED);
	}
	
	/**
	 * 提交工作流申请
	 *
	 * @param array $params 请求参数
	 * @return array 结果
	 */
	public function submitApplication(array $params): array
	{
		$result = $this->processApplication($params, WorkflowStatusConstant::APPROVING);
		
		// 获取新创建的工作流实例
		if (!empty($result['instance_id'])) {
			
			try {
				$instance = $this->crudService->getDetail($result['instance_id'], ['submitter']);
				
				$workflowEngineService = new WorkflowEngineService();
				
				// 调用工作流引擎启动流程
				$engineResult = $workflowEngineService->startWorkflow($instance->toArray());
				
			}
			catch (\Exception $e) {
				Log::error('❌ 工作流引擎启动过程异常: ' . $e->getMessage());
				Log::error('异常堆栈: ' . $e->getTraceAsString());
			}
		}
		else {
			Log::error('❌ 创建实例失败，无法处理流程节点');
		}
		
		Log::info('🏁 WorkflowInstanceService::submitApplication 处理完成');
		return $result;
	}
	
	/**
	 * 处理工作流中的审批节点
	 *
	 * @param array $workflowInstance 工作流实例
	 * @param array $flowConfig       流程配置
	 * @return void
	 */
	protected function processApprovalNodes(array $workflowInstance, array $flowConfig): void
	{
		// 解析流程配置
		$nodeConfig = $flowConfig['nodeConfig'] ?? [];
		Log::info('处理审批节点配置：' . json_encode($nodeConfig));
		
		if (empty($nodeConfig)) {
			Log::error('节点配置为空');
			return;
		}
		
		// 递归查找审批节点并创建审批任务
		$this->createApprovalTasksFromNode($workflowInstance, $nodeConfig);
	}
	
	/**
	 * 递归处理节点树，找出首个审批节点
	 *
	 * @param array  $instance     工作流实例
	 * @param array  $node         当前节点
	 * @param string $parentNodeId 父节点ID
	 * @return void
	 */
	protected function createApprovalTasksFromNode(array $instance, array $node, string $parentNodeId = ''): void
	{
		// 如果当前节点是审批节点(type=1)
		if (isset($node['type']) && ($node['type'] === '1' || $node['type'] === 1)) {
			Log::info('找到审批节点: ' . json_encode($node));
			// 创建审批任务
			$this->createApprovalTasks($instance, $node, $parentNodeId);
			// 仅处理第一个审批节点，后续节点由审批流程触发
			return;
		}
		
		// 递归处理子节点
		if (!empty($node['childNode'])) {
			$this->createApprovalTasksFromNode($instance, $node['childNode'], $node['nodeId'] ?? '');
		}
	}
	
	/**
	 * 创建审批任务
	 *
	 * @param array  $instance     工作流实例
	 * @param array  $node         审批节点
	 * @param string $parentNodeId 父节点ID
	 * @return void
	 */
	protected function createApprovalTasks(array $instance, array $node, string $parentNodeId): void
	{
		// 获取审批人列表
		$approvers = $node['nodeUserList'] ?? [];
		Log::info('审批节点：' . $node['nodeName'] . '，审批人：' . json_encode($approvers));
		
		if (empty($approvers)) {
			Log::error('审批人列表为空');
			return;
		}
		
		$taskService    = WorkflowTaskService::getInstance();
		$historyService = WorkflowHistoryService::getInstance();
		
		// 更新实例的当前节点
		$this->edit([
			'current_node' => $node['nodeId'],
			'status'       => WorkflowStatusConstant::APPROVING
		], ['id' => $instance['id']]);
		
		foreach ($approvers as $user) {
			// 创建审批任务
			$taskData = [
				'task_id'     => uniqid('task_'),
				'instance_id' => $instance['id'],
				'process_id'  => $instance['process_id'],
				'node_id'     => $node['nodeId'],
				'node_name'   => $node['nodeName'],
				'node_type'   => 'approval',
				'task_type'   => WorkflowStatusConstant::TASK_TYPE_APPROVAL,
				// 审批任务
				'approver_id' => $user['id'],
				'status'      => 0,
				// 待处理
				'sort'        => 0,
				'created_at'  => date('Y-m-d H:i:s'),
				'tenant_id'   => $instance['tenant_id'] ?? 0
			];
			
			Log::info('创建审批任务：' . json_encode($taskData));
			
			try {
				$taskId = $taskService->getCrudService()
				                      ->add($taskData);
				Log::info('审批任务创建成功，ID：' . $taskId);
				
				// 记录审批历史
				$historyData = [
					'instance_id'    => $instance['id'],
					'process_id'     => $instance['process_id'],
					'task_id'        => $taskData['task_id'],
					'node_id'        => $node['nodeId'],
					'node_name'      => $node['nodeName'],
					'node_type'      => 'approval',
					'prev_node_id'   => $parentNodeId,
					'operator_id'    => $instance['submitter_id'],
					'operation'      => WorkflowStatusConstant::OPERATION_PROCESS_START,
					// 流程开始操作
					'operation_time' => date('Y-m-d H:i:s'),
					'tenant_id'      => $instance['tenant_id'] ?? 0
				];
				
				$historyService->getCrudService()
				               ->add($historyData);
				
				// 发送审批通知
				$this->sendTaskApprovalNotification($instance, $taskData, $user);
			}
			catch (\Exception $e) {
				Log::error('创建审批任务失败：' . $e->getMessage());
			}
		}
	}
	
	/**
	 * 发送审批任务通知
	 *
	 * @param array $instance 工作流实例
	 * @param array $task     任务数据
	 * @param array $user     审批人
	 * @return bool
	 */
	protected function sendTaskApprovalNotification(array $instance, array $task, array $user): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$variables = [
				'task_name'      => $task['node_name'],
				'title'          => $instance['title'],
				'submitter_name' => $instance['submitter_name'],
				'created_at'     => $instance['created_at'],
				'detail_url'     => '/workflow/task/detail?id=' . $task['id']
			];
			
			// 发送审批任务通知
			$noticeService->send('workflow', WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, $variables, [$user['id']]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送审批任务通知失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 撤回申请
	 *
	 * @param int $id 工作流实例ID
	 * @return bool 是否成功
	 */
	public function recallApplication(int $id): bool
	{
		// 使用统一状态同步服务
		$syncService = new WorkflowStatusSyncService();
		$result      = $syncService->syncAllWorkflowStatus($id, WorkflowStatusConstant::STATUS_RECALLED, '申请已撤回', request()->adminId ?? 0);
		
		if (!$result) {
			Log::error('撤回申请失败: 状态同步失败', ['instance_id' => $id]);
			return false;
		}
		
		return true;
	}
	
	/**
	 * 作废申请
	 *
	 * @param int $id 工作流实例ID
	 * @return bool 是否成功
	 */
	public function voidApplication(int $id, $reason): bool
	{
		// 使用统一状态同步服务
		$syncService = new WorkflowStatusSyncService();
		$result      = $syncService->syncAllWorkflowStatus($id, WorkflowStatusConstant::STATUS_VOID, $reason, request()->adminId ?? 0);
		
		if (!$result) {
			Log::error('作废申请失败: 状态同步失败', ['instance_id' => $id]);
			return false;
		}
		
		return true;
	}
	
	/**
	 * 删除申请
	 *
	 * @param int $id 工作流实例ID
	 * @return bool 是否成功
	 */
	public function deleteApplication(int $id): bool
	{
		Db::startTrans();
		try {
			// 获取实例信息
			$instance = $this->crudService->getDetail($id);
			
			$status = intval($instance->getData('status'));
			if (!in_array($status, [
				0,
				6
			])) {
				throw new BusinessException('当前申请已提交，不能删除');
			}
			
			// 删除表单数据 - 使用动态工厂
			$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance['business_code']);
			if (!$formService) {
				throw new BusinessException("不支持的业务类型：{$instance['business_code']}");
			}
			$formService->deleteForm((int)$instance['business_id']);
			
			// 删除工作流实例
			$result = $this->delete(['id' => $id]);
			
			if (!$result) {
				throw new BusinessException('删除工作流实例失败');
			}
			
			Db::commit();
			return true;
		}
		catch (BusinessException $e) {
			Db::rollback();
			throw new BusinessException($e->getMessage());
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('删除申请失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 处理参数
	 *
	 * @param array $params 原始参数
	 * @param int   $status 状态
	 * @return array 处理后的参数
	 */
	protected function prepareParams(array $params, int $status): array
	{
		// 设置状态
		$params['status'] = $status;
		
		// 表单适配：将form_type映射为business_code
		if (empty($params['business_code']) && !empty($params['form_type'])) {
			$params['business_code'] = $params['form_type'];
		}
		
		return $params;
	}
	
	/**
	 * 更新申请状态
	 *
	 * @param int    $id     实例ID
	 * @param int    $status 状态值
	 * @param string $remark 备注
	 * @return bool 是否成功
	 */
	public function updateApplicationStatus(int $id, int $status, string $remark = ''): bool
	{
		Db::startTrans();
		try {
			// 获取实例信息
			$instance = $this->detail($id);
			if (!$instance) {
				throw new BusinessException('工作流实例不存在');
			}
			
			// 更新工作流实例状态
			$updateData = ['status' => $status];
			if (!empty($remark)) {
				$updateData['remark'] = $remark;
			}
			
			$result = $this->edit($updateData, ['id' => $id]);
			
			if (!$result) {
				throw new BusinessException('更新工作流实例状态失败');
			}
			
			// 更新表单状态 - 使用动态工厂
			$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance['business_code']);
			if (!$formService) {
				throw new BusinessException("不支持的业务类型：{$instance['business_code']}");
			}
			$formService->updateFormStatus($instance['form_id'], $status, ['remark' => $remark]);
			
			Db::commit();
			return true;
		}
		catch (\Exception $e) {
			Db::rollback();
			Log::error('更新申请状态失败: ' . $e->getMessage());
			return false;
		}
	}
	
	/**
	 * 生成流程实例ID
	 *
	 * @param string $businessCode 业务代码
	 * @return string 流程实例ID
	 */
	protected function generateProcessId(string $businessCode): string
	{
		$prefix = strtoupper($businessCode);
		$date   = date('YmdHis');
		$rand   = mt_rand(1000, 9999);
		return "{$prefix}-{$date}-{$rand}";
	}
	
	/**
	 * 获取详情并包含额外信息
	 *
	 * @param int   $id                  工作流实例ID
	 * @param array $with                包含的字段
	 * @param bool  $includeHistory      是否包含历史记录
	 * @param bool  $includeProcessGraph 是否包含流程图数据
	 * @return mixed
	 */
	public function getDetailWithExtra(
		int $id, array $with = [], bool $includeHistory = true, bool $includeProcessGraph = true
	): mixed
	{
		// 获取基本详情
		$instance = $this->detail($id, $with);
		
		// 添加额外信息
		if ($includeHistory) {
			// 获取历史记录
			$historyService      = WorkflowHistoryService::getInstance();
			$instance['history'] = $historyService->getInstanceHistory($id);
		}
		
		if ($includeProcessGraph) {
			// 处理流程图数据
			$instance['process_graph'] = $this->processProcessGraphData($instance['process_data'], $instance['current_node']);
		}
		
		// 获取当前节点的处理人信息
		$instance['current_handlers'] = $this->getCurrentNodeHandlers($instance->toArray());
		
		return $instance;
	}
	
	/**
	 * 获取当前节点的处理人信息
	 *
	 * @param array|object $instance 工作流实例
	 * @return array 处理人信息数组
	 */
	protected function getCurrentNodeHandlers(array $instance): array
	{
		if (empty($instance['current_node'])) {
			return [];
		}
		
		// 使用静态缓存，避免重复查询
		static $handlersCache = [];
		$cacheKey = 'handlers_' . $instance['id'] . '_' . $instance['current_node'];
		
		if (isset($handlersCache[$cacheKey])) {
			return $handlersCache[$cacheKey];
		}
		
		// 获取流程定义，判断当前节点类型
		/*$definitionService = WorkflowDefinitionService::getInstance();
		$definition        = $definitionService->getModel()
		                                       ->where(['id' => $instance['definition_id']])
		                                       ->findOrEmpty();
		
		if ($definition->isEmpty()) {
			return [];
		}*/
		
		// 解析流程配置
		$flowConfig = $instance['process_data'];
		if (!is_array($flowConfig) && !empty($flowConfig)) {
			$flowConfig = json_decode($flowConfig, true)
				?: [];
		}
		
		// 查找当前节点
		$nodeFinder = new \app\workflow\service\node\NodeFinder();
		$nodeFinder->setNodeConfig($flowConfig['nodeConfig'] ?? []);
		$currentNode = $nodeFinder->findById($instance['current_node']);
		
		// 如果找不到当前节点，或者当前节点已经是结束节点
		if (empty($currentNode) || $instance['status'] >= 2) {
			$handlersCache[$cacheKey] = [];
			return [];
		}
		
		// 根据节点类型返回不同的处理人信息
		$handlers = [];
		
		// 节点类型处理
		switch ($currentNode['type'] ?? 0) {
			case 2: // 抄送节点
				$handlers = $this->getCcNodeHandlers($instance, $currentNode);
				break;
			case 3:
			case 4: // 条件分支节点
				// 条件分支节点本身没有处理人，返回空数组
				$handlers = [];
				break;
			
			default: // 审批节点等其他类型
				$handlers = $this->getApprovalNodeHandlers($instance);
				break;
		}
		
		// 缓存结果
		$handlersCache[$cacheKey] = $handlers;
		return $handlers;
	}
	
	/**
	 * 获取审批节点的处理人信息
	 *
	 * @param array $instance 工作流实例
	 * @return array 处理人信息数组
	 */
	protected function getApprovalNodeHandlers(array $instance): array
	{
		// 获取当前节点未处理的任务
		$taskService = WorkflowTaskService::getInstance();
		$tasks       = $taskService->getList([
			'instance_id' => $instance['id'],
			'node_id'     => $instance['current_node'],
			'status'      => 0
			// 待处理状态
		]);
		
		if ($tasks->isEmpty()) {
			return [];
		}
		
		$handlers = [];
		foreach ($tasks as $task) {
			// 获取处理人信息
			$admin = \app\system\model\AdminModel::where('id', $task['approver_id'])
			                                     ->find();
			if ($admin) {
				$handlers[] = [
					'id'        => $admin['id'],
					'name'      => $admin['real_name'] ?? $admin['username'],
					'task_id'   => $task['id'],
					'task_type' => $task['task_type'],
					'avatar'    => $admin['avatar'] ?? '',
					'dept_name' => $this->getUserDeptName($admin['dept_id'] ?? 0)
				];
			}
		}
		
		return $handlers;
	}
	
	/**
	 * 获取抄送节点的处理人信息
	 *
	 * @param array $instance 工作流实例
	 * @param array $node     抄送节点
	 * @return array 处理人信息数组
	 */
	protected function getCcNodeHandlers(array $instance, array $node): array
	{
		$handlers = [];
		$ccUsers  = $node['nodeUserList'] ?? [];
		
		if (empty($ccUsers)) {
			return [];
		}
		
		foreach ($ccUsers as $user) {
			$admin = \app\system\model\AdminModel::where('id', $user['id'])
			                                     ->find();
			if ($admin) {
				$handlers[] = [
					'id'        => $admin['id'],
					'name'      => $admin['real_name'] ?? $admin['username'],
					'task_type' => \app\workflow\constants\WorkflowStatusConstant::TASK_TYPE_CC,
					'avatar'    => $admin['avatar'] ?? '',
					'dept_name' => $this->getUserDeptName($admin['dept_id'] ?? 0)
				];
			}
		}
		
		return $handlers;
	}
	
	/**
	 * 获取用户部门名称
	 *
	 * @param int $deptId 部门ID
	 * @return string 部门名称
	 */
	protected function getUserDeptName(int $deptId): string
	{
		if (empty($deptId)) {
			return '';
		}
		
		$dept = \app\system\model\DeptModel::where('id', $deptId)
		                                   ->find();
		return $dept
			? ($dept['name'] ?? '')
			: '';
	}
	
	/**
	 * 获取流程图数据
	 *
	 * @param int $id 工作流实例ID
	 * @return array
	 */
	public function getProcessGraph(int $id): array
	{
		$instance = $this->detail($id);
		
		// 处理流程图数据
		return $this->processProcessGraphData($instance['process_data'], $instance['current_node']);
	}
	
	/**
	 * 获取审批历史记录
	 *
	 * @param int $id 工作流实例ID
	 * @return array
	 */
	public function getApprovalHistory(int $id): array
	{
		$historyService = WorkflowHistoryService::getInstance();
		$taskService    = WorkflowTaskService::getInstance();
		
		// 获取历史记录
		$history = $historyService->getList([
			'instance_id' => $id
		], 'operation_time DESC');
		
		// 获取任务信息
		$tasks = $taskService->getList([
			'instance_id' => $id
		]);
		
		// 合并任务信息到历史记录
		$tasksMap = [];
		foreach ($tasks as $task) {
			$tasksMap[$task['task_id']] = $task;
		}
		
		foreach ($history as &$record) {
			if (!empty($record['task_id']) && isset($tasksMap[$record['task_id']])) {
				$record['task_info'] = $tasksMap[$record['task_id']];
			}
		}
		
		return $history;
	}
	
	/**
	 * 处理流程图数据
	 *
	 * @param string|array $processData   JSON格式的流程数据或已解析的数组
	 * @param string|null  $currentNodeId 当前节点ID
	 * @return array 处理后的流程图数据
	 */
	protected function processProcessGraphData($processData, ?string $currentNodeId): array
	{
		// 解码流程配置数据
		$processConfig = $processData;
		if (!is_array($processData) && !empty($processData)) {
			$processConfig = json_decode($processData, true)
				?: [];
		}
		else if (empty($processData)) {
			$processConfig = [];
		}
		
		// 标记当前节点
		if ($currentNodeId && isset($processConfig['nodes'])) {
			foreach ($processConfig['nodes'] as &$node) {
				if ($node['id'] == $currentNodeId) {
					$node['isCurrent'] = true;
				}
				else {
					$node['isCurrent'] = false;
				}
			}
		}
		
		return $processConfig;
	}
	
	/**
	 * 处理工作流中的抄送节点
	 *
	 * @param array $workflowInstance 工作流实例
	 * @param array $flowConfig       流程配置
	 * @return void
	 */
	protected function processCcNodes(array $workflowInstance, array $flowConfig): void
	{
		// 解析流程配置
		$nodeConfig = $flowConfig['nodeConfig'] ?? [];
		Log::info('处理节点配置：' . json_encode($nodeConfig));
		
		if (empty($nodeConfig)) {
			Log::error('节点配置为空');
			return;
		}
		
		// 递归查找抄送节点并创建抄送任务
		$this->createCcTasksFromNode($workflowInstance, $nodeConfig);
	}
	
	/**
	 * 递归处理节点树，找出所有抄送节点
	 *
	 * @param array  $instance     工作流实例
	 * @param array  $node         当前节点
	 * @param string $parentNodeId 父节点ID
	 * @return void
	 */
	protected function createCcTasksFromNode(array $instance, array $node, string $parentNodeId = ''): void
	{
		// 如果当前节点是抄送节点(type=2)
		if (isset($node['type']) && ($node['type'] === '2' || $node['type'] === 2)) {
			Log::info('找到抄送节点: ' . json_encode($node));
			// 创建抄送任务
			$this->createCcTasks($instance, $node, $parentNodeId);
		}
		
		// 递归处理子节点
		if (!empty($node['childNode'])) {
			$this->createCcTasksFromNode($instance, $node['childNode'], $node['nodeId'] ?? '');
		}
	}
	
	/**
	 * 创建抄送任务
	 *
	 * @param array  $instance     工作流实例
	 * @param array  $node         抄送节点
	 * @param string $parentNodeId 父节点ID
	 * @return void
	 */
	protected function createCcTasks(array $instance, array $node, string $parentNodeId): void
	{
		// 获取抄送人列表
		$ccUsers = $node['nodeUserList'] ?? [];
		Log::info('抄送节点：' . $node['nodeName'] . '，抄送人：' . json_encode($ccUsers));
		
		if (empty($ccUsers)) {
			Log::error('抄送人列表为空');
			return;
		}
		
		$taskService    = WorkflowTaskService::getInstance();
		$historyService = WorkflowHistoryService::getInstance();
		
		foreach ($ccUsers as $user) {
			// 创建抄送任务
			$taskData = [
				'task_id'     => uniqid('cc_'),
				'instance_id' => $instance['id'],
				'process_id'  => $instance['process_id'],
				'node_id'     => $node['nodeId'],
				'node_name'   => $node['nodeName'],
				'node_type'   => 'cc',
				'task_type'   => WorkflowStatusConstant::TASK_TYPE_CC,
				// 抄送任务
				'approver_id' => $user['id'],
				'status'      => 0,
				// 待处理
				'sort'        => 0,
				'created_at'  => date('Y-m-d H:i:s'),
				'tenant_id'   => $instance['tenant_id'] ?? 0
			];
			
			Log::info('创建抄送任务：' . json_encode($taskData));
			
			try {
				$taskId = $taskService->getCrudService()
				                      ->add($taskData);
				Log::info('抄送任务创建成功，ID：' . $taskId);
				
				// 记录抄送历史
				$historyData = [
					'instance_id'    => $instance['id'],
					'process_id'     => $instance['process_id'],
					'task_id'        => $taskData['task_id'],
					'node_id'        => $node['nodeId'],
					'node_name'      => $node['nodeName'],
					'node_type'      => 'cc',
					'prev_node_id'   => $parentNodeId,
					'operator_id'    => $instance['submitter_id'],
					'operation'      => WorkflowStatusConstant::OPERATION_PROCESS_START,
					// 抄送操作
					'operation_time' => date('Y-m-d H:i:s'),
					'tenant_id'      => $instance['tenant_id'] ?? 0
				];
				
				$historyService->getCrudService()
				               ->add($historyData);
				
				// 发送抄送通知
				$this->sendCcNotification($instance, $user);
			}
			catch (\Exception $e) {
				Log::error('创建抄送任务失败：' . $e->getMessage());
			}
		}
	}
	
	/**
	 * 发送抄送通知
	 *
	 * @param array $instance 工作流实例
	 * @param array $user     抄送人
	 * @return bool
	 */
	protected function sendCcNotification(array $instance, array $user): bool
	{
		try {
			// 对接消息中心
			$noticeService = NoticeDispatcherService::getInstance();
			
			// 准备变量 - 使用英文键名，消息中心会自动映射为中文
			$data = [
				'title'          => $instance['title'],
				'submitter_name' => $instance['submitter_name'],
				'node_name'      => '抄送',
				'cc_time'        => date('Y-m-d H:i:s'),
				'detail_url'     => '/workflow/detail?id=' . $instance['id']
			];
			
			// 生成URL
			$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
				'instance_id' => $instance['id']
			]);
			
			// 发送抄送通知，使用抄送通知模板
			$noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_CC, $data, [$user['id']], [
				'business_id' => (string)$instance['id'],
				'detail_url'  => $urls['detail_url'],
				'mobile_url'  => $urls['mobile_url']
			]);
			
			return true;
		}
		catch (\Exception $e) {
			Log::error('发送抄送通知失败: ' . $e->getMessage());
			return false;
		}
	}
} 