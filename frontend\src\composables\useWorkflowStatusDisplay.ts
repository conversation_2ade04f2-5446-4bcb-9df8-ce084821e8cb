import { 
  WorkflowStatus,
  getInstanceStatusText,
  getTaskStatusText,
  getInstanceStatusTagType,
  getTaskStatusTagType
} from '@/constants/workflow'

/**
 * 工作流状态显示优化
 * 
 * 提供更准确的状态显示逻辑，优先使用实例状态而不是任务状态
 */

export interface WorkflowTaskItem {
  task_id?: number
  status: number                    // 任务状态
  instance?: {
    status: number                  // 实例状态
    [key: string]: any
  }
  business_type?: 'form' | 'business' // 轨道类型
  [key: string]: any
}

export function useWorkflowStatusDisplay() {
  
  /**
   * 获取准确的显示状态
   * 优先级：实例状态 > 任务状态
   */
  const getAccurateStatus = (task: WorkflowTaskItem): {
    status: number
    text: string
    tagType: string
    source: 'instance' | 'task'
  } => {
    // 如果实例已结束，优先显示实例状态
    if (task.instance?.status && isInstanceEnded(task.instance.status)) {
      return {
        status: task.instance.status,
        text: getInstanceStatusText(task.instance.status),
        tagType: getInstanceStatusTagType(task.instance.status),
        source: 'instance'
      }
    }
    
    // 如果任务已跳过，显示已跳过状态
    if (task.status === WorkflowStatus.SKIPPED) {
      return {
        status: task.status,
        text: getTaskStatusText(task.status),
        tagType: getTaskStatusTagType(task.status),
        source: 'task'
      }
    }
    
    // 默认显示任务状态
    return {
      status: task.status,
      text: getTaskStatusText(task.status),
      tagType: getTaskStatusTagType(task.status),
      source: 'task'
    }
  }
  
  /**
   * 判断实例是否已结束
   */
  const isInstanceEnded = (instanceStatus: number): boolean => {
    const endedStatuses = [
      WorkflowStatus.COMPLETED,  // 已通过
      WorkflowStatus.REJECTED,   // 已驳回
      WorkflowStatus.TERMINATED, // 已终止
      WorkflowStatus.RECALLED,   // 已撤回
      WorkflowStatus.VOID        // 已作废
    ]
    return endedStatuses.includes(instanceStatus)
  }
  
  /**
   * 判断任务是否可以操作
   */
  const isTaskOperable = (task: WorkflowTaskItem): boolean => {
    // 如果实例已结束，任务不可操作
    if (task.instance?.status && isInstanceEnded(task.instance.status)) {
      return false
    }
    
    // 如果任务已跳过，不可操作
    if (task.status === WorkflowStatus.SKIPPED) {
      return false
    }
    
    // 只有待处理的任务可以操作
    return task.status === WorkflowStatus.DRAFT
  }
  
  /**
   * 获取操作按钮的显示状态
   */
  const getOperationButtonState = (task: WorkflowTaskItem): {
    showApprove: boolean
    showReject: boolean
    showTransfer: boolean
    showView: boolean
    disabled: boolean
    disabledReason?: string
  } => {
    const operable = isTaskOperable(task)
    
    if (!operable) {
      let reason = '任务不可操作'
      
      if (task.instance?.status && isInstanceEnded(task.instance.status)) {
        reason = `流程已${getInstanceStatusText(task.instance.status)}`
      } else if (task.status === WorkflowStatus.SKIPPED) {
        reason = '任务已跳过'
      } else if (task.status !== WorkflowStatus.DRAFT) {
        reason = `任务已${getTaskStatusText(task.status)}`
      }
      
      return {
        showApprove: false,
        showReject: false,
        showTransfer: false,
        showView: true,
        disabled: true,
        disabledReason: reason
      }
    }
    
    return {
      showApprove: true,
      showReject: true,
      showTransfer: true,
      showView: true,
      disabled: false
    }
  }
  
  /**
   * 获取状态提示信息
   */
  const getStatusTooltip = (task: WorkflowTaskItem): string => {
    const accurateStatus = getAccurateStatus(task)
    
    if (accurateStatus.source === 'instance') {
      return `流程状态：${accurateStatus.text}`
    }
    
    return `任务状态：${accurateStatus.text}`
  }
  
  /**
   * 检查状态是否需要刷新
   * 简单检查：如果实例已结束但任务仍为待处理，建议刷新
   */
  const shouldRefresh = (task: WorkflowTaskItem): boolean => {
    return !!(
      task.instance?.status && 
      isInstanceEnded(task.instance.status) && 
      task.status === WorkflowStatus.DRAFT
    )
  }
  
  return {
    getAccurateStatus,
    isInstanceEnded,
    isTaskOperable,
    getOperationButtonState,
    getStatusTooltip,
    shouldRefresh
  }
}
