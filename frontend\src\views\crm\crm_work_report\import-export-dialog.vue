<template>
  <ElDialog v-model="dialogVisible" :title="dialogTitle" width="500px" destroy-on-close>
    <div class="import-export-content">
      <!-- 导入区域 -->

      <div v-if="dialogType === 'import'" class="section">
        <h4>数据导入</h4>
        <div class="import-area">
          <ElUpload
            ref="uploadRef"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls"
            drag
          >
            <div class="upload-content">
              <ElIcon class="upload-icon">
                <UploadFilled />
              </ElIcon>
              <div class="upload-text">
                <p>点击或拖拽文件到此区域上传</p>
                <p class="upload-hint">支持 .xlsx, .xls 格式文件</p>
              </div>
            </div>
          </ElUpload>

          <div class="upload-actions">
            <ElButton
              type="primary"
              @click="handleImport"
              :loading="importing"
              :disabled="!selectedFile"
            >
              开始导入
            </ElButton>
            <ElButton @click="downloadTemplate" :loading="downloadingTemplate"> 下载模板 </ElButton>
          </div>

          <div v-if="selectedFile" class="selected-file">
            <ElIcon>
              <Document />
            </ElIcon>
            <span>{{ selectedFile.name }}</span>
            <ElButton type="text" @click="clearFile">
              <ElIcon>
                <Close />
              </ElIcon>
            </ElButton>
          </div>
        </div>
      </div>

      <!-- 导出区域 -->

      <div v-if="dialogType === 'export'" class="section">
        <h4>数据导出</h4>
        <div class="export-area">
          <ElForm :model="exportForm" label-width="80px">
            <ElFormItem label="导出格式">
              <ElRadioGroup v-model="exportForm.format">
                <ElRadio value="xlsx">Excel (.xlsx)</ElRadio>
                <ElRadio value="csv">CSV (.csv)</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="导出范围">
              <ElRadioGroup v-model="exportForm.range">
                <ElRadio value="all">全部数据</ElRadio>
                <ElRadio value="current">当前页数据</ElRadio>
                <ElRadio value="selected">已选择数据</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElForm>

          <div class="export-actions">
            <ElButton type="primary" @click="handleExport" :loading="exporting">
              开始导出
            </ElButton>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="closeDialog">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElUpload } from 'element-plus'
  import { UploadFilled, Document, Close } from '@element-plus/icons-vue'
  import { CrmWorkReportApi } from '@/api/crm/crmWorkReport'
  import { ApiStatus } from '@/utils/http/status'

  // 定义props
  interface Props {
    selectedIds?: number[]
    searchParams?: Record<string, any>
  }

  const props = withDefaults(defineProps<Props>(), {
    selectedIds: () => [],
    searchParams: () => ({})
  })

  const emit = defineEmits(['success', 'close'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref<'import' | 'export'>('import')
  const importing = ref(false)
  const exporting = ref(false)
  const downloadingTemplate = ref(false)

  // 文件相关
  const selectedFile = ref<File | null>(null)
  const uploadRef = ref()

  // 导出表单
  const exportForm = reactive({
    format: 'xlsx',
    range: 'all'
  })

  // 计算对话框标题
  const dialogTitle = computed(() => {
    return dialogType.value === 'import' ? '数据导入' : '数据导出'
  })

  // 显示对话框
  const showDialog = (type: 'import' | 'export' = 'import') => {
    dialogType.value = type
    dialogVisible.value = true
  }

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false
    clearFile()
    emit('close')
  }

  // 文件选择处理
  const handleFileChange = (file: any) => {
    selectedFile.value = file.raw
  }

  // 上传前验证
  const beforeUpload = (file: File) => {
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      ElMessage.error('只能上传 Excel 文件!')
      return false
    }

    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
      ElMessage.error('文件大小不能超过 10MB!')
      return false
    }

    return true
  }

  // 清除文件
  const clearFile = () => {
    selectedFile.value = null
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }

  // 处理导入
  const handleImport = async () => {
    if (!selectedFile.value) {
      ElMessage.warning('请先选择要导入的文件')
      return
    }

    try {
      importing.value = true
      const res = await CrmWorkReportApi.import(selectedFile.value)

      if (res.code === ApiStatus.success) {
        ElMessage.success('导入成功')
        emit('success')
        closeDialog()
      } else {
        ElMessage.error(res.message || '导入失败')
      }
    } catch (error) {
      ElMessage.error('导入失败')
    } finally {
      importing.value = false
    }
  }

  // 下载模板
  const downloadTemplate = async () => {
    try {
      downloadingTemplate.value = true

      // 先获取模板文件信息
      const templateRes = await CrmWorkReportApi.importTemplate()
      if (templateRes.code !== ApiStatus.success) {
        ElMessage.error(templateRes.message || '获取模板失败')
        return
      }

      // 下载模板文件
      const fileName = templateRes.data.file
      const res = await CrmWorkReportApi.downloadTemplate(fileName)

      // 处理二进制数据下载
      const blob = new Blob([res], { type: 'application/vnd.ms-excel' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = `工作报告表导入模板.xlsx`
      link.click()
      URL.revokeObjectURL(link.href)

      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
    } finally {
      downloadingTemplate.value = false
    }
  }

  // 处理导出
  const handleExport = async () => {
    // 如果是勾选数据导出，先检查是否有选中数据
    if (exportForm.range === 'selected') {
      if (!props.selectedIds || props.selectedIds.length === 0) {
        ElMessage.warning('请先勾选要导出的数据')
        return
      }
    }

    try {
      exporting.value = true

      // 根据导出范围构建参数
      const params = {
        export_type: exportForm.range,
        format: exportForm.format,
        ...props.searchParams
      }

      // 如果是勾选数据导出，添加选中的ID
      if (exportForm.range === 'selected') {
        params.ids = props.selectedIds
      }

      // 如果是当前页导出，添加分页信息
      if (exportForm.range === 'current') {
        params.page = props.searchParams?.page || 1
        params.page_limit = props.searchParams?.limit || 10
      }

      const res = await CrmWorkReportApi.export(params)

      // 检查响应是否为错误信息（JSON格式）
      if (res instanceof Blob) {
        // 检查blob的类型，如果是application/json说明是错误响应
        if (res.type === 'application/json') {
          const text = await res.text()
          try {
            const jsonResponse = JSON.parse(text)
            // 检查返回的code状态
            if (jsonResponse.code !== ApiStatus.success) {
              ElMessage.error(jsonResponse.message || '导出失败')
              return
            }
          } catch (e) {
            ElMessage.error('导出失败')
            return
          }
        }

        // 正常的文件数据，直接下载
        const link = document.createElement('a')
        link.href = URL.createObjectURL(res)
        link.download = `工作报告表导出_${new Date().getTime()}.${exportForm.format}`
        link.click()
        URL.revokeObjectURL(link.href)

        ElMessage.success('导出成功')
        closeDialog()
      } else {
        // 如果返回的不是Blob，检查是否是JSON响应
        if (res && typeof res === 'object' && 'code' in res) {
          if (res.code !== ApiStatus.success) {
            ElMessage.error(res.message || '导出失败')
            return
          }
        }
        ElMessage.error('导出响应格式错误')
      }
    } catch (error: any) {
      // 只显示后端返回的错误信息，不显示多个错误提示
      const errorMessage = error?.response?.data?.message || error?.message || '导出失败'
      ElMessage.error(errorMessage)
    } finally {
      exporting.value = false
    }
  }

  // 暴露方法
  defineExpose({
    showDialog,
    closeDialog
  })
</script>

<style scoped>
  .import-export-content {
    padding: 20px 0;
  }

  .section {
    margin-bottom: 30px;
  }

  .section h4 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .import-area,
  .export-area {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 20px;
    background-color: #fafafa;
  }

  .upload-content {
    text-align: center;
    padding: 40px 20px;
  }

  .upload-icon {
    font-size: 48px;
    color: #c0c4cc;
    margin-bottom: 16px;
  }

  .upload-text p {
    margin: 0;
    color: #606266;
  }

  .upload-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
  }

  .upload-actions,
  .export-actions {
    margin-top: 20px;
    text-align: center;
  }

  .upload-actions .el-button,
  .export-actions .el-button {
    margin: 0 10px;
  }

  .selected-file {
    display: flex;
    align-items: center;
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
  }

  .selected-file .el-icon {
    margin-right: 8px;
    color: #409eff;
  }

  .selected-file span {
    flex: 1;
    color: #303133;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
