<template>
  <el-dialog v-model="visible" title="合同详情" width="800px" :close-on-click-modal="false">
    <div v-loading="loading" class="contract-detail">
      <div v-if="contractDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同名称">
            {{ contractDetail.contract_name }}
          </el-descriptions-item>
          <el-descriptions-item label="合同编号">
            {{ contractDetail.contract_number }}
          </el-descriptions-item>
          <el-descriptions-item label="签约人姓名">
            {{ contractDetail.contact_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="签约人电话">
            {{ contractDetail.contact_mobile || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="合同金额">
            ¥{{ contractDetail.contract_amount?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="已付金额">
            ¥{{ contractDetail.paid_amount?.toLocaleString() }}
          </el-descriptions-item>
          <!--          <el-descriptions-item label="合同类型">
                      {{ contractDetail.type }}
                    </el-descriptions-item>
                    <el-descriptions-item label="付款方式">
                      {{ contractDetail.payment_method }}
                    </el-descriptions-item>-->
          <el-descriptions-item label="开始日期">
            {{ contractDetail.start_date }}
          </el-descriptions-item>
          <el-descriptions-item label="结束日期">
            {{ contractDetail.end_date }}
          </el-descriptions-item>
          <el-descriptions-item label="签署日期">
            {{ contractDetail.sign_date }}
          </el-descriptions-item>
          <el-descriptions-item label="付款期限">
            {{ contractDetail.payment_deadline }}
          </el-descriptions-item>
          <el-descriptions-item label="付款状态">
            <el-tag :type="getPaymentStatusType(contractDetail.payment_status)">
              {{ getPaymentStatusText(contractDetail.payment_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ contractDetail.created_at }}
          </el-descriptions-item>
          <el-descriptions-item label="付款条件" :span="2">
            {{ contractDetail.payment_terms }}
          </el-descriptions-item>
          <el-descriptions-item label="合同描述" :span="2">
            {{ contractDetail.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button
          v-if="
            hasButtonPermission('crm:crm_customer_my:edit_contract') &&
            contractDetail &&
            contractDetail.approval_status === 0
          "
          type="primary"
          @click="handleEdit"
        >
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'

  // 组件属性
  interface Props {
    modelValue: boolean
    contractId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    contractId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    edit: [contractId: number]
  }>()

  // 权限验证
  const { hasButtonPermission } = useCustomerPermission()

  // 响应式数据
  const loading = ref(false)
  const contractDetail = ref<any>(null)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 监听对话框显示状态
  watch(visible, (newVal) => {
    if (newVal && props.contractId) {
      loadContractDetail()
    }
  })

  // 加载合同详情
  const loadContractDetail = async () => {
    if (!props.contractId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getContractDetail(props.contractId)

      if (res.code === ApiStatus.success) {
        contractDetail.value = res.data
      } else {
        ElMessage.error(res.message || '加载合同详情失败')
      }
    } catch (error) {
      console.error('加载合同详情失败:', error)
      ElMessage.error('加载合同详情失败')
    } finally {
      loading.value = false
    }
  }

  // 获取付款状态类型
  const getPaymentStatusType = (status: number) => {
    switch (status) {
      case 0:
        return 'info' // 未付款
      case 1:
        return 'warning' // 部分付款
      case 2:
        return 'success' // 已付清
      default:
        return 'info'
    }
  }

  // 获取付款状态文本
  const getPaymentStatusText = (status: number) => {
    switch (status) {
      case 0:
        return '未付款'
      case 1:
        return '部分付款'
      case 2:
        return '已付清'
      default:
        return '未知'
    }
  }

  // 编辑按钮处理
  const handleEdit = () => {
    emit('edit', props.contractId!)
    visible.value = false
  }
</script>

<style scoped lang="scss">
  .contract-detail {
    .detail-content {
      margin-top: 16px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
