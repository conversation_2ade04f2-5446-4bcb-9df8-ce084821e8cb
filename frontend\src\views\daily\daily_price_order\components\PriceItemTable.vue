<template>
  <div class="price-item-table">
    <!-- 表格工具栏 -->
    <div class="table-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :disabled="readonly" @click="addItem">
          <el-icon>
            <Plus />
          </el-icon>
          添加明细
        </el-button>
        <el-button :disabled="readonly" @click="copyYesterday">
          <el-icon>
            <CopyDocument />
          </el-icon>
          从昨日复制
        </el-button>
        <el-button v-if="items.length > 0" type="danger" :disabled="readonly" @click="clearAll">
          <el-icon>
            <Delete />
          </el-icon>
          清空明细
        </el-button>
      </div>
      <div class="toolbar-right">
        <span class="item-count">共 {{ items.length }} 个产品</span>
      </div>
    </div>

    <!-- 明细表格 -->
    <el-table
      :data="items"
      border
      stripe
      :loading="loading"
      empty-text="暂无明细数据，请添加产品"
      class="price-table"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column label="供应商" min-width="150">
        <template #default="{ row, $index }">
          <div :class="{ 'duplicate-item': row.isDuplicate }">
            <el-select
              v-if="!readonly"
              v-model="row.supplier_id"
              placeholder="选择供应商"
              filterable
              clearable
              @change="onSupplierChange(row)"
              style="width: 100%"
            >
              <el-option
                v-for="supplier in suppliers"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
            <span v-else>{{ getSupplierName(row.supplier_id) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="规格" min-width="200">
        <template #default="{ row }">
          <div :class="{ 'duplicate-item': row.isDuplicate }">
            <div v-if="!readonly">
              <el-select
                v-model="row.product_id"
                placeholder="选择规格"
                filterable
                clearable
                style="width: 100%"
                @change="onProductChange(row)"
              >
                <el-option
                  v-for="product in getProductsBySupplier(row.supplier_id)"
                  :key="product.id"
                  :label="`名称:${product.name}-规格:${product.spec}`"
                  :value="product.id"
                />
              </el-select>
              <div v-if="row.product_id" class="selected-spec">
                {{ getProductSpec(row.product_id) }}
              </div>
            </div>
            <span v-else>{{ getProductSpec(row.product_id) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="单价" width="120" align="center">
        <template #default="{ row }">
          <el-input-number
            v-if="!readonly"
            v-model="row.unit_price"
            :precision="2"
            :min="0"
            :max="999999"
            :controls="false"
            @change="calculatePriceChange(row)"
            style="width: 100%"
          />
          <span v-else class="price-text">¥{{ formatPrice(row.unit_price) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="涨幅" width="150" align="center">
        <template #default="{ row }">
          <span
            v-if="row.price_change_rate !== undefined && row.price_change_rate !== null"
            :class="getPriceChangeClass(row.price_change_rate)"
          >
            {{ formatPriceChangeRate(row.price_change_rate, row) }}
          </span>
          <span v-else class="no-change">-</span>
        </template>
      </el-table-column>

      <el-table-column label="库存价格" width="100" align="center">
        <template #default="{ row }">
          <el-input-number
            v-if="!readonly"
            v-model="row.stock_price"
            :precision="2"
            :min="0"
            :max="999999"
            :controls="false"
            style="width: 100%"
          />
          <span v-else class="price-text">¥{{ formatPrice(row.stock_price) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="库存数量" width="90" align="center">
        <template #default="{ row }">
          <el-input-number
            v-if="!readonly"
            v-model="row.stock_qty"
            :precision="2"
            :min="0"
            :controls="false"
            style="width: 100%"
          />
          <span v-else>{{ row.stock_qty || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="备注" min-width="150">
        <template #default="{ row }">
          <el-input
            v-if="!readonly"
            v-model="row.policy_remark"
            placeholder="优惠政策/备注"
            maxlength="200"
            show-word-limit
          />
          <span v-else>{{ row.policy_remark || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="100" align="center" fixed="right" v-if="!readonly">
        <template #default="{ $index }">
          <el-button type="danger" size="small" :icon="Delete" @click="removeItem($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 表格底部统计 -->
    <div class="table-footer" v-if="items.length > 0">
      <el-descriptions :column="4" size="small">
        <el-descriptions-item label="产品总数">{{ items.length }} 个</el-descriptions-item>
        <el-descriptions-item label="涨价产品">{{ riseCount }} 个</el-descriptions-item>
        <el-descriptions-item label="降价产品">{{ fallCount }} 个</el-descriptions-item>
        <el-descriptions-item label="无变化">{{ stableCount }} 个</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, CopyDocument, Delete } from '@element-plus/icons-vue'
  import { DailyPriceOrderApi } from '@/api/daily/dailyPriceOrder'
  import type { DailyPriceItem } from '@/api/daily/dailyPriceOrder'

  // import PriceChangeIndicator from './PriceChangeIndicator.vue'

  interface Props {
    modelValue: DailyPriceItem[]
    readonly?: boolean
    loading?: boolean
    orderId?: number
  }

  interface Emits {
    (e: 'update:modelValue', value: DailyPriceItem[]): void

    (e: 'change', items: DailyPriceItem[]): void

    (e: 'validation-change', value: { hasDuplicates: boolean; duplicateCount: number }): void
  }

  const props = withDefaults(defineProps<Props>(), {
    readonly: false,
    loading: false
  })

  const emit = defineEmits<Emits>()

  // 响应式数据
  const suppliers = ref<any[]>([])
  const products = ref<any[]>([])
  const yesterdayPrices = ref<Record<string, any>>({})
  const loading = ref(false)

  // 计算属性
  const items = computed({
    get: () => props.modelValue,
    set: (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }
  })

  const riseCount = computed(() => {
    return items.value.filter((item) => (item.price_change || 0) > 0).length
  })

  const fallCount = computed(() => {
    return items.value.filter((item) => (item.price_change || 0) < 0).length
  })

  const stableCount = computed(() => {
    return items.value.filter((item) => (item.price_change || 0) === 0).length
  })

  // 生命周期
  onMounted(() => {
    loadSuppliers()
    loadProducts()
    loadYesterdayPrices()
  })

  // 方法
  const loadSuppliers = async () => {
    try {
      const { data } = await DailyPriceOrderApi.getSupplierList()
      suppliers.value = data.list || []
    } catch (error) {
      console.error('加载供应商失败:', error)
    }
  }

  const loadProducts = async () => {
    try {
      const { data } = await DailyPriceOrderApi.getProductList()
      products.value = data.list || []
    } catch (error) {
      console.error('加载产品失败:', error)
    }
  }

  const loadYesterdayPrices = async () => {
    try {
      loading.value = true
      const res = await DailyPriceOrderApi.getYesterdayPrices()
      yesterdayPrices.value = res.code === 1 && res.data ? res.data : {}
      console.log('昨日价格数据加载完成:', yesterdayPrices.value)
    } catch (error) {
      console.error('加载昨日价格失败:', error)
      yesterdayPrices.value = {}
    } finally {
      loading.value = false
    }
  }

  const getProductsBySupplier = (supplierId: number) => {
    // 暂时返回所有产品，因为产品表中没有supplier_id字段
    // 如果后续需要按供应商筛选，需要在产品表中添加supplier_id字段
    return products.value
  }

  const getSupplierName = (supplierId: number) => {
    const supplier = suppliers.value.find((s) => s.id === supplierId)
    return supplier?.name || '-'
  }

  const getProductName = (productId: number) => {
    const product = products.value.find((p) => p.id === productId)
    return product?.name || '-'
  }

  const getProductDisplayName = (productId: number) => {
    const product = products.value.find((p) => p.id === productId)
    return product?.display_name || product?.name || '-'
  }

  const getProductSpec = (productId: number) => {
    const product = products.value.find((p) => p.id === productId)
    return product?.spec || '-'
  }

  const formatPrice = (price: number) => {
    return (price || 0).toFixed(2)
  }

  // 新的涨幅计算方法
  const calculatePriceChange = (item: DailyPriceItem) => {
    if (!item.supplier_id || !item.product_id || !item.unit_price) {
      item.price_change_rate = null
      // 清空相关字段
      item.old_price = undefined
      item.price_change = undefined
      item.change_rate = undefined
      return
    }

    const key = `${item.supplier_id}-${item.product_id}`
    const yesterdayData = yesterdayPrices.value[key]

    if (!yesterdayData || !yesterdayData.price) {
      item.price_change_rate = null
      // 清空相关字段
      item.old_price = undefined
      item.price_change = undefined
      item.change_rate = undefined
      return
    }

    const currentPrice = item.unit_price
    const yesterdayPrice = yesterdayData.price
    const priceChange = currentPrice - yesterdayPrice
    const changeRate = (priceChange / yesterdayPrice) * 100

    // 设置完整的价格变动数据
    item.old_price = yesterdayPrice
    item.price_change = priceChange
    item.change_rate = changeRate
    item.price_change_rate = changeRate  // 保持兼容性
    item.is_manual_price = 1
  }

  // 格式化涨幅显示 - 显示具体数值和百分比
  const formatPriceChangeRate = (rate: number | null, item?: DailyPriceItem) => {
    if (rate === null || rate === undefined || rate === 0) {
      return '-'
    }

    // 如果有明细项，计算具体的价格变动数值
    if (item && item.supplier_id && item.product_id && item.unit_price) {
      const key = `${item.supplier_id}-${item.product_id}`
      const yesterdayData = yesterdayPrices.value[key]

      if (yesterdayData && yesterdayData.price) {
        const priceChange = item.unit_price - yesterdayData.price
        const sign = priceChange > 0 ? '+' : ''
        const priceText = `${sign}${priceChange.toFixed(2)}`
        // const rateText = `${sign}${rate.toFixed(2)}%`
        // return `${priceText}(${rateText})`
        return `${priceText}`
      }
    }

    // 如果没有昨日数据，只显示百分比
    const sign = rate > 0 ? '+' : ''
    return `${sign}${rate.toFixed(2)}%`
  }

  // 获取涨幅样式类
  const getPriceChangeClass = (rate: number | null) => {
    if (rate === null || rate === undefined || rate === 0) {
      return 'no-change'
    }
    return rate > 0 ? 'price-rise' : 'price-fall'
  }

  // 产品变化时的处理
  const onProductChange = (item: DailyPriceItem) => {
    calculatePriceChange(item)
    validateDuplicateSelection()
  }

  // 供应商变化时的处理
  const onSupplierChange = (item: DailyPriceItem) => {
    // 清空产品选择
    item.product_id = null
    item.price_change_rate = null
    validateDuplicateSelection()
  }

  // 验证重复选择
  const validateDuplicateSelection = () => {
    const combinations = new Set()
    const duplicates = new Set()

    items.value.forEach((item, index) => {
      if (item.supplier_id && item.product_id) {
        const key = `${item.supplier_id}-${item.product_id}`
        if (combinations.has(key)) {
          duplicates.add(key)
        } else {
          combinations.add(key)
        }
      }
    })

    // 标记重复项
    items.value.forEach((item, index) => {
      if (item.supplier_id && item.product_id) {
        const key = `${item.supplier_id}-${item.product_id}`
        item.isDuplicate = duplicates.has(key)
      } else {
        item.isDuplicate = false
      }
    })

    // 发出验证结果
    const hasDuplicates = duplicates.size > 0
    emit('validation-change', { hasDuplicates, duplicateCount: duplicates.size })
  }

  const addItem = () => {
    const newItem: DailyPriceItem = {
      order_id: props.orderId || 0,
      supplier_id: null,
      product_id: null,
      unit_price: 0,
      price_change_rate: null,
      stock_price: 0,
      stock_qty: 0,
      policy_remark: '',
      is_manual_price: 1,
      sort_order: items.value.length + 1,
      status: 1
    }

    items.value = [...items.value, newItem]
  }

  const removeItem = (index: number) => {
    items.value = items.value.filter((_, i) => i !== index)
  }

  const clearAll = async () => {
    try {
      await ElMessageBox.confirm('确定要清空所有明细吗？', '确认清空', {
        type: 'warning'
      })
      items.value = []
    } catch (error) {
      // 用户取消
    }
  }

  const copyYesterday = async () => {
    try {
      // 检查当前是否已有明细数据
      if (items.value.length > 0) {
        ElMessage.warning('请先清空当前明细数据后再从昨日加载')
        return
      }

      // 获取昨日数据
      const res = await DailyPriceOrderApi.getYesterdayPrices()
      console.log('从昨日复制 - API响应:', res)
      if (res.code === 1 && res.data && Object.keys(res.data).length > 0) {
        // 将昨日数据转换为明细格式
        const yesterdayItems: DailyPriceItem[] = []
        let sortOrder = 1

        for (const [key, priceData] of Object.entries(res.data)) {
          const [supplierId, productId] = key.split('-').map(Number)
          yesterdayItems.push({
            order_id: props.orderId || 0,
            supplier_id: supplierId,
            product_id: productId,
            unit_price: (priceData as any).price || 0,
            price_change_rate: null, // 从昨日复制时不显示涨幅
            stock_price: (priceData as any).stock_price || 0,
            stock_qty: (priceData as any).stock_qty || 0,
            policy_remark: (priceData as any).policy_remark || '',
            is_manual_price: 0, // 标记为非手动价格
            sort_order: sortOrder++,
            status: 1,
            // 添加关联数据用于显示
            supplier: {
              id: supplierId,
              name: (priceData as any).supplier_name || ''
            },
            product: {
              id: productId,
              name: (priceData as any).product_name || '',
              spec: (priceData as any).product_spec || ''
            }
          })
        }

        if (yesterdayItems.length > 0) {
          console.log('从昨日复制 - 转换后的明细数据:', yesterdayItems)
          items.value = yesterdayItems
          ElMessage.success(`成功加载 ${yesterdayItems.length} 个昨日产品明细`)
        } else {
          ElMessage.warning('昨日无可加载的报价数据')
        }
      } else {
        ElMessage.warning('昨日无可加载的报价数据')
      }
    } catch (error) {
      console.error('加载昨日数据失败:', error)
      ElMessage.error('加载昨日数据失败')
    }
  }
</script>

<style scoped>
  .price-item-table {
    width: 100%;
    max-height: 55vh;
    overflow: auto;
  }

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .toolbar-left {
    display: flex;
    gap: 12px;
  }

  .toolbar-right {
    color: #606266;
    font-size: 14px;
  }

  .item-count {
    font-weight: 600;
  }

  .price-table {
    width: 100%;
  }

  .price-text {
    font-weight: 600;
    color: #303133;
  }

  /* 涨幅样式 */
  .price-rise {
    color: #f56c6c;
    font-weight: 500;
  }

  .price-fall {
    color: #67c23a;
    font-weight: 500;
  }

  .no-change {
    color: #909399;
    font-weight: 400;
  }

  .selected-spec {
    margin-top: 4px;
    padding: 2px 8px;
    background-color: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    font-size: 12px;
    color: #1976d2;
    text-align: center;
  }

  /* 重复项样式 */
  .duplicate-item {
    position: relative;
  }

  .duplicate-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #f56c6c;
    border-radius: 4px;
    pointer-events: none;
    z-index: 1;
  }

  .duplicate-item::after {
    content: '重复';
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #f56c6c;
    color: white;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: 2px;
    z-index: 2;
  }

  .table-footer {
    margin-top: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .toolbar-left {
      justify-content: center;
    }

    .toolbar-right {
      text-align: center;
    }
  }
</style>
