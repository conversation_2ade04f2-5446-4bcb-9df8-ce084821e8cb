/**
 * CRM系统主题配置
 * 针对25-60岁中文用户群体的颜色主题优化
 * 
 * 设计原则：
 * 1. 高对比度，减少视觉疲劳
 * 2. 色彩语义化，提升信息识别效率
 * 3. 支持亮色/暗色主题切换
 * 4. 符合无障碍设计标准
 */

// ==================== 亮色主题 ====================

:root {
  // 基础色彩
  --crm-primary: #1664ff;           // 主色调 - 蓝色
  --crm-primary-light: #4080ff;     // 主色调浅色
  --crm-primary-dark: #0052d9;      // 主色调深色
  
  // 功能色彩
  --crm-success: #00a870;           // 成功 - 绿色
  --crm-success-light: #33b885;     // 成功浅色
  --crm-success-bg: #e8f8f2;        // 成功背景色
  
  --crm-warning: #ed7b2f;           // 警告 - 橙色
  --crm-warning-light: #f19654;     // 警告浅色
  --crm-warning-bg: #fef3e8;        // 警告背景色
  
  --crm-error: #d54941;             // 错误 - 红色
  --crm-error-light: #e06b64;       // 错误浅色
  --crm-error-bg: #fdeeed;          // 错误背景色
  
  --crm-info: #1664ff;              // 信息 - 蓝色
  --crm-info-light: #4080ff;        // 信息浅色
  --crm-info-bg: #e8f2ff;           // 信息背景色
  
  // 文本颜色
  --crm-text-primary: #1f2329;      // 主要文本
  --crm-text-secondary: #4e5969;    // 次要文本
  --crm-text-tertiary: #86909c;     // 三级文本
  --crm-text-placeholder: #c9cdd4;  // 占位符文本
  --crm-text-disabled: #c9cdd4;     // 禁用文本
  
  // 背景颜色
  --crm-bg-primary: #ffffff;        // 主背景
  --crm-bg-secondary: #f8f9fa;      // 次背景
  --crm-bg-tertiary: #f2f3f5;       // 三级背景
  --crm-bg-hover: #f8f9fa;          // 悬停背景
  --crm-bg-active: #e9ecef;         // 激活背景
  
  // 边框颜色
  --crm-border-primary: #e9ecef;    // 主边框
  --crm-border-secondary: #dee2e6;  // 次边框
  --crm-border-hover: #1664ff;      // 悬停边框
  --crm-border-focus: #1664ff;      // 聚焦边框
  
  // 阴影
  --crm-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --crm-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.1);
  --crm-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.15);
  
  // 业务特定颜色
  --crm-customer-color: #1664ff;    // 客户相关
  --crm-lead-color: #7c3aed;        // 线索相关
  --crm-contract-color: #ed7b2f;    // 合同相关
  --crm-follow-color: #00a870;      // 跟进相关
  --crm-amount-color: #ed7b2f;      // 金额显示
  --crm-paid-color: #00a870;        // 已付金额
  
  // 性别标识颜色
  --crm-gender-male: #4080ff;       // 男性 - 蓝色
  --crm-gender-female: #f56c6c;     // 女性 - 红色
  --crm-gender-private: #86909c;    // 保密 - 灰色
  
  // 重要程度颜色
  --crm-importance-high: #d54941;   // 高重要性
  --crm-importance-medium: #ed7b2f; // 中重要性
  --crm-importance-low: #00a870;    // 低重要性
}

// ==================== 暗色主题 ====================

html.dark {
  // 基础色彩 (暗色主题下稍微调整饱和度)
  --crm-primary: #4080ff;
  --crm-primary-light: #5d87ff;
  --crm-primary-dark: #1664ff;
  
  // 功能色彩
  --crm-success: #33b885;
  --crm-success-light: #4dc896;
  --crm-success-bg: #1a3d2e;
  
  --crm-warning: #f19654;
  --crm-warning-light: #f4a875;
  --crm-warning-bg: #3d2a1a;
  
  --crm-error: #e06b64;
  --crm-error-light: #e5827c;
  --crm-error-bg: #3d1f1e;
  
  --crm-info: #4080ff;
  --crm-info-light: #5d87ff;
  --crm-info-bg: #1a2a3d;
  
  // 文本颜色 (暗色主题)
  --crm-text-primary: #f5f5f5;
  --crm-text-secondary: #b5b7c8;
  --crm-text-tertiary: #808290;
  --crm-text-placeholder: #636674;
  --crm-text-disabled: #464852;
  
  // 背景颜色 (暗色主题)
  --crm-bg-primary: #161618;
  --crm-bg-secondary: #1b1c22;
  --crm-bg-tertiary: #26272f;
  --crm-bg-hover: #26272f;
  --crm-bg-active: #363843;
  
  // 边框颜色 (暗色主题)
  --crm-border-primary: #26272f;
  --crm-border-secondary: #363843;
  --crm-border-hover: #4080ff;
  --crm-border-focus: #4080ff;
  
  // 阴影 (暗色主题)
  --crm-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
  --crm-shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.3);
  --crm-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.4);
  
  // 业务特定颜色 (暗色主题调整)
  --crm-customer-color: #4080ff;
  --crm-lead-color: #8b5cf6;
  --crm-contract-color: #f19654;
  --crm-follow-color: #33b885;
  --crm-amount-color: #f19654;
  --crm-paid-color: #33b885;
  
  // 性别标识颜色 (暗色主题)
  --crm-gender-male: #4080ff;
  --crm-gender-female: #f56c6c;
  --crm-gender-private: #808290;
  
  // 重要程度颜色 (暗色主题)
  --crm-importance-high: #e06b64;
  --crm-importance-medium: #f19654;
  --crm-importance-low: #33b885;
}

// ==================== 状态标签主题 ====================

.crm-status-tags {
  // 客户状态
  .customer-status {
    &.active { background-color: var(--crm-success-bg); color: var(--crm-success); }
    &.inactive { background-color: var(--crm-error-bg); color: var(--crm-error); }
    &.potential { background-color: var(--crm-warning-bg); color: var(--crm-warning); }
  }
  
  // 线索状态
  .lead-status {
    &.new { background-color: var(--crm-info-bg); color: var(--crm-info); }
    &.contacted { background-color: var(--crm-warning-bg); color: var(--crm-warning); }
    &.qualified { background-color: var(--crm-success-bg); color: var(--crm-success); }
    &.lost { background-color: var(--crm-error-bg); color: var(--crm-error); }
  }
  
  // 合同状态
  .contract-status {
    &.draft { background-color: var(--crm-bg-tertiary); color: var(--crm-text-secondary); }
    &.pending { background-color: var(--crm-warning-bg); color: var(--crm-warning); }
    &.approved { background-color: var(--crm-success-bg); color: var(--crm-success); }
    &.rejected { background-color: var(--crm-error-bg); color: var(--crm-error); }
    &.cancelled { background-color: var(--crm-bg-tertiary); color: var(--crm-text-tertiary); }
  }
  
  // 跟进类型
  .follow-type {
    &.call { background-color: var(--crm-info-bg); color: var(--crm-info); }
    &.email { background-color: var(--crm-success-bg); color: var(--crm-success); }
    &.meeting { background-color: var(--crm-warning-bg); color: var(--crm-warning); }
    &.visit { background-color: var(--crm-error-bg); color: var(--crm-error); }
  }
}

// ==================== 业务模块颜色标识 ====================

.crm-module-colors {
  // 客户模块
  .customer-module {
    --module-primary: var(--crm-customer-color);
    --module-bg: #e8f2ff;
    --module-border: #b3d9ff;
  }
  
  // 线索模块
  .lead-module {
    --module-primary: var(--crm-lead-color);
    --module-bg: #f3e8ff;
    --module-border: #d1b3ff;
  }
  
  // 合同模块
  .contract-module {
    --module-primary: var(--crm-contract-color);
    --module-bg: #fef3e8;
    --module-border: #fdd5b3;
  }
  
  // 跟进模块
  .follow-module {
    --module-primary: var(--crm-follow-color);
    --module-bg: #e8f8f2;
    --module-border: #b3e6cc;
  }
}

// ==================== 辅助工具类 ====================

// 文本颜色工具类
.crm-text-primary { color: var(--crm-text-primary) !important; }
.crm-text-secondary { color: var(--crm-text-secondary) !important; }
.crm-text-tertiary { color: var(--crm-text-tertiary) !important; }
.crm-text-success { color: var(--crm-success) !important; }
.crm-text-warning { color: var(--crm-warning) !important; }
.crm-text-error { color: var(--crm-error) !important; }
.crm-text-info { color: var(--crm-info) !important; }

// 背景颜色工具类
.crm-bg-primary { background-color: var(--crm-bg-primary) !important; }
.crm-bg-secondary { background-color: var(--crm-bg-secondary) !important; }
.crm-bg-tertiary { background-color: var(--crm-bg-tertiary) !important; }

// 边框工具类
.crm-border { border: 1px solid var(--crm-border-primary) !important; }
.crm-border-secondary { border: 1px solid var(--crm-border-secondary) !important; }

// 阴影工具类
.crm-shadow-light { box-shadow: var(--crm-shadow-light) !important; }
.crm-shadow-medium { box-shadow: var(--crm-shadow-medium) !important; }
.crm-shadow-heavy { box-shadow: var(--crm-shadow-heavy) !important; }
