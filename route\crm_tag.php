<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 标签表路由
/*Route::group('api/crm/crm_tag', function () {
	Route::get('index', 'app\crm\controller\CrmTagController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmTagController@detail');
	Route::post('add', 'app\crm\controller\CrmTagController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmTagController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmTagController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmTagController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmTagController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmTagController@status');
	Route::post('import', 'app\crm\controller\CrmTagController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmTagController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmTagController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmTagController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);*/