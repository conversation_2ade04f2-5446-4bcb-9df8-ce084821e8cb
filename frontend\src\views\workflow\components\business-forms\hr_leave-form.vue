<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <div v-loading="loading">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="请假类型" prop="leave_type">
              <ElSelect
                v-model="formData.leave_type"
                placeholder="请选择请假类型"
                style="width: 100%"
                :disabled="!isEditable"
              >
                <ElOption
                  v-for="item in leaveTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="开始时间" prop="start_time">
              <ElDatePicker
                v-model="formData.start_time"
                type="datetime"
                placeholder="请选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="!isEditable"
                @change="calculateDuration"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="结束时间" prop="end_time">
              <ElDatePicker
                v-model="formData.end_time"
                type="datetime"
                placeholder="请选择结束时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="!isEditable"
                @change="calculateDuration"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol>
            <ElFormItem label="请假时长">
              <ElInput
                :value="formData.duration + ' 小时'"
                readonly
                style="width: 100%"
                placeholder="自动计算"
              />
              <div style="margin-top: 4px; color: #909399; font-size: 12px">
                按半小时向上取整规则自动计算（不足0.5小时按0.5小时计算）
              </div>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="请假事由" prop="reason">
          <ElInput
            v-model="formData.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入请假事由"
            :disabled="!isEditable"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="附件">
          <FormUploader
            v-model="formData.attachment"
            :disabled="!isEditable"
            :limit="10"
            multiple
            file-type="image"
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton v-if="isEditable" type="primary" :loading="saving" @click="handleSave">
          保存
        </ElButton>
        <ElButton v-if="isEditable" type="success" :loading="submitting" @click="handleSubmit">
          提交审批
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
  import { calculateWorkingHoursWithHalfHourRule } from '@/utils/date'
  import HrLeaveApi, { type HrLeaveItem } from '@/api/hr/hrLeave'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'
  import { FormUploader } from '@/components/custom/FormUploader'

  // 简化的表单数据类型（保持与原始接口兼容）
  interface SimpleHrLeaveFormData {
    id?: number
    leave_type: number
    start_time: string
    end_time: string
    duration: number
    reason: string
    emergency_contact?: string
    emergency_phone?: string
    attachment?: string[]
    remark?: string
  }

  /**
   * 组件属性定义
   */
  interface Props {
    /** 对话框显示状态 */
    modelValue: boolean
    /** 表单数据ID（编辑时传入） */
    formId?: number | string
    /** 工作流定义ID */
    definitionId?: number | string
  }

  /**
   * 组件事件定义
   */
  interface Emits {
    /** 更新显示状态 */
    (e: 'update:modelValue', value: boolean): void

    /** 成功事件（统一） */
    (e: 'success', data: any): void

    /** 取消事件 */
    (e: 'cancel'): void

    /** 保存事件 */
    (e: 'save', data: any): void

    /** 提交事件 */
    (e: 'submit', data: any): void
  }

  // 组件属性和事件
  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    definitionId: 0
  })

  const emit = defineEmits<Emits>()

  // ==================== 响应式数据 ====================

  /** 表单引用 */
  const formRef = ref<FormInstance>()

  /** 对话框显示状态 */
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  /** 对话框标题 */
  const dialogTitle = computed(() => {
    if (formData.id) {
      return `请假申请 - ${formData.approval_status_text || '草稿'}`
    }
    return '发起请假申请'
  })

  /** 表单数据 */
  const formData = reactive<SimpleHrLeaveFormData & Partial<HrLeaveItem>>({
    leave_type: null,
    start_time: '',
    end_time: '',
    duration: 0,
    reason: '',
    emergency_contact: '',
    emergency_phone: '',
    attachment: [],
    remark: '',
    approval_status: 0
  })

  /** 请假类型选项 */
  const leaveTypeOptions = ref<Array<any>>([])

  /** 加载状态 */
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)

  // ==================== 计算属性 ====================

  /** 是否可以编辑 */
  const isEditable = computed(() => {
    return !formData.approval_status || formData.approval_status === 0
  })

  // ==================== 表单验证规则 ====================

  /** 表单验证规则 */
  const formRules: FormRules = {
    leave_type: [{ required: true, message: '请选择请假类型', trigger: 'change' }],
    start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
    end_time: [
      { required: true, message: '请选择结束时间', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && formData.start_time && value <= formData.start_time) {
            callback(new Error('结束时间必须晚于开始时间'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    reason: [
      { required: true, message: '请输入请假事由', trigger: 'blur' },
      { min: 2, max: 500, message: '请假事由长度在5-500个字符之间', trigger: 'blur' }
    ]
  }

  // ==================== 方法定义 ====================

  /**
   * 计算请假时长
   * 使用半小时向上取整规则，考虑工作时间配置
   */
  const calculateDuration = () => {
    if (formData.start_time && formData.end_time) {
      // 使用新的工作时间计算方法
      // 默认工作时间配置：08:00-12:00,14:00-18:00
      formData.duration = calculateWorkingHoursWithHalfHourRule(
        formData.start_time,
        formData.end_time,
        '08:00-12:00,14:00-18:00'
      )
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    Object.assign(formData, {
      id: undefined,
      leave_type: null,
      start_time: '',
      end_time: '',
      duration: 0,
      reason: '',
      emergency_contact: '',
      emergency_phone: '',
      attachment: [],
      remark: '',
      approval_status: 0
    })
  }

  /**
   * 加载表单数据
   */
  const loadFormData = async (id: number | string) => {
    try {
      loading.value = true
      // 使用通用工作流接口获取详情
      const response = await ApplicationApi.detail(id)

      if (response.data) {
        // 合并表单数据
        Object.assign(formData, response.data.formData || {})

        // 设置ID和状态
        formData.id = response.data.id
        formData.approval_status = response.data.approval_status
        formData.approval_status_text = response.data.approval_status_text
        formData.workflow_instance_id = response.data.workflow_instance_id

        console.log('请假申请表单数据加载完成:', formData)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 设置表单数据（供FormManager调用）
   */
  const setFormData = (data: any) => {
    console.log('hr_leave-form setFormData called with:', data)
    Object.assign(formData, data)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    console.log('hr_leave-form.handleSave 被调用')
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      saving.value = true

      // 准备业务数据
      const businessData: SimpleHrLeaveFormData = {
        leave_type: formData.leave_type,
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact || '',
        emergency_phone: formData.emergency_phone || '',
        attachment: formData.attachment,
        remark: formData.remark || ''
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('请假申请保存数据:', businessData)
      emit('save', businessData)
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  /**
   * 提交审批
   */
  const handleSubmit = async () => {
    try {
      // 表单验证
      const valid = await formRef.value?.validate()
      if (!valid) return

      await ElMessageBox.confirm('确定要提交审批吗？提交后将无法修改。', '确认提交', {
        confirmButtonText: '确定提交',
        cancelButtonText: '取消',
        type: 'warning'
      })

      submitting.value = true

      // 准备业务数据
      const businessData: SimpleHrLeaveFormData = {
        leave_type: formData.leave_type,
        start_time: formData.start_time,
        end_time: formData.end_time,
        duration: formData.duration,
        reason: formData.reason,
        emergency_contact: formData.emergency_contact || '',
        emergency_phone: formData.emergency_phone || '',
        attachment: formData.attachment,
        remark: formData.remark || ''
      }

      // 如果是编辑模式，添加ID
      if (formData.id) {
        businessData.id = formData.id
      }

      console.log('请假申请提交数据:', businessData)
      emit('submit', businessData)
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交审批失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  /**
   * 取消操作
   */
  const handleCancel = () => {
    dialogVisible.value = false
    emit('cancel')
  }

  // ==================== 生命周期 ====================

  onMounted(async () => {
    // 加载请假类型选项
    try {
      const response = await HrLeaveApi.getLeaveTypes()
      if (response.code === 1) {
        leaveTypeOptions.value = response.data
      }
    } catch (error) {
      console.error('加载请假类型失败:', error)
    }
  })

  // 监听formId变化
  watch(
    () => props.formId,
    (newId) => {
      if (newId && dialogVisible.value) {
        loadFormData(newId)
      }
    },
    { immediate: false }
  )

  // 暴露方法供FormManager调用
  defineExpose({
    resetForm,
    loadFormData,
    setFormData
  })
</script>

<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
  }
</style>
