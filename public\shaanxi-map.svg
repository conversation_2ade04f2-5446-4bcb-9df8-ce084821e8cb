<svg viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 地图纹理 -->
    <pattern id="mapTexture" patternUnits="userSpaceOnUse" width="10" height="10">
      <rect width="10" height="10" fill="#d2691e"/>
      <circle cx="5" cy="5" r="0.5" fill="#8b4513" opacity="0.3"/>
    </pattern>
    
    <!-- 阴影效果 -->
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 榆林市 -->
  <path id="yulin" class="city-region" 
        d="M280,30 Q320,25 360,40 L370,70 Q350,90 320,85 L300,95 Q280,80 270,60 Q275,45 280,30 Z"
        data-city="榆林市"/>
  
  <!-- 延安市 -->
  <path id="yanan" class="city-region"
        d="M180,120 Q230,115 270,130 L280,170 Q260,195 230,190 L200,205 Q175,185 170,160 Q175,140 180,120 Z"
        data-city="延安市"/>
  
  <!-- 咸阳市 -->
  <path id="xianyang" class="city-region"
        d="M120,220 Q150,215 180,225 L185,250 Q165,265 140,260 L115,270 Q105,250 110,235 Q115,225 120,220 Z"
        data-city="咸阳市"/>
  
  <!-- 铜川市 -->
  <path id="tongchuan" class="city-region"
        d="M200,200 Q220,195 240,205 L245,225 Q230,235 210,230 L195,240 Q185,225 190,210 Q195,205 200,200 Z"
        data-city="铜川市"/>
  
  <!-- 西安市 -->
  <path id="xian" class="city-region"
        d="M140,260 Q170,255 200,265 L205,290 Q185,305 160,300 L135,310 Q125,290 130,275 Q135,265 140,260 Z"
        data-city="西安市"/>
  
  <!-- 渭南市 -->
  <path id="weinan" class="city-region"
        d="M210,270 Q250,265 290,280 L295,310 Q275,325 245,320 L220,330 Q200,310 205,285 Q210,275 210,270 Z"
        data-city="渭南市"/>
  
  <!-- 宝鸡市 -->
  <path id="baoji" class="city-region"
        d="M60,280 Q90,275 120,285 L125,315 Q105,330 80,325 L55,335 Q45,315 50,300 Q55,285 60,280 Z"
        data-city="宝鸡市"/>
  
  <!-- 汉中市 -->
  <path id="hanzhong" class="city-region"
        d="M80,360 Q120,355 160,370 L165,400 Q145,415 115,410 L90,420 Q70,400 75,385 Q80,370 80,360 Z"
        data-city="汉中市"/>
  
  <!-- 安康市 -->
  <path id="ankang" class="city-region"
        d="M180,380 Q220,375 260,390 L265,420 Q245,435 215,430 L190,440 Q170,420 175,405 Q180,390 180,380 Z"
        data-city="安康市"/>
  
  <!-- 商洛市 -->
  <path id="shangluo" class="city-region"
        d="M270,320 Q310,315 350,330 L355,360 Q335,375 305,370 L280,380 Q260,360 265,345 Q270,330 270,320 Z"
        data-city="商洛市"/>
  
  <!-- 城市标签 -->
  <text x="320" y="65" class="city-label">榆林市</text>
  <text x="225" y="160" class="city-label">延安市</text>
  <text x="150" y="245" class="city-label">咸阳市</text>
  <text x="220" y="220" class="city-label">铜川市</text>
  <text x="170" y="285" class="city-label">西安市</text>
  <text x="250" y="300" class="city-label">渭南市</text>
  <text x="90" y="310" class="city-label">宝鸡市</text>
  <text x="120" y="395" class="city-label">汉中市</text>
  <text x="220" y="415" class="city-label">安康市</text>
  <text x="310" y="355" class="city-label">商洛市</text>
  
  <!-- 地标建筑装饰 -->
  <g id="landmarks">
    <!-- 榆林古城墙 -->
    <rect x="310" y="50" width="20" height="12" fill="#8b4513" rx="2"/>
    <rect x="312" y="52" width="16" height="8" fill="#a0522d" rx="1"/>
    
    <!-- 延安宝塔 -->
    <polygon points="225,145 230,135 235,145" fill="#654321"/>
    <rect x="228" y="145" width="4" height="8" fill="#8b4513"/>
    
    <!-- 西安钟楼 -->
    <rect x="165" y="270" width="10" height="8" fill="#daa520"/>
    <polygon points="170,270 175,265 180,270" fill="#b8860b"/>
    
    <!-- 兵马俑 -->
    <circle cx="190" cy="295" r="3" fill="#8b7355"/>
    <circle cx="185" cy="298" r="2" fill="#8b7355"/>
    <circle cx="195" cy="298" r="2" fill="#8b7355"/>
  </g>
  
  <!-- 装饰性树木 -->
  <g id="trees">
    <circle cx="100" cy="100" r="3" fill="#228b22"/>
    <rect x="99" y="103" width="2" height="4" fill="#8b4513"/>
    
    <circle cx="300" cy="200" r="3" fill="#228b22"/>
    <rect x="299" y="203" width="2" height="4" fill="#8b4513"/>
    
    <circle cx="150" cy="350" r="3" fill="#228b22"/>
    <rect x="149" y="353" width="2" height="4" fill="#8b4513"/>
  </g>
  
  <style>
    .city-region {
      fill: #d2691e;
      stroke: #8b4513;
      stroke-width: 2;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .city-region:hover {
      fill: #ff7f50;
      stroke-width: 3;
      filter: url(#shadow);
    }
    
    .city-label {
      font-family: 'Microsoft YaHei', sans-serif;
      font-size: 12px;
      font-weight: bold;
      fill: #2c1810;
      text-anchor: middle;
      pointer-events: none;
    }
  </style>
</svg>