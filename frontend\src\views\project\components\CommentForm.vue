<template>
  <div class="comment-form">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="0">
      <el-form-item prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="添加评论..."
          maxlength="1000"
          show-word-limit
          resize="none"
        />
      </el-form-item>

      <el-form-item prop="attachments">
        <el-upload
          v-model:file-list="fileList"
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
          :limit="5"
          class="upload-demo"
        >
          <el-button size="small" type="text">
            <el-icon>
              <Paperclip />
            </el-icon>
            添加附件
          </el-button>
        </el-upload>
      </el-form-item>

      <el-form-item>
        <div class="form-actions">
          <el-button @click="handleCancel" size="default">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading" size="default">
            发布评论
          </el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { Paperclip } from '@element-plus/icons-vue'
  import { TaskApi } from '@/api/project/projectApi'

  // Props
  interface Props {
    taskId: number
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    success: []
    cancel: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const fileList = ref<any[]>([])

  const formData = reactive({
    content: '',
    attachments: []
  })

  // 验证规则
  const rules = {
    content: [
      { required: true, message: '请输入评论内容', trigger: 'blur' },
      { min: 1, max: 1000, message: '评论内容长度在 1 到 1000 个字符', trigger: 'blur' }
    ]
  }

  // 方法
  const handleFileChange = (file: any) => {
    // 这里可以添加文件上传逻辑
    console.log('文件变化:', file)
  }

  const handleFileRemove = (file: any) => {
    console.log('移除文件:', file)
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      loading.value = true

      // 处理附件
      const attachments = fileList.value.map((file) => ({
        name: file.name,
        size: file.size,
        type: file.raw?.type || '',
        url: '' // 这里需要实际的上传逻辑
      }))

      const data = {
        task_id: props.taskId,
        content: formData.content,
        attachments
      }

      await TaskApi.addComment(data)

      ElMessage.success('评论发布成功')

      // 重置表单
      formData.content = ''
      fileList.value = []

      emit('success')
    } catch (error: any) {
      console.error('发布评论失败:', error)
      ElMessage.error(error.message || '发布评论失败')
    } finally {
      loading.value = false
    }
  }

  const handleCancel = () => {
    formData.content = ''
    fileList.value = []
    emit('cancel')
  }
</script>

<style lang="scss" scoped>
  .comment-form {
    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }

    :deep(.el-textarea__inner) {
      border-radius: 8px;
      border: 1px solid #e1e6ef;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }

    :deep(.el-upload) {
      .el-button {
        color: #606266;

        &:hover {
          color: #409eff;
        }
      }
    }
  }
</style>
