<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 任务记录表（评论+跟进）路由 - 仅保留必要的编辑删除接口
Route::group('api/project/project_task_record', function () {
    // 仅保留编辑和删除接口，其他功能已迁移到任务控制器
    Route::put('edit/:id', 'app\project\controller\ProjectTaskRecordController@edit');
    Route::delete('delete/:id', 'app\project\controller\ProjectTaskRecordController@delete');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);