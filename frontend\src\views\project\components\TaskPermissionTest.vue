<template>
  <div class="permission-test">
    <el-card header="任务权限测试">
      <div class="permission-list">
        <h4>当前用户权限状态：</h4>
        
        <div class="permission-item">
          <span class="permission-name">编辑任务:</span>
          <el-tag :type="hasEditTask ? 'success' : 'danger'">
            {{ hasEditTask ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">添加评论:</span>
          <el-tag :type="hasAddComment ? 'success' : 'danger'">
            {{ hasAddComment ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">编辑评论:</span>
          <el-tag :type="hasEditComment ? 'success' : 'danger'">
            {{ hasEditComment ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">删除评论:</span>
          <el-tag :type="hasDeleteComment ? 'success' : 'danger'">
            {{ hasDeleteComment ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">添加跟进:</span>
          <el-tag :type="hasAddFollow ? 'success' : 'danger'">
            {{ hasAddFollow ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">编辑跟进:</span>
          <el-tag :type="hasEditFollow ? 'success' : 'danger'">
            {{ hasEditFollow ? '有权限' : '无权限' }}
          </el-tag>
        </div>

        <div class="permission-item">
          <span class="permission-name">删除跟进:</span>
          <el-tag :type="hasDeleteFollow ? 'success' : 'danger'">
            {{ hasDeleteFollow ? '有权限' : '无权限' }}
          </el-tag>
        </div>
      </div>

      <div class="button-test">
        <h4>按钮权限测试：</h4>
        
        <el-button v-auth="'project:task:edit'" type="primary">
          编辑任务
        </el-button>
        
        <el-button v-auth="'project:task:comment:add'" type="success">
          添加评论
        </el-button>
        
        <el-button v-auth="'project:task:comment:edit'" type="warning">
          编辑评论
        </el-button>
        
        <el-button v-auth="'project:task:comment:delete'" type="danger">
          删除评论
        </el-button>
        
        <el-button v-auth="'project:task:follow:add'" type="success">
          添加跟进
        </el-button>
        
        <el-button v-auth="'project:task:follow:edit'" type="warning">
          编辑跟进
        </el-button>
        
        <el-button v-auth="'project:task:follow:delete'" type="danger">
          删除跟进
        </el-button>
      </div>

      <div class="auth-list">
        <h4>当前路由权限列表：</h4>
        <el-tag 
          v-for="auth in authList" 
          :key="auth.auth_mark" 
          size="small" 
          style="margin: 2px;"
        >
          {{ auth.auth_mark }}
        </el-tag>
        <div v-if="authList.length === 0" class="no-auth">
          暂无权限数据
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import { useAuth } from '@/composables/useAuth'

  const route = useRoute()
  const { hasAuth } = useAuth()

  // 权限检查
  const hasEditTask = computed(() => hasAuth('project:task:edit'))
  const hasAddComment = computed(() => hasAuth('project:task:comment:add'))
  const hasEditComment = computed(() => hasAuth('project:task:comment:edit'))
  const hasDeleteComment = computed(() => hasAuth('project:task:comment:delete'))
  const hasAddFollow = computed(() => hasAuth('project:task:follow:add'))
  const hasEditFollow = computed(() => hasAuth('project:task:follow:edit'))
  const hasDeleteFollow = computed(() => hasAuth('project:task:follow:delete'))

  // 当前路由权限列表
  const authList = computed(() => {
    return Array.isArray(route.meta.authList) ? route.meta.authList : []
  })
</script>

<style lang="scss" scoped>
  .permission-test {
    padding: 20px;

    .permission-list {
      margin-bottom: 24px;

      .permission-item {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .permission-name {
          min-width: 100px;
          font-weight: 500;
        }
      }
    }

    .button-test {
      margin-bottom: 24px;

      .el-button {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    .auth-list {
      .no-auth {
        color: #999;
        font-style: italic;
      }
    }
  }
</style>
