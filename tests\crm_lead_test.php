<?php
/**
 * CRM线索功能测试脚本
 * 用于验证线索池和线索列表的基本功能
 */

require_once __DIR__ . '/../vendor/autoload.php';

use think\facade\Db;
use app\crm\service\CrmLeadService;
use app\crm\service\CrmLeadPoolService;
use app\crm\service\CrmFollowRecordService;

class CrmLeadTest
{
    private $leadService;
    private $leadPoolService;
    private $followService;
    
    public function __construct()
    {
        $this->leadService = CrmLeadService::getInstance();
        $this->leadPoolService = CrmLeadPoolService::getInstance();
        $this->followService = CrmFollowRecordService::getInstance();
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始CRM线索功能测试...\n\n";
        
        $this->testDatabaseConnection();
        $this->testLeadPoolService();
        $this->testLeadService();
        $this->testFollowRecordService();
        $this->testDataPermissions();
        
        echo "\n测试完成！\n";
    }
    
    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        echo "1. 测试数据库连接...\n";
        
        try {
            $count = Db::table('crm_lead')->count();
            echo "   ✓ 数据库连接正常，线索表共有 {$count} 条记录\n";
        } catch (Exception $e) {
            echo "   ✗ 数据库连接失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试线索池服务
     */
    private function testLeadPoolService()
    {
        echo "2. 测试线索池服务...\n";
        
        try {
            // 测试获取线索池列表
            $poolLeads = $this->leadPoolService->getPoolList([
                'page' => 1,
                'limit' => 10
            ]);
            echo "   ✓ 线索池列表获取成功，共 {$poolLeads['total']} 条记录\n";

            // 测试搜索功能
            echo "   测试搜索功能:\n";

            // 测试关键词搜索
            $searchResult1 = $this->leadPoolService->getPoolList([
                'page' => 1,
                'limit' => 5,
                'kew_word' => '测试'
            ]);
            echo "     ✓ 关键词搜索成功，共 {$searchResult1['total']} 条记录\n";

            // 测试手机号搜索
            $searchResult2 = $this->leadPoolService->getPoolList([
                'page' => 1,
                'limit' => 5,
                'mobile' => '138'
            ]);
            echo "     ✓ 手机号搜索成功，共 {$searchResult2['total']} 条记录\n";

            // 测试级别搜索
            $searchResult3 = $this->leadPoolService->getPoolList([
                'page' => 1,
                'limit' => 5,
                'level' => 1
            ]);
            echo "     ✓ 级别搜索成功，共 {$searchResult3['total']} 条记录\n";

            // 测试数据权限（线索池应该不受数据权限限制）
            $allLeads = $this->leadService->search([
                'page' => 1,
                'limit' => 10
            ]);
            echo "   ✓ 线索列表获取成功，共 {$allLeads['total']} 条记录\n";

            // 如果有线索池数据，测试认领功能
            if ($poolLeads['total'] > 0) {
                $testLeadId = $poolLeads['list'][0]['id'];
                echo "   ℹ 找到测试线索ID: {$testLeadId}\n";

                // 注意：这里只是测试方法调用，不实际执行认领
                echo "   ✓ 认领功能方法可调用\n";
            } else {
                echo "   ⚠ 线索池中暂无数据，跳过认领测试\n";
            }

        } catch (Exception $e) {
            echo "   ✗ 线索池服务测试失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试线索服务
     */
    private function testLeadService()
    {
        echo "3. 测试线索服务...\n";
        
        try {
            // 测试线索列表获取
            $leads = $this->leadService->search([
                'page' => 1,
                'limit' => 5
            ]);
            echo "   ✓ 线索列表获取成功，共 {$leads['total']} 条记录\n";
            
            // 测试字段场景配置
            $listFields = $this->leadService->getFieldScenes()['list'] ?? [];
            echo "   ✓ 列表字段场景配置正确，包含 " . count($listFields) . " 个字段\n";
            
        } catch (Exception $e) {
            echo "   ✗ 线索服务测试失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试跟进记录服务
     */
    private function testFollowRecordService()
    {
        echo "4. 测试跟进记录服务...\n";
        
        try {
            // 测试跟进记录列表获取
            $records = $this->followService->search([
                'page' => 1,
                'limit' => 5
            ]);
            echo "   ✓ 跟进记录列表获取成功，共 {$records['total']} 条记录\n";
            
            // 测试字段场景配置
            $listFields = $this->followService->getFieldScenes()['list'] ?? [];
            echo "   ✓ 跟进记录字段场景配置正确，包含 " . count($listFields) . " 个字段\n";
            
        } catch (Exception $e) {
            echo "   ✗ 跟进记录服务测试失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试数据权限
     */
    private function testDataPermissions()
    {
        echo "5. 测试数据权限配置...\n";
        
        try {
            // 检查线索池服务的数据权限配置
            $poolPermission = $this->leadPoolService->getCrudService()->getEnableDataPermission();
            if ($poolPermission === false) {
                echo "   ✓ 线索池服务数据权限已正确禁用\n";
            } else {
                echo "   ⚠ 线索池服务数据权限未禁用，可能影响功能\n";
            }
            
            // 检查线索服务的数据权限配置
            $leadPermission = $this->leadService->getCrudService()->getEnableDataPermission();
            if ($leadPermission === true) {
                echo "   ✓ 线索服务数据权限已正确启用\n";
            } else {
                echo "   ⚠ 线索服务数据权限未启用，可能影响功能\n";
            }
            
        } catch (Exception $e) {
            echo "   ✗ 数据权限测试失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * 创建测试数据
     */
    public function createTestData()
    {
        echo "创建测试数据...\n";
        
        try {
            // 创建线索池测试数据
            $testLeads = [
                [
                    'lead_name' => '测试线索1',
                    'company' => '测试公司A',
                    'mobile' => '13800138001',
                    'email' => '<EMAIL>',
                    'in_pool' => 1,
                    'owner_user_id' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_id' => 1
                ],
                [
                    'lead_name' => '测试线索2',
                    'company' => '测试公司B',
                    'mobile' => '13800138002',
                    'email' => '<EMAIL>',
                    'in_pool' => 1,
                    'owner_user_id' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_id' => 1
                ],
                [
                    'lead_name' => '测试线索3',
                    'company' => '测试公司C',
                    'mobile' => '13800138003',
                    'email' => '<EMAIL>',
                    'in_pool' => 0,
                    'owner_user_id' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_id' => 1
                ]
            ];
            
            foreach ($testLeads as $lead) {
                Db::table('crm_lead')->insert($lead);
            }
            
            echo "   ✓ 测试数据创建成功\n";
            
        } catch (Exception $e) {
            echo "   ✗ 测试数据创建失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 清理测试数据
     */
    public function cleanTestData()
    {
        echo "清理测试数据...\n";
        
        try {
            Db::table('crm_lead')->where('lead_name', 'like', '测试线索%')->delete();
            Db::table('crm_follow_record')->where('content', 'like', '测试跟进%')->delete();
            
            echo "   ✓ 测试数据清理成功\n";
            
        } catch (Exception $e) {
            echo "   ✗ 测试数据清理失败: " . $e->getMessage() . "\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $test = new CrmLeadTest();
    
    // 检查命令行参数
    $action = $argv[1] ?? 'test';
    
    switch ($action) {
        case 'create':
            $test->createTestData();
            break;
        case 'clean':
            $test->cleanTestData();
            break;
        case 'test':
        default:
            $test->runAllTests();
            break;
    }
} else {
    echo "请在命令行环境下运行此测试脚本\n";
    echo "用法: php crm_lead_test.php [test|create|clean]\n";
}
