<script setup lang="ts">
  import { ref, reactive, defineExpose, defineEmits, computed } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { CrmFollowRecordApi } from '@/api/crm/crmFollowRecord'
  import { ApiStatus } from '@/utils/http/status'
  import FormUploader from '@/components/custom/FormUploader/index.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    related_type: '',
    related_id: null, // 改为 null，避免显示默认 0
    follow_type: '',
    content: '',
    follow_date: '',
    next_plan: '',
    next_date: '',
    attachments: ''
  })

  // 关联业务显示名称（编辑时使用）
  const relatedDisplayName = ref('')

  // 关联业务选项列表
  const relatedOptions = ref([])
  const relatedLoading = ref(false)

  // 计算属性：根据跟进类型确定标签文字
  const relatedLabel = computed(() => {
    switch (formData.related_type) {
      case 'lead':
        return '线索名称'
      case 'customer':
        return '客户名称'
      case 'business':
        return '商机名称'
      default:
        return '关联业务'
    }
  })

  // 计算属性：根据跟进类型确定占位符
  const relatedPlaceholder = computed(() => {
    switch (formData.related_type) {
      case 'lead':
        return '请选择线索名称'
      case 'customer':
        return '请选择客户名称'
      case 'business':
        return '请选择商机名称'
      default:
        return '请先选择跟进类型'
    }
  })

  // 根据跟进类型获取关联对象的标签名称
  const getRelatedLabel = (relatedType: string): string => {
    const labelMap: Record<string, string> = {
      lead: '线索名称',
      business: '商机名称',
      customer: '客户名称'
    }
    return labelMap[relatedType] || '关联对象'
  }

  // 表单验证规则
  const rules = {
    related_type: [
      {
        required: true,
        message: '请选择跟进类型',
        trigger: 'blur'
      }
    ],
    related_id: [
      {
        required: true,
        validator: (rule: any, value: any, callback: any) => {
          if (!value) {
            let message = '请选择关联业务'
            switch (formData.related_type) {
              case 'lead':
                message = '请选择线索名称'
                break
              case 'customer':
                message = '请选择客户名称'
                break
              // case 'business':
              //   message = '请选择商机名称'
              //   break
            }
            callback(new Error(message))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    follow_type: [
      {
        required: true,
        message: '请选择跟进方式',
        trigger: 'blur'
      }
    ],
    content: [
      {
        required: true,
        message: '跟进内容不能为空',
        trigger: 'blur'
      }
    ],
    follow_date: [
      {
        required: true,
        message: '跟进时间不能为空',
        trigger: 'blur'
      }
    ]
  }

  // 显示对话框
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    dialogVisible.value = true

    // 重置表单数据
    Object.assign(formData, {
      related_type: '',
      related_id: null,
      follow_type: '',
      content: '',
      follow_date: null,
      next_plan: '',
      next_date: null,
      attachments: ''
    })

    // 重置关联业务相关状态
    relatedOptions.value = []
    relatedDisplayName.value = ''

    // 编辑模式下获取详情数据
    if (type === 'edit' && id) {
      try {
        loading.value = true
        const res = await CrmFollowRecordApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 处理数据类型转换
          const data = { ...res.data }

          Object.assign(formData, data)

          // 编辑模式直接使用详情返回的 related_name 字段
          if (data.related_name) {
            relatedDisplayName.value = data.related_name
          } else {
            // 如果没有 related_name，使用备用显示方式
            relatedDisplayName.value = `${getRelatedLabel(data.related_type)}(ID: ${data.related_id})`
          }
        }
      } finally {
        loading.value = false
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          // 处理提交数据转换
          const submitData = { ...formData }

          let res

          if (dialogType.value === 'add') {
            res = await CrmFollowRecordApi.add(submitData)
          } else {
            res = await CrmFollowRecordApi.update(submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            emit('success')
          }
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 跟进类型变化处理
  const handleRelatedTypeChange = async (newType: string) => {
    // 清空关联ID和选项
    formData.related_id = null
    relatedDisplayName.value = ''
    relatedOptions.value = []

    // 如果有跟进类型，加载所有选项数据
    if (newType) {
      await loadAllRelatedOptions(newType)
    }
  }

  // 加载所有关联业务选项（不带搜索参数，获取全部数据）
  const loadAllRelatedOptions = async (relatedType: string) => {
    try {
      relatedLoading.value = true
      /*let result: any = null

      switch (relatedType) {
        case 'lead':
          result = await CrmLeadApi.options() // 不传搜索参数，获取全部
          break
        case 'customer':
          result = await CrmCustomerMyApi.options() // 不传搜索参数，获取全部
          break
        case 'business':
          result = await CrmBusinessApi.options() // 不传搜索参数，获取全部
          break
        default:
          relatedOptions.value = []
          return
      }*/
      const result = await CrmFollowRecordApi.relatedOptions(relatedType)

      if (result.code === ApiStatus.success) {
        const options = result.data || []

        // 转换数据格式为 SelectV2 期望的格式
        relatedOptions.value = options.map((item: any) => ({
          label: item.label || item.name || `选项${item.value || item.id}`,
          value: item.value || item.id,
          ...item // 保留原始数据用于显示详情
        }))
      }
    } catch (error) {
      console.error('Failed to load related options:', error)
      relatedOptions.value = []
    } finally {
      relatedLoading.value = false
    }
  }

  // 获取选项显示名称
  const getOptionDisplayName = (option: any) => {
    if (!option) return ''

    // 根据不同类型返回对应的名称字段
    switch (formData.related_type) {
      case 'lead':
        return option.lead_name || option.label || option.name || `线索${option.id || ''}`
      case 'customer':
        return option.customer_name || option.label || option.name || `客户${option.id || ''}`
      case 'business':
        return option.business_name || option.label || option.name || `商机${option.id || ''}`
      default:
        // SelectV2 的选项格式
        return option.label || option.name || `选项${option.value || option.id || ''}`
    }
  }

  // 获取选项显示详情
  const getOptionDisplayDetail = (option: any) => {
    if (!option) return ''

    switch (formData.related_type) {
      case 'lead':
        return option.company || option.company_name || ''
      case 'customer':
        return option.company || option.company_name || ''
      case 'business':
        return option.amount ? `¥${option.amount}` : option.customer_name || ''
      default:
        return ''
    }
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增跟进记录表' : '编辑跟进记录表'"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <ElFormItem label="跟进类型" prop="related_type">
          <ElSelect
            v-model="formData.related_type"
            placeholder="请选择跟进类型"
            style="width: 100%"
            :disabled="dialogType === 'edit'"
            @change="handleRelatedTypeChange"
          >
            <ElOption label="线索" value="lead" />
            <ElOption label="客户" value="customer" />
            <ElOption label="商机" value="business" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem
          v-if="formData.related_type || dialogType === 'edit'"
          :label="relatedLabel"
          prop="related_id"
        >
          <ElSelectV2
            v-if="dialogType === 'add'"
            v-model="formData.related_id"
            :options="relatedOptions"
            :loading="relatedLoading"
            :placeholder="relatedPlaceholder"
            filterable
            clearable
            style="width: 100%"
          >
            <template #default="{ item }">
              <div class="related-option">
                <div class="option-header">
                  <span class="option-type-label">{{ relatedLabel }}</span>
                  <span class="option-name">{{ getOptionDisplayName(item) }}</span>
                </div>
                <div class="option-detail" v-if="getOptionDisplayDetail(item)">
                  {{ getOptionDisplayDetail(item) }}
                </div>
              </div>
            </template>
          </ElSelectV2>
          <div v-else class="edit-related-display">
            <div class="edit-related-header">
              <!--              <span class="edit-type-label">{{ relatedLabel }}</span>-->
              <span class="edit-related-name">{{ relatedDisplayName || '加载中...' }}</span>
            </div>
            <div class="edit-related-note">编辑模式下不可修改</div>
          </div>
        </ElFormItem>
        <ElFormItem label="跟进方式" prop="follow_type">
          <ElSelect v-model="formData.follow_type" placeholder="请选择跟进方式" style="width: 100%">
            <ElOption label="电话" value="phone" />
            <ElOption label="拜访" value="visit" />
            <ElOption label="邮件" value="email" />
            <ElOption label="微信" value="wechat" />
            <ElOption label="其他" value="other" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="跟进内容" prop="content">
          <ElInput
            v-model="formData.content"
            type="textarea"
            :rows="4"
            placeholder="请输入跟进内容"
          />
        </ElFormItem>
        <ElFormItem label="跟进时间" prop="follow_date">
          <ElDatePicker
            v-model="formData.follow_date"
            type="datetime"
            placeholder="请选择跟进时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </ElFormItem>
        <ElFormItem label="下次跟进计划" prop="next_plan">
          <ElInput
            v-model="formData.next_plan"
            type="textarea"
            :rows="4"
            placeholder="请输入下次跟进计划"
          />
        </ElFormItem>
        <ElFormItem label="下次跟进时间" prop="next_date">
          <ElDatePicker
            v-model="formData.next_date"
            type="datetime"
            placeholder="请选择下次跟进时间"
            style="width: 100%"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </ElFormItem>
        <ElFormItem label="附件" prop="attachments">
          <FormUploader
            v-model="formData.attachments"
            fileType="file"
            :limit="1"
            buttonText="上传附件"
          />
        </ElFormItem>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .dialog-content {
    max-height: calc(90vh - 200px);
    overflow-y: auto;
    padding: 0 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid #e4e7ed;
  }

  /* 关联选项样式 */
  .related-option {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px 0;
  }

  .related-option .option-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .related-option .option-type-label {
    font-size: 11px;
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    padding: 1px 6px;
    border-radius: 10px;
    font-weight: 500;
    flex-shrink: 0;
  }

  .related-option .option-name {
    font-weight: 600;
    color: var(--el-text-color-primary);
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
  }

  .related-option .option-detail {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    line-height: 1.2;
    margin-left: 2px;
  }

  /* 编辑模式关联对象显示样式 */
  .edit-related-display {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 12px;
    background-color: var(--el-fill-color-lighter);
  }

  .edit-related-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
  }

  .edit-related-name {
    font-weight: 600;
    color: var(--el-text-color-primary);
    font-size: 14px;
    flex: 1;
  }

  .edit-related-note {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    font-style: italic;
  }
</style>
