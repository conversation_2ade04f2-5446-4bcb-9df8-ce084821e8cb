<!--通用API选择器 - 表单专用版本-->
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="props.placeholder || '请选择'"
    :loading="loading"
    :clearable="props.clearable"
    :filterable="props.filterable"
    :multiple="props.multiple"
    :disabled="props.disabled"
    :size="props.size"
    :remote="false"
    :reserve-keyword="props.reserveKeyword"
    @change="handleChange"
    @clear="handleClear"
    @focus="handleFocus"
    @blur="handleBlur"
    @visible-change="handleVisibleChange"
  >
    <!-- 调试信息已移除 -->
    <el-option
      v-for="option in optionList"
      :key="getOptionValue(option)"
      :label="getOptionLabel(option)"
      :value="getOptionValue(option)"
      :disabled="getOptionDisabled(option)"
    >
      <slot
        name="option"
        :option="option"
        :label="getOptionLabel(option)"
        :value="getOptionValue(option)"
      >
        <div class="option-content">
          <span class="option-label">{{ getOptionLabel(option) }}</span>
          <span v-if="showOptionExtra && getOptionExtra(option)" class="option-extra">
            {{ getOptionExtra(option) }}
          </span>
        </div>
      </slot>
    </el-option>

    <!-- 正常的empty插槽 -->
    <template #empty>
      <div style="padding: 12px; text-align: center; color: #909399;">
        {{ props.emptyText || '暂无数据' }}
      </div>
    </template>

    <template v-if="showFooter" #footer>
      <div class="select-footer">
        <slot name="footer" :refresh="refresh" :clear-cache="clearCache">
          <el-button type="primary" text size="small" @click="refresh"> 刷新数据</el-button>
        </slot>
      </div>
    </template>
  </el-select>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, nextTick } from 'vue'
  import { debounce } from '@pureadmin/utils'
  import { ElMessage } from 'element-plus'

  // 值类型定义
  export type SelectValue = string | number | (string | number)[] | null | undefined

  // API 响应类型
  interface ApiResponse {
    code: number
    data: any
    message?: string

    [key: string]: any
  }

  // 选项数据类型
  interface OptionItem {
    [key: string]: any
  }

  // API 配置类型
  interface ApiConfig {
    url: string // API 地址 *必填*
    method?: 'get' | 'post' // 请求方法，默认 'get'
    params?: Record<string, any> // 固定参数
    searchParam?: string // 搜索参数名，默认 'keyword'
    dataPath?: string // 数据路径，默认 'data'
    listPath?: string // 列表路径，默认 null（直接使用 dataPath）
    transform?: (data: any) => OptionItem[] // 数据转换函数
    headers?: Record<string, string> // 请求头
  }

  // Props 定义
  interface Props {
    modelValue?: SelectValue // v-model 绑定值

    // API 配置
    api: ApiConfig // API 配置 *必填*

    // 字段映射
    labelField?: string // label 字段名，默认 'name'
    valueField?: string // value 字段名，默认 'id'
    disabledField?: string // disabled 字段名，默认 'disabled'
    extraField?: string // 额外信息字段名

    // 基础配置
    multiple?: boolean // 是否多选，默认 false
    placeholder?: string // 占位符
    clearable?: boolean // 是否可清除，默认 true
    readonly?: boolean // 是否只读，默认 false
    disabled?: boolean // 是否禁用，默认 false
    size?: 'large' | 'default' | 'small' // 尺寸，默认 'default'

    // 搜索配置
    filterable?: boolean // 是否可搜索，默认 true
    remote?: boolean // 是否远程搜索，默认 true
    minSearchLength?: number // 最小搜索长度，默认 0
    searchDelay?: number // 搜索延迟，默认 300ms
    reserveKeyword?: boolean // 是否保留搜索关键字，默认 false

    // 行为配置
    autoLoad?: boolean // 是否自动加载，默认 true
    cacheResults?: boolean // 是否缓存结果，默认 true
    loadOnFocus?: boolean // 是否聚焦时加载，默认 true

    // 显示配置
    showOptionExtra?: boolean // 是否显示选项额外信息，默认 false
    showFooter?: boolean // 是否显示底部，默认 false
    emptyText?: string // 空数据文本，默认 '暂无数据'
    loadingText?: string // 加载文本，默认 '加载中...'

    // Element Plus 原生配置
    collapseTags?: boolean // 是否折叠标签，默认 true
    collapseTagsTooltip?: boolean // 是否显示折叠标签提示，默认 true
    maxCollapseTags?: number // 最大显示标签数
    multipleLimit?: number // 多选时最多选择数量
    tagType?: 'success' | 'info' | 'warning' | 'danger' // 标签类型
    effect?: 'dark' | 'light' | 'plain' // 主题

    // 下拉配置
    popperClass?: string // 下拉框类名
    teleported?: boolean // 是否传送到 body，默认 true
    persistent?: boolean // 是否持久化，默认 true
    automaticDropdown?: boolean // 是否自动下拉，默认 false
    fitInputWidth?: boolean // 是否适应输入框宽度，默认 false

    // 验证配置
    validateEvent?: boolean // 是否触发表单验证，默认 true
  }

  const props = withDefaults(defineProps<Props>(), {
    labelField: 'name',
    valueField: 'id',
    disabledField: 'disabled',
    multiple: false,
    clearable: true,
    readonly: false,
    disabled: false,
    size: 'default',
    filterable: true,
    remote: true,
    minSearchLength: 0,
    searchDelay: 300,
    reserveKeyword: false,
    autoLoad: true,
    cacheResults: true,
    loadOnFocus: true,
    showOptionExtra: false,
    showFooter: false,
    emptyText: '暂无数据',
    loadingText: '加载中...',
    collapseTags: true,
    collapseTagsTooltip: true,
    teleported: true,
    persistent: true,
    automaticDropdown: false,
    fitInputWidth: false,
    validateEvent: true
  })

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: SelectValue): void

    (e: 'change', value: SelectValue, option?: OptionItem | OptionItem[]): void

    (e: 'clear'): void

    (e: 'focus', event: FocusEvent): void

    (e: 'blur', event: FocusEvent): void

    (e: 'visible-change', visible: boolean): void

    (e: 'load-success', data: OptionItem[]): void

    (e: 'load-error', error: any): void
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const loading = ref(false)
  const optionList = ref<OptionItem[]>([])
  const searchCache = ref<Map<string, OptionItem[]>>(new Map())
  const isInitialized = ref(false) // 是否已初始化

  // 计算属性
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // selectProps已移除，直接在模板中设置属性

  // 移除emptyText计算属性，直接使用Element Plus原生的no-data-text

  // 工具函数
  const getOptionValue = (option: OptionItem): string | number => {
    return option[props.valueField] ?? option.value ?? option.id
  }

  const getOptionLabel = (option: OptionItem): string => {
    return option[props.labelField] ?? option.label ?? option.name ?? String(getOptionValue(option))
  }

  const getOptionDisabled = (option: OptionItem): boolean => {
    return option[props.disabledField] ?? option.disabled ?? false
  }

  const getOptionExtra = (option: OptionItem): string => {
    return props.extraField ? (option[props.extraField] ?? '') : ''
  }

  // API 请求
  const fetchData = async (searchKeyword = ''): Promise<OptionItem[]> => {
    const cacheKey = searchKeyword || '__default__'

    // 检查缓存
    if (props.cacheResults && searchCache.value.has(cacheKey)) {
      return searchCache.value.get(cacheKey)!
    }

    try {
      loading.value = true

      // 构建请求参数
      const params = {
        ...props.api.params,
        ...(searchKeyword && { [props.api.searchParam || 'keyword']: searchKeyword })
      }

      // 动态导入 HTTP 客户端
      const http = await import('@/utils/http')

      // 发起请求
      const response: ApiResponse = await http.default[props.api.method || 'get']({
        url: props.api.url,
        params: props.api.method === 'get' ? params : undefined,
        data: props.api.method === 'post' ? params : undefined,
        headers: props.api.headers
      })

      // HTTP拦截器已确保请求成功，直接处理数据

      // 提取数据
      let data = response.data
      const dataPath = props.api.dataPath || 'data'
      if (dataPath !== 'data') {
        const paths = dataPath.split('.')
        for (const path of paths) {
          data = data?.[path]
        }
      }

      // 处理列表路径
      if (props.api.listPath) {
        const listPaths = props.api.listPath.split('.')
        for (const path of listPaths) {
          data = data?.[path]
        }
      }

      // 确保是数组
      if (!Array.isArray(data)) {
        console.warn('ApiSelect: Response data is not an array', data)
        data = []
      }

      // 数据转换
      if (props.api.transform) {
        data = props.api.transform(data)
      }

      // 缓存结果
      if (props.cacheResults) {
        searchCache.value.set(cacheKey, data)
      }

      emit('load-success', data)
      return data
    } catch (error) {
      console.error('ApiSelect: Request failed', error)
      emit('load-error', error)
      // HTTP拦截器已处理错误提示
      return []
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleRemoteSearch = debounce(async (query: string) => {
    if (query.length < props.minSearchLength) {
      return
    }

    const data = await fetchData(query)
    optionList.value = data
  }, props.searchDelay)

  const handleChange = (value: SelectValue) => {
    // 获取选中的选项数据
    let selectedOption: OptionItem | OptionItem[] | undefined

    if (props.multiple && Array.isArray(value)) {
      selectedOption = optionList.value.filter((option) => value.includes(getOptionValue(option)))
    } else if (!props.multiple && value !== null && value !== undefined) {
      selectedOption = optionList.value.find((option) => getOptionValue(option) === value)
    }

    emit('change', value, selectedOption)
  }

  const handleClear = () => {
    emit('clear')
    // 清除时不需要重新加载数据，保持现有选项
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
    // 聚焦时不加载数据
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleVisibleChange = (visible: boolean) => {
    emit('visible-change', visible)
    // 下拉框打开时不加载数据，依赖autoLoad在组件挂载时加载
  }

  // 公共方法
  const loadDefaultData = async () => {
    // 防止重复请求
    if (loading.value) {
      return
    }

    // 如果已经初始化过且有数据，则不重新加载
    // 但如果是空数据，仍然允许重新加载以确保正确显示no-data-text
    if (isInitialized.value && optionList.value.length > 0) {
      return
    }

    const data = await fetchData()
    optionList.value = data
    isInitialized.value = true

    // 调试信息
    console.log('ApiSelect: 数据加载完成', {
      dataLength: data.length,
      optionListLength: optionList.value.length,
      loading: loading.value,
      isInitialized: isInitialized.value
    })

    // 确保在数据加载完成后触发视图更新
    await nextTick()
  }

  const refresh = async () => {
    clearCache()
    await loadDefaultData()
  }

  const clearCache = () => {
    searchCache.value.clear()
  }

  // 生命周期
  onMounted(() => {
    if (props.autoLoad && !isInitialized.value) {
      nextTick(() => {
        loadDefaultData()
      })
    }
  })

  // 监听 API 配置变化（只监听URL变化，避免不必要的重新加载）
  watch(
    () => props.api.url,
    (newUrl, oldUrl) => {
      if (newUrl !== oldUrl && oldUrl !== undefined) {
        clearCache()
        isInitialized.value = false // 重置初始化状态
        if (props.autoLoad) {
          loadDefaultData()
        }
      }
    }
  )

  // 暴露方法
  defineExpose({
    refresh,
    clearCache,
    loadData: fetchData,
    getOptionList: () => optionList.value
  })
</script>

<style lang="scss" scoped>
  .option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .option-label {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .option-extra {
      margin-left: 8px;
      font-size: 12px;
      color: var(--el-text-color-secondary);
      flex-shrink: 0;
    }
  }

  .empty-content {
    padding: 20px 0;
  }

  .select-footer {
    padding: 8px 12px;
    border-top: 1px solid var(--el-border-color-light);
    text-align: center;
  }
</style>
