<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 数据统计表路由
Route::group('api/crm/crm_statistics', function () {
	Route::get('index', 'app\crm\controller\CrmStatisticsController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmStatisticsController@detail');
	Route::post('add', 'app\crm\controller\CrmStatisticsController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmStatisticsController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmStatisticsController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmStatisticsController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmStatisticsController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmStatisticsController@status');
	Route::post('import', 'app\crm\controller\CrmStatisticsController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmStatisticsController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmStatisticsController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmStatisticsController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);