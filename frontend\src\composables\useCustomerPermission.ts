import { router } from '@/router'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

/**
 * 客户权限验证组合式函数
 */
export const useCustomerPermission = () => {
  const route = useRoute()

  /**
   * 响应式的权限列表
   */
  const authList = computed(() => {
    return (route.meta.authList as Array<{ auth_mark: string }>) || []
  })

  /**
   * 验证功能权限
   * @param permission 权限标识
   */
  const hasAuth = (permission: string): boolean => {
    return authList.value.some((item) => item.auth_mark === permission)
  }

  /**
   * 响应式的按钮权限检查
   * @param permission 功能权限标识
   */
  const hasButtonPermission = computed(() => {
    return (permission: string): boolean => {
      return hasAuth(permission)
    }
  })

  /**
   * 非响应式的按钮权限检查（兼容现有代码）
   * @param permission 功能权限标识
   */
  const hasButtonPermissionSync = (permission: string): boolean => {
    return hasAuth(permission)
  }



  /**
   * 根据操作获取权限标识
   * @param action 操作名称
   */
  const getPermissionByAction = (action: string): string => {
    const permissionMap: Record<string, string> = {
      // 联系人操作
      'add-contact': 'crm:crm_customer_my:add_contact',
      'edit-contact': 'crm:crm_customer_my:edit_contact',
      'delete-contact': 'crm:crm_customer_my:delete_contact',
      'contact-list': 'crm:crm_customer_my:contact_list',

      // 合同操作
      'add-contract': 'crm:crm_customer_my:add_contract',
      'edit-contract': 'crm:crm_customer_my:edit_contract',
      'delete-contract': 'crm:crm_customer_my:delete_contract',
      'contract-detail': 'crm:crm_customer_my:contract_detail',
      'contract-list': 'crm:crm_customer_my:contract_list',
      'submit-approval': 'crm:crm_customer_my:submit_approval',

      // 回款操作
      'add-receivable': 'crm:crm_customer_my:add_receivable',
      'edit-receivable': 'crm:crm_customer_my:edit_receivable',
      'delete-receivable': 'crm:crm_customer_my:delete_receivable',
      'receivable-detail': 'crm:crm_customer_my:receivable_detail',
      'receivable-list': 'crm:crm_customer_my:receivable_list',
      'submit-receivable-approval': 'crm:crm_customer_my:submit_receivable_approval',
      'add-receivable-more': 'crm:crm_customer_my:add_receivable_more',

      // 跟进记录操作
      'add-follow': 'crm:crm_customer_my:add_follow',
      'edit-follow': 'crm:crm_customer_my:edit_follow',
      'delete-follow': 'crm:crm_customer_my:delete_follow',
      'follow-detail': 'crm:crm_customer_my:follow_detail',

      // 客户操作（当前实施）
      'recycle-customer': 'crm:crm_customer_my:recycle_customer'

      // 预留操作（暂不实施）
      // 'transfer-customer': 'crm:crm_customer_my:transfer_customer',
      // 'share-customer': 'crm:crm_customer_my:share_customer'
    }
    return permissionMap[action] || ''
  }

  /**
   * 根据操作获取数据权限操作类型
   * @param action 操作名称
   */
  const getOperationByAction = (action: string): string => {
    const operationMap: Record<string, string> = {
      // 联系人操作
      'add-contact': 'edit',
      'edit-contact': 'edit',
      'delete-contact': 'edit',
      'contact-list': 'view',

      // 合同操作
      'add-contract': 'edit',
      'edit-contract': 'edit',
      'delete-contract': 'edit',
      'contract-detail': 'view',
      'contract-list': 'view',
      'submit-approval': 'edit',

      // 回款操作
      'add-receivable': 'edit',
      'edit-receivable': 'edit',
      'delete-receivable': 'edit',
      'receivable-detail': 'view',
      'receivable-list': 'view',
      'submit-receivable-approval': 'edit',
      'add-receivable-more': 'edit',

      // 跟进记录操作
      'add-follow': 'view',
      'edit-follow': 'edit',
      'delete-follow': 'edit',
      'follow-detail': 'view',

      // 客户操作
      'recycle-customer': 'recycle'
    }
    return operationMap[action] || 'view'
  }

  return {
    hasAuth,
    hasButtonPermission: hasButtonPermissionSync, // 保持向后兼容
    hasButtonPermissionReactive: hasButtonPermission, // 新的响应式版本
    getPermissionByAction,
    getOperationByAction
  }
}
