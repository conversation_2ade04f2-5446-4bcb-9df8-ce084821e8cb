# Workflow模块消息中心对接开发规范

## 📋 概述

本文档规范了Workflow模块与消息中心的对接方式，确保消息通知功能的统一性和可维护性。

## 🎯 核心原则

### 1. 变量命名规范
- **代码传入**: 使用英文键名（如 `title`、`task_name`）
- **模板配置**: 使用英文字段路径（如 `title`、`task_name`）
- **模板内容**: 使用中文变量名（如 `${流程标题}`、`${任务名称}`）
- **用户界面**: 显示中文变量名称（如"流程标题"、"任务名称"）

### 2. 消息类型规范
所有workflow消息类型统一使用 `workflow_` 前缀：
- `workflow_task_approval` - 审批通知
- `workflow_task_approved` - 审批结果通知
- `workflow_task_cc` - 抄送通知
- `workflow_task_urge` - 催办通知
- `workflow_task_transfer` - 转交通知
- `workflow_task_terminated` - 终止通知
- `workflow_task_void` - 作废通知（新增）

## 🛠️ 实现规范

### 1. 代码实现规范

#### 发送消息的标准格式
```php
use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;

// 准备变量 - 使用英文键名
$variables = [
    'title'          => $instance['title'],           // 流程标题
    'task_name'      => $node['nodeName'],           // 任务名称
    'submitter_name' => $instance['submitter_name'], // 提交人
    'created_at'     => $instance['created_at'],     // 提交时间
    'detail_url'     => '/workflow/task/detail?id=' . $taskId
];

// 发送消息
$noticeService = NoticeDispatcherService::getInstance();
$result = $noticeService->send(
    WorkflowStatusConstant::MODULE_NAME,    // 模块名: 'workflow'
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, // 消息类型: 'task_approval'
    $variables,                             // 变量数据
    [$userId]                              // 接收人ID数组
);
```

#### 错误处理规范
```php
try {
    $result = $noticeService->send($module, $action, $variables, $userIds);
    
    if ($result) {
        Log::info("消息发送成功，消息ID: {$result}");
        return true;
    } else {
        Log::error("消息发送失败，返回值为空");
        return false;
    }
} catch (\Exception $e) {
    Log::error("消息发送异常: " . $e->getMessage());
    return false;
}
```

### 2. 模板配置规范

#### 模板基本信息
```sql
INSERT INTO notice_template (
    code,           -- 模板编码: workflow_task_approval
    name,           -- 模板名称: 工作流审批通知
    title,          -- 消息标题: 您有一个待审批任务
    content,        -- 消息内容: 包含${变量名}
    module_code,    -- 模块编码: workflow
    status,         -- 状态: 1启用/0禁用
    send_channels,  -- 发送渠道: site,email
    variables_config -- 变量配置JSON
) VALUES (...);
```

#### 变量配置JSON格式
```json
{
  "variables": [
    {
      "name": "流程标题",           // 用户看到的中文名称
      "code": "title",            // 内部英文编码
      "field": "title",           // 数据提取字段路径
      "required": true,           // 是否必填
      "description": "工作流程标题" // 变量说明
    }
  ]
}
```

## 📊 标准变量映射表

### 通用变量
| 中文名称 | 英文编码 | 字段路径 | 说明 |
|---------|---------|---------|------|
| 流程标题 | title | title | 工作流程标题 |
| 提交人 | submitter_name | submitter_name | 流程提交人姓名 |
| 提交时间 | created_at | created_at | 流程提交时间 |
| 详情链接 | detail_url | detail_url | 流程详情页链接 |

### 审批相关变量
| 中文名称 | 英文编码 | 字段路径 | 说明 |
|---------|---------|---------|------|
| 任务名称 | task_name | task_name | 当前审批任务名称 |
| 审批结果 | result | result | 审批结果(通过/拒绝) |
| 审批人 | approver_name | approver_name | 审批人姓名 |
| 审批时间 | completed_at | completed_at | 审批完成时间 |
| 审批意见 | opinion | opinion | 审批意见内容 |

### 操作相关变量
| 中文名称 | 英文编码 | 字段路径 | 说明 |
|---------|---------|---------|------|
| 催办人 | urger_name | urger_name | 催办人姓名 |
| 催办时间 | urge_time | created_at | 催办时间 |
| 催办原因 | reason | reason | 催办原因 |
| 转交人 | from_user | from_user | 转交人姓名 |
| 接收人 | to_user | to_user | 接收人姓名 |
| 转交时间 | transfer_time | transfer_time | 转交时间 |

### 状态变更变量
| 中文名称 | 英文编码 | 字段路径 | 说明 |
|---------|---------|---------|------|
| 终止结果 | result | result | 终止结果 |
| 终止时间 | terminate_time | terminate_time | 终止时间 |
| 终止人 | terminate_by | terminate_by | 终止人姓名 |
| 终止原因 | reason | reason | 终止原因 |
| 作废结果 | result | result | 作废结果 |
| 作废时间 | void_time | void_time | 作废时间 |
| 作废人 | void_by | void_by | 作废人姓名 |
| 作废原因 | reason | reason | 作废原因 |

## 🔧 开发最佳实践

### 1. 变量准备
```php
// ✅ 正确：使用英文键名
$variables = [
    'title' => $instance['title'],
    'task_name' => $node['nodeName']
];

// ❌ 错误：使用中文键名
$variables = [
    '流程标题' => $instance['title'],
    '任务名称' => $node['nodeName']
];
```

### 2. 常量使用
```php
// ✅ 正确：使用常量
$noticeService->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL,
    $variables,
    $userIds
);

// ❌ 错误：硬编码字符串
$noticeService->send('workflow', 'task_approval', $variables, $userIds);
```

### 3. 日志记录
```php
// 发送前记录
Log::info('发送工作流通知', [
    'type' => $messageType,
    'variables' => $variables,
    'users' => $userIds
]);

// 发送后记录
if ($result) {
    Log::info("消息发送成功，消息ID: {$result}");
} else {
    Log::error('消息发送失败');
}
```

## 🚨 注意事项

### 1. 变量命名
- 代码中传入的变量键名必须与模板配置中的 `field` 字段一致
- 模板内容中的变量名使用中文，格式为 `${中文变量名}`
- 变量配置中的 `code` 字段用于内部映射

### 2. 数据类型
- 时间字段统一使用 `Y-m-d H:i:s` 格式
- 布尔值转换为中文描述（如：通过/拒绝）
- 确保所有必填变量都有值

### 3. 错误处理
- 必须捕获异常并记录日志
- 发送失败不应影响主业务流程
- 提供降级处理机制

## 📝 模板内容规范

### 标题格式
- 审批类：`您有一个待审批任务`、`审批结果通知：${流程标题}`
- 通知类：`您收到一个抄送：${流程标题}`、`催办提醒：${流程标题}`
- 状态类：`流程已终止：${流程标题}`、`流程已作废：${流程标题}`

### 内容格式
```
您的申请 ${流程标题} 已审批完成
审批结果：${审批结果}
审批人：${审批人}
审批时间：${审批时间}
审批意见：${审批意见}
```

## 🔍 调试指南

### 1. 检查模板是否存在
```sql
SELECT * FROM notice_template WHERE code = 'workflow_task_approval';
```

### 2. 检查变量配置
```sql
SELECT variables_config FROM notice_template WHERE code = 'workflow_task_approval';
```

### 3. 检查消息记录
```sql
SELECT * FROM notice_message WHERE code = 'workflow_task_approval' ORDER BY created_at DESC LIMIT 10;
```

### 4. 检查未替换变量
```sql
SELECT id, title, content FROM notice_message 
WHERE (title LIKE '%${%' OR content LIKE '%${%') 
AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY);
```

---

**版本**: 1.0  
**更新时间**: 2025-07-16  
**维护人**: Augment Agent
