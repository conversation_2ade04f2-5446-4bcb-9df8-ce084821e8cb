/**
 * 基于Browser Tools MCP的设计采集器
 * 利用你已有的browser-tools配置
 */

class BrowserToolsCollector {
  constructor() {
    this.collectionData = [];
    this.currentSession = null;
  }

  /**
   * 采集设计方案的主流程
   */
  async collectDesignFromURL(url, componentName, options = {}) {
    console.log(`开始采集: ${componentName} from ${url}`);
    
    try {
      // 1. 导航到目标页面
      await this.navigateToPage(url);
      
      // 2. 等待页面加载完成
      await this.waitForPageLoad(options.waitTime || 3000);
      
      // 3. 截图保存
      const screenshot = await this.takeScreenshot(componentName);
      
      // 4. 提取页面信息
      const pageInfo = await this.extractPageInfo();
      
      // 5. 分析组件结构
      const componentAnalysis = await this.analyzeComponents();
      
      // 6. 保存采集结果
      const collectionResult = {
        componentName,
        url,
        timestamp: new Date().toISOString(),
        screenshot,
        pageInfo,
        componentAnalysis,
        metadata: {
          userAgent: navigator.userAgent,
          viewport: { width: window.innerWidth, height: window.innerHeight }
        }
      };
      
      this.collectionData.push(collectionResult);
      await this.saveToFile(componentName, collectionResult);
      
      console.log(`✅ ${componentName} 采集完成`);
      return collectionResult;
      
    } catch (error) {
      console.error(`❌ 采集失败: ${componentName}`, error);
      return null;
    }
  }

  /**
   * 导航到页面
   */
  async navigateToPage(url) {
    // 使用browser_navigate_Playwright
    return new Promise((resolve, reject) => {
      // 这里需要调用你的browser tools
      // 实际使用时通过MCP调用
      console.log(`导航到: ${url}`);
      setTimeout(resolve, 1000); // 模拟异步操作
    });
  }

  /**
   * 等待页面加载
   */
  async waitForPageLoad(waitTime = 3000) {
    return new Promise(resolve => {
      setTimeout(resolve, waitTime);
    });
  }

  /**
   * 截图
   */
  async takeScreenshot(componentName) {
    // 使用browser_take_screenshot_Playwright
    const filename = `${componentName}-${Date.now()}.png`;
    console.log(`截图保存: ${filename}`);
    return filename;
  }

  /**
   * 提取页面信息
   */
  async extractPageInfo() {
    // 使用browser_snapshot_Playwright获取页面结构
    return {
      title: document.title,
      url: window.location.href,
      elements: this.getPageElements(),
      styles: this.extractCSSVariables(),
      scripts: this.getScriptInfo()
    };
  }

  /**
   * 获取页面元素信息
   */
  getPageElements() {
    const elements = [];
    const targetSelectors = [
      'button', 'input', 'select', 'textarea',
      '.el-button', '.el-input', '.el-table', '.el-form',
      '[class*="btn"]', '[class*="form"]', '[class*="card"]',
      '[class*="modal"]', '[class*="dialog"]'
    ];

    targetSelectors.forEach(selector => {
      const els = document.querySelectorAll(selector);
      els.forEach((el, index) => {
        const rect = el.getBoundingClientRect();
        elements.push({
          selector,
          index,
          tagName: el.tagName,
          className: el.className,
          id: el.id,
          text: el.textContent?.trim().substring(0, 100),
          position: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          },
          styles: this.getElementStyles(el)
        });
      });
    });

    return elements;
  }

  /**
   * 提取CSS变量和设计token
   */
  extractCSSVariables() {
    const rootStyles = getComputedStyle(document.documentElement);
    const cssVariables = {};
    
    // 提取CSS自定义属性
    for (let i = 0; i < rootStyles.length; i++) {
      const property = rootStyles[i];
      if (property.startsWith('--')) {
        cssVariables[property] = rootStyles.getPropertyValue(property).trim();
      }
    }

    return {
      cssVariables,
      commonColors: this.extractCommonColors(),
      typography: this.extractTypography(),
      spacing: this.extractSpacing()
    };
  }

  /**
   * 提取常用颜色
   */
  extractCommonColors() {
    const colors = new Set();
    const elements = document.querySelectorAll('*');
    
    elements.forEach(el => {
      const styles = getComputedStyle(el);
      [
        'color', 'backgroundColor', 'borderColor',
        'borderTopColor', 'borderRightColor', 
        'borderBottomColor', 'borderLeftColor'
      ].forEach(prop => {
        const value = styles[prop];
        if (value && value !== 'rgba(0, 0, 0, 0)' && value !== 'transparent') {
          colors.add(value);
        }
      });
    });

    return Array.from(colors).slice(0, 50); // 限制数量
  }

  /**
   * 提取字体信息
   */
  extractTypography() {
    const typography = new Set();
    const elements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, button, input');
    
    elements.forEach(el => {
      const styles = getComputedStyle(el);
      typography.add({
        fontSize: styles.fontSize,
        fontFamily: styles.fontFamily,
        fontWeight: styles.fontWeight,
        lineHeight: styles.lineHeight,
        letterSpacing: styles.letterSpacing
      });
    });

    return Array.from(typography).slice(0, 20);
  }

  /**
   * 提取间距信息
   */
  extractSpacing() {
    const spacing = new Set();
    const elements = document.querySelectorAll('*');
    
    elements.forEach(el => {
      const styles = getComputedStyle(el);
      ['margin', 'padding'].forEach(prop => {
        const value = styles[prop];
        if (value && value !== '0px') {
          spacing.add(value);
        }
      });
    });

    return Array.from(spacing).slice(0, 30);
  }

  /**
   * 获取元素样式
   */
  getElementStyles(element) {
    const styles = getComputedStyle(element);
    return {
      display: styles.display,
      position: styles.position,
      width: styles.width,
      height: styles.height,
      margin: styles.margin,
      padding: styles.padding,
      border: styles.border,
      borderRadius: styles.borderRadius,
      backgroundColor: styles.backgroundColor,
      color: styles.color,
      fontSize: styles.fontSize,
      fontFamily: styles.fontFamily,
      boxShadow: styles.boxShadow,
      transform: styles.transform,
      transition: styles.transition
    };
  }

  /**
   * 分析组件结构
   */
  async analyzeComponents() {
    return {
      componentCount: this.countComponents(),
      layoutStructure: this.analyzeLayout(),
      interactiveElements: this.findInteractiveElements(),
      designPatterns: this.identifyDesignPatterns()
    };
  }

  /**
   * 统计组件数量
   */
  countComponents() {
    const componentSelectors = {
      buttons: 'button, .btn, .el-button',
      inputs: 'input, .el-input',
      tables: 'table, .el-table',
      forms: 'form, .el-form',
      cards: '.card, .el-card',
      modals: '.modal, .el-dialog'
    };

    const counts = {};
    Object.entries(componentSelectors).forEach(([name, selector]) => {
      counts[name] = document.querySelectorAll(selector).length;
    });

    return counts;
  }

  /**
   * 分析布局结构
   */
  analyzeLayout() {
    const layout = {
      hasHeader: !!document.querySelector('header, .header, .navbar'),
      hasSidebar: !!document.querySelector('aside, .sidebar, .side-nav'),
      hasFooter: !!document.querySelector('footer, .footer'),
      mainContentArea: !!document.querySelector('main, .main, .content'),
      gridSystem: this.detectGridSystem(),
      flexboxUsage: this.detectFlexboxUsage()
    };

    return layout;
  }

  /**
   * 检测网格系统
   */
  detectGridSystem() {
    const gridElements = document.querySelectorAll('[class*="grid"], [class*="col-"], [class*="row"]');
    return {
      hasGrid: gridElements.length > 0,
      gridElements: gridElements.length,
      gridClasses: Array.from(gridElements).map(el => el.className).slice(0, 10)
    };
  }

  /**
   * 检测Flexbox使用
   */
  detectFlexboxUsage() {
    const flexElements = Array.from(document.querySelectorAll('*')).filter(el => {
      const styles = getComputedStyle(el);
      return styles.display === 'flex' || styles.display === 'inline-flex';
    });

    return {
      hasFlexbox: flexElements.length > 0,
      flexElements: flexElements.length
    };
  }

  /**
   * 查找交互元素
   */
  findInteractiveElements() {
    const interactive = {
      clickable: document.querySelectorAll('button, a, [onclick], [role="button"]').length,
      inputs: document.querySelectorAll('input, textarea, select').length,
      hovers: document.querySelectorAll('[onmouseover], [onmouseenter]').length,
      focusable: document.querySelectorAll('[tabindex], input, button, select, textarea, a').length
    };

    return interactive;
  }

  /**
   * 识别设计模式
   */
  identifyDesignPatterns() {
    return {
      hasNavigation: !!document.querySelector('nav, .navigation, .navbar'),
      hasBreadcrumbs: !!document.querySelector('.breadcrumb, [aria-label*="breadcrumb"]'),
      hasPagination: !!document.querySelector('.pagination, .pager'),
      hasSearch: !!document.querySelector('input[type="search"], .search'),
      hasFilters: !!document.querySelector('.filter, .filters'),
      hasTabs: !!document.querySelector('.tabs, .tab-container, [role="tablist"]'),
      hasAccordion: !!document.querySelector('.accordion, [role="accordion"]'),
      hasCarousel: !!document.querySelector('.carousel, .slider, .swiper')
    };
  }

  /**
   * 保存到文件
   */
  async saveToFile(componentName, data) {
    const filename = `design-collection-${componentName}-${Date.now()}.json`;
    const jsonData = JSON.stringify(data, null, 2);
    
    // 这里可以保存到本地文件或发送到服务器
    console.log(`保存数据到: ${filename}`);
    console.log(`数据大小: ${jsonData.length} 字符`);
    
    return filename;
  }

  /**
   * 批量采集
   */
  async batchCollect(urlList) {
    const results = [];
    
    for (const item of urlList) {
      const result = await this.collectDesignFromURL(item.url, item.name, item.options);
      if (result) {
        results.push(result);
      }
      
      // 延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    return results;
  }

  /**
   * 获取所有采集数据
   */
  getAllCollectionData() {
    return this.collectionData;
  }

  /**
   * 清空采集数据
   */
  clearCollectionData() {
    this.collectionData = [];
  }
}

// 使用示例
const collector = new BrowserToolsCollector();

// 采集Element Plus组件
const elementPlusTargets = [
  { 
    name: 'button', 
    url: 'https://element-plus.org/zh-CN/component/button.html',
    options: { waitTime: 3000 }
  },
  { 
    name: 'table', 
    url: 'https://element-plus.org/zh-CN/component/table.html',
    options: { waitTime: 4000 }
  }
];

// 导出供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BrowserToolsCollector;
}

// 全局使用
if (typeof window !== 'undefined') {
  window.BrowserToolsCollector = BrowserToolsCollector;
}
