import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 滚动优化composable
 * 提供滚动事件的节流、防抖和性能优化功能
 */
export function useScrollOptimization() {
  // 是否正在滚动中
  const isScrolling = ref(false)
  let scrollTimer: number | null = null
  
  /**
   * 节流函数 - 限制函数调用频率
   * @param func 要执行的函数
   * @param delay 延迟时间（毫秒）
   */
  const throttle = <T extends (...args: any[]) => any>(func: T, delay: number) => {
    let lastTime = 0
    return function(this: any, ...args: Parameters<T>) {
      const now = Date.now()
      if (now - lastTime >= delay) {
        lastTime = now
        return func.apply(this, args)
      }
    }
  }
  
  /**
   * 防抖函数 - 延迟函数执行，如果在延迟期间再次调用则重新计时
   * @param func 要执行的函数
   * @param delay 延迟时间（毫秒）
   */
  const debounce = <T extends (...args: any[]) => any>(func: T, delay: number) => {
    let timer: number | null = null
    return function(this: any, ...args: Parameters<T>) {
      if (timer) clearTimeout(timer)
      timer = window.setTimeout(() => func.apply(this, args), delay)
    }
  }
  
  /**
   * 滚动开始处理
   */
  const handleScrollStart = () => {
    isScrolling.value = true
    if (scrollTimer) clearTimeout(scrollTimer)
  }
  
  /**
   * 滚动结束处理（防抖）
   */
  const handleScrollEnd = debounce(() => {
    isScrolling.value = false
  }, 150)
  
  /**
   * 创建优化的滚动处理器
   * @param handler 滚动处理函数
   * @param throttleDelay 节流延迟（毫秒）
   */
  const createOptimizedScrollHandler = <T extends (event: Event) => any>(
    handler: T, 
    throttleDelay = 16
  ) => {
    return throttle((event: Event) => {
      handleScrollStart()
      handler(event)
      handleScrollEnd()
    }, throttleDelay) // 约60fps
  }
  
  /**
   * 应用滚动优化到元素
   * @param element 要应用优化的元素
   * @param handler 滚动处理函数
   */
  const applyScrollOptimization = (
    element: HTMLElement | null, 
    handler?: (event: Event) => void
  ) => {
    if (!element) return
    
    // 添加CSS优化
    element.style.transform = 'translateZ(0)'
    element.style.willChange = 'scroll-position'
    
    // 如果提供了处理函数，添加优化的滚动监听
    if (handler) {
      const optimizedHandler = createOptimizedScrollHandler(handler)
      element.addEventListener('scroll', optimizedHandler, { passive: true })
      
      // 返回清理函数
      return () => {
        element.removeEventListener('scroll', optimizedHandler)
      }
    }
  }
  
  /**
   * 性能监控
   */
  const performanceMonitor = {
    startTime: 0,
    scrollCount: 0,
    frameCount: 0,
    lastFrameTime: 0,
    isMonitoring: false,
    rafId: 0,
    
    /**
     * 开始监控
     */
    startMonitoring() {
      this.startTime = performance.now()
      this.scrollCount = 0
      this.frameCount = 0
      this.lastFrameTime = performance.now()
      this.isMonitoring = true
      
      // 监听滚动事件
      document.addEventListener('scroll', this.onScroll.bind(this), { passive: true })
      
      // 监控帧率
      this.monitorFrameRate()
    },
    
    /**
     * 滚动事件处理
     */
    onScroll() {
      this.scrollCount++
      
      // 每100次滚动输出一次性能数据
      if (this.scrollCount % 100 === 0) {
        const avgTime = (performance.now() - this.startTime) / this.scrollCount
        console.log(`平均滚动处理时间: ${avgTime.toFixed(2)}ms`)
      }
    },
    
    /**
     * 监控帧率
     */
    monitorFrameRate() {
      if (!this.isMonitoring) return
      
      const now = performance.now()
      const elapsed = now - this.lastFrameTime
      
      this.frameCount++
      
      // 每秒计算一次帧率
      if (elapsed >= 1000) {
        const fps = Math.round((this.frameCount * 1000) / elapsed)
        console.log(`当前帧率: ${fps} FPS`)
        
        this.frameCount = 0
        this.lastFrameTime = now
      }
      
      this.rafId = requestAnimationFrame(this.monitorFrameRate.bind(this))
    },
    
    /**
     * 停止监控
     */
    stopMonitoring() {
      this.isMonitoring = false
      document.removeEventListener('scroll', this.onScroll.bind(this))
      cancelAnimationFrame(this.rafId)
      
      const totalTime = performance.now() - this.startTime
      console.log(`总滚动时间: ${totalTime.toFixed(2)}ms, 滚动次数: ${this.scrollCount}`)
    }
  }
  
  return {
    isScrolling,
    throttle,
    debounce,
    createOptimizedScrollHandler,
    applyScrollOptimization,
    performanceMonitor
  }
}
