<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\common\lib\upload\UploadService;
use app\system\service\ConfigService;
use think\response\Json;

class UploadController extends BaseAdminController
{
	
	/**
	 * 上传文件
	 */
	public function index(): Json
	{
		$file     = $this->request->file('file');
		$storage  = $this->request->param('storage', 'local');
		$cateId   = $this->request->param('cate_id', 0, 'intval');
		$tenantId = $this->tenantId;
		
		if (empty($file)) {
			return $this->error('请选择文件');
		}
		
		try {
			$fileInfo = [
				'name'     => $file->getOriginalName(),
				'tmp_name' => $file->getPathname(),
				'type'     => $file->getMime(),
				'size'     => $file->getSize(),
			];
			
			$service = new UploadService();
			$result  = $service->uploadFile($fileInfo, $storage, $cateId, $tenantId);
			if ($result) {
				$domain         = request()->domain();
				$result['path'] = $domain . $result['path'];
				$result['url'] = $result['path'];
			}
			return $this->success('上传成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	public function getUploadConfig(): Json
	{
		$uploadInfo = ConfigService::getInstance()
		                           ->getInfo('upload');
		$type       = $uploadInfo['upload_allow_type'] ?? 'local';
		$data       = [
			'upload_allow_type' => $type,
			'upload_allow_ext'  => $uploadInfo['upload_allow_ext'] ?? '',
			'upload_allow_mime' => generateCategoryMap($uploadInfo['upload_allow_ext']),
			'upload_allow_size' => intval($uploadInfo['upload_allow_size'] ?? 2),
			'max_file_count'    => intval($uploadInfo['max_file_count'] ?? 5),
			'domain'            => '',
		];
		
		$typeArr = [
			'qnoss',
			'alioss',
			'txoss'
		];
		if (in_array($type, $typeArr)) {
			$data['domain'] = $uploadInfo[$type . '_domain'] ?? '';
		}
		
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 获取上传Token
	 */
	public function getUploadToken(): Json
	{
		$storage    = $this->request->param('storage/s');  // todo 后续可支持租户独立配置；
		$storageArr = [
			'alioss',
			'txoss',
			'qnoss'
		];
		if (!in_array($storage, $storageArr)) {
			return $this->error('暂不支持该存储方式');
		}
		$cateId   = $this->request->param('cate_id', 0, 'intval');
		$tenantId = $this->tenantId;
		
		$service = new UploadService();
		$result  = $service->getUploadToken($storage, $tenantId, ['cate_id' => $cateId]);
		
		return $this->success('获取成功', $result);
	}
	
	
	/**
	 * todo 需放在不需要权限的接口中
	 * 七牛云回调
	 */
	public function qiniuCallback()
	{
		try {
			$service = new UploadService();
			$result  = $service->handleCallback('qiniu', $this->request->post(), 0);
			
			return json([
				'ret'  => 'success',
				'data' => $result
			]);
		}
		catch (\Exception $e) {
			return json([
				'ret'   => 'fail',
				'error' => $e->getMessage()
			]);
		}
	}
	
	/**
	 * 阿里云回调
	 */
	public function aliyunCallback()
	{
		try {
			$service = new UploadService();
			$result  = $service->handleCallback('aliyun', $this->request->post(), 0);
			
			return json([
				'Status' => 'Ok',
				'data'   => $result
			]);
		}
		catch (\Exception $e) {
			return json([
				'Status'  => 'Error',
				'message' => $e->getMessage()
			]);
		}
	}
	
}