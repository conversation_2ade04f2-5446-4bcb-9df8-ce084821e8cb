-- 每日报价工作流配置SQL
-- 执行此SQL以配置每日报价审批工作流

-- 1. 添加工作流类型
INSERT INTO `workflow_type` (`code`, `name`, `description`, `module_code`, `business_code`, `status`, `created_time`, `updated_time`) 
VALUES ('daily_price_order', '每日报价审批', '每日报价单审批流程', 'daily', 'daily_price_order', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
    `name` = '每日报价审批',
    `description` = '每日报价单审批流程',
    `module_code` = 'daily',
    `business_code` = 'daily_price_order',
    `status` = 1,
    `updated_time` = NOW();

-- 2. 获取工作流类型ID（需要在实际执行时替换为真实ID）
-- SET @workflow_type_id = (SELECT id FROM workflow_type WHERE code = 'daily_price_order' LIMIT 1);

-- 3. 添加工作流定义（简单的一级审批流程）
-- 注意：这里需要根据实际的工作流定义表结构来调整
-- INSERT INTO `workflow_definition` (`type_id`, `name`, `description`, `process_data`, `status`, `created_time`, `updated_time`)
-- VALUES (@workflow_type_id, '每日报价审批流程', '每日报价单标准审批流程', '{"nodeConfig":{"nodeId":"start","nodeName":"发起人","type":"promoter"},"childNode":{"nodeId":"approval1","nodeName":"审批人","type":"approval","props":{"assignedUser":[{"name":"管理员","id":1}]}}}', 1, NOW(), NOW());

-- 4. 检查是否需要添加动态工厂映射
-- 确保 DynamicWorkflowFactory 能够正确映射 daily_price_order 到对应的模型和服务

-- 验证SQL
SELECT 
    wt.id as type_id,
    wt.code,
    wt.name,
    wt.module_code,
    wt.business_code,
    wt.status
FROM workflow_type wt 
WHERE wt.code = 'daily_price_order';
