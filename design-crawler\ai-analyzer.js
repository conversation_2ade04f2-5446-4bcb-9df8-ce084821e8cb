/**
 * AI设计分析器 - 分析采集的设计数据并生成可用方案
 */

class AIDesignAnalyzer {
  constructor() {
    this.analysisResults = [];
    this.designPatterns = new Map();
    this.componentLibrary = new Map();
  }

  /**
   * 分析采集的设计数据
   */
  async analyzeCollectedData(collectionData) {
    console.log('开始AI分析设计数据...');
    
    const analysis = {
      colorPalettes: this.analyzeColorPalettes(collectionData),
      typographyPatterns: this.analyzeTypography(collectionData),
      layoutPatterns: this.analyzeLayouts(collectionData),
      componentPatterns: this.analyzeComponents(collectionData),
      interactionPatterns: this.analyzeInteractions(collectionData),
      designTrends: this.identifyDesignTrends(collectionData)
    };

    // 生成AI提示词模板
    const aiPrompts = this.generateAIPrompts(analysis);
    
    // 生成设计建议
    const designRecommendations = this.generateDesignRecommendations(analysis);

    const result = {
      timestamp: new Date().toISOString(),
      dataSource: collectionData.length,
      analysis,
      aiPrompts,
      designRecommendations
    };

    this.analysisResults.push(result);
    return result;
  }

  /**
   * 分析颜色调色板
   */
  analyzeColorPalettes(data) {
    const colorFrequency = new Map();
    const colorCombinations = [];
    
    data.forEach(item => {
      if (item.designTokens?.colors) {
        item.designTokens.colors.forEach(color => {
          const normalizedColor = this.normalizeColor(color);
          colorFrequency.set(normalizedColor, (colorFrequency.get(normalizedColor) || 0) + 1);
        });
      }
    });

    // 提取最常用的颜色
    const popularColors = Array.from(colorFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([color, frequency]) => ({ color, frequency }));

    // 生成调色板建议
    const palettes = this.generateColorPalettes(popularColors);

    return {
      popularColors,
      palettes,
      colorTrends: this.analyzeColorTrends(popularColors)
    };
  }

  /**
   * 标准化颜色值
   */
  normalizeColor(color) {
    // 将各种颜色格式转换为统一格式
    if (color.startsWith('#')) {
      return color.toLowerCase();
    }
    if (color.startsWith('rgb')) {
      // 转换rgb到hex
      const matches = color.match(/\d+/g);
      if (matches && matches.length >= 3) {
        const hex = matches.slice(0, 3)
          .map(x => parseInt(x).toString(16).padStart(2, '0'))
          .join('');
        return `#${hex}`;
      }
    }
    return color;
  }

  /**
   * 生成调色板
   */
  generateColorPalettes(popularColors) {
    const palettes = [];
    
    // 基于流行色生成不同主题的调色板
    const themes = ['professional', 'modern', 'warm', 'cool', 'vibrant'];
    
    themes.forEach(theme => {
      const palette = this.createThemePalette(theme, popularColors);
      palettes.push(palette);
    });

    return palettes;
  }

  /**
   * 创建主题调色板
   */
  createThemePalette(theme, popularColors) {
    const baseColors = popularColors.slice(0, 5).map(item => item.color);
    
    const themeConfig = {
      professional: {
        primary: '#1890ff',
        secondary: '#52c41a',
        accent: '#faad14',
        neutral: '#8c8c8c',
        background: '#ffffff'
      },
      modern: {
        primary: '#722ed1',
        secondary: '#13c2c2',
        accent: '#fa541c',
        neutral: '#595959',
        background: '#fafafa'
      },
      // 更多主题...
    };

    return {
      name: theme,
      colors: themeConfig[theme] || themeConfig.professional,
      usage: this.generateColorUsageGuide(theme),
      cssVariables: this.generateCSSVariables(themeConfig[theme] || themeConfig.professional)
    };
  }

  /**
   * 生成颜色使用指南
   */
  generateColorUsageGuide(theme) {
    return {
      primary: '主要操作按钮、链接、重要信息标识',
      secondary: '次要操作、成功状态、确认操作',
      accent: '警告信息、重要提醒、特殊标记',
      neutral: '文本内容、边框、分割线',
      background: '页面背景、卡片背景、内容区域'
    };
  }

  /**
   * 生成CSS变量
   */
  generateCSSVariables(colors) {
    const cssVars = {};
    Object.entries(colors).forEach(([key, value]) => {
      cssVars[`--color-${key}`] = value;
    });
    return cssVars;
  }

  /**
   * 分析字体排版模式
   */
  analyzeTypography(data) {
    const fontFamilies = new Map();
    const fontSizes = new Map();
    const fontWeights = new Map();

    data.forEach(item => {
      if (item.pageInfo?.styles?.typography) {
        item.pageInfo.styles.typography.forEach(typo => {
          if (typo.fontFamily) {
            fontFamilies.set(typo.fontFamily, (fontFamilies.get(typo.fontFamily) || 0) + 1);
          }
          if (typo.fontSize) {
            fontSizes.set(typo.fontSize, (fontSizes.get(typo.fontSize) || 0) + 1);
          }
          if (typo.fontWeight) {
            fontWeights.set(typo.fontWeight, (fontWeights.get(typo.fontWeight) || 0) + 1);
          }
        });
      }
    });

    return {
      popularFonts: this.getTopEntries(fontFamilies, 10),
      commonSizes: this.getTopEntries(fontSizes, 15),
      popularWeights: this.getTopEntries(fontWeights, 8),
      typographyScale: this.generateTypographyScale(),
      recommendations: this.generateTypographyRecommendations()
    };
  }

  /**
   * 获取频率最高的条目
   */
  getTopEntries(map, limit) {
    return Array.from(map.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([value, frequency]) => ({ value, frequency }));
  }

  /**
   * 生成字体比例系统
   */
  generateTypographyScale() {
    return {
      scale: 1.25, // Major Third
      baseSize: '16px',
      sizes: {
        'xs': '12px',
        'sm': '14px',
        'base': '16px',
        'lg': '20px',
        'xl': '25px',
        '2xl': '31px',
        '3xl': '39px',
        '4xl': '49px'
      }
    };
  }

  /**
   * 生成字体建议
   */
  generateTypographyRecommendations() {
    return {
      chinese: {
        primary: 'PingFang SC, Hiragino Sans GB, Microsoft YaHei, sans-serif',
        code: 'Fira Code, Consolas, Monaco, monospace'
      },
      english: {
        primary: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
        serif: 'Georgia, Times New Roman, serif',
        code: 'Fira Code, Monaco, Consolas, monospace'
      },
      fallback: 'system-ui, -apple-system, sans-serif'
    };
  }

  /**
   * 分析布局模式
   */
  analyzeLayouts(data) {
    const layoutPatterns = {
      hasHeader: 0,
      hasSidebar: 0,
      hasFooter: 0,
      gridUsage: 0,
      flexboxUsage: 0
    };

    let totalItems = 0;

    data.forEach(item => {
      if (item.componentAnalysis?.layoutStructure) {
        const layout = item.componentAnalysis.layoutStructure;
        totalItems++;
        
        if (layout.hasHeader) layoutPatterns.hasHeader++;
        if (layout.hasSidebar) layoutPatterns.hasSidebar++;
        if (layout.hasFooter) layoutPatterns.hasFooter++;
        if (layout.gridSystem?.hasGrid) layoutPatterns.gridUsage++;
        if (layout.flexboxUsage?.hasFlexbox) layoutPatterns.flexboxUsage++;
      }
    });

    // 计算百分比
    const percentages = {};
    Object.entries(layoutPatterns).forEach(([key, count]) => {
      percentages[key] = totalItems > 0 ? (count / totalItems * 100).toFixed(1) : 0;
    });

    return {
      patterns: layoutPatterns,
      percentages,
      recommendations: this.generateLayoutRecommendations(percentages)
    };
  }

  /**
   * 生成布局建议
   */
  generateLayoutRecommendations(percentages) {
    const recommendations = [];

    if (parseFloat(percentages.hasSidebar) > 60) {
      recommendations.push({
        type: 'layout',
        suggestion: '建议使用侧边栏布局，这是管理后台的主流设计',
        implementation: 'Element Plus Layout组件 + el-aside'
      });
    }

    if (parseFloat(percentages.flexboxUsage) > 70) {
      recommendations.push({
        type: 'css',
        suggestion: 'Flexbox是主流布局方式，建议优先使用',
        implementation: 'display: flex; justify-content: space-between;'
      });
    }

    return recommendations;
  }

  /**
   * 分析组件模式
   */
  analyzeComponents(data) {
    const componentUsage = new Map();
    
    data.forEach(item => {
      if (item.componentAnalysis?.componentCount) {
        Object.entries(item.componentAnalysis.componentCount).forEach(([component, count]) => {
          componentUsage.set(component, (componentUsage.get(component) || 0) + count);
        });
      }
    });

    const popularComponents = this.getTopEntries(componentUsage, 15);
    
    return {
      popularComponents,
      componentRecommendations: this.generateComponentRecommendations(popularComponents),
      elementPlusMapping: this.mapToElementPlus(popularComponents)
    };
  }

  /**
   * 生成组件建议
   */
  generateComponentRecommendations(popularComponents) {
    return popularComponents.map(({ value: component, frequency }) => ({
      component,
      frequency,
      priority: frequency > 50 ? 'high' : frequency > 20 ? 'medium' : 'low',
      suggestion: this.getComponentSuggestion(component)
    }));
  }

  /**
   * 获取组件建议
   */
  getComponentSuggestion(component) {
    const suggestions = {
      buttons: '按钮是最常用的交互组件，建议统一样式和状态',
      tables: '表格用于数据展示，建议支持排序、筛选、分页',
      forms: '表单是数据录入的核心，建议统一验证和布局',
      inputs: '输入框需要清晰的标签和错误提示',
      cards: '卡片适合信息分组展示，建议统一间距和阴影'
    };
    
    return suggestions[component] || '建议保持设计一致性';
  }

  /**
   * 映射到Element Plus组件
   */
  mapToElementPlus(popularComponents) {
    const mapping = {
      buttons: 'el-button',
      inputs: 'el-input',
      tables: 'el-table',
      forms: 'el-form',
      cards: 'el-card',
      modals: 'el-dialog',
      selects: 'el-select',
      checkboxes: 'el-checkbox',
      radios: 'el-radio'
    };

    return popularComponents.map(({ value: component, frequency }) => ({
      component,
      elementPlusComponent: mapping[component] || 'custom',
      frequency,
      documentation: `https://element-plus.org/zh-CN/component/${mapping[component]?.replace('el-', '') || 'overview'}.html`
    }));
  }

  /**
   * 生成AI提示词模板
   */
  generateAIPrompts(analysis) {
    const prompts = {
      colorScheme: this.generateColorPrompt(analysis.colorPalettes),
      typography: this.generateTypographyPrompt(analysis.typographyPatterns),
      layout: this.generateLayoutPrompt(analysis.layoutPatterns),
      components: this.generateComponentPrompt(analysis.componentPatterns)
    };

    return prompts;
  }

  /**
   * 生成颜色相关提示词
   */
  generateColorPrompt(colorAnalysis) {
    const popularColors = colorAnalysis.popularColors.slice(0, 5).map(c => c.color).join(', ');
    
    return {
      template: `请为我设计一个现代化的管理后台配色方案，要求：
- 主色调参考：${popularColors}
- 适合长时间使用，护眼舒适
- 符合中国用户审美习惯
- 支持深色/浅色主题切换
- 提供完整的CSS变量定义

请提供：
1. 完整的颜色体系（主色、辅助色、中性色）
2. 各颜色的使用场景说明
3. CSS变量代码
4. Element Plus主题定制代码`,
      
      variables: {
        popularColors,
        paletteCount: colorAnalysis.palettes.length,
        trendColors: colorAnalysis.colorTrends
      }
    };
  }

  /**
   * 生成字体相关提示词
   */
  generateTypographyPrompt(typographyAnalysis) {
    const popularFonts = typographyAnalysis.popularFonts.slice(0, 3).map(f => f.value).join(', ');
    
    return {
      template: `请为我设计一个适合中文管理后台的字体系统：
- 参考字体：${popularFonts}
- 支持中英文混排
- 适老化友好（字体偏大）
- 层次清晰，易于阅读

请提供：
1. 字体族选择和fallback方案
2. 字体大小比例系统
3. 行高和字间距建议
4. CSS字体定义代码`,
      
      variables: {
        popularFonts,
        commonSizes: typographyAnalysis.commonSizes,
        scale: typographyAnalysis.typographyScale
      }
    };
  }

  /**
   * 识别设计趋势
   */
  identifyDesignTrends(data) {
    return {
      layoutTrends: ['侧边栏导航', 'Flexbox布局', '响应式设计'],
      colorTrends: ['低饱和度色彩', '深色主题支持', '品牌色突出'],
      componentTrends: ['大按钮设计', '卡片式布局', '微交互动效'],
      typographyTrends: ['无衬线字体', '大字号标题', '合理行间距']
    };
  }

  /**
   * 生成设计建议
   */
  generateDesignRecommendations(analysis) {
    return {
      immediate: [
        '建立统一的设计token系统',
        '选择合适的组件库主题',
        '定义标准的布局模板'
      ],
      shortTerm: [
        '创建组件使用规范',
        '建立设计评审流程',
        '制作设计系统文档'
      ],
      longTerm: [
        '建设完整的设计系统',
        '自动化设计token管理',
        '建立设计与开发协作流程'
      ]
    };
  }

  /**
   * 导出分析结果
   */
  exportAnalysis(format = 'json') {
    const latestAnalysis = this.analysisResults[this.analysisResults.length - 1];
    
    if (format === 'json') {
      return JSON.stringify(latestAnalysis, null, 2);
    }
    
    if (format === 'markdown') {
      return this.generateMarkdownReport(latestAnalysis);
    }
    
    return latestAnalysis;
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(analysis) {
    return `# 设计分析报告

## 分析概览
- 分析时间：${analysis.timestamp}
- 数据来源：${analysis.dataSource} 个项目

## 颜色分析
### 流行色彩
${analysis.analysis.colorPalettes.popularColors.map(c => `- ${c.color} (使用频率: ${c.frequency})`).join('\n')}

## 字体分析
### 常用字体
${analysis.analysis.typographyPatterns.popularFonts.map(f => `- ${f.value} (使用频率: ${f.frequency})`).join('\n')}

## 布局分析
### 布局模式使用率
${Object.entries(analysis.analysis.layoutPatterns.percentages).map(([key, value]) => `- ${key}: ${value}%`).join('\n')}

## AI提示词模板
### 颜色设计提示词
\`\`\`
${analysis.aiPrompts.colorScheme.template}
\`\`\`

## 设计建议
### 立即执行
${analysis.designRecommendations.immediate.map(item => `- ${item}`).join('\n')}

### 短期规划
${analysis.designRecommendations.shortTerm.map(item => `- ${item}`).join('\n')}

### 长期规划
${analysis.designRecommendations.longTerm.map(item => `- ${item}`).join('\n')}
`;
  }
}

module.exports = AIDesignAnalyzer;
