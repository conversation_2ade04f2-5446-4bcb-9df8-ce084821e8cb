<?php
declare(strict_types=1);

namespace app\workflow\service\node;

use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\constants\WorkflowOperationConstant;
use app\workflow\service\WorkflowContext;
use app\workflow\service\WorkflowHistoryService;
use app\workflow\service\WorkflowTaskService;
use think\facade\Log;
use app\workflow\service\WorkflowInstanceService;
use app\system\service\AdminService;
use think\facade\Db;

/**
 * 抄送节点处理器
 */
class CcNodeHandler extends AbstractNodeHandler
{
    /**
     * 处理抄送节点
     *
     * @param array           $instance 工作流实例
     * @param array           $node     当前节点
     * @param WorkflowContext $context  工作流上下文
     * @return bool
     */
    protected function handleNode(array $instance, array $node, WorkflowContext $context): bool
    {
        Log::info('处理抄送节点: ' . ($node['nodeName'] ?? '未知') . ', 节点ID: ' . ($node['nodeId'] ?? '未知'));
        Log::info('抄送节点详情: ' . json_encode($node));
        
        // 验证节点类型是否为抄送节点
        if (($node['type'] ?? '') != WorkflowStatusConstant::NODE_TYPE_COPYER && ($node['type'] ?? '') != (string)WorkflowStatusConstant::NODE_TYPE_COPYER) {
            Log::error('节点类型错误，期望抄送节点(type=' . WorkflowStatusConstant::NODE_TYPE_COPYER . ')，实际类型: ' . ($node['type'] ?? '未知'));
            return false;
        }
        
        // 创建抄送任务
        $this->createCcTasks($instance, $node);
        
        // 处理抄送节点的下一个节点（如果有）
        if (!empty($node['childNode'])) {
            Log::info('抄送节点处理完成，继续处理下一节点: ' . ($node['childNode']['nodeName'] ?? '未知'));
            return $this->handleNextNode($instance, $node['childNode'], $context);
        }
        
        // 如果没有下一个节点，完成工作流
        Log::info('抄送节点处理完成，没有下一节点，流程结束');
        return $this->completeWorkflow($instance, $context);
    }
    
    /**
     * 创建抄送任务
     *
     * @param array $instance 工作流实例
     * @param array $node     抄送节点
     * @return void
     */
    protected function createCcTasks(array $instance, array $node): void
    {
        // 获取抄送人列表
        $ccUsers = $node['nodeUserList'] ?? [];
        if (empty($ccUsers)) {
            Log::error('抄送人列表为空: ' . json_encode($node));
            return;
        }
        
        Log::info('开始创建抄送任务，节点: ' . ($node['nodeName'] ?? '未知') . ', 抄送人数: ' . count($ccUsers));
        
        $taskService = WorkflowTaskService::getInstance();
        $historyService = WorkflowHistoryService::getInstance();
        
        // 更新当前节点
        WorkflowInstanceService::getInstance()->edit(
            ['current_node' => $node['nodeId']], 
            ['id' => $instance['id']]
        );
        
        // 记录已处理的用户，避免重复处理
        $processedUsers = [];
        
        foreach ($ccUsers as $user) {
            // 检查用户ID是否有效
            $userId = isset($user['id']) ? intval($user['id']) : 0;
            if ($userId <= 0) {
                Log::warning('无效的抄送用户ID: ' . json_encode($user));
                continue;
            }
            
            // 避免重复处理同一用户
            if (in_array($userId, $processedUsers)) {
                Log::info('跳过重复的抄送用户: ' . $userId);
                continue;
            }
            $processedUsers[] = $userId;
            
            // 检查是否已有任务记录
            $existingTask = $taskService->getModel()
                ->where([
                    'instance_id' => $instance['id'],
                    'node_id' => $node['nodeId'],
                    'approver_id' => $userId,
                    'task_type' => WorkflowStatusConstant::TASK_TYPE_CC
                ])
                ->find();
                
            if ($existingTask) {
                Log::info('该用户已有抄送任务记录，跳过: 用户ID=' . $userId);
                continue;
            }
            
            // 获取用户姓名
            $userName = $this->getUserName($userId);

            // 创建抄送任务
            $taskData = [
                'task_id'       => uniqid('cc_'),
                'instance_id'   => $instance['id'],
                'process_id'    => $instance['process_id'],
                'node_id'       => $node['nodeId'],
                'node_name'     => $node['nodeName'] ?? '抄送节点',
                'node_type'     => 'cc',
                'task_type'     => WorkflowStatusConstant::TASK_TYPE_CC,
                'approver_id'   => $userId,
                'approver_name' => $userName,
                'status'        => 0, // 待处理
                'sort'          => 0,
                'created_at'    => date('Y-m-d H:i:s'),
                'tenant_id'     => $instance['tenant_id'] ?? 0
            ];
            
            Log::info('创建抄送任务数据: ' . json_encode($taskData));
            
            try {
                $taskId = $taskService->getCrudService()
                                     ->add($taskData);
                
                if (!$taskId) {
                    Log::error('抄送任务创建失败，没有返回ID');
                    continue;
                }
                
                Log::info('抄送任务创建成功，ID: ' . $taskId);
                
                // 检查是否已有抄送历史记录
                $existingHistory = $historyService->getModel()
                    ->where([
                        'instance_id' => $instance['id'],
                        'node_id' => $node['nodeId'],
                        'operator_id' => $userId,
                        'operation' => WorkflowOperationConstant::CC
                    ])
                    ->find();
                
                if ($existingHistory) {
                    Log::info('该用户已有抄送历史记录，跳过创建: 用户ID=' . $userId);
                    continue;
                }
                
                // 记录任务历史
                $historyData = [
                    'instance_id'    => $instance['id'],
                    'process_id'     => $instance['process_id'],
                    'task_id'        => $taskData['task_id'],
                    'node_id'        => $node['nodeId'],
                    'node_name'      => $node['nodeName'] ?? '抄送节点',
                    'node_type'      => 'cc',
                    'operator_id'    => $userId,
                    'operation'      => WorkflowOperationConstant::CC,
                    'remark'         => '抄送任务',
                    'operation_time' => date('Y-m-d H:i:s'),
                    'tenant_id'      => $instance['tenant_id'] ?? 0
                ];
                
                $historyId = $historyService->safeAddHistory($historyData);
                Log::info('抄送历史记录创建结果: ' . ($historyId ? '成功，ID: ' . $historyId : '已存在，跳过'));
                
                // 查询任务详情以获取完整信息
                $taskInfo = $taskService->detail($taskId);
                
                // 发送任务通知
                $notificationResult = $this->sendCcNotification($instance, $taskInfo->isEmpty()
                    ? $taskData
                    : $taskInfo->toArray(), $user);
                Log::info('发送抄送通知结果: ' . ($notificationResult
                        ? '成功'
                        : '失败'));
            }
            catch (\Exception $e) {
                Log::error('创建抄送任务异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            }
        }
    }
    
    /**
     * 发送抄送通知
     *
     * @param array $instance 工作流实例
     * @param array $task     任务数据
     * @param array $user     抄送人
     * @return bool
     */
    protected function sendCcNotification(array $instance, array $task, array $user): bool
    {
        try {
            Log::info('准备发送抄送通知, 用户ID: ' . $user['id'] . ', 用户名: ' . $user['name']);
            
            // 对接消息中心
            $noticeService = NoticeDispatcherService::getInstance();
            
            // 准备变量 - 使用扁平结构
            $variables = [
                'title' => $instance['title'],                 // 流程标题
                'submitter_name' => $instance['submitter_name'], // 提交人姓名
                'node_name' => $task['node_name'],             // 节点名称
                'created_at' => $instance['created_at'],       // 创建时间
                'cc_time' => date('Y-m-d H:i:s'),             // 抄送时间
                'detail_url' => '/workflow/task/detail?id=' . $task['id']  // 详情链接
            ];
            
            Log::info('发送抄送通知, 通知变量: ' . json_encode($variables));
            
            // 发送抄送通知
            $noticeService->send(WorkflowStatusConstant::MODULE_NAME, WorkflowStatusConstant::MESSAGE_TASK_CC, $variables, [$user['id']]);
            
            Log::info('抄送通知发送成功');
            return true;
        }
        catch (\Exception $e) {
            Log::error('发送抄送通知失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * 完成工作流
     *
     * @param array $instance 工作流实例
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    protected function completeWorkflow(array $instance, WorkflowContext $context): bool
    {
        Log::info('抄送节点完成工作流: 实例ID=' . $instance['id']);
        
        // 调用工作流引擎的完成方法
        $engine = new \app\workflow\service\WorkflowEngine();
        return $engine->completeWorkflow($instance);
    }

    /**
     * 获取用户姓名
     *
     * @param int $userId 用户ID
     * @return string
     */
    private function getUserName(int $userId): string
    {
        try {
            $admin = Db::name('system_admin')
                ->where('id', $userId)
                ->field('real_name, username')
                ->find();

            if ($admin) {
                return $admin['real_name'] ?: $admin['username'];
            }

            return '未知用户';
        } catch (\Exception $e) {
            Log::error('获取用户姓名失败: ' . $e->getMessage());
            return '未知用户';
        }
    }
}