<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 线索池相关
Route::group('api/crm/crm_lead_pool', function () {
	Route::get('index', 'app\crm\controller\CrmLeadPoolController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmLeadPoolController@detail');
	Route::post('claimLead/:id', 'app\crm\controller\CrmLeadPoolController@claimLead');
	Route::post('assign/:id', 'app\crm\controller\CrmLeadPoolController@assign');
	Route::get('options', 'app\crm\controller\CrmLeadPoolController@options');
	//	Route::post('releaseToPool/:id', 'app\crm\controller\CrmLeadPoolController@releaseToPool');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);