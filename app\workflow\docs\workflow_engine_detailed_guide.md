# 工作流引擎详细技术文档

## 一、概述

工作流引擎是处理业务流程自动化的核心组件，负责流程定义解析、节点流转控制、任务分配、消息通知等功能。本文档详细说明工作流引擎的架构设计、核心功能和与消息中心的集成方式。

## 二、系统架构

### 2.1 核心组件架构

```
工作流引擎架构
├── 控制层 (Controller)
│   ├── TaskController - 任务操作控制器
│   ├── ApplicationController - 申请控制器
│   └── DefinitionController - 流程定义控制器
├── 服务层 (Service)
│   ├── WorkflowEngine - 工作流引擎核心
│   ├── WorkflowEngineService - 引擎服务封装
│   ├── WorkflowTaskService - 任务服务
│   ├── WorkflowInstanceService - 实例服务
│   └── WorkflowHistoryService - 历史记录服务
├── 节点处理层 (Node Handlers)
│   ├── PromoterNodeHandler - 发起人节点处理器
│   ├── ApprovalNodeHandler - 审批节点处理器
│   ├── CcNodeHandler - 抄送节点处理器
│   └── ConditionNodeHandler - 条件节点处理器
├── 数据层 (Model)
│   ├── WorkflowDefinition - 流程定义
│   ├── WorkflowInstance - 流程实例
│   ├── WorkflowTask - 工作任务
│   └── WorkflowHistory - 历史记录
└── 集成层 (Integration)
    ├── NoticeDispatcherService - 消息通知
    ├── FormServiceFactory - 表单服务工厂
    └── WorkflowValidator - 流程验证器
```

### 2.2 数据库表结构

#### workflow_definition (流程定义表)
```sql
CREATE TABLE `workflow_definition` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '流程定义ID',
    `name` varchar(100) NOT NULL COMMENT '流程名称',
    `type_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '流程类型ID',
    `flow_config` text COMMENT '流程配置JSON',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0=禁用,1=启用',
    `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流程定义表';
```

#### workflow_instance (流程实例表)
```sql
CREATE TABLE `workflow_instance` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '实例ID',
    `process_id` varchar(64) NOT NULL COMMENT '流程实例ID',
    `definition_id` bigint(20) unsigned NOT NULL COMMENT '流程定义ID',
    `title` varchar(200) NOT NULL COMMENT '流程标题',
    `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID',
    `form_data` text COMMENT '表单数据JSON',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0已撤回，1审批中，2已通过，3已驳回，4已终止',
    `current_node` varchar(64) DEFAULT NULL COMMENT '当前节点ID',
    `submitter_id` bigint(20) unsigned NOT NULL COMMENT '提交人ID',
    `submitter_name` varchar(50) NOT NULL COMMENT '提交人姓名',
    `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_process_id` (`process_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流程实例表';
```

#### workflow_task (工作任务表)
```sql
CREATE TABLE `workflow_task` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `task_id` varchar(64) NOT NULL COMMENT '任务ID',
    `instance_id` bigint(20) unsigned NOT NULL COMMENT '流程实例ID',
    `process_id` varchar(64) NOT NULL COMMENT '流程实例ID',
    `node_id` varchar(64) NOT NULL COMMENT '节点ID',
    `node_name` varchar(100) NOT NULL COMMENT '节点名称',
    `node_type` varchar(30) NOT NULL COMMENT '节点类型',
    `task_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务类型：0审批任务，1抄送任务',
    `approver_id` bigint(20) unsigned NOT NULL COMMENT '审批人ID',
    `approver_name` varchar(50) NOT NULL COMMENT '审批人姓名',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1已同意，2已驳回，3已转交，4已终止，5已撤回',
    `opinion` varchar(500) DEFAULT NULL COMMENT '审批意见',
    `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
    `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流程任务表';
```

#### workflow_history (历史记录表)
```sql
CREATE TABLE `workflow_history` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `instance_id` bigint(20) unsigned NOT NULL COMMENT '流程实例ID',
    `process_id` varchar(64) NOT NULL COMMENT '流程实例ID',
    `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
    `node_id` varchar(64) NOT NULL COMMENT '节点ID',
    `node_name` varchar(100) NOT NULL COMMENT '节点名称',
    `node_type` varchar(30) NOT NULL COMMENT '节点类型',
    `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
    `operation` tinyint(1) NOT NULL COMMENT '操作类型:1=同意,2=驳回,3=转交,4=终止,5=撤回,6=催办,7=抄送,8=流程开始,9=流程结束',
    `opinion` varchar(500) DEFAULT NULL COMMENT '操作意见',
    `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `tenant_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流程历史记录表';
```

## 三、核心功能模块

### 3.1 工作流引擎核心 (WorkflowEngine)

#### 主要职责
- 流程推进控制
- 节点流转逻辑
- 审批模式处理
- 条件分支评估
- 流程状态管理

#### 核心方法

```php
class WorkflowEngine
{
    /**
     * 处理审批结果
     * @param array $instance 工作流实例
     * @param array $task 当前任务
     * @param bool $isApproved 是否通过
     * @return bool
     */
    public function processApprovalResult(array $instance, array $task, bool $isApproved): bool;
    
    /**
     * 继续工作流
     * @param array $instance 工作流实例
     * @param array $currentNode 当前节点
     * @return bool
     */
    public function continueWorkflow(array $instance, array $currentNode): bool;
    
    /**
     * 完成工作流
     * @param array $instance 工作流实例
     * @return bool
     */
    public function completeWorkflow(array $instance): bool;
    
    /**
     * 驳回工作流
     * @param array $instance 工作流实例
     * @return bool
     */
    public function rejectWorkflow(array $instance): bool;
}
```

### 3.2 任务服务 (WorkflowTaskService)

#### 主要功能
- 任务创建和管理
- 审批操作处理
- 任务状态更新
- 消息通知发送

#### 关键操作

```php
class WorkflowTaskService
{
    /**
     * 审批通过任务
     * @param array $params 请求参数
     * @return bool
     */
    public function approveTask(array $params): bool;
    
    /**
     * 驳回任务
     * @param array $params 请求参数
     * @return bool
     */
    public function rejectTask(array $params): bool;
    
    /**
     * 转交任务
     * @param int $taskId 任务ID
     * @param int $targetUserId 目标用户ID
     * @param int $fromUserId 原用户ID
     * @param string $remark 转交备注
     * @return bool
     */
    public function transferTask(int $taskId, int $targetUserId, int $fromUserId, string $remark): bool;
}
```

### 3.3 节点处理器 (Node Handlers)

#### 节点处理器工厂
```php
class NodeHandlerFactory
{
    public static function create(int $nodeType): AbstractNodeHandler
    {
        return match ($nodeType) {
            WorkflowStatusConstant::NODE_TYPE_PROMOTER => new PromoterNodeHandler(),
            WorkflowStatusConstant::NODE_TYPE_APPROVER => new ApprovalNodeHandler(),
            WorkflowStatusConstant::NODE_TYPE_COPYER => new CcNodeHandler(),
            WorkflowStatusConstant::NODE_TYPE_CONDITION => new ConditionNodeHandler(),
            default => throw new \InvalidArgumentException("不支持的节点类型: {$nodeType}")
        };
    }
}
```

#### 审批节点处理器
```php
class ApprovalNodeHandler extends AbstractNodeHandler
{
    /**
     * 处理审批节点
     * @param array $node 节点配置
     * @param array $instance 工作流实例
     * @param WorkflowContext $context 工作流上下文
     * @return bool
     */
    public function handleNode(array $node, array $instance, WorkflowContext $context): bool;
    
    /**
     * 创建审批任务
     * @param array $node 节点配置
     * @param array $instance 工作流实例
     * @param array $user 审批人信息
     * @return bool
     */
    protected function createApprovalTask(array $node, array $instance, array $user): bool;
}
```

## 四、与消息中心集成

### 4.1 集成架构

工作流引擎通过 `NoticeDispatcherService` 与消息中心集成，实现统一的消息通知功能。

```php
// 消息发送示例
NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,           // 模块：workflow
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL, // 动作：task_approval
    $variables,                                    // 变量数据
    [$userId]                                      // 接收人ID数组
);
```

### 4.2 消息发送时机

#### 1. 审批任务创建通知
**触发时机**：创建审批任务时
**发送位置**：`ApprovalNodeHandler::sendApprovalNotification()`
**模板编码**：`workflow_task_approval`

```php
$variables = [
    'task_name'      => $node['nodeName'],
    'title'          => $instance['title'],
    'submitter_name' => $instance['submitter_name'],
    'created_at'     => $instance['created_at'],
    'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id']
];
```

#### 2. 审批结果通知
**触发时机**：审批完成后
**发送位置**：`WorkflowEngine::sendResultNotification()`
**模板编码**：`workflow_task_approved`

```php
$variables = [
    'title'       => $instance['title'],
    'result'      => $isApproved ? '已通过' : '已驳回',
    'submit_time' => $instance['created_at'],
    'finish_time' => date('Y-m-d H:i:s'),
    'detail_url'  => '/workflow/detail?id=' . $instance['id']
];
```

#### 3. 抄送通知
**触发时机**：创建抄送任务时
**发送位置**：`CcNodeHandler::sendCcNotification()`
**模板编码**：`workflow_task_cc`

```php
$variables = [
    'title'          => $instance['title'],
    'submitter_name' => $instance['submitter_name'],
    'node_name'      => $node['nodeName'],
    'created_at'     => $instance['created_at'],
    'cc_time'        => date('Y-m-d H:i:s'),
    'detail_url'     => '/workflow/task/detail?id=' . $taskId
];
```

#### 4. 催办通知
**触发时机**：用户发起催办时
**发送位置**：`WorkflowTaskService::sendUrgeNotification()`
**模板编码**：`workflow_task_urge`

```php
$variables = [
    'title'      => $instance['title'],
    'task_name'  => $task['node_name'],
    'urger_name' => $urgerName,
    'created_at' => date('Y-m-d H:i:s'),
    'reason'     => $urgeReason
];
```

#### 5. 转交通知
**触发时机**：任务转交时
**发送位置**：`WorkflowTaskService::sendTransferNotification()`
**模板编码**：`workflow_task_transfer`

```php
$variables = [
    'title'         => $instance['title'],
    'node_name'     => $task['node_name'],
    'from_user'     => $fromUserName,
    'to_user'       => $toUser['realname'],
    'transfer_time' => date('Y-m-d H:i:s'),
    'detail_url'    => '/workflow/task/detail?id=' . $task['id']
];
```

#### 6. 终止通知
**触发时机**：流程终止时
**发送位置**：`WorkflowEngine::sendTerminationNotification()`
**模板编码**：`workflow_task_terminated`

```php
$variables = [
    'title'          => $instance['title'],
    'result'         => '已终止',
    'submit_time'    => $instance['created_at'],
    'terminate_time' => date('Y-m-d H:i:s'),
    'terminate_by'   => $operatorName,
    'reason'         => $reason,
    'detail_url'     => '/workflow/detail?id=' . $instance['id']
];
```

### 4.3 消息常量定义

```php
class WorkflowStatusConstant
{
    const MODULE_NAME = 'workflow';
    
    // 消息通知类型
    const MESSAGE_TASK_APPROVAL = 'task_approval';     // 任务审批通知
    const MESSAGE_TASK_APPROVED = 'task_approved';     // 审批结果通知
    const MESSAGE_TASK_CC = 'task_cc';                 // 抄送通知
    const MESSAGE_TASK_URGE = 'task_urge';             // 催办通知
    const MESSAGE_TASK_TRANSFER = 'task_transfer';     // 转交通知
    const MESSAGE_APPROVAL_REQUEST = 'request';        // 申请通知
    const MESSAGE_TASK_TERMINATED = 'task_terminated'; // 终止通知
}
```

## 五、流程执行流程

### 5.1 审批流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Controller as TaskController
    participant Service as WorkflowTaskService
    participant Engine as WorkflowEngine
    participant Notice as NoticeDispatcherService
    
    User->>Controller: 提交审批
    Controller->>Service: approveTask()
    Service->>Service: 更新任务状态
    Service->>Service: 记录审批历史
    Service->>Engine: processApprovalResult()
    Engine->>Engine: 判断审批模式
    Engine->>Engine: 继续流程或完成流程
    Engine->>Notice: 发送结果通知
    Service-->>Controller: 返回结果
    Controller-->>User: 响应结果
```

### 5.2 节点流转逻辑

1. **获取当前节点**：从流程实例中获取当前节点ID
2. **查找节点配置**：在流程定义中查找节点详细配置
3. **创建节点处理器**：根据节点类型创建对应的处理器
4. **执行节点逻辑**：处理器执行具体的节点逻辑
5. **流转到下一节点**：根据流程配置流转到下一个节点

### 5.3 审批模式处理

#### 任意一人通过 (APPROVAL_MODE_ANY)
- 一人通过即可继续流程
- 取消其他待处理任务

#### 所有人通过 (APPROVAL_MODE_ALL)
- 所有人都通过才能继续流程
- 任何人驳回则流程结束

#### 按顺序审批 (APPROVAL_MODE_SEQUENCE)
- 按照指定顺序依次审批
- 当前审批人完成后流转给下一人

## 六、异常处理和日志

### 6.1 异常处理策略

1. **数据库事务**：关键操作使用事务确保数据一致性
2. **消息发送容错**：消息发送失败不影响主流程
3. **历史记录防重**：使用 `safeAddHistory` 避免重复记录
4. **状态检查**：操作前检查任务和实例状态

### 6.2 关键日志记录

```php
// 流程推进日志
Log::info('处理审批结果: 实例ID=' . $instance['id'] . ', 任务ID=' . $task['id']);

// 节点处理日志
Log::info('处理节点: ' . $node['nodeName'] . ', 节点ID: ' . $node['nodeId']);

// 消息发送日志
Log::info('发送审批通知: 用户ID=' . $userId);

// 异常日志
Log::error('审批通过失败: ' . $e->getMessage());
```

## 七、性能优化

### 7.1 数据库优化

1. **索引优化**：为常用查询字段添加索引
2. **批量操作**：使用批量插入减少数据库交互
3. **分页查询**：大数据量查询使用分页

### 7.2 缓存策略

1. **流程定义缓存**：缓存流程配置减少数据库查询
2. **用户信息缓存**：缓存审批人信息
3. **模板缓存**：缓存消息模板配置

## 八、扩展和定制

### 8.1 自定义节点处理器

```php
class CustomNodeHandler extends AbstractNodeHandler
{
    public function handleNode(array $node, array $instance, WorkflowContext $context): bool
    {
        // 自定义节点处理逻辑
        return true;
    }
}
```

### 8.2 自定义表单服务

```php
class CustomFormService implements FormServiceInterface
{
    public function updateFormStatus(int $businessId, int $status): bool
    {
        // 自定义表单状态更新逻辑
        return true;
    }
}
```

## 九、总结

工作流引擎通过模块化设计实现了灵活的流程控制功能，与消息中心的集成提供了完整的通知体系。正确使用工作流引擎需要：

1. **理解流程配置结构**
2. **掌握节点处理机制**
3. **配置正确的消息模板**
4. **做好异常处理和监控**

遵循本文档的设计原则和最佳实践，可以构建稳定可靠的工作流系统。
