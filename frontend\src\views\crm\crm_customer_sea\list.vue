<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { SearchFormItem } from '@/types/search-form'
  import { BgColorEnum } from '@/enums/appEnum'
  import { CrmCustomerSeaApi } from '@/api/crm/crmCustomerSea'
  // import { AdminApi } from '@/api/adminApi'
  import { ApiStatus } from '@/utils/http/status'
  import { useAuth } from '@/composables/useAuth'
  import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
  import {
    ElCard,
    ElButton,
    ElTag,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElMessage,
    ElMessageBox,
    ElDialog,
    ElDescriptions,
    ElDescriptionsItem,
    ElLink,
    ElForm,
    ElFormItem,
    ElDatePicker
  } from 'element-plus'
  import { User, Lock, Unlock, Edit, Delete } from '@element-plus/icons-vue'
  import CustomerSeaFormDialog from './form-dialog.vue'

  // 权限验证
  const { hasAuth } = useAuth()

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 客户详情弹出框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 批量选择
  const selectedRows = ref<any[]>([])

  // 锁定对话框
  const lockDialogVisible = ref(false)
  const lockFormData = ref({
    lock_expire_time: ''
  })
  const currentLockRow = ref<any>(null)

  // 分配对话框
  const assignDialogVisible = ref(false)
  const assignFormData = ref({
    owner_user_id: ''
  })
  const currentAssignRow = ref<any>(null)

  // 定义表单搜索初始值
  const initialSearchState = {
    customer_name: '',
    level: null,
    status: null,
    // into_sea_time_start: '',
    // into_sea_time_end: '',
    lock_status: null
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 搜索表单配置
  const formItems: SearchFormItem[] = [
    {
      prop: 'customer_name',
      type: 'input',
      label: '客户名称',
      config: {
        clearable: true
      }
    },
    {
      prop: 'level',
      type: 'select',
      label: '客户级别',
      options: [
        { label: '普通客户', value: 1 },
        { label: '重要客户', value: 2 },
        { label: '战略客户', value: 3 }
      ],
      config: {
        clearable: true
      }
    },
    /*{
      prop: 'status',
      type: 'select',
      label: '客户状态',
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ],
      config: {
        clearable: true
      }
    },*/
    {
      prop: 'lock_status',
      type: 'select',
      label: '锁定状态',
      options: [
        { label: '未锁定', value: 0 },
        { label: '已锁定', value: 1 }
      ],
      config: {
        clearable: true
      }
    }
  ]

  // 表格列配置（声明式渲染，不再需要动态列配置）

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 处理每页条数变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    currentPage.value = 1
    getTableData()
  }

  // 处理页码变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmCustomerSeaApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示客户详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await CrmCustomerSeaApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      } else {
        ElMessage.error(res.message || '获取详情失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 认领客户
  const handleClaim = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        `确定要认领客户"${row.customer_name}"吗？认领后该客户将分配给您负责。`,
        '确认认领',
        {
          confirmButtonText: '确定认领',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const res = await CrmCustomerSeaApi.claim(row.id)
      if (res.code === ApiStatus.success) {
        ElMessage.success('认领成功')
        await getTableData()
      } else {
        ElMessage.error(res.message || '认领失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('认领失败')
      }
    }
  }

  // 批量认领
  /*const handleBatchClaim = async () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('请选择要认领的客户')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要认领选中的 ${selectedRows.value.length} 个客户吗？`,
        '批量认领确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      batchLoading.value = true
      const customerIds = selectedRows.value.map((row) => row.id)
      const res = await CrmCustomerSeaApi.batchClaim(customerIds)

      if (res.code === ApiStatus.success) {
        ElMessage.success('批量认领成功')
        selectedRows.value = []
        await getTableData()
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量认领失败')
      }
    } finally {
      batchLoading.value = false
    }
  }*/

  // 表格选择变化
  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const res = await CrmCustomerSeaApi.export(formFilters)

      // 创建下载链接
      const blob = new Blob([res as any], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `公海客户_${new Date().getTime()}.xlsx`
      link.click()
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } catch {
      ElMessage.error('导出失败')
    }
  }

  // 格式化锁定状态
  const formatLockStatus = (
    row: any
  ): {
    text: string
    type: 'success' | 'primary' | 'warning' | 'info' | 'danger'
  } => {
    if (row.lock_status === 1) {
      const expireTime = new Date(row.lock_expire_time)
      const now = new Date()
      if (expireTime > now) {
        return { text: '已锁定', type: 'warning' }
      } else {
        return { text: '锁定已过期', type: 'info' }
      }
    }
    return { text: '未锁定', type: 'success' }
  }

  // 格式化客户级别
  const formatLevel = (
    level: number
  ): {
    text: string
    type: 'success' | 'primary' | 'warning' | 'info' | 'danger'
  } => {
    const levelMap: Record<
      number,
      { text: string; type: 'success' | 'primary' | 'warning' | 'info' | 'danger' }
    > = {
      1: { text: '普通客户', type: 'info' },
      2: { text: '重要客户', type: 'warning' },
      3: { text: '战略客户', type: 'danger' }
    }
    return levelMap[level] || { text: '未知', type: 'info' }
  }

  // 获取完整地址
  const getFullAddress = (row: any) => {
    const parts = [row.region_province, row.region_city, row.region_district, row.address].filter(
      Boolean
    )
    return parts.length > 0 ? parts.join(' ') : '-'
  }

  // 判断是否可以锁定
  const canLock = (row: any) => {
    // 客户必须未锁定
    return row.lock_status !== 1
  }

  // 判断是否可以解锁
  const canUnlock = (row: any) => {
    // 客户必须已锁定且锁定未过期
    if (row.lock_status !== 1) return false
    if (!row.lock_expire_time) return true

    const expireTime = new Date(row.lock_expire_time)
    const now = new Date()
    return expireTime > now
  }

  // 判断是否可以编辑
  const canEdit = (row: any) => {
    // 根据业务规则判断，这里简单示例
    return row.lock_status !== 1 || row.owner_user_id === getCurrentUserId()
  }

  // 判断是否可以删除
  const canDelete = (row: any) => {
    // 根据业务规则判断，这里简单示例
    return row.lock_status !== 1
  }

  // 判断是否有任何操作可用
  const hasAnyAction = (row: any) => {
    return canEdit(row) || canDelete(row)
  }

  // 获取当前用户ID（示例方法）
  const getCurrentUserId = () => {
    // 这里应该从用户状态或token中获取当前用户ID
    return 1 // 示例返回
  }

  // 显示锁定对话框
  const showLockDialog = (row: any) => {
    currentLockRow.value = row
    lockFormData.value.lock_expire_time = ''
    lockDialogVisible.value = true
  }

  // 确认锁定
  const confirmLock = async () => {
    if (!lockFormData.value.lock_expire_time) {
      ElMessage.warning('请选择锁定到期时间')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要锁定客户"${currentLockRow.value.customer_name}"到 ${lockFormData.value.lock_expire_time} 吗？`,
        '确认锁定',
        {
          confirmButtonText: '确定锁定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const res = await CrmCustomerSeaApi.lock(
        currentLockRow.value.id,
        lockFormData.value.lock_expire_time
      )
      if (res.code === ApiStatus.success) {
        ElMessage.success('锁定成功')
        lockDialogVisible.value = false
        await getTableData()
      } else {
        ElMessage.error(res.message || '锁定失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('锁定失败')
      }
    }
  }

  // 显示分配对话框
  const showAssignDialog = (row: any) => {
    currentAssignRow.value = row
    assignFormData.value.owner_user_id = ''
    assignDialogVisible.value = true
  }

  // 确认分配
  const confirmAssign = async () => {
    if (!assignFormData.value.owner_user_id) {
      ElMessage.warning('请选择分配的用户')
      return
    }

    try {
      const res = await CrmCustomerSeaApi.assign(
        currentAssignRow.value.id,
        assignFormData.value.owner_user_id
      )
      if (res.code === ApiStatus.success) {
        ElMessage.success('分配成功')
        assignDialogVisible.value = false
        await getTableData()
      } else {
        ElMessage.error(res.message || '分配失败')
      }
    } catch {
      ElMessage.error('分配失败')
    }
  }

  // 处理更多操作
  const handleMoreAction = async (command: string, row: any) => {
    switch (command) {
      case 'assign':
        showAssignDialog(row)
        break
      case 'lock':
        showLockDialog(row)
        break
      case 'unlock':
        await handleUnlock(row)
        break
      case 'edit':
        handleEdit(row)
        break
      case 'delete':
        await handleDelete(row)
        break
      default:
        console.warn('未知的操作命令:', command)
    }
  }

  // 解锁客户
  const handleUnlock = async (row: any) => {
    try {
      await ElMessageBox.confirm('确定要解锁该客户吗？', '确认解锁', {
        type: 'warning'
      })

      // 调用解锁API
      const res = await CrmCustomerSeaApi.unlock(row.id)
      if (res.code === ApiStatus.success) {
        ElMessage.success('解锁成功')
        await getTableData()
      }
    } finally {
      console.log('aa')
    }
  }

  // 编辑表单对话框
  const editDialogVisible = ref(false)
  const currentEditId = ref<number | string>('')

  // 编辑客户
  const handleEdit = (row: any) => {
    currentEditId.value = row.id
    editDialogVisible.value = true
  }

  // 处理编辑成功
  const handleEditSuccess = () => {
    editDialogVisible.value = false
    getTableData()
  }

  // 删除客户
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除客户"${row.customer_name}"吗？此操作不可恢复！`,
        '确认删除',
        {
          type: 'error',
          confirmButtonText: '确定删除',
          cancelButtonText: '取消'
        }
      )

      // 调用删除API
      const res = await CrmCustomerSeaApi.delete(row.id)
      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  // 计算是否可以认领
  const canClaim = (row: any) => {
    if (row.lock_status === 1) {
      const expireTime = new Date(row.lock_expire_time)
      const now = new Date()
      return expireTime <= now // 锁定已过期才能认领
    }
    return true // 未锁定可以认领
  }

  // 组件挂载时获取数据
  onMounted(() => {
    getTableData()
  })
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-customer-sea-page crm-table-optimized customer-module" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columns="[]" @refresh="handleRefresh">
          <template #left>
            <!--            <ElButton
                          type="primary"
                          @click="handleBatchClaim"
                          :loading="batchLoading"
                          :disabled="selectedRows.length === 0"
                        >
                          批量认领 ({{ selectedRows.length }})
                        </ElButton>-->

            <!--            <ElButton type="warning" @click="handleExport">导出</ElButton>-->
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :data="tableData"
          :loading="loading"
          :total="total"
          :current-page="currentPage"
          :page-size="pageSize"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <!-- 选择列 -->
          <ElTableColumn type="selection" width="55" />

          <!-- 数据列 - 分组优化显示 -->
          <!-- ID列 -->
          <ElTableColumn prop="id" label="ID" width="80" show-overflow-tooltip />

          <!-- 1. 基础信息组 -->
          <ElTableColumn label="基础信息" min-width="280" align="left">
            <template #default="scope">
              <div class="basic-info-group">
                <div class="info-row main-info">
                  <span class="info-label">客户:</span>
                  <span class="info-value customer_name crm-text-primary">{{
                    scope.row.customer_name || '-'
                  }}</span>
                </div>
                <div class="info-row secondary-info">
                  <span class="info-label">级别:</span>
                  <ElTag :type="formatLevel(scope.row.level).type" size="small">
                    {{ formatLevel(scope.row.level).text }}
                  </ElTag>
                  <span class="info-separator">|</span>
                  <span class="info-label">行业:</span>
                  <span class="info-value industry crm-text-secondary">{{
                    scope.row.industry || '-'
                  }}</span>
                </div>
                <div class="info-row secondary-info">
                  <span class="info-label">来源:</span>
                  <span class="info-value source crm-text-secondary">{{
                    scope.row.source || '-'
                  }}</span>
                  <span class="info-separator">|</span>
                  <span class="info-label">电话:</span>
                  <span class="info-value phone crm-text-info">{{ scope.row.phone || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 2. 地区信息组 -->
          <ElTableColumn label="详细地址" width="220" align="left">
            <template #default="scope">
              <div class="region-info-group">
                <div class="region-item">
                  <span class="region-value crm-text-secondary">{{
                    getFullAddress(scope.row)
                  }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 3. 时间信息组 -->
          <ElTableColumn label="创建时间" width="180" align="left">
            <template #default="scope">
              <div class="time-info-group">
                <div class="time-item">
                  <span class="time-value time-info">{{ scope.row.created_at || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 4. 状态信息组 -->
          <ElTableColumn label="客户状态" width="160" align="center">
            <template #default="scope">
              <ElTag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 1 ? '启用' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn label="锁定" width="160" align="center">
            <template #default="scope">
              <ElTag :type="formatLockStatus(scope.row).type" size="small">
                {{ formatLockStatus(scope.row).text }}
              </ElTag>
            </template>
          </ElTableColumn>

          <!-- 创建时间列 -->
          <!--          <ElTableColumn prop="created_at" label="创建时间" width="160" show-overflow-tooltip />-->

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="220">
            <template #default="scope">
              <div class="operation-buttons crm-operation-buttons">
                <!-- 详情按钮 -->
                <ArtButtonTable
                  v-auth="'crm:customer_sea:detail'"
                  text="详情"
                  :iconClass="BgColorEnum.SECONDARY"
                  @click="showDetail(scope.row.id)"
                />

                <!-- 认领按钮 -->
                <ArtButtonTable
                  v-if="canClaim(scope.row) && hasAuth('crm:customer_sea:claim')"
                  text="认领"
                  :iconClass="BgColorEnum.PRIMARY"
                  @click="handleClaim(scope.row)"
                />

                <!-- 更多操作下拉菜单 -->
                <ElDropdown
                  @command="(command) => handleMoreAction(command, scope.row)"
                  trigger="hover"
                >
                  <ArtButtonTable text="更多" type="more" />
                  <template #dropdown>
                    <ElDropdownMenu>
                      <!-- 分配按钮 - 管理员才可以分配 -->
                      <ElDropdownItem v-if="hasAuth('crm:customer_sea:assign')" command="assign" :icon="User"> 分配</ElDropdownItem>

                      <!-- 锁定按钮 - 只有未锁定的客户可以锁定 -->
                      <ElDropdownItem v-if="canLock(scope.row) && hasAuth('crm:customer_sea:lock')" command="lock" :icon="Lock">
                        锁定
                      </ElDropdownItem>

                      <!-- 解锁按钮 - 只有已锁定且锁定未过期的客户可以解锁 -->
                      <ElDropdownItem v-if="canUnlock(scope.row) && hasAuth('crm:customer_sea:unlock')" command="unlock" :icon="Unlock">
                        解锁
                      </ElDropdownItem>

                      <!-- 分隔线 -->
                      <ElDropdownItem v-if="hasAnyAction(scope.row)" divided disabled>
                        <span style="color: #999; font-size: 12px">其他操作</span>
                      </ElDropdownItem>

                      <!-- 编辑按钮 - 根据权限显示 -->
                      <ElDropdownItem v-if="canEdit(scope.row)" command="edit" :icon="Edit">
                        编辑
                      </ElDropdownItem>

                      <!-- 删除按钮 - 根据权限显示 -->
                      <ElDropdownItem v-if="canDelete(scope.row)" command="delete" :icon="Delete">
                        删除
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 客户详情弹出框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="公海客户详情"
          width="800px"
          destroy-on-close
          class="detail-dialog crm-detail-optimized"
        >
          <div class="detail-content" style="height: 500px; overflow-y: auto; padding-right: 10px">
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="ID">
                <span class="crm-text-secondary">{{ detailData.id || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="客户名称">
                <span class="crm-text-primary">{{ detailData.customer_name || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="客户级别">
                <ElTag :type="formatLevel(detailData.level).type">
                  {{ formatLevel(detailData.level).text }}
                </ElTag>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="所属行业">
                <span class="crm-text-secondary">{{ detailData.industry || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="客户来源">
                <span class="crm-text-secondary">{{ detailData.source || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="联系电话">
                <span class="crm-text-info">{{ detailData.phone || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="邮箱">
                <span class="crm-text-info">{{ detailData.email || '-' }}</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="官网">
                <ElLink
                  v-if="detailData.website"
                  :href="detailData.website"
                  target="_blank"
                  type="primary"
                >
                  {{ detailData.website }}
                </ElLink>
                <span v-else class="crm-text-tertiary">-</span>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="传真">
                {{ detailData.fax || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="所在地区" :span="2">
                {{ getFullAddress(detailData) }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="邮政编码">
                {{ detailData.zip_code || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="信用代码">
                {{ detailData.credit_code || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="年销售额">
                {{ detailData.annual_revenue ? `${detailData.annual_revenue}万元` : '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="员工数量">
                {{ detailData.employee_count ? `${detailData.employee_count}人` : '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="注册资金">
                {{ detailData.registered_capital ? `${detailData.registered_capital}万元` : '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="锁定状态">
                <ElTag :type="formatLockStatus(detailData).type">
                  {{ formatLockStatus(detailData).text }}
                </ElTag>
              </ElDescriptionsItem>

              <ElDescriptionsItem label="进入公海时间">
                {{ detailData.into_sea_time || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="创建时间">
                {{ detailData.created_at || '-' }}
              </ElDescriptionsItem>

              <ElDescriptionsItem label="备注" :span="2">
                {{ detailData.remark || '-' }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 编辑表单对话框 -->
        <CustomerSeaFormDialog
          v-model="editDialogVisible"
          :customer-id="currentEditId"
          @success="handleEditSuccess"
        />

        <!-- 锁定对话框 -->
        <ElDialog
          v-model="lockDialogVisible"
          title="锁定客户"
          width="500px"
          :close-on-click-modal="false"
        >
          <ElForm :model="lockFormData" label-width="120px">
            <ElFormItem label="锁定到期时间" required>
              <ElDatePicker
                v-model="lockFormData.lock_expire_time"
                type="datetime"
                placeholder="选择锁定到期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="lockDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="confirmLock">确定锁定</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 分配对话框 -->
        <ElDialog
          v-model="assignDialogVisible"
          title="分配客户"
          width="500px"
          :close-on-click-modal="false"
        >
          <ElForm :model="assignFormData" label-width="120px">
            <ElFormItem label="分配给用户" required>
              <ApiSelect
                v-model="assignFormData.owner_user_id"
                :api="{ url: '/system/admin/options' }"
                placeholder="请选择分配的人员"
                clearable
                :auto-load="true"
                :load-on-focus="false"
              />
            </ElFormItem>
          </ElForm>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="assignDialogVisible = false">取消</ElButton>
              <ElButton type="primary" @click="confirmAssign">确定分配</ElButton>
            </div>
          </template>
        </ElDialog>
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-customer-sea-page {
    height: 100%;
  }

  .art-table-card {
    margin-top: 16px;
  }

  .operation-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .operation-buttons :deep(.btn-text) {
    margin-right: 0;
  }

  /* 详情对话框固定高度样式 */
  :deep(.detail-dialog) {
    .el-dialog__body {
      height: 500px !important;
      padding: 20px !important;
      overflow: hidden !important;
    }

    .detail-content {
      height: 100%;
      overflow-y: auto;
      padding-right: 10px;
    }

    /* 滚动条样式优化 */

    .detail-content::-webkit-scrollbar {
      width: 6px;
    }

    .detail-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .detail-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .detail-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  .info-row {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
  }

  .info-row.main-info {
    font-size: 15px; /* 主要信息字体更大 */
    font-weight: 500;
  }

  .info-row.secondary-info {
    font-size: 13px;
    color: #666;
  }

  .info-label {
    color: #909399;
    font-weight: 600;
    min-width: 36px;
    font-size: inherit;
  }

  .customer_name {
    //color: #303133;
    color: var(--art-text-gray-800);
    font-weight: 600;
    font-size: 15px;
  }
</style>
