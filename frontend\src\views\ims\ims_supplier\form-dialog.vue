<script setup lang="ts">
  import { ref, reactive, defineExpose, defineEmits } from 'vue'
  import { ElMessage, FormInstance, ElRow, ElCol } from 'element-plus'
  import { ImsSupplierApi } from '@/api/ims/imsSupplier'
  import { ApiStatus } from '@/utils/http/status'
  import RegionSelector from '@/components/custom/RegionSelector/index.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    name: '',
    code: '',
    contact_name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detailed_address: '',
    remark: ''
  })

  // 表单验证规则
  const rules = {
    name: [
      {
        required: true,
        message: '供应商名称不能为空',
        trigger: 'blur'
      },
      {
        max: 100,
        message: '供应商名称长度不能超过100个字符',
        trigger: 'blur'
      }
    ],
    code: [
      {
        max: 50,
        message: '供应商编码长度不能超过50个字符',
        trigger: 'blur'
      }
    ],
    contact_name: [
      {
        max: 50,
        message: '联系人长度不能超过50个字符',
        trigger: 'blur'
      }
    ],
    phone: [
      {
        max: 20,
        message: '联系电话长度不能超过20个字符',
        trigger: 'blur'
      }
    ],
    detailed_address: [
      {
        max: 200,
        message: '详细地址长度不能超过200个字符',
        trigger: 'blur'
      }
    ]
  }

  // 显示对话框
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    dialogVisible.value = true

    // 重置表单数据
    Object.assign(formData, {
      name: '',
      code: '',
      contact_name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detailed_address: '',
      remark: ''
    })

    // 编辑模式下获取详情数据
    if (type === 'edit' && id) {
      try {
        loading.value = true
        const res = await ImsSupplierApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 处理数据类型转换
          const data = { ...res.data }

          Object.assign(formData, data)
        }
      } finally {
        loading.value = false
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          // 处理提交数据转换
          const submitData = { ...formData }

          let res

          if (dialogType.value === 'add') {
            res = await ImsSupplierApi.add(submitData)
          } else {
            res = await ImsSupplierApi.update(submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
            dialogVisible.value = false
            emit('success')
          }
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增供应商表' : '编辑供应商表'"
    width="800px"
    top="5vh"
    destroy-on-close
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        v-loading="loading"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="供应商名称" prop="name">
              <ElInput v-model="formData.name" placeholder="请输入供应商名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="供应商编码" prop="code">
              <ElInput v-model="formData.code" placeholder="请输入供应商编码" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="联系人" prop="contact_name">
              <ElInput v-model="formData.contact_name" placeholder="请输入联系人" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="联系电话" prop="phone">
              <ElInput v-model="formData.phone" placeholder="请输入联系电话" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="省市区" prop="region">
              <RegionSelector
                v-model="formData"
                :return-type="'object'"
                :field-mapping="{
                  province: 'province',
                  city: 'city',
                  district: 'district'
                }"
                placeholder="请选择省市区"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="详细地址" prop="detailed_address">
              <ElInput
                v-model="formData.detailed_address"
                type="textarea"
                :rows="3"
                placeholder="请输入详细地址"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="备注" prop="remark">
              <ElInput v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .dialog-content {
    max-height: calc(90vh - 200px);
    overflow-y: auto;
    padding: 0 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid #e4e7ed;
  }

  .avatar-uploader .avatar {
    width: 100px;
    height: 100px;
    display: block;
  }

  .avatar-uploader .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    line-height: 100px;
  }
</style>
