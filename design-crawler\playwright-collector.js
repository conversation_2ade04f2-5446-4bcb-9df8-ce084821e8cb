/**
 * 设计方案自动采集器 - Playwright版本
 * 支持截图、样式提取、组件分析
 */

const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');

class DesignCollector {
  constructor(options = {}) {
    this.browser = null;
    this.page = null;
    this.outputDir = options.outputDir || './design-collection';
    this.delay = options.delay || 2000; // 页面加载延迟
  }

  async init() {
    this.browser = await chromium.launch({ 
      headless: false, // 可视化调试
      slowMo: 100 
    });
    this.page = await this.browser.newPage();
    
    // 设置视口大小
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    
    // 创建输出目录
    await this.ensureDir(this.outputDir);
  }

  /**
   * 采集组件库页面
   */
  async collectComponentLibrary(url, componentName) {
    console.log(`正在采集组件: ${componentName} from ${url}`);
    
    try {
      await this.page.goto(url, { waitUntil: 'networkidle' });
      await this.page.waitForTimeout(this.delay);

      // 1. 截图保存
      const screenshotPath = path.join(this.outputDir, `${componentName}-screenshot.png`);
      await this.page.screenshot({ 
        path: screenshotPath,
        fullPage: true 
      });

      // 2. 提取CSS样式
      const styles = await this.extractStyles();
      
      // 3. 提取HTML结构
      const htmlStructure = await this.extractHTMLStructure();
      
      // 4. 提取设计token
      const designTokens = await this.extractDesignTokens();

      // 5. 保存数据
      const collectionData = {
        componentName,
        url,
        timestamp: new Date().toISOString(),
        screenshot: screenshotPath,
        styles,
        htmlStructure,
        designTokens,
        metadata: await this.extractMetadata()
      };

      await this.saveCollectionData(componentName, collectionData);
      
      console.log(`✅ ${componentName} 采集完成`);
      return collectionData;

    } catch (error) {
      console.error(`❌ 采集失败: ${componentName}`, error.message);
      return null;
    }
  }

  /**
   * 提取CSS样式
   */
  async extractStyles() {
    return await this.page.evaluate(() => {
      const styles = {};
      
      // 获取所有样式表
      const styleSheets = Array.from(document.styleSheets);
      
      styleSheets.forEach((sheet, index) => {
        try {
          const rules = Array.from(sheet.cssRules || sheet.rules || []);
          styles[`stylesheet_${index}`] = rules.map(rule => ({
            selector: rule.selectorText,
            cssText: rule.cssText,
            type: rule.type
          }));
        } catch (e) {
          // 跨域样式表无法访问
          console.warn('无法访问样式表:', e.message);
        }
      });

      // 获取内联样式
      const elementsWithStyle = document.querySelectorAll('[style]');
      styles.inlineStyles = Array.from(elementsWithStyle).map(el => ({
        tagName: el.tagName,
        className: el.className,
        style: el.style.cssText
      }));

      return styles;
    });
  }

  /**
   * 提取HTML结构
   */
  async extractHTMLStructure() {
    return await this.page.evaluate(() => {
      // 查找主要内容区域
      const mainSelectors = [
        '.component-demo',
        '.example',
        '.demo-block',
        '[class*="demo"]',
        'main',
        '.content'
      ];

      let mainContent = null;
      for (const selector of mainSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          mainContent = element;
          break;
        }
      }

      if (!mainContent) {
        mainContent = document.body;
      }

      // 简化HTML结构
      const simplifyElement = (element) => {
        return {
          tagName: element.tagName,
          className: element.className,
          id: element.id,
          attributes: Array.from(element.attributes).reduce((acc, attr) => {
            acc[attr.name] = attr.value;
            return acc;
          }, {}),
          children: Array.from(element.children).map(simplifyElement)
        };
      };

      return simplifyElement(mainContent);
    });
  }

  /**
   * 提取设计token
   */
  async extractDesignTokens() {
    return await this.page.evaluate(() => {
      const tokens = {
        colors: new Set(),
        fonts: new Set(),
        spacing: new Set(),
        borderRadius: new Set(),
        shadows: new Set()
      };

      // 遍历所有元素提取设计token
      const allElements = document.querySelectorAll('*');
      
      allElements.forEach(el => {
        const computedStyle = window.getComputedStyle(el);
        
        // 颜色
        if (computedStyle.color && computedStyle.color !== 'rgba(0, 0, 0, 0)') {
          tokens.colors.add(computedStyle.color);
        }
        if (computedStyle.backgroundColor && computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
          tokens.colors.add(computedStyle.backgroundColor);
        }
        if (computedStyle.borderColor && computedStyle.borderColor !== 'rgba(0, 0, 0, 0)') {
          tokens.colors.add(computedStyle.borderColor);
        }

        // 字体
        if (computedStyle.fontFamily) {
          tokens.fonts.add(computedStyle.fontFamily);
        }
        if (computedStyle.fontSize) {
          tokens.fonts.add(computedStyle.fontSize);
        }

        // 间距
        ['margin', 'padding'].forEach(prop => {
          const value = computedStyle[prop];
          if (value && value !== '0px') {
            tokens.spacing.add(value);
          }
        });

        // 圆角
        if (computedStyle.borderRadius && computedStyle.borderRadius !== '0px') {
          tokens.borderRadius.add(computedStyle.borderRadius);
        }

        // 阴影
        if (computedStyle.boxShadow && computedStyle.boxShadow !== 'none') {
          tokens.shadows.add(computedStyle.boxShadow);
        }
      });

      // 转换Set为Array
      return Object.fromEntries(
        Object.entries(tokens).map(([key, set]) => [key, Array.from(set)])
      );
    });
  }

  /**
   * 提取页面元数据
   */
  async extractMetadata() {
    return await this.page.evaluate(() => ({
      title: document.title,
      description: document.querySelector('meta[name="description"]')?.content || '',
      viewport: document.querySelector('meta[name="viewport"]')?.content || '',
      framework: this.detectFramework(),
      componentLibrary: this.detectComponentLibrary()
    }));
  }

  /**
   * 批量采集组件库
   */
  async batchCollectComponents(componentList) {
    const results = [];
    
    for (const component of componentList) {
      const result = await this.collectComponentLibrary(component.url, component.name);
      if (result) {
        results.push(result);
      }
      
      // 避免请求过快
      await this.page.waitForTimeout(1000);
    }
    
    return results;
  }

  /**
   * 保存采集数据
   */
  async saveCollectionData(componentName, data) {
    const filePath = path.join(this.outputDir, `${componentName}.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
  }

  /**
   * 确保目录存在
   */
  async ensureDir(dirPath) {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }
}

// 使用示例
async function main() {
  const collector = new DesignCollector({
    outputDir: './design-collection'
  });

  await collector.init();

  // Element Plus组件采集列表
  const elementPlusComponents = [
    { name: 'button', url: 'https://element-plus.org/zh-CN/component/button.html' },
    { name: 'table', url: 'https://element-plus.org/zh-CN/component/table.html' },
    { name: 'form', url: 'https://element-plus.org/zh-CN/component/form.html' },
    { name: 'dialog', url: 'https://element-plus.org/zh-CN/component/dialog.html' },
  ];

  const results = await collector.batchCollectComponents(elementPlusComponents);
  
  console.log(`采集完成，共收集 ${results.length} 个组件`);
  
  await collector.close();
}

module.exports = DesignCollector;

// 如果直接运行此文件
if (require.main === module) {
  main().catch(console.error);
}
