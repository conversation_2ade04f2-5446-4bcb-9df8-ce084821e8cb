/**
 * 日期工具函数
 * 基于 @pureadmin/utils 优化，避免重复造轮子
 */

import {
  dateFormat as pureDateFormat,
  getCurrentDate,
  getCurrentWeek,
  monthDays
} from '@pureadmin/utils'

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认 'YYYY-MM-DD'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: string | Date | null | undefined,
  format: string = 'YYYY-MM-DD'
): string {
  if (!date) return '-'

  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'

  // 直接使用原生Date对象进行格式化，避免依赖外部库的问题
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化日期时间
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(date: string | Date | null | undefined): string {
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取当前日期（使用 @pureadmin/utils）
 * @param type 格式类型：1-年月日模式，2-连字符模式，3-斜杠模式
 * @returns 当前日期对象
 */
export function getCurrentDateInfo(type: 1 | 2 | 3 = 2) {
  return getCurrentDate({ type })
}

/**
 * 获取当前星期几（使用 @pureadmin/utils）
 * @param prefix 前缀，默认'星期'
 * @returns 星期几字符串
 */
export function getCurrentWeekInfo(prefix: string = '星期') {
  return getCurrentWeek(prefix)
}

/**
 * 获取指定月份天数（使用 @pureadmin/utils）
 * @param date 日期
 * @returns 天数
 */
export function getMonthDays(date: string | Date) {
  return monthDays(date)
}

/**
 * 格式化相对时间
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串，如 "2小时前"、"3天前"
 */
export function formatRelativeTime(date: string | Date | null | undefined): string {
  if (!date) return '-'

  const d = new Date(date)
  if (isNaN(d.getTime())) return '-'

  const now = new Date()
  const diff = now.getTime() - d.getTime()

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }

  // 小于30天
  if (diff < 30 * 24 * 60 * 60 * 1000) {
    const weeks = Math.floor(diff / (7 * 24 * 60 * 60 * 1000))
    return `${weeks}周前`
  }

  // 小于1年
  if (diff < 365 * 24 * 60 * 60 * 1000) {
    const months = Math.floor(diff / (30 * 24 * 60 * 60 * 1000))
    return `${months}个月前`
  }

  // 超过1年
  const years = Math.floor(diff / (365 * 24 * 60 * 60 * 1000))
  return `${years}年前`
}

/**
 * 判断日期是否过期
 * @param date 日期字符串或Date对象
 * @returns 是否过期
 */
export function isOverdue(date: string | Date | null | undefined): boolean {
  if (!date) return false

  const d = new Date(date)
  if (isNaN(d.getTime())) return false

  return d.getTime() < new Date().getTime()
}

/**
 * 获取日期范围内的天数
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 天数
 */
export function getDaysBetween(startDate: string | Date, endDate: string | Date): number {
  const start = new Date(startDate)
  const end = new Date(endDate)

  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

  const diffTime = Math.abs(end.getTime() - start.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 获取今天的日期字符串
 * @param format 格式化模板
 * @returns 今天的日期字符串
 */
export function getToday(format: string = 'YYYY-MM-DD'): string {
  return pureDateFormat(format)
}

/**
 * 获取明天的日期字符串
 * @param format 格式化模板
 * @returns 明天的日期字符串
 */
export function getTomorrow(format: string = 'YYYY-MM-DD'): string {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return formatDate(tomorrow, format)
}

/**
 * 获取昨天的日期字符串
 * @param format 格式化模板
 * @returns 昨天的日期字符串
 */
export function getYesterday(format: string = 'YYYY-MM-DD'): string {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return formatDate(yesterday, format)
}

/**
 * 获取本周开始日期
 * @param format 格式化模板
 * @returns 本周开始日期字符串
 */
export function getWeekStart(format: string = 'YYYY-MM-DD'): string {
  const now = new Date()
  const day = now.getDay()
  const diff = now.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
  const weekStart = new Date(now.setDate(diff))
  return formatDate(weekStart, format)
}

/**
 * 获取本周结束日期
 * @param format 格式化模板
 * @returns 本周结束日期字符串
 */
export function getWeekEnd(format: string = 'YYYY-MM-DD'): string {
  const now = new Date()
  const day = now.getDay()
  const diff = now.getDate() - day + (day === 0 ? 0 : 7) // 调整为周日结束
  const weekEnd = new Date(now.setDate(diff))
  return formatDate(weekEnd, format)
}

/**
 * 获取本月开始日期
 * @param format 格式化模板
 * @returns 本月开始日期字符串
 */
export function getMonthStart(format: string = 'YYYY-MM-DD'): string {
  const now = new Date()
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
  return formatDate(monthStart, format)
}

/**
 * 获取本月结束日期
 * @param format 格式化模板
 * @returns 本月结束日期字符串
 */
export function getMonthEnd(format: string = 'YYYY-MM-DD'): string {
  const now = new Date()
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  return formatDate(monthEnd, format)
}

/**
 * 计算两个日期之间的天数（包含开始和结束日期）
 * 主要用于出差申请等业务场景
 *
 * @param startDate 开始日期，支持字符串或Date对象
 * @param endDate 结束日期，支持字符串或Date对象
 * @returns 天数，如果日期无效返回0
 *
 * @example
 * // 计算出差天数
 * calculateDays('2025-07-29', '2025-07-31') // 返回 3
 * calculateDays('2025-07-29 09:00', '2025-07-29 18:00') // 返回 1（同一天）
 * calculateDays('2025-07-29', '2025-07-28') // 返回 0（结束日期早于开始日期）
 */
export function calculateDays(startDate: string | Date, endDate: string | Date): number {
  if (!startDate || !endDate) return 0

  const start = new Date(startDate)
  const end = new Date(endDate)

  // 检查日期有效性
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

  // 如果结束日期早于开始日期，返回0
  if (end < start) return 0

  // 将时间设置为当天的开始时间（00:00:00），确保按天计算
  const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate())
  const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate())

  // 计算天数差异并加1（包含开始和结束日期）
  const diffTime = endDay.getTime() - startDay.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1

  return Math.max(0, diffDays)
}

/**
 * 计算两个时间之间的小时数（精确到小数点后1位）
 * 主要用于外出申请等业务场景
 *
 * @param startTime 开始时间，支持字符串或Date对象
 * @param endTime 结束时间，支持字符串或Date对象
 * @returns 小时数，如果时间无效返回0
 *
 * @example
 * // 计算外出时长
 * calculateHours('2025-07-28 14:00', '2025-07-28 17:00') // 返回 3.0
 * calculateHours('2025-07-28 14:30', '2025-07-28 17:15') // 返回 2.8
 * calculateHours('2025-07-28 17:00', '2025-07-28 14:00') // 返回 0（结束时间早于开始时间）
 */
export function calculateHours(startTime: string | Date, endTime: string | Date): number {
  if (!startTime || !endTime) return 0

  const start = new Date(startTime)
  const end = new Date(endTime)

  // 检查时间有效性
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0

  // 如果结束时间早于开始时间，返回0
  if (end <= start) return 0

  // 计算时间差异（毫秒）
  const diffTime = end.getTime() - start.getTime()

  // 转换为小时并保留1位小数
  const diffHours = diffTime / (1000 * 60 * 60)

  return Math.round(diffHours * 10) / 10
}

/**
 * 按半小时规则计算时长（向上取整到0.5小时倍数）
 * 主要用于HR业务场景：请假、外出、出差申请
 *
 * @param startTime 开始时间，支持字符串或Date对象
 * @param endTime 结束时间，支持字符串或Date对象
 * @returns 按半小时规则计算的小时数
 *
 * @example
 * // 半小时规则示例
 * calculateHoursWithHalfHourRule('2025-08-01 14:00', '2025-08-01 14:10') // 返回 0.5（不足0.5小时按0.5小时）
 * calculateHoursWithHalfHourRule('2025-08-01 14:00', '2025-08-01 14:30') // 返回 0.5（正好0.5小时）
 * calculateHoursWithHalfHourRule('2025-08-01 14:00', '2025-08-01 14:40') // 返回 1.0（大于0.5小时小于1小时按1小时）
 * calculateHoursWithHalfHourRule('2025-08-01 14:00', '2025-08-01 15:10') // 返回 1.5（1小时10分钟按1.5小时）
 */
export function calculateHoursWithHalfHourRule(startTime: string | Date, endTime: string | Date): number {
  const actualHours = calculateHours(startTime, endTime)

  if (actualHours <= 0) return 0

  // 向上取整到最近的0.5小时倍数
  return Math.ceil(actualHours / 0.5) * 0.5
}

/**
 * 将小时数转换为天数和小时数的显示格式
 * 基于工作时间配置进行转换
 *
 * @param hours 总小时数
 * @param dailyWorkHours 每日工作小时数，默认8小时
 * @returns 转换结果对象
 *
 * @example
 * // 转换示例
 * convertHoursToDaysAndHours(4.5, 8) // 返回 { days: 0, hours: 4.5, display: '4.5小时' }
 * convertHoursToDaysAndHours(10, 8) // 返回 { days: 1, hours: 2, display: '1天2小时' }
 * convertHoursToDaysAndHours(16, 8) // 返回 { days: 2, hours: 0, display: '2天' }
 */
export function convertHoursToDaysAndHours(hours: number, dailyWorkHours: number = 8): {
  days: number
  hours: number
  display: string
} {
  if (hours <= 0) {
    return { days: 0, hours: 0, display: '0小时' }
  }

  if (hours <= dailyWorkHours) {
    return { days: 0, hours, display: `${hours}小时` }
  }

  const days = Math.floor(hours / dailyWorkHours)
  const remainingHours = hours - (days * dailyWorkHours)

  let display = `${days}天`
  if (remainingHours > 0) {
    display += `${remainingHours}小时`
  }

  return { days, hours: remainingHours, display }
}

/**
 * 解析工作时间配置并计算每日工作小时数
 * 解析格式如：'08:00-12:00,14:00-18:00'
 *
 * @param workTime 工作时间配置字符串
 * @returns 每日工作小时数
 *
 * @example
 * // 解析示例
 * parseWorkTimeConfig('08:00-12:00,14:00-18:00') // 返回 8
 * parseWorkTimeConfig('09:00-12:00,13:00-17:00') // 返回 7
 * parseWorkTimeConfig('08:30-17:30') // 返回 9
 */
export function parseWorkTimeConfig(workTime: string): number {
  const segments = workTime.split(',')
  let totalHours = 0

  segments.forEach(segment => {
    const trimmedSegment = segment.trim()
    if (trimmedSegment.includes('-')) {
      const [start, end] = trimmedSegment.split('-')
      const startTime = new Date(`1970-01-01 ${start.trim()}`)
      const endTime = new Date(`1970-01-01 ${end.trim()}`)

      if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime()) && endTime > startTime) {
        totalHours += (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60)
      }
    }
  })

  return totalHours
}

/**
 * 计算指定时间段内的工作时间（考虑工作时间配置）
 * 并应用半小时向上取整规则
 *
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param workTimeConfig 工作时间配置，如 '08:00-12:00,14:00-18:00'
 * @returns 工作时间（小时）
 */
export function calculateWorkingHoursWithHalfHourRule(
  startTime: string,
  endTime: string,
  workTimeConfig: string = '08:00-12:00,14:00-18:00'
): number {
  const start = new Date(startTime).getTime()
  const end = new Date(endTime).getTime()

  if (end <= start) {
    return 0
  }

  // 解析工作时间配置
  const workSegments = parseWorkTimeSegments(workTimeConfig)
  if (workSegments.length === 0) {
    // 如果没有工作时间配置，回退到简单计算
    return calculateHoursWithHalfHourRule(startTime, endTime)
  }

  let totalWorkingHours = 0
  const startDate = new Date(start)
  const endDate = new Date(end)

  // 按天计算工作时间
  const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
  const finalDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())

  while (currentDate <= finalDate) {
    const dayStart = new Date(currentDate.getTime())
    const dayEnd = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59)

    // 确定当天的实际开始和结束时间
    const actualStart = new Date(Math.max(start, dayStart.getTime()))
    const actualEnd = new Date(Math.min(end, dayEnd.getTime()))

    if (actualStart <= actualEnd) {
      // 计算当天的工作时间
      const dayWorkingHours = calculateDayWorkingHours(
        formatTime(actualStart),
        formatTime(actualEnd),
        workSegments
      )
      totalWorkingHours += dayWorkingHours
    }

    // 移动到下一天
    currentDate.setDate(currentDate.getDate() + 1)
  }

  // 应用半小时向上取整规则
  if (totalWorkingHours <= 0) {
    return 0
  }

  return Math.ceil(totalWorkingHours / 0.5) * 0.5
}

/**
 * 解析工作时间配置为时间段数组
 *
 * @param workTimeConfig 工作时间配置
 * @returns 时间段数组
 */
function parseWorkTimeSegments(workTimeConfig: string): Array<{
  start: string
  end: string
  startSeconds: number
  endSeconds: number
}> {
  const segments: Array<{
    start: string
    end: string
    startSeconds: number
    endSeconds: number
  }> = []

  const parts = workTimeConfig.split(',')

  parts.forEach(part => {
    const trimmedPart = part.trim()
    if (trimmedPart.includes('-')) {
      const [start, end] = trimmedPart.split('-')
      const startTime = new Date(`1970-01-01 ${start.trim()}`)
      const endTime = new Date(`1970-01-01 ${end.trim()}`)

      if (!isNaN(startTime.getTime()) && !isNaN(endTime.getTime()) && endTime > startTime) {
        const baseTime = new Date('1970-01-01 00:00:00').getTime()
        segments.push({
          start: start.trim(),
          end: end.trim(),
          startSeconds: (startTime.getTime() - baseTime) / 1000,
          endSeconds: (endTime.getTime() - baseTime) / 1000
        })
      }
    }
  })

  return segments
}

/**
 * 计算单天内指定时间段的工作时间
 *
 * @param startTime 开始时间（HH:mm:ss格式）
 * @param endTime 结束时间（HH:mm:ss格式）
 * @param workSegments 工作时间段配置
 * @returns 工作时间（小时）
 */
function calculateDayWorkingHours(
  startTime: string,
  endTime: string,
  workSegments: Array<{
    start: string
    end: string
    startSeconds: number
    endSeconds: number
  }>
): number {
  const startSeconds = timeToSeconds(startTime)
  const endSeconds = timeToSeconds(endTime)

  let totalWorkingSeconds = 0

  workSegments.forEach(segment => {
    // 计算重叠时间
    const overlapStart = Math.max(startSeconds, segment.startSeconds)
    const overlapEnd = Math.min(endSeconds, segment.endSeconds)

    if (overlapStart < overlapEnd) {
      totalWorkingSeconds += (overlapEnd - overlapStart)
    }
  })

  return totalWorkingSeconds / 3600 // 转换为小时
}

/**
 * 将时间字符串转换为秒数
 *
 * @param timeStr 时间字符串（HH:mm:ss格式）
 * @returns 秒数
 */
function timeToSeconds(timeStr: string): number {
  const time = new Date(`1970-01-01 ${timeStr}`)
  const baseTime = new Date('1970-01-01 00:00:00')
  return (time.getTime() - baseTime.getTime()) / 1000
}

/**
 * 格式化时间为HH:mm:ss格式
 *
 * @param date 日期对象
 * @returns 格式化的时间字符串
 */
function formatTime(date: Date): string {
  return date.toTimeString().split(' ')[0]
}
