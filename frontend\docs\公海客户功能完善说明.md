# 公海客户功能完善说明

## 修改概述

完善公海客户页面的认领、锁定和分配功能，提升用户体验和操作安全性。

## 主要功能改进

### 1. 认领按钮确认提示框

**修改前**: 直接认领，无确认提示
**修改后**: 增加确认提示框，防止误操作

```typescript
// 认领客户
const handleClaim = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要认领客户"${row.customer_name}"吗？认领后该客户将分配给您负责。`,
      '确认认领',
      {
        confirmButtonText: '确定认领',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const res = await CrmCustomerSeaApi.claim(row.id)
    if (res.code === ApiStatus.success) {
      ElMessage.success('认领成功')
      await getTableData()
    } else {
      ElMessage.error(res.message || '认领失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('认领失败')
    }
  }
}
```

### 2. 锁定按钮流程优化

**新增功能**:
- 点击锁定按钮弹出表单对话框
- 表单包含锁定日期时间选择器
- 确定时进行二次确认
- 自动计算锁定分钟数

```vue
<!-- 锁定对话框 -->
<ElDialog
  v-model="lockDialogVisible"
  title="锁定客户"
  width="500px"
  :close-on-click-modal="false"
>
  <ElForm :model="lockFormData" label-width="120px">
    <ElFormItem label="锁定到期时间" required>
      <ElDatePicker
        v-model="lockFormData.lock_expire_time"
        type="datetime"
        placeholder="选择锁定到期时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        style="width: 100%"
      />
    </ElFormItem>
  </ElForm>
  <template #footer>
    <div class="dialog-footer">
      <ElButton @click="lockDialogVisible = false">取消</ElButton>
      <ElButton type="primary" @click="confirmLock">确定锁定</ElButton>
    </div>
  </template>
</ElDialog>
```

**锁定确认逻辑**:
```typescript
const confirmLock = async () => {
  if (!lockFormData.value.lock_expire_time) {
    ElMessage.warning('请选择锁定到期时间')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要锁定客户"${currentLockRow.value.customer_name}"到 ${lockFormData.value.lock_expire_time} 吗？`,
      '确认锁定',
      {
        confirmButtonText: '确定锁定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 计算锁定分钟数
    const expireTime = new Date(lockFormData.value.lock_expire_time)
    const now = new Date()
    const lockMinutes = Math.ceil((expireTime.getTime() - now.getTime()) / (1000 * 60))
    
    if (lockMinutes <= 0) {
      ElMessage.warning('锁定时间必须大于当前时间')
      return
    }

    const res = await CrmCustomerSeaApi.lock(currentLockRow.value.id, lockMinutes)
    if (res.code === ApiStatus.success) {
      ElMessage.success('锁定成功')
      lockDialogVisible.value = false
      await getTableData()
    } else {
      ElMessage.error(res.message || '锁定失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('锁定失败')
    }
  }
}
```

### 3. 分配按钮功能完善

**新增功能**:
- 点击分配按钮弹出表单对话框
- 使用ApiSelect组件选择用户
- 调用AdminApi.options获取用户列表

```vue
<!-- 分配对话框 -->
<ElDialog
  v-model="assignDialogVisible"
  title="分配客户"
  width="500px"
  :close-on-click-modal="false"
>
  <ElForm :model="assignFormData" label-width="120px">
    <ElFormItem label="分配给用户" required>
      <ApiSelect
        v-model="assignFormData.owner_user_id"
        :api="AdminApi.options"
        placeholder="请选择用户"
        style="width: 100%"
      />
    </ElFormItem>
  </ElForm>
  <template #footer>
    <div class="dialog-footer">
      <ElButton @click="assignDialogVisible = false">取消</ElButton>
      <ElButton type="primary" @click="confirmAssign">确定分配</ElButton>
    </div>
  </template>
</ElDialog>
```

**分配确认逻辑**:
```typescript
const confirmAssign = async () => {
  if (!assignFormData.value.owner_user_id) {
    ElMessage.warning('请选择分配的用户')
    return
  }

  try {
    const res = await CrmCustomerSeaApi.assign(currentAssignRow.value.id, assignFormData.value.owner_user_id)
    if (res.code === ApiStatus.success) {
      ElMessage.success('分配成功')
      assignDialogVisible.value = false
      await getTableData()
    } else {
      ElMessage.error(res.message || '分配失败')
    }
  } catch {
    ElMessage.error('分配失败')
  }
}
```

## API接口扩展

### 新增分配接口

在`CrmCustomerSeaApi`中新增assign方法：

```typescript
/**
 * 分配公海客户
 * @param customerId 客户ID
 * @param ownerUserId 分配给的用户ID
 */
static assign(customerId: number | string, ownerUserId: number | string) {
  return request.post<BaseResult>({
    url: `/crm/crm_customer_sea/sea/assign/${customerId}`,
    data: { owner_user_id: ownerUserId }
  })
}
```

### 锁定接口参数说明

现有的lock接口接受分钟数参数：
```typescript
static lock(customerId: number | string, lockMinutes: number = 30)
```

前端需要将用户选择的日期时间转换为分钟数：
```typescript
const expireTime = new Date(lockFormData.value.lock_expire_time)
const now = new Date()
const lockMinutes = Math.ceil((expireTime.getTime() - now.getTime()) / (1000 * 60))
```

## 组件导入

新增必要的组件导入：

```typescript
import {
  ElCard,
  ElButton,
  ElTag,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElMessage,
  ElMessageBox,
  ElDialog,
  ElDescriptions,
  ElDescriptionsItem,
  ElLink,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElRow,
  ElCol
} from 'element-plus'

import { AdminApi } from '@/api/adminApi'
import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
```

## 数据状态管理

新增对话框状态和表单数据：

```typescript
// 锁定对话框
const lockDialogVisible = ref(false)
const lockFormData = ref({
  lock_expire_time: ''
})
const currentLockRow = ref<any>(null)

// 分配对话框
const assignDialogVisible = ref(false)
const assignFormData = ref({
  owner_user_id: ''
})
const currentAssignRow = ref<any>(null)
```

## 用户体验提升

1. **操作确认**: 所有关键操作都有确认提示，防止误操作
2. **表单验证**: 锁定时间和分配用户的必填验证
3. **实时反馈**: 操作成功/失败的即时消息提示
4. **数据刷新**: 操作完成后自动刷新表格数据
5. **错误处理**: 完善的错误处理和用户提示

## 业务逻辑优化

1. **时间验证**: 锁定时间必须大于当前时间
2. **权限控制**: 分配功能仅管理员可用
3. **状态同步**: 操作后及时更新客户状态
4. **日志记录**: 重要操作可记录操作日志

这些改进使公海客户管理功能更加完善和用户友好，提升了操作的安全性和便捷性。
