<template>
  <div class="form-data-viewer" v-loading="loading">
    <!-- 调试信息 -->
    <div v-if="false" style="margin-bottom: 10px; padding: 10px; background: #f0f0f0; border-radius: 4px; font-size: 12px;">
      <div>业务代码: {{ businessCode }}</div>
      <div>表单数据: {{ JSON.stringify(formData) }}</div>
      <div>组件状态: {{ formComponent ? '已加载特定组件' : '使用通用显示' }}</div>
    </div>

    <!-- 组件加载中 -->
    <div v-if="componentLoading" class="component-loading">
      <el-skeleton :rows="3" animated />
      <div style="text-align: center; margin-top: 10px; color: #666; font-size: 12px;">
        正在加载 {{ businessCode }} 表单查看组件...
      </div>
    </div>

    <!-- 如果存在特定业务表单查看组件，动态加载 -->
    <component
      :is="formComponent"
      v-else-if="formComponent && formData && !componentError"
      :formData="formData"
      :businessCode="businessCode"
    />

    <!-- 组件加载失败，使用通用表单数据展示 -->
    <div v-else-if="(formData && !formComponent) || componentError" class="generic-form-display">
      <div style="margin-bottom: 10px; color: #666; font-size: 12px;">
        <span v-if="componentError">组件加载失败，</span>使用通用表单显示 (业务代码: {{ businessCode }})
      </div>
      <el-descriptions border :column="2" size="small">
        <template v-for="(value, key) in formData" :key="key">
          <el-descriptions-item :label="formatLabel(key)" v-if="shouldDisplayField(key)">
            {{ formatValue(key, value) }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
    </div>

    <!-- 空数据提示 -->
    <div v-else-if="!loading && !componentLoading" class="empty-data">
      <el-empty description="暂无表单数据" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElEmpty, ElSkeleton } from 'element-plus'

  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },
    // 业务代码
    businessCode: {
      type: String,
      default: ''
    }
  })

  // 加载状态
  const loading = ref(false)

  // 组件加载状态
  const componentLoading = ref(false)
  const componentError = ref(false)

  // 动态引入表单组件
  const formComponent = computed(() => {
    if (!props.businessCode) {
      console.log('FormDataViewer: businessCode 为空，无法加载特定组件')
      return null
    }

    try {
      // 尝试加载特定业务的查看组件
      console.log(`FormDataViewer: 尝试加载组件 ${props.businessCode}-form-view.vue`)
      componentLoading.value = true
      componentError.value = false

      return markRaw(
        defineAsyncComponent({
          loader: () => import(`../components/business-forms/${props.businessCode}-form-view.vue`).then((module) => {
            console.log(`成功加载特定组件: ${props.businessCode}-form-view.vue`)
            componentLoading.value = false
            return module
          }).catch((error) => {
            console.log(`没有找到特定的 ${props.businessCode} 表单查看组件，使用通用组件`, error)
            // 如果没有特定视图组件，尝试使用通用的工作流表单查看器
            return import('./workflow-form-viewer.vue').then((module) => {
              console.log('成功加载通用工作流查看组件')
              componentLoading.value = false
              return module
            }).catch((fallbackError) => {
              console.error('加载通用组件也失败了', fallbackError)
              componentLoading.value = false
              componentError.value = true
              throw fallbackError
            })
          }),
          delay: 200,
          timeout: 8000,
          errorComponent: null,
          loadingComponent: null,
          onError: (error) => {
            console.error('异步组件加载错误:', error)
            componentLoading.value = false
            componentError.value = true
          }
        })
      )
    } catch (error) {
      console.error('加载表单查看组件失败', error)
      componentLoading.value = false
      componentError.value = true
      return null
    }
  })

  // 通用字段标签格式化
  const formatLabel = (key: string): string => {
    // 字段名映射表
    const fieldLabelMap: Record<string, string> = {
      id: 'ID',
      leave_type: '请假类型',
      start_time: '开始时间',
      end_time: '结束时间',
      duration: '时长(天)',
      reason: '请假原因',
      emergency_contact: '紧急联系人',
      emergency_phone: '联系电话',
      attachment: '附件',
      created_at: '创建时间',
      updated_at: '更新时间'
    }

    return (
      fieldLabelMap[key] ||
      key
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    )
  }

  // 通用字段值格式化
  const formatValue = (key: string, value: any): string => {
    if (value === undefined || value === null || value === '') {
      return '-'
    }

    // 日期时间字段
    if (key.includes('time') || key.includes('date') || key.includes('_at')) {
      return value
    }

    // 请假类型
    if (key === 'leave_type') {
      const leaveTypes: Record<string, string> = {
        '1': '事假',
        '2': '病假',
        '3': '年假',
        '4': '婚假',
        '5': '产假',
        '6': '丧假',
        '7': '调休',
        '8': '其他'
      }
      return leaveTypes[value] || value
    }

    // 数组或对象类型
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value)
      } catch (e) {
        return String(value)
      }
    }

    return String(value)
  }

  // 判断字段是否应该显示
  const shouldDisplayField = (key: string): boolean => {
    // 排除不需要显示的字段
    const excludedFields = ['id', 'form_type', 'business_code', 'definition_id', 'submitter_id']
    return !excludedFields.includes(key)
  }
</script>

<style scoped>
  .form-data-viewer {
    width: 100%;
    min-height: 100px;
    padding: 10px 0;
  }

  .empty-data {
    padding: 40px 0;
    text-align: center;
  }

  .generic-form-display {
    padding: 10px;
  }
</style>
