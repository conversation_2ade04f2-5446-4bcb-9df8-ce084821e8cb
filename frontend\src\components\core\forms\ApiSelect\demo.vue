<!--ApiSelect 组件使用示例-->
<template>
  <div class="api-select-demo">
    <el-card header="ApiSelect 通用API选择器示例">
      <!-- 推荐配置 -->
      <div class="demo-section">
        <h3>1. 推荐配置（表单场景）</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>产品分类选择器</h4>
            <ApiSelect
              v-model="categoryId"
              :api="{ url: '/crm/crm_product_category/options' }"
              placeholder="请选择产品分类"
              clearable
              :auto-load="true"
              :load-on-focus="false"
              @change="handleChange"
            />
            <div class="result">选中值: {{ categoryId }}</div>
            <div class="tip">✅ 表单打开时预加载，用户体验最佳</div>
          </el-col>

          <el-col :span="12">
            <h4>计量单位选择器</h4>
            <ApiSelect
              v-model="unitId"
              :api="{ url: '/crm/crm_product_unit/options' }"
              placeholder="请选择计量单位"
              clearable
              :auto-load="true"
              :load-on-focus="false"
              @change="handleChange"
            />
            <div class="result">选中值: {{ unitId }}</div>
            <div class="tip">✅ 表单打开时预加载，用户体验最佳</div>
          </el-col>
        </el-row>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h3>2. 基础用法</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>单选模式</h4>
            <ApiSelect
              v-model="basicSingle"
              :api="{ url: '/api/users' }"
              placeholder="请选择用户"
              :auto-load="true"
              :load-on-focus="false"
              @change="handleBasicChange"
            />
            <div class="result">选中值: {{ basicSingle }}</div>
          </el-col>

          <el-col :span="12">
            <h4>多选模式</h4>
            <ApiSelect
              v-model="basicMultiple"
              :api="{ url: '/api/users' }"
              :multiple="true"
              placeholder="请选择用户"
              :collapse-tags="true"
              :auto-load="true"
              :load-on-focus="false"
              @change="handleBasicChange"
            />
            <div class="result">选中值: {{ basicMultiple }}</div>
          </el-col>
        </el-row>
      </div>

      <!-- 自定义字段映射 -->
      <div class="demo-section">
        <h3>2. 自定义字段映射</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>部门选择（自定义字段）</h4>
            <ApiSelect
              v-model="customField"
              :api="{ url: '/api/departments' }"
              label-field="dept_name"
              value-field="dept_id"
              extra-field="dept_code"
              :show-option-extra="true"
              placeholder="请选择部门"
            />
            <div class="result">选中值: {{ customField }}</div>
          </el-col>

          <el-col :span="12">
            <h4>产品选择（数据转换）</h4>
            <ApiSelect v-model="transformData" :api="productApiConfig" placeholder="请选择产品" />
            <div class="result">选中值: {{ transformData }}</div>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索配置 -->
      <div class="demo-section">
        <h3>3. 搜索配置</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>最小搜索长度</h4>
            <ApiSelect
              v-model="searchConfig1"
              :api="{ url: '/api/users', searchParam: 'q' }"
              :min-search-length="2"
              :search-delay="500"
              placeholder="至少输入2个字符搜索"
            />
            <div class="result">选中值: {{ searchConfig1 }}</div>
          </el-col>

          <el-col :span="12">
            <h4>禁用缓存</h4>
            <ApiSelect
              v-model="searchConfig2"
              :api="{ url: '/api/users' }"
              :cache-results="false"
              placeholder="每次都重新请求"
            />
            <div class="result">选中值: {{ searchConfig2 }}</div>
          </el-col>
        </el-row>
      </div>

      <!-- 复杂API配置 -->
      <div class="demo-section">
        <h3>4. 复杂API配置</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>POST请求 + 固定参数</h4>
            <ApiSelect
              v-model="complexApi1"
              :api="postApiConfig"
              :multiple="true"
              placeholder="POST请求示例"
              @load-success="handleLoadSuccess"
              @load-error="handleLoadError"
            />
            <div class="result">选中值: {{ complexApi1 }}</div>
          </el-col>

          <el-col :span="12">
            <h4>嵌套数据路径</h4>
            <ApiSelect v-model="complexApi2" :api="nestedApiConfig" placeholder="嵌套数据示例" />
            <div class="result">选中值: {{ complexApi2 }}</div>
          </el-col>
        </el-row>
      </div>

      <!-- 在搜索表单中使用 -->
      <div class="demo-section">
        <h3>5. 搜索表单中使用</h3>
        <ArtSearchBar :items="searchItems" v-model:filter="searchFilter" @search="handleSearch" />
        <div class="result">搜索条件: {{ JSON.stringify(searchFilter, null, 2) }}</div>
      </div>

      <!-- 方法调用示例 -->
      <div class="demo-section">
        <h3>6. 方法调用</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <ApiSelect
              ref="methodDemoRef"
              v-model="methodDemo"
              :api="{ url: '/api/users' }"
              placeholder="方法调用示例"
            />
            <div class="result">选中值: {{ methodDemo }}</div>
          </el-col>

          <el-col :span="12">
            <div class="method-buttons">
              <el-button @click="refreshData">刷新数据</el-button>
              <el-button @click="clearCache">清空缓存</el-button>
              <el-button @click="getOptions">获取选项</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue'
  import { ElMessage } from 'element-plus'
  import ApiSelect from './index.vue'
  import ArtSearchBar from '../art-search-bar/index.vue'

  // 推荐配置示例
  const categoryId = ref(null)
  const unitId = ref(null)

  // 基础用法
  const basicSingle = ref(null)
  const basicMultiple = ref([])

  // 自定义字段映射
  const customField = ref(null)
  const transformData = ref(null)

  // 搜索配置
  const searchConfig1 = ref(null)
  const searchConfig2 = ref(null)

  // 复杂API配置
  const complexApi1 = ref([])
  const complexApi2 = ref(null)

  // 方法调用示例
  const methodDemo = ref(null)
  const methodDemoRef = ref()

  // 搜索表单
  const searchFilter = reactive({
    owner_id: null,
    dept_ids: [],
    status: null
  })

  // API 配置
  const productApiConfig = {
    url: '/api/products',
    method: 'post' as const,
    params: { category: 'electronics' },
    searchParam: 'keyword',
    transform: (data: any[]) =>
      data.map((item) => ({
        id: item.product_id,
        name: `${item.product_name} (¥${item.price})`,
        disabled: !item.available
      }))
  }

  const postApiConfig = {
    url: '/api/search/users',
    method: 'post' as const,
    params: {
      status: 1,
      role: 'employee'
    },
    searchParam: 'q',
    headers: {
      'X-Custom-Header': 'demo-value'
    }
  }

  const nestedApiConfig = {
    url: '/api/complex-data',
    dataPath: 'result.items',
    listPath: 'users',
    transform: (data: any[]) =>
      data.map((item) => ({
        id: item.user_id,
        name: item.full_name,
        disabled: item.status !== 'active'
      }))
  }

  // 搜索表单配置
  const searchItems = [
    {
      label: '负责人',
      prop: 'owner_id',
      type: 'api-select' as const,
      config: {
        api: {
          url: '/api/users',
          params: { role: 'manager' }
        },
        placeholder: '请选择负责人',
        clearable: true
      }
    },
    {
      label: '部门',
      prop: 'dept_ids',
      type: 'api-select' as const,
      config: {
        api: { url: '/api/departments' },
        multiple: true,
        placeholder: '请选择部门',
        collapseTags: true,
        labelField: 'dept_name',
        valueField: 'dept_id'
      }
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select' as const,
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  ]

  // 事件处理
  const handleBasicChange = (value: any, option?: any) => {
    console.log('值变化:', value, option)
  }

  const handleLoadSuccess = (data: any[]) => {
    console.log('数据加载成功:', data)
    ElMessage.success(`加载了 ${data.length} 条数据`)
  }

  const handleLoadError = (error: any) => {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败')
  }

  const handleSearch = () => {
    console.log('搜索条件:', searchFilter)
    ElMessage.info('执行搜索')
  }

  // 方法调用
  const refreshData = () => {
    methodDemoRef.value?.refresh()
    ElMessage.success('数据已刷新')
  }

  const clearCache = () => {
    methodDemoRef.value?.clearCache()
    ElMessage.success('缓存已清空')
  }

  const getOptions = () => {
    const options = methodDemoRef.value?.getOptionList()
    console.log('当前选项:', options)
    ElMessage.info(`当前有 ${options?.length || 0} 个选项`)
  }
</script>

<style lang="scss" scoped>
  .api-select-demo {
    padding: 20px;
  }

  .demo-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 20px;
      color: #303133;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    h4 {
      margin-bottom: 10px;
      color: #606266;
      font-size: 14px;
    }
  }

  .result {
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
  }

  .tip {
    margin-top: 4px;
    padding: 4px 8px;
    background: #f0f9ff;
    border: 1px solid #67c23a;
    border-radius: 4px;
    font-size: 11px;
    color: #67c23a;
  }

  .method-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .el-button {
      width: 100%;
    }
  }

  :deep(.el-card__body) {
    padding: 20px;
  }
</style>
