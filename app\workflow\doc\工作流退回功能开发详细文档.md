# 工作流退回功能开发详细文档

## 项目概述

**项目名称**: 工作流退回功能新增开发  
**开发时间**: 2025年1月12日  
**技术栈**: ThinkPHP8 + Vue3 + Element Plus  
**开发目标**: 在现有工作流系统基础上新增退回功能，支持审批人将流程退回到指定节点

## 需求分析

### 1. 功能需求

#### 1.1 核心功能
- **退回操作**: 审批人可以将当前流程退回到之前的任意节点
- **退回原因**: 必须填写退回原因，记录在审批历史中
- **节点选择**: 支持选择退回到的目标节点（发起人节点或之前的审批节点）
- **状态更新**: 退回后更新流程实例和相关任务的状态
- **消息通知**: 向相关人员发送退回通知

#### 1.2 业务规则
- 只有待处理状态的审批任务可以执行退回操作
- 退回后原审批任务状态变为"已退回"
- 退回后在目标节点创建新的待处理任务
- 退回后流程实例状态变为"审批中"，当前节点更新为目标节点
- 退回操作需要记录详细的审批历史

#### 1.3 用户体验
- 退回按钮与通过、拒绝按钮并列显示
- 退回时弹出对话框选择目标节点和填写退回原因
- 退回成功后显示成功提示并刷新页面
- 支持查看退回历史记录

### 2. 技术需求

#### 2.1 数据库设计
- 无需新增表，使用现有的工作流相关表
- 新增退回相关的操作类型常量
- 扩展审批历史记录支持退回操作

#### 2.2 接口设计
- 新增退回接口：`POST /workflow/task/return`
- 获取可退回节点列表接口：`GET /workflow/task/return-nodes/{taskId}`
- 扩展任务详情接口，返回退回操作权限

#### 2.3 前端组件
- 退回按钮组件
- 退回对话框组件
- 节点选择组件
- 退回历史展示组件

## 详细设计

### 1. 数据库设计

#### 1.1 现有表结构利用
```sql
-- workflow_task 表（无需修改）
-- 使用现有字段记录退回任务

-- workflow_history 表（无需修改）  
-- operation 字段新增退回操作类型：4

-- workflow_instance 表（无需修改）
-- 使用现有字段更新流程状态
```

#### 1.2 常量定义
```php
// app/workflow/constants/WorkflowOperationConstant.php
class WorkflowOperationConstant 
{
    const AGREE = 1;        // 同意
    const REJECT = 2;       // 拒绝  
    const SUBMIT = 3;       // 提交
    const RETURN = 4;       // 退回 (新增)
    const CANCEL = 5;       // 撤销
    const TRANSFER = 6;     // 转办
    const CC = 7;           // 抄送
}
```

### 2. 后端开发

#### 2.1 服务层设计

**WorkflowTaskService 新增方法**:

```php
/**
 * 退回任务到指定节点
 * @param array $params 参数
 * @return bool
 */
public function returnTask(array $params): bool
{
    // 1. 参数验证
    // 2. 获取当前任务和实例信息
    // 3. 验证退回权限和目标节点
    // 4. 更新当前任务状态为已退回
    // 5. 在目标节点创建新任务
    // 6. 更新流程实例状态
    // 7. 记录退回历史
    // 8. 发送退回通知
    // 9. 返回操作结果
}

/**
 * 获取可退回的节点列表
 * @param int $taskId 任务ID
 * @return array
 */
public function getReturnableNodes(int $taskId): array
{
    // 1. 获取任务和实例信息
    // 2. 获取流程定义和节点配置
    // 3. 分析已经过的节点
    // 4. 返回可退回的节点列表
}
```

#### 2.2 控制器设计

**WorkflowTaskController 新增方法**:

```php
/**
 * 退回任务
 * @return Json
 */
public function returnTask(): Json
{
    $params = $this->request->post();
    
    // 参数验证
    $validate = [
        'task_id' => 'require|integer',
        'target_node_id' => 'require',
        'return_reason' => 'require|max:500'
    ];
    
    if (!$this->validate($params, $validate)) {
        return $this->error($this->getError());
    }
    
    $result = $this->taskService->returnTask($params);
    
    if ($result) {
        return $this->success('退回成功');
    } else {
        return $this->error('退回失败');
    }
}

/**
 * 获取可退回节点
 * @param int $taskId
 * @return Json  
 */
public function getReturnableNodes(int $taskId): Json
{
    $nodes = $this->taskService->getReturnableNodes($taskId);
    return $this->success('获取成功', $nodes);
}
```

#### 2.3 路由配置

```php
// app/workflow/route/app.php
Route::group('task', function () {
    // 现有路由...
    Route::post('return', 'TaskController@returnTask');
    Route::get('return-nodes/:task_id', 'TaskController@getReturnableNodes');
});
```

### 3. 前端开发

#### 3.1 组件设计

**退回按钮组件 (ReturnButton.vue)**:
```vue
<template>
  <el-button 
    type="warning" 
    :icon="Back" 
    @click="handleReturn"
    :disabled="!canReturn"
  >
    退回
  </el-button>
</template>

<script setup>
import { Back } from '@element-plus/icons-vue'

const props = defineProps({
  taskId: Number,
  canReturn: Boolean
})

const emit = defineEmits(['return-success'])

const handleReturn = () => {
  // 打开退回对话框
}
</script>
```

**退回对话框组件 (ReturnDialog.vue)**:
```vue
<template>
  <el-dialog 
    v-model="visible" 
    title="退回流程" 
    width="600px"
    @close="handleClose"
  >
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="100px"
    >
      <el-form-item label="退回到" prop="targetNodeId">
        <el-select 
          v-model="form.targetNodeId" 
          placeholder="请选择退回节点"
          style="width: 100%"
        >
          <el-option
            v-for="node in returnableNodes"
            :key="node.nodeId"
            :label="node.nodeName"
            :value="node.nodeId"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="退回原因" prop="returnReason">
        <el-input
          v-model="form.returnReason"
          type="textarea"
          :rows="4"
          placeholder="请输入退回原因"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleConfirm"
        :loading="loading"
      >
        确认退回
      </el-button>
    </template>
  </el-dialog>
</template>
```

#### 3.2 页面集成

**任务详情页面修改**:
```vue
<!-- 在审批按钮组中添加退回按钮 -->
<div class="approval-buttons">
  <el-button type="success" @click="handleApprove">通过</el-button>
  <el-button type="danger" @click="handleReject">拒绝</el-button>
  <ReturnButton 
    :task-id="taskInfo.id"
    :can-return="taskInfo.canReturn"
    @return-success="handleReturnSuccess"
  />
</div>

<!-- 引入退回对话框 -->
<ReturnDialog 
  ref="returnDialogRef"
  @return-success="handleReturnSuccess"
/>
```

### 4. 核心算法设计

#### 4.1 可退回节点计算算法

```php
/**
 * 计算可退回的节点列表
 * @param array $instance 流程实例
 * @param array $flowConfig 流程配置
 * @return array
 */
private function calculateReturnableNodes(array $instance, array $flowConfig): array
{
    $returnableNodes = [];
    
    // 1. 获取审批历史，按时间倒序
    $histories = $this->getApprovalHistories($instance['id']);
    
    // 2. 分析已经过的节点
    $passedNodes = [];
    foreach ($histories as $history) {
        if ($history['operation'] == WorkflowOperationConstant::AGREE) {
            $passedNodes[] = [
                'nodeId' => $history['node_id'],
                'nodeName' => $history['node_name'],
                'operatorName' => $history['operator_name'],
                'operationTime' => $history['operation_time']
            ];
        }
    }
    
    // 3. 添加发起人节点
    $returnableNodes[] = [
        'nodeId' => 'start',
        'nodeName' => '发起人',
        'description' => '退回到发起人重新提交'
    ];
    
    // 4. 添加已通过的审批节点
    foreach ($passedNodes as $node) {
        $returnableNodes[] = [
            'nodeId' => $node['nodeId'],
            'nodeName' => $node['nodeName'],
            'description' => "退回到{$node['nodeName']}({$node['operatorName']})"
        ];
    }
    
    return $returnableNodes;
}
```

#### 4.2 退回操作执行算法

```php
/**
 * 执行退回操作
 * @param array $currentTask 当前任务
 * @param array $instance 流程实例  
 * @param string $targetNodeId 目标节点ID
 * @param string $returnReason 退回原因
 * @return bool
 */
private function executeReturn(array $currentTask, array $instance, string $targetNodeId, string $returnReason): bool
{
    Db::startTrans();
    
    try {
        // 1. 更新当前任务状态为已退回
        $this->updateTaskStatus($currentTask['id'], WorkflowStatusConstant::RETURNED);
        
        // 2. 创建退回历史记录
        $this->createReturnHistory($instance, $currentTask, $targetNodeId, $returnReason);
        
        // 3. 处理目标节点
        if ($targetNodeId === 'start') {
            // 退回到发起人
            $this->returnToSubmitter($instance);
        } else {
            // 退回到指定审批节点
            $this->returnToApprovalNode($instance, $targetNodeId);
        }
        
        // 4. 更新流程实例状态
        $this->updateInstanceStatus($instance['id'], $targetNodeId);
        
        // 5. 发送退回通知
        $this->sendReturnNotification($instance, $currentTask, $targetNodeId, $returnReason);
        
        Db::commit();
        return true;
        
    } catch (\Exception $e) {
        Db::rollback();
        Log::error('退回操作失败: ' . $e->getMessage());
        return false;
    }
}
```

### 5. 消息通知设计

#### 5.1 退回通知模板

```sql
-- 新增退回通知模板
INSERT INTO `notice_template` (
    `code`, `name`, `title`, `content`, `module_code`, 
    `send_channels`, `status`, `creator_id`, `tenant_id`
) VALUES (
    'workflow_task_returned',
    '工作流退回通知',
    '您的申请被退回：${title}',
    '您的申请被退回\n流程标题：${title}\n退回人：${returner_name}\n退回原因：${return_reason}\n退回时间：${return_time}\n请重新处理。',
    'workflow',
    'site,wework',
    1, 1, 1
);
```

#### 5.2 通知变量配置

```json
{
  "variables": [
    {
      "name": "流程标题",
      "code": "title", 
      "field": "title",
      "required": true
    },
    {
      "name": "退回人",
      "code": "returner_name",
      "field": "returner_name", 
      "required": true
    },
    {
      "name": "退回原因",
      "code": "return_reason",
      "field": "return_reason",
      "required": true
    },
    {
      "name": "退回时间", 
      "code": "return_time",
      "field": "return_time",
      "required": true
    }
  ]
}
```

## 开发计划

### 第一阶段：后端开发 (预计2天)

#### Day 1: 核心服务开发
- [ ] 新增退回操作常量定义
- [ ] 实现 `returnTask` 方法
- [ ] 实现 `getReturnableNodes` 方法  
- [ ] 实现退回算法核心逻辑
- [ ] 编写单元测试

#### Day 2: 接口和通知开发
- [ ] 新增退回相关控制器方法
- [ ] 配置路由
- [ ] 实现退回通知功能
- [ ] 创建退回通知模板
- [ ] 接口测试

### 第二阶段：前端开发 (预计2天)

#### Day 3: 组件开发
- [ ] 开发退回按钮组件
- [ ] 开发退回对话框组件
- [ ] 开发节点选择组件
- [ ] 组件单元测试

#### Day 4: 页面集成
- [ ] 集成退回功能到任务详情页
- [ ] 集成退回功能到任务列表页
- [ ] 优化用户交互体验
- [ ] 前端功能测试

### 第三阶段：测试和优化 (预计1天)

#### Day 5: 综合测试
- [ ] 功能完整性测试
- [ ] 边界条件测试
- [ ] 性能测试
- [ ] 用户体验优化
- [ ] 文档完善

## 测试方案

### 1. 单元测试

#### 1.1 后端测试
```php
// tests/workflow/ReturnTaskTest.php
class ReturnTaskTest extends TestCase
{
    public function testReturnToSubmitter()
    {
        // 测试退回到发起人
    }
    
    public function testReturnToApprovalNode() 
    {
        // 测试退回到审批节点
    }
    
    public function testGetReturnableNodes()
    {
        // 测试获取可退回节点
    }
}
```

#### 1.2 前端测试
```javascript
// tests/components/ReturnDialog.test.js
describe('ReturnDialog', () => {
  test('should render correctly', () => {
    // 测试组件渲染
  })
  
  test('should validate form', () => {
    // 测试表单验证
  })
  
  test('should emit return event', () => {
    // 测试退回事件
  })
})
```

### 2. 集成测试

#### 2.1 测试场景
1. **正常退回流程**
   - 创建测试流程实例
   - 执行审批操作
   - 执行退回操作
   - 验证状态更新
   - 验证通知发送

2. **边界条件测试**
   - 退回到不存在的节点
   - 重复退回操作
   - 权限不足的退回
   - 并发退回操作

3. **异常情况测试**
   - 数据库连接异常
   - 通知发送失败
   - 流程配置错误

### 3. 性能测试

#### 3.1 测试指标
- 退回操作响应时间 < 2秒
- 获取可退回节点响应时间 < 1秒
- 并发退回操作支持 > 100/秒
- 数据库查询优化

## 部署方案

### 1. 数据库更新

```sql
-- 1. 新增退回通知模板
INSERT INTO `notice_template` (...);

-- 2. 更新模板变量配置
UPDATE `notice_template` SET `variables_config` = '...' WHERE `code` = 'workflow_task_returned';
```

### 2. 代码部署

```bash
# 1. 备份现有代码
cp -r app/workflow app/workflow.backup

# 2. 部署新代码
git pull origin main

# 3. 清除缓存
php think clear

# 4. 重启服务
systemctl restart nginx
systemctl restart php-fpm
```

### 3. 验证部署

```bash
# 1. 检查接口可用性
curl -X POST /workflow/task/return

# 2. 检查前端页面
curl -X GET /workflow/task/detail/1

# 3. 检查日志
tail -f runtime/log/$(date +%Y%m)/$(date +%d).log
```

## 风险评估

### 1. 技术风险

#### 1.1 数据一致性风险
- **风险**: 退回操作中途失败导致数据不一致
- **应对**: 使用数据库事务确保原子性操作

#### 1.2 性能风险  
- **风险**: 复杂流程的节点计算影响性能
- **应对**: 优化算法，添加缓存机制

#### 1.3 并发风险
- **风险**: 同时退回和审批操作导致冲突
- **应对**: 添加乐观锁机制

### 2. 业务风险

#### 2.1 权限风险
- **风险**: 退回权限控制不当
- **应对**: 严格的权限验证机制

#### 2.2 流程风险
- **风险**: 退回后流程状态混乱
- **应对**: 完善的状态管理和历史记录

## 后续迭代

### 1. 功能增强

#### 1.1 批量退回
- 支持批量选择任务进行退回
- 批量退回进度显示
- 批量操作结果统计

#### 1.2 退回模板
- 预设常用退回原因模板
- 支持自定义退回原因模板
- 退回原因智能推荐

#### 1.3 退回统计
- 退回率统计分析
- 退回原因分类统计
- 退回趋势分析报表

### 2. 用户体验优化

#### 2.1 交互优化
- 退回操作确认机制
- 退回进度实时显示
- 退回结果反馈优化

#### 2.2 移动端适配
- 移动端退回界面优化
- 触摸操作体验优化
- 响应式布局适配

### 3. 系统集成

#### 3.1 消息推送
- 微信企业号推送
- 邮件通知集成
- 短信通知集成

#### 3.2 审计日志
- 详细的操作审计日志
- 退回操作追踪
- 合规性报告生成

---

**文档版本**: v1.0  
**创建时间**: 2025-01-12  
**创建人**: Augment Agent  
**审核状态**: 待审核  
**预计完成时间**: 2025-01-17
