# ApiSelect 使用指南

## 🎯 快速开始

> **最新优化**：组件已优化加载策略，实现零额外请求和精确监听机制

### 1. 表单中的推荐配置

```vue
<template>
  <el-form :model="formData">
    <!-- 产品分类选择器 -->
    <el-form-item label="产品分类" prop="category_id">
      <ApiSelect
        v-model="formData.category_id"
        :api="{ url: '/crm/crm_product_category/options' }"
        placeholder="请选择产品分类"
        clearable
        :auto-load="true"
        :load-on-focus="false"
      />
    </el-form-item>
    
    <!-- 计量单位选择器 -->
    <el-form-item label="计量单位" prop="unit_id">
      <ApiSelect
        v-model="formData.unit_id"
        :api="{ url: '/crm/crm_product_unit/options' }"
        placeholder="请选择计量单位"
        clearable
        :auto-load="true"
        :load-on-focus="false"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'

const formData = reactive({
  category_id: null,  // 注意：使用 null 而不是 0
  unit_id: null,      // 注意：使用 null 而不是 0
  name: '',
  // ...其他字段
})
</script>
```

### 2. 搜索表单中的配置

```vue
<template>
  <ArtSearchBar :items="searchItems" v-model:filter="searchFilter" />
</template>

<script setup>
const searchItems = [
  {
    label: '产品分类',
    prop: 'category_id',
    type: 'api-select',
    config: {
      api: { url: '/crm/crm_product_category/options' },
      placeholder: '请选择产品分类',
      clearable: true,
      autoLoad: false,      // 搜索场景可以按需加载
      loadOnFocus: true
    }
  }
]
</script>
```

## 📋 配置对比

| 场景 | autoLoad | loadOnFocus | 用户体验 | 性能特点 | 适用场景 |
|------|----------|-------------|----------|----------|----------|
| 表单选择器 | ✅ true | ❌ false | 最佳 | 零额外请求 | 新增/编辑表单 |
| 搜索筛选器 | ❌ false | ✅ true | 良好 | 按需加载 | 搜索表单 |

## ⚠️ 常见错误

### ❌ 错误配置
```vue
<!-- 会导致重复请求 -->
<ApiSelect
  :auto-load="true"
  :load-on-focus="true"  <!-- 错误：会重复请求 -->
/>

<!-- 初始值错误 -->
<script setup>
const formData = reactive({
  category_id: 0,  <!-- 错误：应该使用 null -->
})
</script>
```

### ✅ 正确配置
```vue
<!-- 表单场景推荐配置 -->
<ApiSelect
  :auto-load="true"
  :load-on-focus="false"
/>

<!-- 正确的初始值 -->
<script setup>
const formData = reactive({
  category_id: null,  <!-- 正确：使用 null 显示 placeholder -->
})
</script>
```

## 🔧 API 接口要求

### 标准响应格式
```json
{
  "code": 1,
  "data": [
    {
      "id": 1,
      "name": "电子产品"
    },
    {
      "id": 2, 
      "name": "办公用品"
    }
  ],
  "message": "success"
}
```

### 后端实现示例
```php
// 控制器方法
public function options()
{
    $result = $this->service->getOptions();
    return $this->success('获取成功', $result);
}

// 服务方法
public function getOptions(): array
{
    $where = [
        ['status', '=', 1] // 只获取启用状态
    ];
    
    $list = $this->model->field('id,name')
                       ->where($where)
                       ->order('id asc')
                       ->select();
    
    return $list->toArray();
}
```

## 🎯 最佳实践总结

1. **表单场景**：使用 `autoLoad: true, loadOnFocus: false`
2. **初始值**：使用 `null` 而不是 `0`
3. **API设计**：返回 `{id, name}` 格式的数组
4. **缓存策略**：启用 `cacheResults: true`
5. **错误处理**：监听 `load-error` 事件
6. **性能优化**：避免同时启用 `autoLoad` 和 `loadOnFocus`
7. **监听策略**：组件只监听API URL变化，避免误触发

## 🚀 性能优化成果

- ✅ **零额外请求**：选择值时无额外API请求
- ✅ **智能监听**：只监听API URL变化，避免误触发
- ✅ **防重复机制**：完善的防重复请求机制
- ✅ **状态管理**：精确的初始化状态管理

这样配置可以获得最佳的用户体验和性能！
