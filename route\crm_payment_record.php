<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 回款记录表路由
Route::group('api/crm/crm_payment_record', function () {
	Route::get('index', 'app\crm\controller\CrmPaymentRecordController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmPaymentRecordController@detail');
	Route::post('add', 'app\crm\controller\CrmPaymentRecordController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmPaymentRecordController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmPaymentRecordController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmPaymentRecordController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmPaymentRecordController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmPaymentRecordController@status');
	Route::post('import', 'app\crm\controller\CrmPaymentRecordController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmPaymentRecordController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmPaymentRecordController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmPaymentRecordController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);