<template>
  <div class="project-card" @click="handleCardClick">
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="project-info">
        <h3 class="project-name" :title="project.name">{{ project.name }}</h3>
        <p class="project-description" :title="project.description">
          {{ project.description || '暂无描述' }}
        </p>
      </div>

      <div class="header-actions">
        <!-- 状态指示器移到这里，避免与操作按钮冲突 -->
        <div class="status-badge" :class="statusClass">
          <el-icon>
            <component :is="statusIcon" />
          </el-icon>
        </div>

        <div class="card-actions" @click.stop>
          <el-dropdown trigger="hover">
            <el-button circle class="action-btn">
              <el-icon :size="16">
                <MoreFilled />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleCardClick">
                  <el-icon>
                    <View />
                  </el-icon>
                  详情
                </el-dropdown-item>
                <el-dropdown-item @click="handleEdit">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  编辑
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleDelete">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-section">
      <div class="progress-info">
        <span class="progress-label">项目进度</span>
        <span class="progress-value">{{ progressPercentage }}%</span>
      </div>
      <el-progress
        :percentage="progressPercentage"
        :color="progressColor"
        :stroke-width="6"
        :show-text="false"
      />
    </div>

    <!-- 项目统计 -->
    <div class="project-stats">
      <div class="stat-item">
        <el-icon>
          <Tickets />
        </el-icon>
        <span class="stat-label">任务</span>
        <span class="stat-value">{{ project.task_count || 0 }}</span>
      </div>
      <div class="stat-item">
        <el-icon>
          <User />
        </el-icon>
        <span class="stat-label">成员</span>
        <span class="stat-value">{{ project.member_count || 0 }}</span>
      </div>
      <!-- TODO: 工时显示暂时注释，后续需要完善工时估算准确性和管理机制 -->
      <!--
      <div class="stat-item">
        <el-icon>
          <Clock />
        </el-icon>
        <span class="stat-label">工时</span>
        <span class="stat-value">{{ project.total_hours || 0 }}h</span>
      </div>
      -->
    </div>

    <!-- 项目信息 -->
    <div class="project-meta">
      <div class="meta-item">
        <el-icon>
          <User />
        </el-icon>
        <span class="meta-label">负责人:</span>
        <span class="meta-value">{{ project.owner_name || '未分配' }}</span>
      </div>
      <div class="meta-item">
        <el-icon>
          <Calendar />
        </el-icon>
        <span class="meta-label">截止时间:</span>
        <span class="meta-value" :class="{ overdue: isOverdue }">
          {{ formatDate(project.end_date) }}
        </span>
      </div>
    </div>

    <!-- 项目标签 -->
    <div class="project-tags" v-if="project.tags && project.tags.length">
      <el-tag v-for="tag in project.tags" :key="tag" size="small" type="info" effect="plain">
        {{ tag }}
      </el-tag>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity" v-if="project.last_activity">
      <div class="activity-info">
        <el-icon>
          <Clock />
        </el-icon>
        <span class="activity-text">{{ project.last_activity }}</span>
      </div>
      <span class="activity-time">{{ formatRelativeTime(project.updated_at) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    MoreFilled,
    View,
    Edit,
    Delete,
    Tickets,
    User,
    Clock,
    Calendar,
    CircleCheck,
    Loading,
    VideoPause,
    CircleClose
  } from '@element-plus/icons-vue'
  import { formatDate, formatRelativeTime } from '@/utils/date'
  import { isAllEmpty } from '@pureadmin/utils'
  import { useRouter } from 'vue-router'

  // Props
  interface Props {
    project: {
      id: number
      name: string
      description?: string
      status: number
      progress?: number
      task_count?: number
      member_count?: number
      total_hours?: number
      owner_name?: string
      end_date?: string
      tags?: string[]
      last_activity?: string
      updated_at?: string
    }
  }

  const props = defineProps<Props>()

  // Router
  const router = useRouter()

  // Emits
  const emit = defineEmits<{
    'view-detail': [project: any]
    edit: [project: any]
    delete: [project: any]
  }>()

  // 计算属性
  const statusClass = computed(() => {
    const statusMap = {
      1: 'status-active', // 进行中
      2: 'status-completed', // 已完成
      3: 'status-paused', // 已暂停
      4: 'status-cancelled' // 已取消
    }
    return statusMap[props.project.status] || 'status-active'
  })

  const statusIcon = computed(() => {
    const iconMap = {
      1: Loading, // 进行中
      2: CircleCheck, // 已完成
      3: VideoPause, // 已暂停
      4: CircleClose // 已取消
    }
    return iconMap[props.project.status] || Loading
  })

  const progressPercentage = computed(() => {
    return Math.round(props.project.progress || 0)
  })

  const progressColor = computed(() => {
    const percentage = progressPercentage.value
    if (percentage >= 80) return '#67c23a'
    if (percentage >= 50) return '#e6a23c'
    return '#f56c6c'
  })

  const isOverdue = computed(() => {
    if (!props.project.end_date) return false
    return new Date(props.project.end_date) < new Date()
  })

  // 方法
  const handleCardClick = () => {
    // 点击卡片查看详情，跳转到项目详情页
    if (props.project.id) {
      router.push(`/project/detail/${props.project.id}`)
    }
  }

  // 操作按钮处理方法 - 阻止事件冒泡
  /*const handleViewDetail = (event?: Event) => {
    if (event) {
      event.stopPropagation()
    }
    emit('view-detail', props.project)
  }*/

  const handleEdit = (event?: Event) => {
    if (event) {
      event.stopPropagation()
    }
    emit('edit', props.project)
  }

  const handleDelete = (event?: Event) => {
    if (event) {
      event.stopPropagation()
    }
    emit('delete', props.project)
  }
</script>

<style scoped lang="scss">
  .project-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }
    position: relative;
    border: 1px solid #f0f0f0;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .project-info {
        flex: 1;
        margin-right: 12px;

        .project-name {
          font-size: 16px;
          font-weight: 600;
          color: #1f2329;
          margin: 0 0 8px 0;
          line-height: 1.4;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }

        .project-description {
          font-size: 13px;
          color: #86909c;
          margin: 0;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
          overflow: hidden;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;

        .status-badge {
          width: 24px; // 增大状态图标
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px; // 增大图标字体

          &.status-active {
            background: #e6f7ff;
            color: #1890ff;

            // 黑暗模式适配
            html.dark & {
              background: rgba(var(--art-info), 0.15);
              color: rgb(var(--art-info));
            }
          }

          &.status-completed {
            background: #f6ffed;
            color: #52c41a;

            // 黑暗模式适配
            html.dark & {
              background: rgba(var(--art-success), 0.15);
              color: rgb(var(--art-success));
            }
          }

          &.status-paused {
            background: #fff7e6;
            color: #fa8c16;

            // 黑暗模式适配
            html.dark & {
              background: rgba(var(--art-warning), 0.15);
              color: rgb(var(--art-warning));
            }
          }

          &.status-cancelled {
            background: #fff2f0;
            color: #ff4d4f;

            // 黑暗模式适配
            html.dark & {
              background: rgba(var(--art-danger), 0.15);
              color: rgb(var(--art-danger));
            }
          }
        }

        .card-actions {
          opacity: 1; // 改为一直显示
          transition: all 0.2s;

          :deep(.action-btn) {
            color: #86909c !important;
            width: 28px !important;
            height: 28px !important;
            border-radius: 50% !important;
            padding: 0 !important;
            min-width: unset !important;
            border: none !important;
            background-color: #f8fafc !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;

            &:hover {
              color: #1664ff !important;
              transform: scale(1.1);
              border: none !important;
            }

            &:focus {
              color: #1664ff !important;
              background-color: #f0f9ff !important;
              border: none !important;
              outline: none !important;
              box-shadow: none !important;
            }

            &:active {
              color: #1664ff !important;
              background-color: #e6f4ff !important;
              transform: scale(1.05);
            }

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600) !important;
              background-color: var(--art-color) !important;

              &:hover {
                color: rgb(var(--art-primary)) !important;
              }

              &:focus {
                color: rgb(var(--art-primary)) !important;
                background-color: rgba(var(--art-primary), 0.1) !important;
              }

              &:active {
                color: rgb(var(--art-primary)) !important;
                background-color: rgba(var(--art-primary), 0.15) !important;
              }
            }
          }
        }
      }
    }

    .progress-section {
      margin-bottom: 16px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .progress-label {
          font-size: 13px;
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .progress-value {
          font-size: 13px;
          font-weight: 600;
          color: #1f2329;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }
      }
    }

    .project-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 12px;
      background: #f7f8fa;
      border-radius: 6px;

      // 黑暗模式适配
      html.dark & {
        background: var(--art-color);
        border: 1px solid var(--art-border-color);
      }

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .el-icon {
          font-size: 16px;
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .stat-label {
          font-size: 12px;
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .stat-value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2329;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }
      }
    }

    .project-meta {
      margin-bottom: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 6px;
        font-size: 13px;

        .el-icon {
          font-size: 14px;
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .meta-label {
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .meta-value {
          color: #1f2329;

          &.overdue {
            color: #ff4d4f;

            // 黑暗模式适配
            html.dark & {
              color: rgb(var(--art-danger));
            }
          }

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }
      }
    }

    .project-tags {
      margin-bottom: 12px;
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }

    .recent-activity {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      // 黑暗模式适配
      html.dark & {
        border-top-color: var(--art-border-color);
      }

      .activity-info {
        display: flex;
        align-items: center;
        gap: 6px;
        flex: 1;

        .el-icon {
          font-size: 14px;
          color: #86909c;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .activity-text {
          font-size: 12px;
          color: #86909c;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }
      }

      .activity-time {
        font-size: 12px;
        color: #c9cdd4;
        white-space: nowrap;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-500);
        }
      }
    }
  }
</style>
