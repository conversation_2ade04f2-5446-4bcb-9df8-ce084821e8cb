import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 每日报价单表相关接口
 */
export class DailyPriceOrderApi {
  /**
   * 获取每日报价单表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/daily/daily_price_order/index',
      params
    })
  }

  /**
   * 获取每日报价单表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/daily/daily_price_order/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加每日报价单表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/daily/daily_price_order/add',
      data
    })
  }

  /**
   * 更新每日报价单表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/daily/daily_price_order/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除每日报价单表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/daily/daily_price_order/delete/${id}`
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */

  /*static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/daily/daily_price_order/updateField',
      data
    })
  }*/

  /**
   * 修改每日报价单表状态
   * @param data 状态数据
   */

  /*static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/daily/daily_price_order/status/${data.id}`,
      data
    })
  }*/

  // ==================== 业务扩展接口 ====================

  /**
   * 提交审批
   * @param id 报价单ID
   */
  static submitApproval(id: number) {
    return request.post<BaseResult>({
      url: '/daily/daily_price_order/submit_approval',
      data: { id }
    })
  }

  /**
   * 撤回审批
   * @param id 报价单ID
   */
  static recallApproval(id: number) {
    return request.post<BaseResult>({
      url: '/daily/daily_price_order/recall_approval',
      data: { id }
    })
  }

  /**
   * 作废报价单
   * @param id 报价单ID
   * @param reason 作废原因
   */
  static voidOrder(id: number, reason: string) {
    return request.post<BaseResult>({
      url: '/daily/daily_price_order/void_order',
      data: { id, reason }
    })
  }

  /**
   * 获取供应商列表
   */
  static getSupplierList() {
    return request.get<BaseResult>({
      url: '/daily/daily_price_order/get_supplier_list'
    })
  }

  /**
   * 获取产品列表
   * @param supplierId 供应商ID
   */
  static getProductList(supplierId?: number) {
    return request.get<BaseResult>({
      url: '/daily/daily_price_order/get_product_list',
      params: { supplier_id: supplierId }
    })
  }

  /**
   * 获取昨日价格数据
   */
  static getYesterdayPrices() {
    return request.get<BaseResult>({
      url: '/daily/daily_price_order/get_yesterday_prices'
    })
  }
}

// ==================== 类型定义 ====================

/**
 * 报价单数据类型
 */
export interface DailyPriceOrder {
  id?: number
  tenant_id?: number
  title: string
  price_date: string
  total_items: number
  remark?: string
  status: number
  approval_status?: number
  workflow_instance_id?: number
  submit_time?: string
  approval_time?: string
  submitter_id?: number
  void_reason?: string
  void_time?: string
  void_user_id?: number
  creator_id?: number
  updated_id?: number
  created_at?: string
  updated_at?: string
}

/**
 * 报价明细数据类型
 */
export interface DailyPriceItem {
  id?: number
  tenant_id?: number
  order_id: number
  supplier_id: number | null
  product_id: number | null
  unit_price: number
  old_price?: number // 昨日价格
  price_change?: number // 价格变动金额
  change_rate?: number // 变动比例
  price_change_rate?: number | null // 涨幅百分比（前端计算用）
  stock_price: number
  stock_qty: number
  policy_remark?: string
  is_manual_price: number
  sort_order: number
  status: number
  created_at?: string
  updated_at?: string

  // 前端验证字段
  isDuplicate?: boolean

  // 关联数据
  supplier?: any
  product?: any
}

/**
 * 审批状态枚举
 */
export enum ApprovalStatus {
  DRAFT = 0, // 草稿
  PENDING = 1, // 审批中
  APPROVED = 2, // 已通过
  REJECTED = 3, // 已拒绝
  TERMINATED = 4, // 已终止
  RECALLED = 5, // 已撤回
  VOIDED = 6 // 已作废
}

/**
 * 审批状态文本映射
 */
export const ApprovalStatusText = {
  [ApprovalStatus.DRAFT]: '草稿',
  [ApprovalStatus.PENDING]: '审批中',
  [ApprovalStatus.APPROVED]: '已通过',
  [ApprovalStatus.REJECTED]: '已拒绝',
  [ApprovalStatus.TERMINATED]: '已终止',
  [ApprovalStatus.RECALLED]: '已撤回',
  [ApprovalStatus.VOIDED]: '已作废'
}

/**
 * 审批状态样式类映射
 */
export const ApprovalStatusClass = {
  [ApprovalStatus.DRAFT]: 'status-draft',
  [ApprovalStatus.PENDING]: 'status-pending',
  [ApprovalStatus.APPROVED]: 'status-approved',
  [ApprovalStatus.REJECTED]: 'status-rejected',
  [ApprovalStatus.TERMINATED]: 'status-terminated',
  [ApprovalStatus.RECALLED]: 'status-recalled',
  [ApprovalStatus.VOIDED]: 'status-voided'
}

/**
 * 价格历史记录数据类型
 */
export interface DailyPriceHistory {
  id?: number
  tenant_id?: number
  order_id: number
  item_id: number
  supplier_id: number
  product_id: number
  old_price: number
  new_price: number
  price_change: number
  change_rate: number
  change_date: string
  approval_time: string
  creator_id?: number
  created_at?: string

  // 关联数据
  order?: DailyPriceOrder
  item?: DailyPriceItem
  supplier?: any
  product?: any
  creator?: any
}

/**
 * 统计数据类型
 */
export interface DailyPriceStatistics {
  total_orders: number
  approved_orders: number
  pending_orders: number
  approval_rate: number
  price_changes: {
    rise_count: number
    fall_count: number
    stable_count: number
    avg_change_rate: number
    max_rise_rate: number
    max_fall_rate: number
  }
}

/**
 * 供应商数据类型
 */
export interface Supplier {
  id: number
  name: string
  contact_person?: string
  contact_phone?: string
}

/**
 * 产品数据类型
 */
export interface Product {
  id: number
  name: string
  model?: string
  unit?: string
  supplier_id?: number
}
