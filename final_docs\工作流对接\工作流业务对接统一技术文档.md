# 工作流业务对接统一技术文档

## 📋 文档概述

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**适用范围：** 所有需要集成工作流审批的业务模块  
**技术栈：** ThinkPHP8 + 动态工厂模式 + FormServiceInterface  

## 🏗️ 架构设计

### 1. 整体架构图

```mermaid
graph TD
    A[业务Controller] --> B[业务Service]
    B --> C[FormServiceInterface实现]
    C --> D[DynamicWorkflowFactory]
    D --> E[WorkflowInstanceService]
    E --> F[工作流引擎]
    F --> G[审批回调]
    G --> C
    
    C --> H[BaseModel统一保存]
    H --> I[数据库]
    
    J[workflow_type配置表] --> D
```

### 2. 核心组件说明

| 组件 | 职责 | 实现方式 |
|------|------|----------|
| **FormServiceInterface** | 统一业务表单接口 | 7个标准方法 |
| **DynamicWorkflowFactory** | 动态Service创建 | 基于workflow_type表 |
| **WorkflowInstanceService** | 工作流实例管理 | 统一的流程控制 |
| **BaseModel** | 统一数据保存 | saveByCreate/saveByUpdate/batchSave |

## 📝 FormServiceInterface接口规范

### 接口定义

```php
interface FormServiceInterface
{
    /**
     * 获取表单数据
     * @param int $id 表单ID
     * @return array 表单详情数据
     */
    public function getFormData(int $id): array;

    /**
     * 保存表单数据
     * @param array $data 表单数据
     * @return array [id, formData]
     */
    public function saveForm(array $data): array;

    /**
     * 更新表单数据
     * @param int $id 表单ID
     * @param array $data 表单数据
     * @return bool 是否成功
     */
    public function updateForm(int $id, array $data): bool;

    /**
     * 删除表单
     * @param int $id 记录ID
     * @return bool 是否成功
     */
    public function deleteForm(int $id): bool;

    /**
     * 更新表单状态（工作流回调）
     * @param int $id 表单ID
     * @param int $status 状态值
     * @param array $extra 额外数据
     * @return bool 是否成功
     */
    public function updateFormStatus(int $id, int $status, array $extra = []): bool;

    /**
     * 获取流程实例标题
     * @param $formData 表单数据
     * @return string 实例标题
     */
    public function getInstanceTitle($formData): string;

    /**
     * 验证表单数据
     * @param array $data 表单数据
     * @param string $scene 验证场景
     * @return array 验证后的数据
     */
    public function validateFormData(array $data, string $scene = 'create'): array;
}
```

### 方法实现标准

#### 1. getFormData方法
```php
public function getFormData(int $id): array
{
    $record = $this->getDetail($id);
    if ($record->isEmpty()) {
        throw new BusinessException('记录不存在');
    }
    return $record->toArray();
}
```

#### 2. saveForm方法
```php
public function saveForm(array $data): array
{
    // 1. 数据验证
    $data = $this->validateFormData($data, 'create');
    
    // 2. 设置默认值
    $data['approval_status'] = BusinessModel::STATUS_DRAFT;
    $data['workflow_instance_id'] = 0;
    $data['submitter_id'] = $data['submitter_id'] ?? get_user_id();
    
    // 3. 使用模型层统一保存
    $model = new BusinessModel();
    $id = $model->saveByCreate($data);
    
    if (!$id) {
        throw new BusinessException('保存失败');
    }
    
    // 4. 获取完整数据
    $formData = $this->getFormData($id);
    
    Log::info('表单保存成功', ['id' => $id]);
    
    return [$id, $formData];
}
```

#### 3. updateForm方法
```php
public function updateForm(int $id, array $data): bool
{
    // 1. 先查询记录，确保记录存在
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    // 2. 检查是否可以编辑
    if (!in_array($record->approval_status, [
        BusinessModel::STATUS_DRAFT,
        BusinessModel::STATUS_REJECTED,
        BusinessModel::STATUS_RECALLED
    ])) {
        throw new BusinessException('当前状态不允许编辑');
    }
    
    // 3. 数据验证
    $data = $this->validateFormData($data, 'update');
    
    // 4. 使用模型层统一更新
    $result = $record->saveByUpdate($data);
    
    if ($result) {
        Log::info('表单更新成功', ['id' => $id]);
    }
    
    return $result;
}
```

#### 4. updateFormStatus方法
```php
public function updateFormStatus(int $id, int $status, array $extra = []): bool
{
    // 1. 检查记录是否存在
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    // 2. 准备更新数据
    $updateData = [
        'approval_status' => $status,
        'updated_id' => get_user_id()
    ];
    
    // 3. 根据状态设置相关字段
    switch ($status) {
        case BusinessModel::STATUS_PENDING:
            $updateData['submit_time'] = date('Y-m-d H:i:s');
            break;
        case BusinessModel::STATUS_APPROVED:
        case BusinessModel::STATUS_REJECTED:
        case BusinessModel::STATUS_TERMINATED:
            $updateData['approval_time'] = date('Y-m-d H:i:s');
            break;
    }
    
    // 4. 使用模型层统一更新
    $result = $record->saveByUpdate($updateData);
    
    if ($result) {
        Log::info('状态更新成功', ['id' => $id, 'status' => $status]);
        
        // 5. 审批完成后的业务处理
        if ($status === BusinessModel::STATUS_APPROVED) {
            $this->afterApprovalComplete($record, $status, $extra['opinion'] ?? '');
        }
    }
    
    return $result;
}
```

#### 5. getInstanceTitle方法
```php
public function getInstanceTitle($formData): string
{
    // 根据业务特点生成标题
    $date = $formData['business_date'] ?? date('Y-m-d');
    $amount = $formData['amount'] ?? 0;
    return "业务审批-{$date}(金额:{$amount})";
}
```

#### 6. validateFormData方法
```php
public function validateFormData(array $data, string $scene = 'create'): array
{
    // 1. 基础验证
    if ($scene === 'create') {
        if (empty($data['business_date'])) {
            throw new BusinessException('业务日期不能为空');
        }
        
        // 业务特定验证...
    }
    
    // 2. 数据清理和格式化
    if (isset($data['business_date'])) {
        $data['business_date'] = date('Y-m-d', strtotime($data['business_date']));
    }
    
    Log::info('数据验证通过', ['scene' => $scene]);
    
    return $data;
}
```

#### 7. deleteForm方法
```php
public function deleteForm(int $id): bool
{
    // 1. 检查记录是否存在
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    // 2. 检查是否可以删除
    if ($record->approval_status !== BusinessModel::STATUS_DRAFT) {
        throw new BusinessException('只有草稿状态的记录可以删除');
    }
    
    // 3. 执行删除（软删除）
    $result = $record->delete();
    
    if ($result) {
        Log::info('记录删除成功', ['id' => $id]);
    }
    
    return $result !== false;
}
```

## 🏭 动态工厂配置

### 1. workflow_type表配置

```sql
INSERT INTO workflow_type (name, module_code, business_code, status) VALUES
('请假申请', 'hr', 'hr_leave', 1),
('出差申请', 'hr', 'hr_travel', 1),
('合同审批', 'crm', 'crm_contract', 1),
('回款审批', 'crm', 'crm_contract_receivable', 1),
('每日报价审批', 'daily', 'daily_price_order', 1);
```

### 2. 动态工厂命名规则

```php
// 业务代码转换规则
$businessCode = 'daily_price_order';
$businessName = str_replace('_', '', ucwords($businessCode, '_')); // DailyPriceOrder
$className = "\\app\\{$moduleCode}\\service\\{$businessName}Service";
// 结果: \app\daily\service\DailyPriceOrderService
```

### 3. Service创建验证

```php
// 使用动态工厂创建Service
$service = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
if (!$service) {
    throw new BusinessException("不支持的业务类型：{$businessCode}");
}

// 验证接口实现
if (!$service instanceof FormServiceInterface) {
    throw new BusinessException("Service未正确实现FormServiceInterface接口");
}
```

## 📊 业务模型标准

### 1. 模型基础配置

```php
class BusinessModel extends BaseModel
{
    use CreatorTrait; // 如果需要创建人关联
    
    // 设置表名
    protected $name = 'business_table';
    
    // 状态常量
    const STATUS_DRAFT = 1;      // 草稿
    const STATUS_PENDING = 2;    // 待审批
    const STATUS_APPROVED = 3;   // 已审批
    const STATUS_REJECTED = 4;   // 已驳回
    const STATUS_RECALLED = 5;   // 已撤回
    const STATUS_TERMINATED = 6; // 已终止
    
    // 类型转换
    protected $type = [
        'approval_status' => 'integer',
        'workflow_instance_id' => 'integer',
        'submitter_id' => 'integer',
        'creator_id' => 'integer',
        'updated_id' => 'integer',
    ];
}
```

### 2. 统一保存方法使用

```php
// 创建记录
$model = new BusinessModel();
$id = $model->saveByCreate($data); // 自动填充租户ID和创建人ID

// 更新记录
$model = BusinessModel::find($id);
$result = $model->saveByUpdate($data); // 自动保护权限字段

// 批量保存
$model = new BusinessModel();
$insertCount = $model->batchSave($dataList); // 自动处理权限字段
```

## 🔄 工作流集成流程

### 1. 提交审批流程

```php
// Controller层
public function submitApproval(Request $request): Json
{
    $id = $request->param('id');
    $businessCode = 'your_business_code';
    
    // 1. 获取FormService
    $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
    if (!$formService) {
        return $this->error('不支持的业务类型');
    }
    
    // 2. 获取表单数据
    $formData = $formService->getFormData($id);
    
    // 3. 提交工作流
    $workflowService = new WorkflowInstanceService();
    $result = $workflowService->processApplication([
        'business_code' => $businessCode,
        'business_id' => $id,
        'business_data' => $formData,
        'submitter_id' => get_user_id()
    ]);
    
    if ($result['success']) {
        return $this->success('提交成功');
    } else {
        return $this->error($result['message']);
    }
}
```

### 2. 审批回调处理

```php
// 工作流引擎自动调用
public function approvalCallback(int $instanceId, int $status, array $extra = []): bool
{
    // 1. 获取工作流实例信息
    $instance = WorkflowInstance::find($instanceId);
    if (!$instance) {
        return false;
    }
    
    // 2. 获取FormService
    $formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($instance->business_code);
    if (!$formService) {
        return false;
    }
    
    // 3. 更新业务状态
    return $formService->updateFormStatus($instance->business_id, $status, $extra);
}
```

## 🧪 测试验证

### 1. 接口实现完整性测试

```bash
# 检查所有Service的接口实现
php think test:form-service-interface
```

### 2. 统一保存方法测试

```bash
# 测试saveByCreate、saveByUpdate、batchSave方法
php think test:unified-model-save
```

### 3. 字段检查测试

```bash
# 调试模型字段和权限处理
php think debug:field-check
```

## 📋 新业务接入清单

### 步骤1：创建Service类
```php
class NewBusinessService extends BaseService implements FormServiceInterface
{
    use CrudServiceTrait;
    
    public function __construct()
    {
        $this->model = new NewBusiness();
        parent::__construct();
    }
    
    // 实现FormServiceInterface的7个方法
    public function getFormData(int $id): array { /* 实现 */ }
    public function saveForm(array $data): array { /* 实现 */ }
    public function updateForm(int $id, array $data): bool { /* 实现 */ }
    public function deleteForm(int $id): bool { /* 实现 */ }
    public function updateFormStatus(int $id, int $status, array $extra = []): bool { /* 实现 */ }
    public function getInstanceTitle($formData): string { /* 实现 */ }
    public function validateFormData(array $data, string $scene = 'create'): array { /* 实现 */ }
}
```

### 步骤2：配置workflow_type表
```sql
INSERT INTO workflow_type (name, module_code, business_code, status) 
VALUES ('新业务审批', 'new_module', 'new_module_new_business', 1);
```

### 步骤3：创建模型类
```php
class NewBusiness extends BaseModel
{
    protected $name = 'new_business';
    
    const STATUS_DRAFT = 1;
    const STATUS_PENDING = 2;
    const STATUS_APPROVED = 3;
    const STATUS_REJECTED = 4;
    
    protected $type = [
        'approval_status' => 'integer',
        'workflow_instance_id' => 'integer',
        'submitter_id' => 'integer',
        'creator_id' => 'integer',
    ];
}
```

### 步骤4：验证实现
```bash
# 运行测试验证
php think test:form-service-interface
```

## ⚠️ 注意事项

### 1. 必须遵循的规范
- ✅ Service类必须完整实现FormServiceInterface的7个方法
- ✅ 使用统一的模型层保存方法（saveByCreate/saveByUpdate）
- ✅ workflow_type表配置必须正确
- ✅ 业务代码命名必须符合动态工厂规则

### 2. 常见问题排查
- ❌ Service未实现FormServiceInterface → 检查implements声明
- ❌ 动态工厂创建失败 → 检查workflow_type配置和类名规则
- ❌ 权限字段未填充 → 检查模型是否有对应字段
- ❌ 状态更新失败 → 检查updateFormStatus方法实现

### 3. 性能优化建议
- 🚀 使用batchSave进行批量操作
- 🚀 先查询再更新，避免无效操作
- 🚀 合理使用事务保证数据一致性
- 🚀 添加适当的日志记录便于调试

## 📚 相关文档

- [FormServiceInterface统一化实施完成报告](./FormServiceInterface统一化实施完成报告.md)
- [第4阶段统一模型层保存方法实施完成报告](./第4阶段统一模型层保存方法实施完成报告.md)
- [工作流业务集成架构设计方案](./工作流业务集成架构设计方案.md)
- [工作流业务集成实施指南](./工作流业务集成实施指南.md)

## 🔧 高级配置

### 1. 复杂业务场景处理

#### 明细数据处理
```php
// 在saveForm中处理主从表数据
public function saveForm(array $data): array
{
    // 1. 验证主表数据
    $mainData = $this->validateFormData($data, 'create');

    // 2. 提取明细数据
    $items = $data['items'] ?? [];
    unset($mainData['items']);

    // 3. 保存主表
    $mainModel = new MainBusinessModel();
    $mainId = $mainModel->saveByCreate($mainData);

    // 4. 保存明细数据
    if (!empty($items)) {
        $this->saveDetailItems($mainId, $items);
    }

    // 5. 返回完整数据
    $formData = $this->getFormData($mainId);
    return [$mainId, $formData];
}

private function saveDetailItems(int $mainId, array $items): void
{
    $processedItems = [];
    foreach ($items as $index => $item) {
        $item['main_id'] = $mainId;
        $item['sort_order'] = $index + 1;
        $processedItems[] = $item;
    }

    $detailModel = new DetailBusinessModel();
    $insertCount = $detailModel->batchSave($processedItems);

    if ($insertCount !== count($processedItems)) {
        throw new BusinessException('明细数据保存失败');
    }
}
```

#### 状态流转控制
```php
// 定义状态流转规则
class BusinessStatusManager
{
    // 允许的状态流转
    const ALLOWED_TRANSITIONS = [
        self::STATUS_DRAFT => [self::STATUS_PENDING],
        self::STATUS_PENDING => [self::STATUS_APPROVED, self::STATUS_REJECTED],
        self::STATUS_REJECTED => [self::STATUS_PENDING, self::STATUS_DRAFT],
        self::STATUS_APPROVED => [self::STATUS_TERMINATED],
    ];

    public static function canTransition(int $fromStatus, int $toStatus): bool
    {
        return in_array($toStatus, self::ALLOWED_TRANSITIONS[$fromStatus] ?? []);
    }
}

// 在updateFormStatus中使用
public function updateFormStatus(int $id, int $status, array $extra = []): bool
{
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }

    // 检查状态流转是否合法
    if (!BusinessStatusManager::canTransition($record->approval_status, $status)) {
        throw new BusinessException('不允许的状态流转');
    }

    // 继续处理...
}
```

### 2. 错误处理和日志

#### 统一异常处理
```php
abstract class BaseFormService implements FormServiceInterface
{
    protected function handleException(\Exception $e, string $operation, array $context = []): void
    {
        Log::error("FormService操作失败: {$operation}", [
            'exception' => $e->getMessage(),
            'context' => $context,
            'trace' => $e->getTraceAsString()
        ]);

        // 根据异常类型决定是否重新抛出
        if ($e instanceof BusinessException) {
            throw $e;
        } else {
            throw new BusinessException("系统错误，请稍后重试");
        }
    }
}
```

#### 操作日志记录
```php
trait FormServiceLogTrait
{
    protected function logOperation(string $operation, int $id, array $data = []): void
    {
        Log::info("FormService操作: {$operation}", [
            'business_type' => $this->getBusinessType(),
            'record_id' => $id,
            'user_id' => get_user_id(),
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    abstract protected function getBusinessType(): string;
}
```

### 3. 性能优化策略

#### 数据缓存
```php
class FormDataCache
{
    public static function getFormData(string $businessType, int $id): ?array
    {
        $cacheKey = "form_data:{$businessType}:{$id}";
        return Cache::get($cacheKey);
    }

    public static function setFormData(string $businessType, int $id, array $data): void
    {
        $cacheKey = "form_data:{$businessType}:{$id}";
        Cache::set($cacheKey, $data, 300); // 5分钟缓存
    }

    public static function clearFormData(string $businessType, int $id): void
    {
        $cacheKey = "form_data:{$businessType}:{$id}";
        Cache::delete($cacheKey);
    }
}
```

#### 批量操作优化
```php
// 批量状态更新
public function batchUpdateStatus(array $ids, int $status, array $extra = []): array
{
    $results = [];
    $successCount = 0;
    $failCount = 0;

    Db::startTrans();
    try {
        foreach ($ids as $id) {
            try {
                $result = $this->updateFormStatus($id, $status, $extra);
                $results[$id] = ['success' => $result];
                if ($result) $successCount++;
            } catch (\Exception $e) {
                $results[$id] = ['success' => false, 'error' => $e->getMessage()];
                $failCount++;
            }
        }

        if ($failCount === 0) {
            Db::commit();
        } else {
            Db::rollback();
            throw new BusinessException("批量操作失败，成功:{$successCount}，失败:{$failCount}");
        }

    } catch (\Exception $e) {
        Db::rollback();
        throw $e;
    }

    return $results;
}
```

---

**文档维护：** 请在新增业务或修改接口时及时更新此文档
**技术支持：** 如有问题请参考测试命令进行排查
**版本历史：** v1.0 - 初始版本，包含完整的对接规范和最佳实践
