# CRM线索池与线索列表功能测试清单

## 测试环境准备

### 1. 数据库准备
- [ ] 确保 `crm_lead` 表存在且包含测试数据
- [ ] 确保 `crm_follow_record` 表存在
- [ ] 确保 `crm_lead_assignment` 表存在
- [ ] 确保用户表包含测试用户数据

### 2. 后端服务准备
- [ ] 启动ThinkPHP8后端服务
- [ ] 确认所有路由配置正确
- [ ] 确认中间件配置正常

### 3. 前端环境准备
- [ ] 启动Vue3前端服务
- [ ] 确认API接口配置正确
- [ ] 确认用户已登录

## 功能测试项目

### 一、线索池功能测试

#### 1.1 线索池列表功能
- [ ] **测试项目**: 访问线索池页面
  - **预期结果**: 页面正常加载，显示线索池列表
  - **测试数据**: 确保数据库中有 `in_pool=1` 且 `owner_user_id=0` 的线索
  - **验证点**: 
    - 列表数据正确显示
    - 分页功能正常
    - 搜索功能正常

#### 1.2 线索认领功能
- [ ] **测试项目**: 点击"认领"按钮
  - **预期结果**: 
    - 弹出确认对话框
    - 确认后线索从线索池中移除
    - 线索的 `owner_user_id` 更新为当前用户
    - 线索的 `in_pool` 更新为 0
  - **API接口**: `POST /api/crm/crm_lead_pool/claimLead/{id}`
  - **验证点**:
    - 数据库记录更新正确
    - 页面数据刷新正确
    - 成功提示信息显示

#### 1.3 线索分配功能
- [ ] **测试项目**: 点击"分配"按钮
  - **预期结果**: 
    - 打开分配对话框
    - 用户下拉列表正常加载
    - 表单验证正常工作
    - 分配成功后线索状态更新
  - **API接口**: `POST /api/crm/crm_lead_pool/assign/{id}`
  - **验证点**:
    - 用户列表加载正确
    - 表单验证规则生效
    - 分配记录写入 `crm_lead_assignment` 表
    - 线索状态更新正确

### 二、线索列表功能测试

#### 2.1 线索列表显示
- [ ] **测试项目**: 访问线索列表页面
  - **预期结果**: 显示当前用户有权限的线索
  - **验证点**:
    - 数据权限过滤正确
    - 只显示当前用户负责的线索
    - 列表功能正常

#### 2.2 跟进记录功能
- [ ] **测试项目**: 点击"跟进"按钮
  - **预期结果**: 
    - 打开跟进记录对话框
    - 显示线索基本信息
    - 显示历史跟进记录
    - 可以添加新的跟进记录
  - **API接口**: 
    - `POST /api/crm/crm_lead/addFollowRecord/{id}`
    - `GET /api/crm/crm_lead/getFollowRecords/{id}`
  - **验证点**:
    - 表单验证正确
    - 跟进记录保存成功
    - 历史记录显示正确
    - 线索的跟进时间字段更新

#### 2.3 线索转化功能
- [ ] **测试项目**: 点击"转化"按钮
  - **预期结果**: 
    - 打开转化对话框
    - 预填充线索信息
    - 表单验证正常
    - 转化成功后创建客户和联系人
  - **API接口**: `POST /api/crm/crm_lead/convertToCustomer/{id}`
  - **验证点**:
    - 客户数据创建正确
    - 联系人数据创建正确
    - 线索状态更新为已转化
    - 转化记录正确

### 三、数据权限测试

#### 3.1 线索池数据权限
- [ ] **测试项目**: 不同用户访问线索池
  - **预期结果**: 所有用户都能看到线索池中的所有线索
  - **验证点**: 线索池不受数据权限限制

#### 3.2 线索列表数据权限
- [ ] **测试项目**: 不同用户访问线索列表
  - **预期结果**: 用户只能看到自己负责的线索
  - **验证点**: 数据权限过滤正确

### 四、表单验证测试

#### 4.1 跟进记录表单验证
- [ ] **测试项目**: 提交空的跟进记录表单
  - **预期结果**: 显示相应的验证错误信息
  - **验证点**:
    - 跟进方式必选
    - 跟进内容必填且长度验证
    - 跟进时间必选且不能晚于当前时间

#### 4.2 转化表单验证
- [ ] **测试项目**: 提交不完整的转化表单
  - **预期结果**: 显示相应的验证错误信息
  - **验证点**:
    - 客户名称必填
    - 公司名称必填
    - 联系人信息必填
    - 手机号格式验证
    - 邮箱格式验证

#### 4.3 分配表单验证
- [ ] **测试项目**: 提交不完整的分配表单
  - **预期结果**: 显示相应的验证错误信息
  - **验证点**:
    - 目标用户必选
    - 分配原因长度验证

### 五、错误处理测试

#### 5.1 网络错误处理
- [ ] **测试项目**: 模拟网络断开情况
  - **预期结果**: 显示友好的错误提示
  - **验证点**: 错误信息清晰明确

#### 5.2 服务器错误处理
- [ ] **测试项目**: 模拟服务器500错误
  - **预期结果**: 显示服务器错误提示
  - **验证点**: 不会导致页面崩溃

#### 5.3 权限错误处理
- [ ] **测试项目**: 访问无权限的数据
  - **预期结果**: 显示权限不足提示
  - **验证点**: 安全性验证正确

### 六、用户体验测试

#### 6.1 加载状态
- [ ] **测试项目**: 各种操作的加载状态
  - **预期结果**: 显示适当的加载指示器
  - **验证点**: 用户体验良好

#### 6.2 操作反馈
- [ ] **测试项目**: 各种操作的成功/失败反馈
  - **预期结果**: 显示清晰的操作结果提示
  - **验证点**: 反馈信息准确及时

#### 6.3 响应式设计
- [ ] **测试项目**: 在不同屏幕尺寸下测试
  - **预期结果**: 界面适配良好
  - **验证点**: 移动端体验良好

## 测试数据准备SQL

```sql
-- 插入测试线索数据
INSERT INTO crm_lead (lead_name, company, mobile, email, in_pool, owner_user_id, created_at) VALUES
('张三', '测试公司A', '13800138001', '<EMAIL>', 1, 0, NOW()),
('李四', '测试公司B', '13800138002', '<EMAIL>', 1, 0, NOW()),
('王五', '测试公司C', '13800138003', '<EMAIL>', 0, 1, NOW());

-- 插入测试跟进记录
INSERT INTO crm_follow_record (related_type, related_id, follow_type, content, follow_date, creator_id, created_at) VALUES
('lead', 3, 'phone', '电话沟通了解需求', '2024-01-15 10:00:00', 1, NOW());
```

## 测试完成标准

- [ ] 所有功能测试项目通过
- [ ] 所有验证点确认无误
- [ ] 无严重bug和用户体验问题
- [ ] 代码质量检查通过
- [ ] 性能测试满足要求

## 已知问题记录

| 问题描述 | 严重程度 | 状态 | 备注 |
|---------|---------|------|------|
|         |         |      |      |

## 测试总结

### 测试结果
- [ ] 通过
- [ ] 部分通过（需要修复问题）
- [ ] 未通过

### 主要问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 
