# 工作流对接快速上手指南

## 🚀 5分钟快速上手

### 第一步：配置workflow_type表
```sql
INSERT INTO workflow_type (name, module_code, business_code, status, created_id, updated_id) 
VALUES ('您的业务审批', '模块代码', '业务代码', 1, 1, 1);
```

### 第二步：业务表添加必需字段
```sql
ALTER TABLE your_business_table ADD COLUMN approval_status INT DEFAULT 0 COMMENT '审批状态';
ALTER TABLE your_business_table ADD COLUMN workflow_instance_id INT DEFAULT NULL COMMENT '工作流实例ID';
ALTER TABLE your_business_table ADD COLUMN submit_time DATETIME DEFAULT NULL COMMENT '提交时间';
ALTER TABLE your_business_table ADD COLUMN approval_time DATETIME DEFAULT NULL COMMENT '审批完成时间';
```

### 第三步：创建FormService类
```php
<?php
namespace app\your_module\service;

use app\common\core\base\BaseService;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\traits\DefaultWorkflowCallbackTrait;

class YourBusinessService extends BaseService implements FormServiceInterface
{
    use DefaultWorkflowCallbackTrait;
    
    protected string $modelClass = YourBusinessModel::class;
    
    public function getFormData(int $id): array
    {
        $model = $this->getModel()->find($id);
        return $model ? $model->toArray() : [];
    }
    
    public function saveForm(array $data): array
    {
        if (isset($data['id']) && $data['id'] > 0) {
            $model = $this->getModel()->find($data['id']);
            $result = $model->saveByUpdate($data);
            return [$data['id'], $model->toArray()];
        } else {
            $id = $this->getModel()->saveByCreate($data);
            $model = $this->getModel()->find($id);
            return [$id, $model->toArray()];
        }
    }
    
    public function updateFormStatus(int $id, int $status): bool
    {
        $statusData = ['approval_status' => $status];
        
        if ($status === 1) {
            $statusData['submit_time'] = date('Y-m-d H:i:s');
        } elseif (in_array($status, [2, 3, 4, 5, 6])) {
            $statusData['approval_time'] = date('Y-m-d H:i:s');
        }
        
        return $this->getModel()->where('id', $id)->update($statusData) > 0;
    }
    
    public function getInstanceTitle(array $formData): string
    {
        return "您的业务审批-{$formData['name']}";
    }
    
    public function validateFormData(array $data): bool
    {
        return !empty($data['name']); // 根据业务需求修改
    }
    
    public function deleteFormData(int $id): bool
    {
        return $this->getModel()->destroy($id);
    }
    
    public function afterWorkflowStatusChange(int $id, int $status, array $context = []): bool
    {
        // 在这里添加状态变更后的业务逻辑
        return true;
    }
}
```

### 第四步：在Controller中使用
```php
use app\workflow\service\UnifiedWorkflowService;

// 提交审批
public function submitApproval(): Json
{
    $id = $this->request->post('id');
    
    try {
        $unifiedWorkflowService = new UnifiedWorkflowService();
        $result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
            'business_code' => 'your_business_code',
            'business_id' => $id,
            'operator_id' => get_user_id()
        ]);
        
        return $this->success($result['message'], $result);
    } catch (\Exception $e) {
        return $this->error('提交失败：' . $e->getMessage());
    }
}

// 撤回审批
public function withdrawApproval(): Json
{
    $id = $this->request->post('id');
    
    try {
        $unifiedWorkflowService = new UnifiedWorkflowService();
        $result = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
            'business_code' => 'your_business_code',
            'business_id' => $id,
            'operator_id' => get_user_id(),
            'reason' => '用户撤回申请'
        ]);
        
        return $this->success($result['message'], $result);
    } catch (\Exception $e) {
        return $this->error('撤回失败：' . $e->getMessage());
    }
}
```

## 📋 业务代码对照表

| 业务类型 | business_code | module_code | 说明 |
|---------|---------------|-------------|------|
| CRM合同 | crm_contract | crm | 独立业务场景 |
| CRM回款 | crm_contract_receivable | crm | 独立业务场景 |
| 每日报价 | daily_price_order | daily | 独立业务场景 |
| HR请假 | hr_leave | hr | 通用页面集成 |

## 🎯 两种对接模式选择

### 模式一：独立业务场景（推荐）
- **适用**: 合同、报价、回款等有独立管理页面的业务
- **特点**: 在各自Controller/Trait中直接调用
- **优势**: 灵活性高，可自定义业务逻辑

### 模式二：通用页面集成
- **适用**: 请假等标准化审批流程
- **特点**: 集成在ApplicationController中
- **优势**: 统一管理，标准化程度高

## ⚡ 常用代码模板

### 状态检查
```php
// 检查是否可以提交
if ($record->approval_status === 0) {
    // 可以提交审批
}

// 检查是否可以撤回
if ($record->approval_status === 1 && !empty($record->workflow_instance_id)) {
    // 可以撤回
}
```

### 批量操作
```php
public function batchSubmit(array $ids): array
{
    $results = [];
    $unifiedWorkflowService = new UnifiedWorkflowService();
    
    foreach ($ids as $id) {
        try {
            $result = $unifiedWorkflowService->executeWorkflowOperation('submit', [
                'business_code' => 'your_business_code',
                'business_id' => $id,
                'operator_id' => get_user_id()
            ]);
            $results[$id] = $result;
        } catch (\Exception $e) {
            $results[$id] = ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    return $results;
}
```

## 🔧 DynamicWorkflowFactory配置

如果您的业务不在默认映射中，需要在DynamicWorkflowFactory中添加：

```php
// app/workflow/factory/DynamicWorkflowFactory.php
private static $serviceMap = [
    'your_business_code' => \app\your_module\service\YourBusinessService::class,
];

private static $modelMap = [
    'your_business_code' => \app\your_module\model\YourBusinessModel::class,
];
```

## 🧪 测试验证

### 功能测试
```bash
# 运行完整测试
php think test:phase3-complete

# 运行迁移测试
php think test:workflowable-service-migration
```

### 手动测试步骤
1. 创建业务记录（状态为草稿）
2. 提交审批 → 状态变为"审批中"
3. 撤回审批 → 状态变为"已撤回"
4. 重新提交 → 审批通过 → 状态变为"已通过"

## ⚠️ 注意事项

1. **必须实现FormServiceInterface的7个方法**
2. **业务表必须有approval_status等字段**
3. **使用统一的状态常量**
4. **记录操作日志**
5. **处理异常情况**

## 📞 技术支持

如遇问题，请参考：
- [工作流统一对接开发指南](./工作流统一对接开发指南.md) - 详细文档
- 查看测试用例了解具体实现
- 检查现有业务模块的实现方式

---

**快速上手指南** | **5分钟完成工作流对接** | **简单易用**
