import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 跟进记录相关接口
 */
export class CrmFollowRecordApi {
  /**
   * 获取跟进记录列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_follow_record/index',
      params
    })
  }

  /**
   * 获取跟进记录详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number, options?: any) {
    return request.get<BaseResult>({
      url: `/crm/crm_follow_record/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加跟进记录
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_follow_record/add',
      data
    })
  }

  /**
   * 更新跟进记录
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/crm/crm_follow_record/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除跟进记录
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_follow_record/delete/${id}`
    })
  }

  /**
   * 批量删除跟进记录
   * @param ids 记录ID数组
   */

  /*static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/crm/crm_follow_record/batchDelete`,
      data: { ids }
    })
  }*/

  /**
   * 获取关联下拉选项
   * @param related_type 类型名称
   */
  static relatedOptions(related_type: string) {
    return request.get<BaseResult>({
      url: `/crm/crm_follow_record/relatedOptions`,
      params: {
        related_type
      }
    })
  }
}
