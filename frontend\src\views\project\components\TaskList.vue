<template>
  <div class="task-list">
    <div class="list-header">
      <div class="header-left">
        <h3>任务列表</h3>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAddTask">
          <el-icon>
            <Plus />
          </el-icon>
          新建任务
        </el-button>
      </div>
    </div>

    <div class="list-content">
      <el-table
        :data="tasks"
        stripe
        @row-click="handleRowClick"
        v-loading="loading"
        element-loading-text="加载任务列表..."
      >
        <el-table-column prop="title" label="任务名称" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee_name" label="负责人" width="120" />
        <el-table-column prop="end_date" label="截止时间" width="160">
          <template #default="{ row }">
            <span :class="{ overdue: isOverdue(row.end_date) }">
              {{ formatDate(row.end_date) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="{ row }">
            <div class="operation-buttons">
              <ArtButtonTable text="详情" @click="handleTaskClick(row.id)" />
              <ArtButtonTable text="编辑" type="edit" @click="handleEditTask(row)" />
              <ArtButtonTable text="删除" type="delete" @click="handleDeleteTask(row)" />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { Plus } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { formatDate, isOverdue } from '@/utils/date'
  import { TaskApi } from '@/api/project/projectApi'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'

  // Props
  interface Props {
    projectId: number
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    'task-click': [taskId: number]
    'edit-task': [task: any]
    'delete-task': [taskId: number]
    'add-task': []
  }>()

  // 暴露刷新方法给父组件
  const refreshTasks = () => {
    loadTasks()
  }

  defineExpose({
    refreshTasks
  })

  // 响应式数据
  const tasks = ref([])
  const loading = ref(false)

  // 方法
  const handleAddTask = () => {
    emit('add-task')
  }

  const handleRowClick = (row: any) => {
    console.log('TaskList: 行点击', row)
    emit('task-click', row.id)
  }

  const handleTaskClick = (taskId: number) => {
    console.log('TaskList: 点击任务', taskId)
    emit('task-click', taskId)
  }

  const handleEditTask = (task: any) => {
    emit('edit-task', task)
  }

  const handleDeleteTask = (task: any) => {
    emit('delete-task', task.id)
  }

  const getStatusType = (status: number) => {
    const typeMap = {
      1: 'info', // 待办
      2: 'primary', // 进行中
      3: 'success', // 已完成
      4: 'danger' // 已关闭
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: number) => {
    const textMap = {
      1: '待办',
      2: '进行中',
      3: '已完成',
      4: '已关闭'
    }
    return textMap[status] || '未知'
  }

  const getPriorityType = (priority: number) => {
    const typeMap = {
      1: 'info', // 低
      2: 'primary', // 中
      3: 'warning', // 高
      4: 'danger' // 紧急
    }
    return typeMap[priority] || 'info'
  }

  const getPriorityText = (priority: number) => {
    const textMap = {
      1: '低',
      2: '中',
      3: '高',
      4: '紧急'
    }
    return textMap[priority] || '未知'
  }

  // 加载任务列表
  const loadTasks = async () => {
    loading.value = true
    try {
      console.log('TaskList: 开始加载任务列表')
      const response = await TaskApi.list({ project_id: props.projectId })
      tasks.value = response.data.list || []
      console.log('TaskList: 任务列表加载完成', tasks.value.length, '个任务')
    } catch (error) {
      console.error('加载任务列表失败:', error)
      ElMessage.error('加载任务列表失败')
      tasks.value = []
    } finally {
      loading.value = false
    }
  }

  // 初始化
  onMounted(() => {
    loadTasks()
  })
</script>

<style scoped lang="scss">
  .task-list {
    .list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .header-left {
        h3 {
          margin: 0;
          color: #1f2329;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }
      }
    }

    .list-content {
      .overdue {
        color: #ff4d4f;

        // 黑暗模式适配
        html.dark & {
          color: rgb(var(--art-danger));
        }
      }

      .operation-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
      }
    }
  }

  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;

        // 黑暗模式适配
        html.dark & {
          background-color: var(--art-hoverColor);
        }
      }
    }
  }
</style>
