<?php
/**
 * Workflow模块URL配置
 * 用于配置PC端和移动端的URL映射
 */

return [
    // 基础配置
    'base_url' => [
        'pc' => '/workflow',
        'mobile' => '/m/workflow',  // 移动端基础路径
        'app' => 'workflow://',     // APP内跳转协议（预留）
    ],
    
    // URL映射配置
    'url_mapping' => [
        // 工作流实例相关
        'instance' => [
            'detail' => [
                'pc' => '/workflow/detail?id={instance_id}',
                'mobile' => '',  // 暂时为空，等移动端页面开发完成后配置
                'app' => '',     // APP页面暂时为空
            ],
            'edit' => [
                'pc' => '/workflow/edit?id={instance_id}',
                'mobile' => '',
                'app' => '',
            ],
            'history' => [
                'pc' => '/workflow/history?id={instance_id}',
                'mobile' => '',
                'app' => '',
            ]
        ],
        
        // 工作流任务相关
        'task' => [
            'detail' => [
                'pc' => '/workflow/task/detail?id={task_id}',
                'mobile' => '',  // 暂时为空
                'app' => '',
            ],
            'approval' => [
                'pc' => '/workflow/task/approval?id={task_id}',
                'mobile' => '',
                'app' => '',
            ],
            'list' => [
                'pc' => '/workflow/task/list',
                'mobile' => '',
                'app' => '',
            ]
        ],
        
        // 工作流流程相关
        'process' => [
            'detail' => [
                'pc' => '/workflow/process/detail?id={process_id}',
                'mobile' => '',
                'app' => '',
            ],
            'design' => [
                'pc' => '/workflow/process/design?id={process_id}',
                'mobile' => '',  // 流程设计移动端不需要
                'app' => '',
            ]
        ],
        
        // 消息中心相关
        'message' => [
            'list' => [
                'pc' => '/notice/message/list',
                'mobile' => '',
                'app' => '',
            ],
            'detail' => [
                'pc' => '/notice/message/detail?id={message_id}',
                'mobile' => '',
                'app' => '',
            ]
        ]
    ],
    
    // 默认跳转配置（当具体页面不存在时的备用方案）
    'fallback' => [
        'pc' => '/workflow/index',
        'mobile' => '',  // 移动端暂时跳转到空，可以配置为移动端首页
        'app' => '',
    ],
    
    // 参数映射配置
    'param_mapping' => [
        'instance_id' => 'id',      // 实例ID参数名
        'task_id' => 'id',          // 任务ID参数名
        'process_id' => 'id',       // 流程ID参数名
        'message_id' => 'id',       // 消息ID参数名
    ],
    
    // 平台检测配置
    'platform_detection' => [
        'mobile_user_agents' => [
            'Mobile', 'Android', 'iPhone', 'iPad', 'iPod', 
            'BlackBerry', 'Windows Phone', 'webOS'
        ],
        'app_user_agents' => [
            'WorkflowApp',  // 自定义APP标识
        ]
    ],
    
    // 开发配置
    'development' => [
        'enable_debug' => false,     // 是否启用调试模式
        'log_url_generation' => false,  // 是否记录URL生成日志
        'show_empty_urls' => true,   // 是否显示空URL的警告
    ]
];
