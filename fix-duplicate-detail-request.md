# 修复编辑时重复请求详情接口的问题

## 🚨 问题描述

编辑申请时出现重复请求详情接口的问题：

1. **Application.vue** 中的 `editApplication` 方法已经请求了详情
2. **FormManager** 的 `setEditData` 正确设置了数据
3. 但 **FormManager** 的 watch 逻辑仍然调用了 `showForm()`
4. **hr_leave-form.vue** 的 `showForm` 方法又发起了详情请求

## 🔍 问题分析

### 调用时序问题
```
1. Application.vue.editApplication() 
   ↓ 请求详情接口 (第一次)
2. FormManager.setEditData({id: 134, formData: {...}})
   ↓ 设置数据成功
3. FormManager watch modelValue 变化
   ↓ 判断为"新建表单"，调用 showForm()
4. hr_leave-form.vue.showForm()
   ↓ 再次请求详情接口 (第二次) ❌
```

### 根本原因
FormManager 的 watch 逻辑中，判断是否为"新建表单"的条件不准确：
- 即使已经通过 `setEditData` 设置了数据
- watch 仍然认为没有预加载数据，触发了 `showForm()`

## ✅ 修复方案

### 1. 优化 watch 逻辑
修改判断条件，同时检查 `preloadedEditData` 和 `currentFormId`：

```typescript
// 修改前：只检查 preloadedEditData
if (!preloadedEditData.value) {
  // 认为是新建表单，调用 showForm()
}

// 修改后：同时检查两个条件
if (!preloadedEditData.value && !currentFormId.value) {
  // 确实是新建表单，调用 showForm()
} else {
  // 编辑模式或已有数据，跳过 showForm 调用
}
```

### 2. 提前设置 currentFormId
在 `setEditData` 方法中，立即设置 `currentFormId`：

```typescript
const setEditData = (data: any) => {
  console.log('FormManager收到编辑数据:', data)
  
  // 立即设置当前表单ID，防止watch中的重复调用
  currentFormId.value = data.id
  preloadedEditData.value = data
  
  // ... 其他逻辑
}
```

### 3. 延迟检查机制
在 watch 中添加延迟检查，确保数据设置完成后再判断：

```typescript
watch(() => props.modelValue, (val) => {
  formVisible.value = val
  if (val) {
    // 延迟检查，确保 setEditData 已经执行
    setTimeout(() => {
      if (!preloadedEditData.value && !currentFormId.value) {
        console.log('FormManager: 新建表单，调用 showForm()')
        if (formComponentRef.value?.showForm) {
          formComponentRef.value.showForm()
        }
      } else {
        console.log('FormManager: 编辑模式或已有数据，跳过 showForm 调用')
      }
    }, 100)
  }
})
```

## 🎯 修复效果

### 修复前的日志
```
FormManager收到编辑数据: {id: 134, formData: {…}}
直接传递表单数据到业务组件
hr_leave-form setFormData called with data: {...}
FormManager: 新建表单，调用 showForm()  ❌ 错误判断
hr_leave-form showForm called with id: undefined  ❌ 重复请求
```

### 修复后的预期日志
```
FormManager收到编辑数据: {id: 134, formData: {…}}
直接传递表单数据到业务组件
hr_leave-form setFormData called with data: {...}
FormManager: 编辑模式或已有数据，跳过 showForm 调用  ✅ 正确判断
```

## 🧪 测试验证

### 测试步骤
1. 打开浏览器开发者工具的Network面板
2. 访问工作流申请页面
3. 找到一个草稿状态的申请
4. 点击"更多" → "编辑"
5. 观察Network面板中的请求数量

### 预期结果
- ✅ 只有一次详情接口请求（来自 Application.vue）
- ✅ 表单正确显示并填充数据
- ✅ 时间字段正确渲染
- ✅ 控制台显示"跳过 showForm 调用"

## 📋 修复清单

- ✅ 修改 FormManager watch 逻辑，添加双重条件判断
- ✅ 在 setEditData 中提前设置 currentFormId
- ✅ 添加延迟检查机制，确保数据设置完成
- ✅ 优化日志输出，便于问题排查
- ✅ 修复 hr_leave-form.vue 中的数据结构处理

## 🔄 相关文件修改

1. `frontend/src/views/workflow/components/form-manager.vue`
   - 优化 watch 逻辑的判断条件
   - 在 setEditData 中提前设置 currentFormId
   - 添加延迟检查机制

2. `frontend/src/views/workflow/components/business-forms/hr_leave-form.vue`
   - 修复 loadFormData 中的数据结构处理
   - 优化 setFormData 方法的数据处理

## 🎯 关键改进点

1. **时序控制**：确保数据设置在判断逻辑之前完成
2. **条件优化**：使用更准确的条件判断是否为新建表单
3. **状态同步**：及时更新 currentFormId 状态
4. **防重复机制**：通过多重检查避免重复API调用

修复完成后，编辑申请时不再会重复请求详情接口，提升了性能和用户体验。
