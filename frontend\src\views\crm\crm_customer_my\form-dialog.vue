<script setup lang="ts">
  import { ref, reactive, defineExpose, defineEmits, computed } from 'vue'
  import { ElMessage, FormInstance } from 'element-plus'
  import { Document, Phone, OfficeBuilding, User } from '@element-plus/icons-vue'
  import { CrmCustomerMyApi } from '@/api/crm/crmCustomerMy'
  import { ApiStatus } from '@/utils/http/status'
  import RegionSelector from '@/components/custom/RegionSelector/index.vue'
  import DepartmentTreeSelect from '@/components/custom/DepartmentTreeSelect.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    customer_name: '',
    industry: '',
    level: 1,
    source: '',
    phone: '',
    website: '',
    region_province: '',
    region_city: '',
    region_district: '',
    address: '',
    zip_code: '',
    remark: '',
    // owner_user_id: 0,
    status: 1,
    in_sea: 0,
    into_sea_time: '',
    // sea_id: 0,
    credit_code: '',
    annual_revenue: 0,
    employee_count: 0,
    registered_capital: 0,
    last_followed_at: '',
    next_followed_at: '',
    lock_status: 0,
    lock_expire_time: '',
    // 联系人信息（仅新增时使用）
    contact_name: '',
    contact_gender: 0,
    contact_mobile: '',
    contact_wechat: '',
    contact_qq: '',
    contact_position: '',
    contact_email: '',
    // 编辑时的主要联系人ID
    primary_contact_id: null
  })

  // 地区数据处理
  const regionData = computed({
    get() {
      return {
        region_province: formData.region_province,
        region_city: formData.region_city,
        region_district: formData.region_district
      }
    },
    set(value: any) {
      if (value && typeof value === 'object') {
        formData.region_province = value.region_province || ''
        formData.region_city = value.region_city || ''
        formData.region_district = value.region_district || ''
      } else {
        formData.region_province = ''
        formData.region_city = ''
        formData.region_district = ''
      }
    }
  })

  // 地区变化处理
  const handleRegionChange = (value: any, selectedData: any) => {
    console.log('地区选择变化:', value, selectedData)
    // 确保地区数据已经通过 computed 属性正确更新到 formData 中
    if (value && typeof value === 'object') {
      console.log('地区数据已更新到表单:', {
        region_province: formData.region_province,
        region_city: formData.region_city,
        region_district: formData.region_district
      })
    }
  }

  // 表单验证规则
  const rules = {
    customer_name: [
      {
        required: true,
        message: '请输入客户名称或公司名称',
        trigger: 'blur'
      },
      {
        min: 2,
        max: 200,
        message: '客户名称长度应在2-200个字符之间',
        trigger: 'blur'
      }
    ],
    /*owner_user_id: [
      {
        required: true,
        message: '请选择负责人',
        trigger: 'change'
      }
    ],*/
    // 联系人信息验证（仅新增时）
    contact_name: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      },
      {
        min: 2,
        max: 50,
        message: '联系人姓名长度应在2-50个字符之间',
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      {
        required: true,
        message: '请输入联系人手机号',
        trigger: 'blur'
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号格式',
        trigger: 'blur'
      }
    ],
    industry: [
      {
        max: 50,
        message: '所属行业长度不能超过50个字符',
        trigger: 'blur'
      }
    ],
    source: [
      {
        max: 50,
        message: '客户来源长度不能超过50个字符',
        trigger: 'blur'
      }
    ],
    phone: [
      {
        max: 20,
        message: '电话长度不能超过20个字符',
        trigger: 'blur'
      }
    ],
    website: [
      {
        max: 100,
        message: '网址长度不能超过100个字符',
        trigger: 'blur'
      }
    ],
    region_province: [
      {
        max: 30,
        message: '省份长度不能超过30个字符',
        trigger: 'blur'
      }
    ],
    region_city: [
      {
        max: 30,
        message: '城市长度不能超过30个字符',
        trigger: 'blur'
      }
    ],
    region_district: [
      {
        max: 30,
        message: '区/县长度不能超过30个字符',
        trigger: 'blur'
      }
    ],
    credit_code: [
      {
        max: 50,
        message: '统一社会信用代码长度不能超过50个字符',
        trigger: 'blur'
      }
    ],
    annual_revenue: [
      {
        validator: (_rule: any, value: any, callback: any) => {
          if (isNaN(Number(value))) {
            callback(new Error('年营业额必须是数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    employee_count: [
      {
        validator: (_rule: any, value: any, callback: any) => {
          if (isNaN(Number(value))) {
            callback(new Error('员工人数必须是数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    registered_capital: [
      {
        validator: (_rule: any, value: any, callback: any) => {
          if (isNaN(Number(value))) {
            callback(new Error('注册资本必须是数字'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 显示对话框
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    dialogVisible.value = true

    // 重置表单数据
    Object.assign(formData, {
      customer_name: '',
      industry: '',
      level: 1,
      source: '',
      phone: '',
      website: '',
      region_province: '',
      region_city: '',
      region_district: '',
      address: '',
      zip_code: '',
      remark: '',
      // owner_user_id: 0,
      status: 1,
      in_sea: 0,
      into_sea_time: '',
      // sea_id: 0,
      credit_code: '',
      annual_revenue: 0,
      employee_count: 0,
      registered_capital: 0,
      // last_followed_at: '',
      // next_followed_at: '',
      lock_status: 0,
      lock_expire_time: ''
    })

    // 编辑模式下获取详情数据
    if (type === 'edit' && id) {
      try {
        loading.value = true
        const res = await CrmCustomerMyApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 处理数据类型转换
          const data = { ...res.data }
          // 将字符串格式的 annual_revenue 转换为数字
          if (data.annual_revenue && typeof data.annual_revenue === 'string') {
            data.annual_revenue = parseFloat(data.annual_revenue)
          }
          // 将字符串格式的 registered_capital 转换为数字
          if (data.registered_capital && typeof data.registered_capital === 'string') {
            data.registered_capital = parseFloat(data.registered_capital)
          }

          Object.assign(formData, data)
        }
      } finally {
        loading.value = false
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          loading.value = true

          // 处理提交数据转换
          const submitData = { ...formData }

          let res

          if (dialogType.value === 'add') {
            res = await CrmCustomerMyApi.add(formData)
          } else {
            // 编辑时，发送完整表单数据（包含id用于查询）
            res = await CrmCustomerMyApi.update(submitData)
          }

          if (res.code === ApiStatus.success) {
            ElMessage.success(
              dialogType.value === 'add' ? '客户和联系人创建成功' : '客户信息更新成功'
            )
            dialogVisible.value = false
            emit('success')
          }
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增客户' : '编辑客户'"
    width="1200px"
    top="5vh"
    destroy-on-close
    :close-on-click-modal="false"
    class="customer-form-dialog"
  >
    <div class="dialog-content">
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="160px"
        label-position="right"
        v-loading="loading"
        class="customer-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Document />
            </el-icon>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="客户名称/公司名称" prop="customer_name" required>
                <el-input
                  v-model="formData.customer_name"
                  placeholder="请输入客户名称或公司名称"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="客户级别" prop="level">
                <el-select
                  v-model="formData.level"
                  placeholder="请选择客户级别"
                  style="width: 100%"
                  size="large"
                >
                  <el-option label="普通客户" :value="1" />
                  <el-option label="重要客户" :value="2" />
                  <el-option label="战略客户" :value="3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="客户来源" prop="source">
                <el-input
                  v-model="formData.source"
                  placeholder="请输入客户来源"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 负责人设置 - 仅编辑时显示 -->
        <!--        <div v-if="dialogType === 'edit'" class="form-section">
                  <div class="section-title">
                    <el-icon>
                      <User />
                    </el-icon>
                    <span>负责人设置</span>
                  </div>
                  <div class="contact-info-tip">
                    <el-alert
                      title="提示：编辑模式下可以重新指定客户负责人"
                      type="info"
                      :closable="false"
                      show-icon
                    />
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="负责人" prop="owner_user_id">
                        <DepartmentTreeSelect
                          v-model="formData.owner_user_id"
                          placeholder="请选择负责人"
                          style="width: 100%"
                          size="large"
                          clearable
                          filterable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>-->

        <!-- 联系信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <Phone />
            </el-icon>
            <span>联系信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="formData.phone"
                  placeholder="请输入联系电话"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="公司网站" prop="website">
                <el-input
                  v-model="formData.website"
                  placeholder="请输入公司网站"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="所在地区" prop="region">
                <RegionSelector
                  v-model="regionData"
                  return-type="object"
                  :field-mapping="{
                    province: 'region_province',
                    city: 'region_city',
                    district: 'region_district'
                  }"
                  placeholder="请选择省市区"
                  style="width: 100%"
                  size="large"
                  @change="handleRegionChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="详细地址" prop="address">
            <el-input
              v-model="formData.address"
              type="textarea"
              :rows="3"
              placeholder="请输入详细地址"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 企业信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon>
              <OfficeBuilding />
            </el-icon>
            <span>企业信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="所属行业" prop="industry">
                <el-input
                  v-model="formData.industry"
                  placeholder="请输入所属行业"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="统一社会信用代码" prop="credit_code">
                <el-input
                  v-model="formData.credit_code"
                  placeholder="请输入统一社会信用代码"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="注册资本(万元)" prop="registered_capital">
                <el-input-number
                  v-model="formData.registered_capital"
                  placeholder="0.00"
                  style="width: 100%"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  size="large"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="年营业额(万元)" prop="annual_revenue">
                <el-input-number
                  v-model="formData.annual_revenue"
                  placeholder="0.00"
                  style="width: 100%"
                  controls-position="right"
                  :min="0"
                  :precision="2"
                  size="large"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12" :xs="24" :sm="12">
              <el-form-item label="员工人数" prop="employee_count">
                <el-input-number
                  v-model="formData.employee_count"
                  placeholder="0"
                  style="width: 100%"
                  controls-position="right"
                  :min="0"
                  size="large"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 业务设置 -->
        <div class="form-section">
          <div class="section-title">⚙️ 业务设置</div>
          <ElRow :gutter="20">
            <ElCol :span="12" :xs="24" :sm="12">
              <ElFormItem label="客户状态" prop="status">
                <ElSwitch
                  v-model="formData.status"
                  inline-prompt
                  :active-value="1"
                  :inactive-value="0"
                  active-text="正常"
                  inactive-text="停用"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12" :xs="24" :sm="12">
              <ElFormItem label="是否锁定" prop="lock_status">
                <ElSwitch
                  v-model="formData.lock_status"
                  inline-prompt
                  :active-value="1"
                  :inactive-value="0"
                  active-text="是"
                  inactive-text="否"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <!-- 锁定相关设置 - 条件显示 -->
          <div v-if="formData.lock_status === 1" class="conditional-fields">
            <ElFormItem label="锁定到期时间" prop="lock_expire_time">
              <ElDatePicker
                v-model="formData.lock_expire_time"
                type="datetime"
                placeholder="请选择锁定到期时间"
                style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </ElFormItem>
          </div>

          <!-- 跟进时间设置 -->
          <!--          <ElRow :gutter="20">
                      <ElCol :span="12">
                        <ElFormItem label="最后跟进时间" prop="last_followed_at">
                          <ElDatePicker
                            v-model="formData.last_followed_at"
                            type="datetime"
                            placeholder="请选择最后跟进时间"
                            style="width: 100%"
                            value-format="YYYY-MM-DD HH:mm:ss"
                          />
                        </ElFormItem>
                      </ElCol>
                      <ElCol :span="12">
                        <ElFormItem label="下次跟进时间" prop="next_followed_at">
                          <ElDatePicker
                            v-model="formData.next_followed_at"
                            type="datetime"
                            placeholder="请选择下次跟进时间"
                            style="width: 100%"
                            value-format="YYYY-MM-DD HH:mm:ss"
                          />
                        </ElFormItem>
                      </ElCol>
                    </ElRow>-->
        </div>

        <!-- 主要联系人信息 - 仅新增时显示 -->
        <div v-if="dialogType === 'add'" class="form-section">
          <div class="section-title">
            <el-icon>
              <User />
            </el-icon>
            <span>主要联系人信息</span>
          </div>
          <div class="contact-info-tip">
            <el-alert
              title="提示：新增客户时需要填写主要联系人信息，该联系人将自动关联到此客户"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
          <el-row :gutter="20">
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="联系人姓名" prop="contact_name" required>
                <el-input
                  v-model="formData.contact_name"
                  placeholder="请输入联系人姓名"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="性别" prop="contact_gender">
                <el-select
                  v-model="formData.contact_gender"
                  placeholder="请选择性别"
                  style="width: 100%"
                  size="large"
                >
                  <el-option label="未知" :value="0" />
                  <el-option label="男" :value="1" />
                  <el-option label="女" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="职位" prop="contact_position">
                <el-input
                  v-model="formData.contact_position"
                  placeholder="请输入职位"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="手机号" prop="contact_mobile" required>
                <el-input
                  v-model="formData.contact_mobile"
                  placeholder="请输入手机号"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="微信号" prop="contact_wechat">
                <el-input
                  v-model="formData.contact_wechat"
                  placeholder="请输入微信号"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" :xs="24" :sm="12" :md="8">
              <el-form-item label="QQ号" prop="contact_qq">
                <el-input
                  v-model="formData.contact_qq"
                  placeholder="请输入QQ号"
                  size="large"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="邮箱" prop="contact_email">
            <el-input
              v-model="formData.contact_email"
              placeholder="请输入邮箱"
              size="large"
              clearable
            />
          </el-form-item>
        </div>
      </ElForm>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="submitForm" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
  .dialog-content {
    max-height: 62vh;
    overflow-y: auto;
    padding: 20px 24px 24px;
    background: #f8f9fa;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    background: white;
    border-top: 1px solid #e4e7ed;
  }

  /* 表单整体样式 */
  .customer-form {
    background: transparent;
  }

  /* 表单分组样式 */
  .form-section {
    margin-bottom: 28px;
    padding: 28px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 28px;
    padding-bottom: 16px;
    border-bottom: 2px solid #409eff;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .section-title .el-icon {
    color: #409eff;
    font-size: 18px;
  }

  /* 条件显示字段样式 */
  .conditional-fields {
    margin-bottom: 24px;
    padding: 20px;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #b3d8ff;
    border-left: 4px solid #409eff;
  }

  /* 联系人信息提示样式 */
  .contact-info-tip {
    margin-bottom: 24px;
  }
</style>
