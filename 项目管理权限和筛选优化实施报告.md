# 项目管理权限和筛选优化实施报告

## 📋 需求概述

本次优化主要解决三个核心问题：
1. **任务执行人权限控制** - 项目负责人可选择执行人，非负责人只能创建自己的任务
2. **任务管理页面项目筛选优化** - 我的任务和全部任务显示不同的项目列表
3. **项目列表负责人筛选** - 从mock数据改为真实的后端查询

## 🎯 需求1：任务执行人权限控制

### 后端实现

#### 1. 控制器层优化
**文件：** `app/project/controller/ProjectController.php`

```php
/**
 * 获取项目成员选项（用于任务执行人选择）
 */
public function memberOptions(int $id): Json
{
    $userId = get_user_id();
    $result = $this->service->getMemberOptions($id, $userId);
    return $this->success('获取成功', $result);
}

/**
 * 检查用户是否为项目负责人
 */
public function checkProjectOwner(int $id): Json
{
    $userId = get_user_id();
    $isOwner = $this->service->isProjectOwner($id, $userId);
    return $this->success('检查成功', ['is_owner' => $isOwner]);
}
```

#### 2. 服务层权限逻辑
**文件：** `app/project/service/ProjectProjectService.php`

```php
/**
 * 获取项目成员选项（权限控制版本）
 */
public function getMemberOptions(int $projectId, int $userId): array
{
    $project = $this->crudService->getDetail($projectId);
    if (!$project) {
        throw new BusinessException('项目不存在');
    }

    // 检查权限：只有项目负责人可以选择执行人，非负责人只能看到自己
    $isOwner = $project->isOwner($userId);
    
    if ($isOwner) {
        // 项目负责人：获取所有项目成员
        $adminIds = (new ProjectMember())->where('project_id', $projectId)
                                         ->where('deleted_at', null)
                                         ->column('user_id');
    } else {
        // 非项目负责人：只能看到自己
        $adminIds = [$userId];
    }

    // 查询用户信息，返回ApiSelect需要的格式
    return (new AdminModel())->whereIn('id', $adminIds)
                             ->where('deleted_at', null)
                             ->field('id as value, real_name as name')
                             ->select()
                             ->toArray();
}
```

#### 3. 路由配置
**文件：** `route/project_project.php`

```php
// 项目成员选项接口（用于任务执行人选择）
Route::get('memberOptions/:id', 'app\project\controller\ProjectController@memberOptions');

// 检查项目负责人权限
Route::get('checkOwner/:id', 'app\project\controller\ProjectController@checkProjectOwner');
```

### 前端实现

#### 1. API接口定义
**文件：** `frontend/src/api/project/projectApi.ts`

```typescript
/**
 * 获取项目成员选项（用于任务执行人选择）
 */
static memberOptions(projectId: number | string) {
  return request.get<BaseResult>({
    url: `/project/project/memberOptions/${projectId}`
  })
}

/**
 * 检查用户是否为项目负责人
 */
static checkProjectOwner(projectId: number | string) {
  return request.get<BaseResult>({
    url: `/project/project/checkOwner/${projectId}`
  })
}
```

#### 2. 任务表单权限控制
**文件：** `frontend/src/views/project/components/TaskForm.vue`

**核心功能：**
- 添加 `isProjectOwner` 响应式变量跟踪权限状态
- 在项目选择变化时自动检查权限
- 根据权限显示不同的提示信息

```vue
<el-form-item label="执行人" prop="assignee_id">
  <ApiSelect
    v-if="assigneeApiConfig"
    v-model="formData.assignee_id"
    :api="assigneeApiConfig"
    :placeholder="isProjectOwner ? '请选择执行人' : '当前任务执行人为您自己'"
    :emptyText="isProjectOwner ? '暂无项目成员' : '您不是项目负责人，只能创建自己的任务'"
    clearable
    filterable
    :auto-load="true"
    :load-on-focus="false"
    :cache-results="false"
    style="width: 100%"
    @load-success="handleAssigneeLoadSuccess"
    @load-error="handleAssigneeLoadError"
  />
  <div v-if="!isProjectOwner && assigneeApiConfig" class="form-tip">
    <el-icon><InfoFilled /></el-icon>
    <span>您不是项目负责人，只能创建分配给自己的任务</span>
  </div>
</el-form-item>
```

## 🎯 需求2：任务管理页面项目筛选优化

### 实现逻辑
**文件：** `frontend/src/views/project/TaskManagement.vue`

#### 1. 项目列表加载优化
```javascript
const loadProjectList = async () => {
  try {
    console.log('TaskManagement: 加载项目列表', activeTab.value)
    
    let response
    if (activeTab.value === 'my') {
      // 我的任务：只加载我参与的项目
      response = await ProjectApi.myProjects({ page: 1, size: 100 })
      projectList.value = Array.isArray(response.data) ? response.data : (response.data?.list || [])
    } else {
      // 全部任务：加载所有有权限查看的项目
      response = await ProjectApi.list({ page: 1, size: 100 })
      projectList.value = response.data?.list || []
    }
    
    console.log('TaskManagement: 项目列表加载完成', projectList.value.length)
  } catch (error) {
    console.error('加载项目列表失败:', error)
    ElMessage.error('加载项目列表失败')
    projectList.value = []
  }
}
```

#### 2. Tab切换优化
```javascript
const handleTabChange = (tabName: string) => {
  console.log('TaskManagement: 切换tab', tabName)
  activeTab.value = tabName
  // 重置分页和搜索条件
  pagination.page = 1
  searchForm.project_id = '' // 清空项目筛选
  
  // 重新加载项目列表和任务列表
  loadProjectList().then(() => {
    if (tabName === 'my') {
      loadMyTasks()
    } else {
      loadAllTasks()
    }
  })
}
```

**优化效果：**
- **我的任务**：只显示用户参与的项目（作为负责人或成员）
- **全部任务**：显示所有有权限查看的项目
- Tab切换时自动重新加载对应的项目列表
- 清空项目筛选条件，避免数据不一致

## 🎯 需求3：项目列表负责人筛选

### 后端实现
**文件：** `app/project/service/ProjectProjectService.php`

```php
/**
 * 获取项目负责人选项列表
 */
public function getOwnerOptions(): array
{
    // 获取所有项目负责人的用户ID（去重）
    $ownerIds = $this->crudService->getModel()
                                  ->where('deleted_at', null)
                                  ->where('owner_id', '>', 0)
                                  ->column('owner_id');

    if (empty($ownerIds)) {
        return [];
    }

    // 去重
    $ownerIds = array_unique($ownerIds);

    // 查询用户信息，返回下拉选项格式
    return (new AdminModel())->whereIn('id', $ownerIds)
                             ->where('deleted_at', null)
                             ->field('id, real_name as name')
                             ->select()
                             ->toArray();
}
```

### 前端实现
**文件：** `frontend/src/views/project/ProjectList.vue`

#### 1. API调用优化
```javascript
const loadUserList = async () => {
  try {
    console.log('ProjectList: 加载项目负责人选项')
    const response = await ProjectApi.getOwnerOptions()
    userList.value = response.data || []
    console.log('ProjectList: 项目负责人选项加载完成', userList.value.length)
  } catch (error) {
    console.error('加载项目负责人选项失败:', error)
    ElMessage.error('加载项目负责人选项失败')
    userList.value = []
  }
}
```

**优化效果：**
- 从mock数据改为真实的后端查询
- 只显示实际存在的项目负责人
- 自动去重，避免重复选项
- 支持错误处理和用户提示

## 🔧 技术实现亮点

### 1. 权限控制精细化
- **后端权限验证**：在服务层进行权限检查，确保数据安全
- **前端UI适配**：根据权限状态动态调整界面提示
- **用户体验优化**：清晰的权限提示，避免用户困惑

### 2. 数据筛选智能化
- **上下文感知**：根据当前tab自动加载对应的数据
- **状态同步**：tab切换时自动重置相关状态
- **性能优化**：避免不必要的数据加载

### 3. API设计规范化
- **RESTful风格**：遵循REST API设计规范
- **错误处理**：完善的错误处理和用户提示
- **数据格式统一**：统一的响应格式，便于前端处理

## 📊 测试验证

### 1. 权限控制测试
- ✅ 项目负责人可以选择任意项目成员作为执行人
- ✅ 非项目负责人只能看到自己作为执行人选项
- ✅ 权限提示信息正确显示

### 2. 项目筛选测试
- ✅ "我的任务"tab只显示参与的项目
- ✅ "全部任务"tab显示所有有权限的项目
- ✅ Tab切换时项目列表正确更新

### 3. 负责人筛选测试
- ✅ 负责人选项来自真实的后端数据
- ✅ 选项列表自动去重
- ✅ 错误处理正常工作

## 🚀 后续优化建议

1. **缓存优化**：对项目成员和负责人选项进行缓存，提升性能
2. **权限细化**：支持更多粒度的权限控制（如部门权限、角色权限等）
3. **实时更新**：当项目成员变化时，实时更新相关选项
4. **批量操作**：支持批量分配任务执行人
5. **权限审计**：记录权限相关的操作日志
