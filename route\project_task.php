<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 任务表路由
Route::group('api/project/task', function () {
    // 基础CRUD路由
    Route::get('index', 'app\project\controller\ProjectTaskController@index');
    Route::get('detail/:id', 'app\project\controller\ProjectTaskController@detail');
    Route::post('add', 'app\project\controller\ProjectTaskController@add');
    Route::post('edit/:id', 'app\project\controller\ProjectTaskController@edit');
    Route::post('delete/:id', 'app\project\controller\ProjectTaskController@delete');
    Route::post('batchDelete', 'app\project\controller\ProjectTaskController@batchDelete');
    Route::post('updateField', 'app\project\controller\ProjectTaskController@updateField');
    Route::post('status/:id', 'app\project\controller\ProjectTaskController@status');

    // 自定义API路由
    Route::get('my', 'app\project\controller\ProjectTaskController@myTasks');
    Route::post('update-status/:id', 'app\project\controller\ProjectTaskController@updateStatus');
    Route::post('assign/:id', 'app\project\controller\ProjectTaskController@assign');

    // 任务记录相关路由（集成在任务中）
    Route::post('add-comment', 'app\project\controller\ProjectTaskController@addComment');
    Route::post('add-follow', 'app\project\controller\ProjectTaskController@addFollow');
    Route::get(':id/comments', 'app\project\controller\ProjectTaskController@getComments');
    Route::get(':id/follows', 'app\project\controller\ProjectTaskController@getFollows');
    Route::put('record/:record_id', 'app\project\controller\ProjectTaskController@editRecord');
    Route::delete('record/:record_id', 'app\project\controller\ProjectTaskController@deleteRecord');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);