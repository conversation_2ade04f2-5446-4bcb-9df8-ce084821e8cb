<!--部门人员搜索器 - 表格搜索专用-->
<template>
  <div class="dept-person-search">
    <el-select
      v-model="selectedValue"
      :placeholder="placeholder"
      :size="size"
      :clearable="clearable"
      :multiple="multiple"
      :collapse-tags="collapseTags"
      :filterable="filterable"
      :remote="remote"
      :remote-method="handleRemoteSearch"
      :loading="loading"
      @change="handleChange"
      @clear="handleClear"
    >
      <el-option-group v-for="group in groupedOptions" :key="group.label" :label="group.label">
        <el-option
          v-for="item in group.options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <div class="option-item">
            <el-avatar v-if="showAvatar" :size="20" :src="item.avatar">
              {{ item.label?.charAt(0) }}
            </el-avatar>
            <span class="option-name">{{ item.label }}</span>
            <span v-if="item.department" class="option-dept">{{ item.department }}</span>
          </div>
        </el-option>
      </el-option-group>

      <template #footer>
        <div class="select-footer">
          <el-button type="primary" size="small" text @click="openAdvancedSelector">
            高级选择
          </el-button>
        </div>
      </template>
    </el-select>

    <!-- 高级选择器 -->
    <DepartmentPersonSelector
      v-model="advancedSelectorVisible"
      :selected-data="selectedPersonsData"
      :multiple="multiple"
      :title="advancedTitle"
      :department-api="departmentApi"
      :user-api="userApi"
      @confirm="handleAdvancedConfirm"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { debounce } from '@pureadmin/utils'
  import DepartmentPersonSelector from './DepartmentPersonSelector.vue'
  import { UserApi } from '@/api/system/user'
  import { DepartmentApi } from '@/api/departmentApi'
  import { ApiStatus } from '@/utils/http/status'

  // 简化的数据类型定义
  interface PersonItem {
    id: string | number
  }

  // 内部使用的完整人员信息
  interface FullPersonItem {
    id: string | number
    name: string
    avatar?: string
    position?: string
    department?: string

    [key: string]: any
  }

  interface OptionItem {
    label: string
    value: string | number
    avatar?: string
    department?: string
    type: 'user' | 'department'
  }

  // Props 定义
  interface Props {
    modelValue?: PersonItem[] | PersonItem | null
    multiple?: boolean
    placeholder?: string
    size?: 'large' | 'default' | 'small'
    clearable?: boolean
    collapseTags?: boolean
    filterable?: boolean
    remote?: boolean
    showAvatar?: boolean

    // 高级选择器配置
    advancedTitle?: string
    departmentApi?: () => Promise<any>
    userApi?: (params?: any) => Promise<any>
  }

  const props = withDefaults(defineProps<Props>(), {
    multiple: false,
    placeholder: '搜索人员或部门',
    size: 'default',
    clearable: true,
    collapseTags: true,
    filterable: true,
    remote: true,
    showAvatar: true,
    advancedTitle: '高级人员选择'
  })

  // Emits 定义
  interface Emits {
    'update:modelValue': [value: PersonItem[] | PersonItem | null]
    change: [value: PersonItem[] | PersonItem | null]
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const loading = ref(false)
  const advancedSelectorVisible = ref(false)
  const allOptions = ref<OptionItem[]>([])
  const filteredOptions = ref<OptionItem[]>([])
  const personInfoCache = ref<Map<string | number, FullPersonItem>>(new Map())

  // 计算属性
  const selectedValue = computed({
    get: () => {
      if (!props.modelValue) return props.multiple ? [] : ''

      if (props.multiple) {
        const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
        return items.map((item) => item.id)
      } else {
        const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
        return item ? item.id : ''
      }
    },
    set: (value) => {
      // 通过 handleChange 处理
    }
  })

  const selectedPersonsData = computed(() => {
    if (!props.modelValue) return props.multiple ? [] : null

    const getPersonInfo = (id: string | number): FullPersonItem | null => {
      return personInfoCache.value.get(id) || { id, name: `用户${id}` }
    }

    if (props.multiple) {
      const items = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
      return items.map((item) => getPersonInfo(item.id)).filter(Boolean)
    } else {
      const item = Array.isArray(props.modelValue) ? props.modelValue[0] : props.modelValue
      return item ? getPersonInfo(item.id) : null
    }
  })

  const groupedOptions = computed(() => {
    const users = filteredOptions.value.filter((item) => item.type === 'user')
    const departments = filteredOptions.value.filter((item) => item.type === 'department')

    const groups = []
    if (users.length > 0) {
      groups.push({ label: '人员', options: users })
    }
    if (departments.length > 0) {
      groups.push({ label: '部门', options: departments })
    }

    return groups
  })

  // 方法
  const loadInitialData = async () => {
    try {
      loading.value = true

      // 加载常用人员和部门
      const [usersRes, deptsRes] = await Promise.all([
        UserApi.getFrequentUsers?.() || UserApi.list({ page: 1, limit: 20 }),
        DepartmentApi.options()
      ])

      const options: OptionItem[] = []

      // 添加用户选项
      if (usersRes.code === ApiStatus.success) {
        const users = usersRes.data?.list || usersRes.data || []
        users.forEach((user: any) => {
          options.push({
            label: user.name,
            value: user.id,
            avatar: user.avatar,
            department: user.department,
            type: 'user'
          })

          // 缓存用户信息
          personInfoCache.value.set(user.id, {
            id: user.id,
            name: user.name,
            avatar: user.avatar,
            position: user.position,
            department: user.department
          })
        })
      }

      // 添加部门选项（扁平化）
      if (deptsRes.code === ApiStatus.success) {
        const flattenDepts = (depts: any[], prefix = '') => {
          depts.forEach((dept) => {
            const label = prefix ? `${prefix} / ${dept.name}` : dept.name
            options.push({
              label: label,
              value: dept.id,
              type: 'department'
            })
          })
        }

        flattenDepts(deptsRes.data || [])
      }

      allOptions.value = options
      filteredOptions.value = options.slice(0, 10) // 初始显示前10个
    } catch (error) {
      console.error('加载数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const handleRemoteSearch = debounce(async (query: string) => {
    if (!query) {
      filteredOptions.value = allOptions.value.slice(0, 10)
      return
    }

    loading.value = true

    try {
      // 本地搜索
      const localResults = allOptions.value.filter((item) =>
        item.label.toLowerCase().includes(query.toLowerCase())
      )

      // 远程搜索
      const searchRes =
        (await UserApi.search?.({ keyword: query })) ||
        (await UserApi.list({ keyword: query, page: 1, limit: 10 }))

      const remoteOptions: OptionItem[] = []
      if (searchRes.code === ApiStatus.success) {
        const users = searchRes.data?.list || searchRes.data || []
        users.forEach((user: any) => {
          if (!localResults.some((item) => item.value === user.id)) {
            remoteOptions.push({
              label: user.name,
              value: user.id,
              avatar: user.avatar,
              department: user.department,
              type: 'user'
            })

            // 缓存用户信息
            personInfoCache.value.set(user.id, {
              id: user.id,
              name: user.name,
              avatar: user.avatar,
              position: user.position,
              department: user.department
            })
          }
        })
      }

      filteredOptions.value = [...localResults, ...remoteOptions].slice(0, 20)
    } catch (error) {
      console.error('搜索失败:', error)
      filteredOptions.value = allOptions.value
        .filter((item) => item.label.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 10)
    } finally {
      loading.value = false
    }
  }, 300)

  const handleChange = (value: any) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      emit('update:modelValue', props.multiple ? [] : null)
      emit('change', props.multiple ? [] : null)
      return
    }

    // 转换为简化格式
    let result: PersonItem[] | PersonItem | null = null

    if (props.multiple) {
      result = Array.isArray(value) ? value.map((id) => ({ id })) : [{ id: value }]
    } else {
      result = { id: Array.isArray(value) ? value[0] : value }
    }

    emit('update:modelValue', result)
    emit('change', result)
  }

  const handleClear = () => {
    const value = props.multiple ? [] : null
    emit('update:modelValue', value)
    emit('change', value)
  }

  const openAdvancedSelector = () => {
    advancedSelectorVisible.value = true
  }

  const handleAdvancedConfirm = (selected: FullPersonItem[] | FullPersonItem | null) => {
    // 缓存人员信息
    if (selected) {
      if (Array.isArray(selected)) {
        selected.forEach((person) => {
          personInfoCache.value.set(person.id, person)
        })
      } else {
        personInfoCache.value.set(selected.id, selected)
      }
    }

    // 转换为简化格式
    let result: PersonItem[] | PersonItem | null = null

    if (props.multiple) {
      result = Array.isArray(selected)
        ? selected.map((person) => ({ id: person.id }))
        : selected
          ? [{ id: selected.id }]
          : []
    } else {
      result = Array.isArray(selected)
        ? selected.length > 0
          ? { id: selected[0].id }
          : null
        : selected
          ? { id: selected.id }
          : null
    }

    emit('update:modelValue', result)
    emit('change', result)
  }

  // 生命周期
  onMounted(() => {
    loadInitialData()
  })
</script>

<style lang="scss" scoped>
  .dept-person-search {
    width: 100%;

    .option-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .option-name {
        flex: 1;
        font-size: 14px;
      }

      .option-dept {
        font-size: 12px;
        color: #909399;
      }
    }

    .select-footer {
      padding: 8px 12px;
      border-top: 1px solid #e6e6e6;
      text-align: center;
    }
  }
</style>
