<template>
  <div style="padding: 20px;">
    <h2>ApiSelect 组件测试</h2>
    
    <div style="margin-bottom: 20px;">
      <h3>测试1: 有数据的API</h3>
      <ApiSelect
        v-model="value1"
        :api="{ url: '/system/admin/options' }"
        placeholder="请选择用户"
        no-data-text="暂无用户数据"
        no-match-text="无匹配的用户"
        style="width: 300px;"
      />
      <p>选中值: {{ value1 }}</p>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h3>测试2: 无数据的API</h3>
      <ApiSelect
        v-model="value2"
        :api="{ url: '/project/project/member-options', params: { project_id: 999 } }"
        placeholder="请选择执行人"
        no-data-text="暂无项目成员"
        no-match-text="无匹配的成员"
        style="width: 300px;"
      />
      <p>选中值: {{ value2 }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'

const value1 = ref(null)
const value2 = ref(null)
</script>
