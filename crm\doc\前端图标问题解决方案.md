# 前端图标问题解决方案

## 问题描述

在访问线索列表页面时，出现以下错误：

```
SyntaxError: The requested module '/node_modules/.vite/deps/@element-plus_icons-vue.js?v=a34f90d2' does not provide an export named 'Office' (at convert-dialog.vue:4:18)
```

## 问题原因

1. **图标名称错误**：`@element-plus/icons-vue` 中没有名为 `Office` 的图标
2. **Vite 缓存问题**：开发服务器的依赖预构建缓存可能导致模块导入错误

## 解决方案

### 方案1：修复图标名称（已实施）

将错误的图标名称替换为正确的图标：

```typescript
// 错误的导入
import { User, Office, Phone } from '@element-plus/icons-vue'

// 正确的导入
import { User, House, Phone } from '@element-plus/icons-vue'
```

**修改的文件**：
- `frontend/src/views/crm/crm_lead/convert-dialog.vue`

### 方案2：清除 Vite 缓存

如果修改图标名称后仍有问题，请执行以下步骤：

#### Windows 用户

1. 运行提供的批处理文件：
   ```bash
   cd frontend
   ./clear-cache.bat
   ```

2. 或手动执行以下命令：
   ```bash
   # 删除 Vite 缓存
   rm -rf node_modules/.vite
   
   # 删除构建目录
   rm -rf dist
   
   # 清除 npm 缓存
   npm cache clean --force
   
   # 重新安装依赖
   npm install
   
   # 重新启动开发服务器
   npm run dev
   ```

#### Linux/Mac 用户

```bash
cd frontend

# 删除 Vite 缓存
rm -rf node_modules/.vite

# 删除构建目录
rm -rf dist

# 清除 npm 缓存
npm cache clean --force

# 重新安装依赖
npm install

# 重新启动开发服务器
npm run dev
```

### 方案3：Vite 配置优化（已实施）

在 `vite.config.ts` 中添加图标库到预构建依赖：

```typescript
optimizeDeps: {
  include: [
    // ... 其他依赖
    '@element-plus/icons-vue'
  ]
}
```

## Element Plus 可用图标

### 常用的公司/办公相关图标

- `House` - 房屋图标（用于公司地址）
- `OfficeBuilding` - 办公楼图标（如果可用）
- `Shop` - 商店图标
- `HomeFilled` - 填充房屋图标

### 常用的用户相关图标

- `User` - 用户图标
- `UserFilled` - 填充用户图标
- `Avatar` - 头像图标

### 常用的通讯相关图标

- `Phone` - 电话图标
- `PhoneFilled` - 填充电话图标
- `Cellphone` - 手机图标
- `Message` - 消息图标

### 常用的时间相关图标

- `Clock` - 时钟图标
- `Timer` - 计时器图标
- `AlarmClock` - 闹钟图标

### 常用的文档相关图标

- `Document` - 文档图标
- `DocumentAdd` - 添加文档图标
- `Files` - 文件图标
- `Folder` - 文件夹图标

## 图标使用最佳实践

### 1. 导入方式

```typescript
// 推荐：按需导入
import { User, House, Phone } from '@element-plus/icons-vue'

// 不推荐：全量导入
import * as Icons from '@element-plus/icons-vue'
```

### 2. 使用方式

```vue
<template>
  <!-- 在 el-input 中使用 -->
  <el-input :prefix-icon="User" placeholder="请输入用户名" />
  
  <!-- 在 el-icon 中使用 -->
  <el-icon><User /></el-icon>
  
  <!-- 直接使用 -->
  <User />
</template>
```

### 3. 图标测试

创建测试页面验证图标是否正常：

```vue
<template>
  <div>
    <h3>图标测试</h3>
    <el-icon><User /></el-icon>
    <el-icon><House /></el-icon>
    <el-icon><Phone /></el-icon>
  </div>
</template>

<script setup lang="ts">
import { User, House, Phone } from '@element-plus/icons-vue'
</script>
```

## 验证修复

### 1. 检查控制台错误

打开浏览器开发者工具，检查是否还有图标相关的错误。

### 2. 测试页面功能

1. 访问线索列表页面
2. 点击"转化"按钮
3. 检查转化对话框是否正常显示
4. 验证输入框的图标是否正常显示

### 3. 测试其他对话框

1. 测试跟进记录对话框
2. 测试线索分配对话框
3. 确保所有图标都正常显示

## 常见问题

### Q1: 修改后仍然报错怎么办？

A: 尝试以下步骤：
1. 清除浏览器缓存
2. 重启开发服务器
3. 清除 Vite 缓存（使用方案2）

### Q2: 如何查找可用的图标？

A: 访问 [Element Plus 图标文档](https://element-plus.org/en-US/component/icon) 查看所有可用图标。

### Q3: 图标显示为空白怎么办？

A: 检查：
1. 图标名称是否正确
2. 是否正确导入
3. Element Plus 版本是否兼容

## 相关文件

- `frontend/src/views/crm/crm_lead/convert-dialog.vue` - 转化对话框
- `frontend/src/views/crm/crm_lead/follow-record-dialog.vue` - 跟进记录对话框
- `frontend/src/views/crm/crm_lead_pool/assign-dialog.vue` - 分配对话框
- `frontend/vite.config.ts` - Vite 配置文件
- `frontend/clear-cache.bat` - 缓存清理脚本
