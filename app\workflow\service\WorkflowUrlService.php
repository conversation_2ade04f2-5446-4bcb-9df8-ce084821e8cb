<?php

namespace app\workflow\service;

use think\facade\Config;
use think\facade\Log;
use think\facade\Request;

/**
 * Workflow URL生成服务
 * 根据配置文件生成PC端和移动端URL
 */
class WorkflowUrlService
{
    /**
     * URL配置缓存
     */
    private static $config = null;
    
    /**
     * 获取URL配置
     */
    private static function getConfig(): array
    {
        if (self::$config === null) {
            self::$config = Config::get('workflow_url', []);
        }
        return self::$config;
    }
    
    /**
     * 生成实例详情URL
     *
     * @param int $instanceId 实例ID
     * @param string $platform 平台类型：pc|mobile|app
     * @return string
     */
    public static function generateInstanceDetailUrl(int $instanceId, string $platform = 'auto'): string
    {
        return self::generateUrl('instance.detail', ['instance_id' => $instanceId], $platform);
    }
    
    /**
     * 生成任务详情URL
     *
     * @param int $taskId 任务ID
     * @param string $platform 平台类型：pc|mobile|app
     * @return string
     */
    public static function generateTaskDetailUrl(int $taskId, string $platform = 'auto'): string
    {
        return self::generateUrl('task.detail', ['task_id' => $taskId], $platform);
    }
    
    /**
     * 生成任务审批URL
     *
     * @param int $taskId 任务ID
     * @param string $platform 平台类型：pc|mobile|app
     * @return string
     */
    public static function generateTaskApprovalUrl(int $taskId, string $platform = 'auto'): string
    {
        return self::generateUrl('task.approval', ['task_id' => $taskId], $platform);
    }
    
    /**
     * 生成实例历史URL
     *
     * @param int $instanceId 实例ID
     * @param string $platform 平台类型：pc|mobile|app
     * @return string
     */
    public static function generateInstanceHistoryUrl(int $instanceId, string $platform = 'auto'): string
    {
        return self::generateUrl('instance.history', ['instance_id' => $instanceId], $platform);
    }
    
    /**
     * 生成任务列表URL
     *
     * @param string $platform 平台类型：pc|mobile|app
     * @return string
     */
    public static function generateTaskListUrl(string $platform = 'auto'): string
    {
        return self::generateUrl('task.list', [], $platform);
    }
    
    /**
     * 通用URL生成方法
     *
     * @param string $urlKey URL配置键名（如：instance.detail）
     * @param array $params 参数数组
     * @param string $platform 平台类型：pc|mobile|app|auto
     * @return string
     */
    public static function generateUrl(string $urlKey, array $params = [], string $platform = 'auto'): string
    {
        $config = self::getConfig();
        
        // 自动检测平台
        if ($platform === 'auto') {
            $platform = self::detectPlatform();
        }
        
        // 获取URL模板
        $urlTemplate = self::getUrlTemplate($urlKey, $platform);
        
        // 如果URL模板为空，返回空字符串
        if (empty($urlTemplate)) {
            self::logEmptyUrl($urlKey, $platform);
            return '';
        }
        
        // 替换参数
        $url = self::replaceUrlParams($urlTemplate, $params);
        
        // 记录调试日志
        self::logUrlGeneration($urlKey, $platform, $params, $url);
        
        return $url;
    }
    
    /**
     * 获取URL模板
     *
     * @param string $urlKey URL配置键名
     * @param string $platform 平台类型
     * @return string
     */
    private static function getUrlTemplate(string $urlKey, string $platform): string
    {
        $config = self::getConfig();
        $keys = explode('.', $urlKey);
        
        // 逐层获取配置
        $urlMapping = $config['url_mapping'] ?? [];
        foreach ($keys as $key) {
            if (isset($urlMapping[$key])) {
                $urlMapping = $urlMapping[$key];
            } else {
                return '';
            }
        }
        
        // 获取平台对应的URL
        if (isset($urlMapping[$platform])) {
            return $urlMapping[$platform];
        }
        
        // 如果没有找到，尝试使用PC端作为备用
        if ($platform !== 'pc' && isset($urlMapping['pc'])) {
            return $urlMapping['pc'];
        }
        
        return '';
    }
    
    /**
     * 替换URL参数
     *
     * @param string $urlTemplate URL模板
     * @param array $params 参数数组
     * @return string
     */
    private static function replaceUrlParams(string $urlTemplate, array $params): string
    {
        $url = $urlTemplate;
        
        foreach ($params as $key => $value) {
            $url = str_replace('{' . $key . '}', $value, $url);
        }
        
        return $url;
    }
    
    /**
     * 检测当前平台
     *
     * @return string
     */
    private static function detectPlatform(): string
    {
        $config = self::getConfig();
        $userAgent = Request::header('User-Agent', '');
        
        // 检测APP
        $appUserAgents = $config['platform_detection']['app_user_agents'] ?? [];
        foreach ($appUserAgents as $appUA) {
            if (stripos($userAgent, $appUA) !== false) {
                return 'app';
            }
        }
        
        // 检测移动端
        $mobileUserAgents = $config['platform_detection']['mobile_user_agents'] ?? [];
        foreach ($mobileUserAgents as $mobileUA) {
            if (stripos($userAgent, $mobileUA) !== false) {
                return 'mobile';
            }
        }
        
        // 默认PC端
        return 'pc';
    }
    
    /**
     * 记录空URL警告
     *
     * @param string $urlKey URL键名
     * @param string $platform 平台类型
     */
    private static function logEmptyUrl(string $urlKey, string $platform): void
    {
        $config = self::getConfig();
        
        if ($config['development']['show_empty_urls'] ?? true) {
            Log::warning("WorkflowUrlService: 空URL配置", [
                'url_key' => $urlKey,
                'platform' => $platform,
                'message' => "请在config/workflow_url.php中配置{$platform}端的{$urlKey}页面URL"
            ]);
        }
    }
    
    /**
     * 记录URL生成日志
     *
     * @param string $urlKey URL键名
     * @param string $platform 平台类型
     * @param array $params 参数
     * @param string $url 生成的URL
     */
    private static function logUrlGeneration(string $urlKey, string $platform, array $params, string $url): void
    {
        $config = self::getConfig();
        
        if ($config['development']['log_url_generation'] ?? false) {
            Log::info("WorkflowUrlService: URL生成", [
                'url_key' => $urlKey,
                'platform' => $platform,
                'params' => $params,
                'generated_url' => $url
            ]);
        }
    }
    
    /**
     * 获取备用URL
     *
     * @param string $platform 平台类型
     * @return string
     */
    public static function getFallbackUrl(string $platform = 'auto'): string
    {
        if ($platform === 'auto') {
            $platform = self::detectPlatform();
        }
        
        $config = self::getConfig();
        return $config['fallback'][$platform] ?? '';
    }
    
    /**
     * 批量生成URL（用于消息发送）
     *
     * @param string $urlKey URL配置键名
     * @param array $params 参数数组
     * @return array ['detail_url' => '', 'mobile_url' => '']
     */
    public static function generateBatchUrls(string $urlKey, array $params = []): array
    {
        return [
            'detail_url' => self::generateUrl($urlKey, $params, 'pc'),
            'mobile_url' => self::generateUrl($urlKey, $params, 'mobile')
        ];
    }
}
