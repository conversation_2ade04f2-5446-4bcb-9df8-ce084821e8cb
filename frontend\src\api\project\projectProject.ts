import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 项目表相关接口
 */
export class ProjectProjectApi {
  /**
   * 获取项目表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_project/index',
      params
    })
  }

  /**
   * 获取项目表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/project/project_project/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加项目表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_project/add',
      data
    })
  }

  /**
   * 更新项目表
   * @param id 项目ID
   * @param data 表单数据
   */
  static update(id: number, data: any) {
    return request.post<BaseResult>({
      url: `/project/project_project/edit/${id}`,
      data
    })
  }

  /**
   * 删除项目表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_project/delete/${id}`
    })
  }

  /**
   * 批量删除项目表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/project/project_project/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_project/updateField',
      data
    })
  }

  
  /**
   * 修改项目表状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/project/project_project/status/${data.id}`,
      data
    })
  }
  

  


  /**
   * 导入项目表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/project/project_project/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/project/project_project/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/project/project_project/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}