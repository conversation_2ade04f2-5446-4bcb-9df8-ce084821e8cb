<!--部门+人员选择组件-->
<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    :width="width"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="dept-person-selector-container">
      <!-- 左侧部门树 -->
      <div class="department-tree-panel">
        <div class="panel-title">
          {{ departmentTitle }}
          <el-button
            v-if="showSelectAll && currentDeptId"
            size="small"
            type="primary"
            text
            @click="selectAllInDept"
          >
            全选当前部门
          </el-button>
        </div>
        <div class="department-tree">
          <!-- 全部选项 -->
          <div
            class="tree-node all-option"
            :class="{ 'is-current': currentDeptId === null }"
            @click="handleDeptClick(null)"
          >
            <span>全部</span>
          </div>

          <el-tree
            ref="treeRef"
            :data="departmentTree"
            node-key="id"
            :props="treeProps"
            highlight-current
            :expand-on-click-node="false"
            :default-expand-all="defaultExpandAll"
            @node-click="handleDeptClick"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <span>{{ node.label }}</span>
                <span v-if="showPersonCount && data.person_count" class="person-count">
                  ({{ data.person_count }}人)
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 中间员工列表 -->
      <div class="employee-list-panel">
        <div class="panel-title">
          <!--          <span>{{ currentDeptName || '全部' }}{{ personTitle }}</span>-->
          <span>{{ personTitle }}</span>
          <el-input
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            prefix-icon="Search"
            clearable
            size="small"
            style="width: 150px"
            @clear="handleSearchClear"
            @input="handleSearch"
          />
        </div>
        <div class="employee-list">
          <el-empty v-if="employeeList.length === 0" :description="emptyText" />
          <div
            v-for="emp in employeeList"
            :key="emp.id"
            class="employee-item"
            @click="selectEmployee(emp)"
          >
            <div class="employee-info">
              <el-avatar v-if="showAvatar" :size="32" :src="emp.avatar">
                {{ emp.name?.charAt(0) }}
              </el-avatar>
              <div class="employee-details">
                <div class="employee-name">{{ emp.name }}</div>
                <div v-if="showPosition && emp.position" class="employee-position">
                  {{ emp.position }}
                </div>
              </div>
            </div>
            <el-icon class="add-icon">
              <Plus />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 右侧已选择人员 -->
      <div class="selected-panel">
        <div class="panel-title">
          {{ selectedTitle }}
          <span class="selected-count">({{ selectedEmployees.length }})</span>
          <el-button
            v-if="selectedEmployees.length > 0"
            size="small"
            type="danger"
            text
            @click="clearSelected"
          >
            清空
          </el-button>
        </div>
        <div class="selected-list">
          <el-empty v-if="selectedEmployees.length === 0" description="暂无选择" />
          <div v-for="emp in selectedEmployees" :key="emp.id" class="selected-item">
            <div class="employee-info">
              <el-avatar v-if="showAvatar" :size="24" :src="emp.avatar">
                {{ emp.name?.charAt(0) }}
              </el-avatar>
              <div class="employee-details">
                <div class="employee-name">{{ emp.name }}</div>
                <div
                  v-if="showPosition && (emp.position || emp.department)"
                  class="employee-position"
                >
                  {{ emp.position || emp.department }}
                </div>
              </div>
            </div>
            <el-icon class="remove-icon" @click="removeEmployee(emp)">
              <Delete />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ cancelText }}</el-button>
        <el-button type="primary" @click="handleConfirm">
          {{ confirmText }}({{ selectedEmployees.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { Plus, Delete } from '@element-plus/icons-vue'
  import { debounce } from '@pureadmin/utils'
  import { DepartmentApi } from '@/api/departmentApi'
  import { UserApi } from '@/api/system/user'
  import { ApiStatus } from '@/utils/http/status'
  import { PersonItem, FullPersonItem, DepartmentItem } from '@/components/custom/types'

  // Props 定义
  interface Props {
    modelValue: boolean
    selectedData?: FullPersonItem[] | FullPersonItem | null

    // 基础配置
    title?: string
    width?: string
    departmentTitle?: string
    personTitle?: string
    selectedTitle?: string

    // 功能配置
    multiple?: boolean
    showAvatar?: boolean
    showPosition?: boolean
    showPersonCount?: boolean
    showSelectAll?: boolean
    defaultExpandAll?: boolean

    // 显示模式配置
    mode?: 'dialog' | 'dropdown' | 'inline'
    size?: 'large' | 'default' | 'small'

    // 文本配置
    searchPlaceholder?: string
    emptyText?: string
    cancelText?: string
    confirmText?: string

    // API 配置
    departmentApi?: () => Promise<any>
    userApi?: (params?: any) => Promise<any>

    // 树形配置
    treeProps?: {
      label: string
      children: string
    }
  }

  const props = withDefaults(defineProps<Props>(), {
    title: '选择人员',
    width: '800px',
    departmentTitle: '部门列表',
    personTitle: '人员列表',
    selectedTitle: '已选择人员',
    multiple: true,
    showAvatar: true,
    showPosition: true,
    showPersonCount: false,
    showSelectAll: true,
    defaultExpandAll: false,
    mode: 'dialog',
    size: 'default',
    searchPlaceholder: '搜索人员',
    emptyText: '暂无人员',
    cancelText: '取消',
    confirmText: '确定',
    treeProps: () => ({ label: 'name', children: 'children' })
  })

  // Emits 定义
  interface Emits {
    'update:modelValue': [value: boolean]
    confirm: [selectedItems: FullPersonItem[] | FullPersonItem | null]
    cancel: []
    change: [selectedItems: FullPersonItem[] | FullPersonItem | null]
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const treeRef = ref()
  const departmentTree = ref<DepartmentItem[]>([])
  const employeeList = ref<FullPersonItem[]>([])
  const allEmployeeList = ref<FullPersonItem[]>([]) // 存储所有员工数据用于本地搜索
  const selectedEmployees = ref<FullPersonItem[]>([])
  const searchKeyword = ref('')
  const currentDeptId = ref<string | number | null>(null)
  const currentDeptName = ref('')

  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 默认 API 函数
  const defaultDepartmentApi = async () => {
    const res = await DepartmentApi.options()
    if (res.code === ApiStatus.success) {
      return res.data || []
    }
    throw new Error(res.message || '获取部门列表失败')
  }

  const defaultUserApi = async (params?: any) => {
    // 如果有部门ID，使用按部门获取用户的接口
    if (params?.dept_id) {
      const res = await UserApi.getUsersByDepartment(params.dept_id, params)
      if (res.code === ApiStatus.success) {
        return res.data || []
      }
      throw new Error(res.message || '获取人员列表失败')
    } else {
      // 否则获取所有用户
      const res = await UserApi.list(params)
      if (res.code === ApiStatus.success) {
        return res.data?.list || []
      }
      throw new Error(res.message || '获取人员列表失败')
    }
  }

  // 方法实现
  const loadDepartmentTree = async () => {
    try {
      const apiFunc = props.departmentApi || defaultDepartmentApi
      const data = await apiFunc()
      departmentTree.value = data
    } catch (error) {
      console.error('获取部门列表失败', error)
      ElMessage.error('获取部门列表失败')
    }
  }

  const loadEmployeeList = async (deptId?: string | number | null) => {
    try {
      // 如果是搜索状态，使用本地搜索
      if (searchKeyword.value) {
        performLocalSearch()
        return
      }

      const params: any = {}
      if (deptId) {
        params.dept_id = deptId
      }

      const apiFunc = props.userApi || defaultUserApi
      const data = await apiFunc(params)

      const mappedData = data.map((user: any) => ({
        id: user.id,
        name: user.name,
        avatar: user.avatar,
        position: user.position,
        department: user.department
      }))

      // 如果是加载全部员工，存储到allEmployeeList
      if (!deptId) {
        allEmployeeList.value = mappedData
      }

      // 过滤掉已选择的员工
      const selectedIds = selectedEmployees.value.map((emp) => emp.id)
      employeeList.value = mappedData.filter((emp: PersonItem) => !selectedIds.includes(emp.id))
    } catch (error) {
      console.error('获取人员列表失败', error)
      ElMessage.error('获取人员列表失败')
    }
  }

  const handleDeptClick = (dept: DepartmentItem | null) => {
    if (dept === null) {
      // 点击"全部"选项
      currentDeptId.value = null
      currentDeptName.value = '全部'
      // 清除树形组件的选中状态
      if (treeRef.value) {
        treeRef.value.setCurrentKey(null)
      }
    } else {
      currentDeptId.value = dept.id
      currentDeptName.value = dept.name
      // 设置树形组件的选中状态
      if (treeRef.value) {
        treeRef.value.setCurrentKey(dept.id)
      }
    }
    searchKeyword.value = ''
    loadEmployeeList(currentDeptId.value)
  }

  // 本地搜索方法
  const performLocalSearch = () => {
    const keyword = searchKeyword.value.toLowerCase().trim()
    if (!keyword) {
      loadEmployeeList(currentDeptId.value)
      return
    }

    // 确定搜索范围
    let searchData = allEmployeeList.value
    if (currentDeptId.value) {
      // 如果选择了特定部门，先获取该部门的员工
      // 这里需要根据实际情况调整，可能需要从API获取部门员工
      searchData = allEmployeeList.value
    }

    // 在当前数据中搜索
    const selectedIds = selectedEmployees.value.map((emp) => emp.id)
    employeeList.value = searchData.filter(
      (emp: FullPersonItem) =>
        emp.name.toLowerCase().includes(keyword) && !selectedIds.includes(emp.id)
    )
  }

  const handleSearch = debounce(() => {
    performLocalSearch()
  }, 300)

  const handleSearchClear = () => {
    searchKeyword.value = ''
    loadEmployeeList(currentDeptId.value)
  }

  const selectEmployee = (employee: FullPersonItem) => {
    if (!props.multiple) {
      // 单选模式：替换当前选择
      selectedEmployees.value = [employee]

      // 重新加载员工列表以显示正确的可选状态
      loadEmployeeList(currentDeptId.value)
    } else {
      // 多选模式：添加到选择列表
      if (selectedEmployees.value.some((emp) => emp.id === employee.id)) {
        ElMessage.warning('该人员已被选择')
        return
      }

      // 从员工列表中移除
      employeeList.value = employeeList.value.filter((emp) => emp.id !== employee.id)

      // 添加到已选择列表
      selectedEmployees.value.push(employee)
    }
  }

  const removeEmployee = (employee: FullPersonItem) => {
    // 从已选择列表中移除
    selectedEmployees.value = selectedEmployees.value.filter((emp) => emp.id !== employee.id)

    // 如果不是搜索状态，重新加载员工列表
    if (!searchKeyword.value) {
      loadEmployeeList(currentDeptId.value)
    }
  }

  const selectAllInDept = () => {
    if (!currentDeptId.value) {
      ElMessage.warning('请先选择部门')
      return
    }

    // 将当前部门所有员工添加到已选择列表
    selectedEmployees.value.push(...employeeList.value)
    employeeList.value = []
  }

  const clearSelected = () => {
    selectedEmployees.value = []
    loadEmployeeList(currentDeptId.value)
  }

  const handleCancel = () => {
    emit('cancel')
    dialogVisible.value = false
  }

  const handleConfirm = () => {
    const result = props.multiple
      ? [...selectedEmployees.value]
      : selectedEmployees.value.length > 0
        ? selectedEmployees.value[0]
        : null

    emit('confirm', result)
    emit('change', result)
    dialogVisible.value = false
  }

  // 监听选中数据变化
  watch(
    () => props.selectedData,
    (newVal) => {
      if (newVal) {
        if (props.multiple) {
          // 多选模式：确保是数组
          selectedEmployees.value = Array.isArray(newVal) ? [...newVal] : [newVal]
        } else {
          // 单选模式：转换为数组进行内部处理
          selectedEmployees.value = Array.isArray(newVal)
            ? newVal.slice(0, 1)
            : newVal
              ? [newVal]
              : []
        }
      } else {
        selectedEmployees.value = []
      }
    },
    { immediate: true, deep: true }
  )

  // 监听对话框显示
  watch(dialogVisible, (visible) => {
    if (visible) {
      loadDepartmentTree()
      loadEmployeeList()
    }
  })

  // 暴露方法
  defineExpose({
    loadDepartmentTree,
    loadEmployeeList,
    clearSelected
  })
</script>

<style lang="scss" scoped>
  .dept-person-selector-container {
    display: flex;
    height: 500px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;

    .department-tree-panel,
    .employee-list-panel,
    .selected-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      border-right: 1px solid #e6e6e6;

      &:last-child {
        border-right: none;
      }

      .panel-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e6e6e6;
        font-weight: 500;
        color: #303133;
        font-size: 14px;

        .selected-count {
          color: #909399;
          font-size: 12px;
          margin-left: 4px;
        }

        .person-count {
          color: #909399;
          font-size: 12px;
          margin-left: 4px;
        }
      }
    }

    .department-tree {
      flex: 1;
      padding: 8px;
      overflow-y: auto;

      .tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-size: 14px;
      }

      .all-option {
        padding: 0 20px;
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        font-size: 14px;
        margin-bottom: 4px;
        transition: all 0.3s;
        border-radius: 4px;

        &:hover {
          background-color: #f5f7fa;
        }

        &.is-current {
          background-color: #409eff;
          color: white;
        }
      }

      :deep(.el-tree-node__content) {
        height: 32px;
      }
    }

    .employee-list,
    .selected-list {
      flex: 1;
      padding: 8px;
      overflow-y: auto;
    }

    .employee-item,
    .selected-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      margin-bottom: 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      border: 1px solid transparent;

      &:hover {
        background-color: #f5f7fa;
        border-color: #e6e6e6;
      }

      .employee-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .employee-details {
          margin-left: 8px;
          flex: 1;
          min-width: 0;

          .employee-name {
            font-size: 14px;
            color: #303133;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .employee-position {
            font-size: 12px;
            color: #909399;
            margin-top: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .add-icon,
      .remove-icon {
        color: #409eff;
        cursor: pointer;
        font-size: 16px;
        flex-shrink: 0;

        &:hover {
          color: #66b1ff;
        }
      }

      .remove-icon {
        color: #f56c6c;

        &:hover {
          color: #f78989;
        }
      }
    }

    .selected-item {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .dept-person-selector-container {
      flex-direction: column;
      height: auto;

      .department-tree-panel,
      .employee-list-panel,
      .selected-panel {
        border-right: none;
        border-bottom: 1px solid #e6e6e6;
        min-height: 200px;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
</style>
