# CRM客户详情页面代码实施计划

## 📋 实施概述

基于《CRM客户详情组件接口对接分析报告》和权限系统设计，制定详细的代码实施计划。本计划将解决现有问题并实施22个权限控制功能。

## 🚨 关键问题分析

### 1. 紧急问题 (必须首先解决)
- **路由配置错误**：`route/Crm.php` 中控制器引用错误
- **API路径冲突**：两套路径系统并存
- **前端API缺失**：缺少客户高级操作API定义

### 2. 核心问题
- **模拟数据**：所有面板使用硬编码数据
- **事件处理不完整**：只有console.log占位代码
- **权限控制缺失**：无权限验证机制

### 3. 功能范围调整
- **暂缓实施**：商机功能、客户转移、客户共享
- **当前实施**：22个权限 (21个核心功能 + 1个回收客户)

## 🎯 实施计划

### 阶段一：基础环境准备 (0.5天)

#### 任务1.1：数据库权限配置
**优先级**：🔴 紧急
**负责人**：后端开发

**数据库信息**：
- 主机：192.168.68.34:3306
- 数据库：www_bs_com
- 用户：www_bs_com

**执行步骤**：
1. [ ] 连接数据库
```bash
mysql -h 192.168.68.34 -P 3306 -u www_bs_com -p www_bs_com
```

2. [ ] 执行权限SQL文件
```bash
mysql -h 192.168.68.34 -P 3306 -u www_bs_com -p www_bs_com < crm_customer_detail_permissions.sql
```

3. [ ] 验证权限是否正确添加
```sql
SELECT COUNT(*) FROM system_menu WHERE name LIKE 'crm:crm_customer_my:%';
-- 应该返回 22
```

#### 任务1.2：API路径统一确认
**说明**：route/Crm.php 已废弃，统一使用CRUD生成器路径

**统一路径**：`/crm/crm_customer_my/`
- 基础CRUD：`/crm/crm_customer_my/index`、`/crm/crm_customer_my/detail`等
- 详情操作：`/crm/crm_customer_my/add_contact`、`/crm/crm_customer_my/edit_contact`等

**验证方法**：
```bash
# 测试客户列表接口
curl -X GET "http://localhost/crm/crm_customer_my/index"

# 测试客户详情接口
curl -X GET "http://localhost/crm/crm_customer_my/detail/1"
```

### 阶段二：权限系统实施 (2天)

#### 任务2.1：数据库权限配置
**负责人**：后端开发

1. [ ] 执行权限SQL文件
```bash
mysql -u root -p123456 base_admin < crm_customer_detail_permissions.sql
```

2. [ ] 验证22个权限是否正确添加
3. [ ] 配置测试角色权限
4. [ ] 测试权限中间件

#### 任务2.2：扩展现有权限中间件
**负责人**：后端开发

**现有权限中间件分析**：
- 文件：`app/common/middleware/PermissionMiddleware.php`
- 功能：基于路由名称生成权限标识 `模块:控制器:方法`
- 验证：检查用户权限列表中是否包含该权限标识

**扩展方案**：
1. [ ] 在现有中间件基础上添加数据权限验证
2. [ ] 保持现有功能权限验证不变
3. [ ] 添加客户数据访问权限验证

**创建文件**：`app/crm/service/CustomerPermissionService.php`

```php
<?php
namespace app\crm\service;

use app\crm\model\CrmCustomer;

class CustomerPermissionService
{
    /**
     * 验证客户数据访问权限
     */
    public function validateCustomerAccess(int $customerId, int $userId): bool
    {
        // 1. 超级管理员
        if (is_super_admin() || is_tenant_super_admin()) {
            return true;
        }

        // 2. 获取客户信息
        $customer = CrmCustomer::find($customerId);
        if (!$customer) {
            return false;
        }

        // 3. 客户负责人 (创建人=负责人)
        if ($customer['creator_id'] == $userId) {
            return true;
        }

        // 4. 数据权限范围检查 (预留)
        // TODO: 实现部门数据权限、共享权限等

        return false;
    }
}
```

#### 任务2.3：集成数据权限验证
**负责人**：后端开发

**修改现有权限中间件**：
在 `PermissionMiddleware.php` 中添加数据权限验证逻辑

```php
// 在权限验证通过后，添加数据权限验证
if (strpos($permission, 'crm:crm_customer_my:') === 0) {
    $customerId = $request->param('id') ?? $request->param('customer_id');
    if ($customerId) {
        $customerPermissionService = app(\app\crm\service\CustomerPermissionService::class);
        if (!$customerPermissionService->validateCustomerAccess($customerId, $adminId)) {
            throw new PermissionException('无权限访问此客户数据');
        }
    }
}
```

### 阶段三：前端API接口实施 (1天)

#### 任务3.1：创建客户详情API文件
**负责人**：前端开发

**创建文件**：`frontend/src/api/crm/crmCustomerDetail.ts`

**实施步骤**：
1. [ ] 创建API接口类
2. [ ] 实现22个操作接口
3. [ ] 统一错误处理
4. [ ] 添加TypeScript类型定义

**接口清单**：
```typescript
export class CrmCustomerDetailApi {
  // 联系人操作 (4个)
  static addContact(customerId: number, data: any)
  static editContact(contactId: number, data: any)
  static deleteContact(contactId: number)
  static getContactList(customerId: number, params?: any)
  
  // 合同操作 (6个)
  static addContract(customerId: number, data: any)
  static editContract(contractId: number, data: any)
  static deleteContract(contractId: number)
  static getContractDetail(contractId: number)
  static getContractList(customerId: number, params?: any)
  static submitApproval(contractId: number, data: any)
  
  // 回款操作 (7个)
  static addReceivable(contractId: number, data: any)
  static editReceivable(receivableId: number, data: any)
  static deleteReceivable(receivableId: number)
  static getReceivableDetail(receivableId: number)
  static getReceivableList(contractId: number, params?: any)
  static submitReceivableApproval(receivableId: number, data: any)
  static addReceivableMore(contractId: number, data: any)
  
  // 跟进记录 (4个)
  static addFollow(customerId: number, data: any)
  static editFollow(followId: number, data: any)
  static deleteFollow(followId: number)
  static getFollowDetail(followId: number)
  
  // 客户操作 (1个)
  static recycleCustomer(customerId: number)
}
```

#### 任务3.2：扩展现有权限指令
**负责人**：前端开发

**现有权限指令分析**：
- 文件：`frontend/src/directives/permission.ts`
- 指令：`v-auth`
- 功能：基于路由meta.authList验证权限

**扩展方案**：
1. [ ] 保持现有 `v-auth` 指令不变
2. [ ] 添加新的 `v-permission` 指令支持完整权限标识
3. [ ] 创建权限验证组合式函数

**修改文件**：`frontend/src/directives/permission.ts`

```typescript
import { router } from '@/router'
import { App, Directive } from 'vue'

// 现有的 v-auth 指令保持不变
const authDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authList = (router.currentRoute.value.meta.authList as Array<{ auth_mark: string }>) || []
    const hasPermission = authList.some((item) => item.auth_mark === binding.value)
    if (!hasPermission) {
      el.parentNode?.removeChild(el)
    }
  }
}

// 新增的 v-permission 指令
const permissionDirective: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const authList = (router.currentRoute.value.meta.authList as Array<{ auth_mark: string }>) || []
    const hasPermission = authList.some((item) => item.auth_mark === binding.value)
    if (!hasPermission) {
      el.parentNode?.removeChild(el)
    }
  }
}

export function setupPermissionDirective(app: App) {
  app.directive('auth', authDirective)
  app.directive('permission', permissionDirective)
}
```

**创建文件**：`frontend/src/composables/useCustomerPermission.ts`

```typescript
import { router } from '@/router'

export const useCustomerPermission = () => {

  const hasAuth = (permission: string): boolean => {
    const authList = (router.currentRoute.value.meta.authList as Array<{ auth_mark: string }>) || []
    return authList.some((item) => item.auth_mark === permission)
  }

  const hasCustomerAccess = (customerId: number, operation: string): boolean => {
    // TODO: 实现客户数据权限验证
    // 当前简化实现，后端会进行实际验证
    return true
  }

  const hasPermissionAndAccess = (
    permission: string,
    customerId?: number,
    operation?: string
  ): boolean => {
    // 先验证功能权限
    if (!hasAuth(permission)) {
      return false
    }

    // 再验证数据权限（如果需要）
    if (customerId && operation) {
      return hasCustomerAccess(customerId, operation)
    }

    return true
  }

  return { hasAuth, hasCustomerAccess, hasPermissionAndAccess }
}
```

### 阶段四：后端API接口实施 (3天)

#### 任务4.1：控制器架构重构
**负责人**：后端开发

**创建文件**：`app/crm/controller/CrmCustomerDetailController.php`

**使用Trait组织代码**：
```php
<?php
namespace app\crm\controller;

use app\crm\controller\traits\CustomerContactTrait;
use app\crm\controller\traits\CustomerContractTrait;
use app\crm\controller\traits\CustomerReceivableTrait;
use app\crm\controller\traits\CustomerFollowTrait;

class CrmCustomerDetailController extends BaseController
{
    use CustomerContactTrait;
    use CustomerContractTrait;
    use CustomerReceivableTrait;
    use CustomerFollowTrait;
}
```

#### 任务4.2：实施各模块API
**负责人**：后端开发

**联系人模块** (1天)：
- [ ] `addContact()` - 新增联系人
- [ ] `editContact()` - 编辑联系人
- [ ] `deleteContact()` - 删除联系人
- [ ] `getContactList()` - 联系人列表

**合同模块** (1天)：
- [ ] `addContract()` - 新增合同
- [ ] `editContract()` - 编辑合同
- [ ] `deleteContract()` - 删除合同
- [ ] `getContractDetail()` - 合同详情
- [ ] `getContractList()` - 合同列表
- [ ] `submitApproval()` - 提交审批

**回款模块** (0.5天)：
- [ ] `addReceivable()` - 新增回款
- [ ] `editReceivable()` - 编辑回款
- [ ] `deleteReceivable()` - 删除回款
- [ ] `getReceivableDetail()` - 回款详情
- [ ] `getReceivableList()` - 回款列表
- [ ] `submitReceivableApproval()` - 提交审批

**跟进记录模块** (0.5天)：
- [ ] `addFollow()` - 新增跟进
- [ ] `editFollow()` - 编辑跟进
- [ ] `deleteFollow()` - 删除跟进
- [ ] `getFollowDetail()` - 跟进详情

### 阶段五：前端组件权限集成 (2天)

#### 任务5.1：主组件权限控制
**负责人**：前端开发

**修改文件**：`frontend/src/components/custom/CustomerDetailDrawer/index.vue`

**实施内容**：
1. [ ] 导入权限验证函数
2. [ ] 添加头部操作按钮权限控制
3. [ ] 扩展事件处理支持22个操作
4. [ ] 实现权限验证逻辑

#### 任务5.2：面板组件权限集成
**负责人**：前端开发

**联系人面板**：
- [ ] 添加权限指令到所有操作按钮
- [ ] 替换模拟数据为真实API调用
- [ ] 实现错误处理和加载状态

**合同面板**：
- [ ] 添加权限指令到所有操作按钮
- [ ] 实现更多操作下拉菜单权限控制
- [ ] 对接真实API接口

**跟进面板**：
- [ ] 添加权限指令到所有操作按钮
- [ ] 实现跟进记录CRUD操作
- [ ] 对接真实API接口

### 阶段六：测试与优化 (1天)

#### 任务6.1：功能测试
**负责人**：前端+后端开发

1. [ ] 权限验证测试
2. [ ] API接口测试
3. [ ] 用户交互测试
4. [ ] 错误处理测试

#### 任务6.2：性能优化
**负责人**：前端开发

1. [ ] 权限验证性能优化
2. [ ] API调用优化
3. [ ] 加载状态优化
4. [ ] 用户体验优化

## ⚠️ 实施前确认事项

### 需要确认的问题

1. **路由配置修复**：
   - 是否可以直接修改 `route/Crm.php` 文件？
   - 修改后是否需要重启服务？

2. **权限中间件集成**：
   - 现有的权限中间件是如何工作的？
   - 新的数据权限中间件如何与现有系统集成？

3. **API路径统一**：
   - 是否统一使用 `/crm/crm_customer/` 路径？
   - 还是保持两套路径系统？

4. **数据库权限配置**：
   - 测试环境的数据库连接信息？
   - 是否需要在生产环境同步执行？

5. **前端权限指令**：
   - 现有的 `v-auth` 指令是如何实现的？
   - 是否需要扩展还是创建新的指令？

### 技术风险评估

1. **路由修改风险**：可能影响现有功能
2. **权限系统集成风险**：可能与现有权限冲突
3. **API接口变更风险**：可能影响其他模块

## 📋 实施检查清单

### 阶段一完成标准
- [ ] 路由配置错误已修复
- [ ] 基础API接口正常工作
- [ ] 无404错误

### 阶段二完成标准
- [ ] 22个权限已添加到数据库
- [ ] 权限验证服务正常工作
- [ ] 权限中间件集成成功

### 阶段三完成标准
- [ ] 前端API接口文件创建完成
- [ ] 权限验证函数实现完成
- [ ] TypeScript类型定义完整

### 阶段四完成标准
- [ ] 后端22个API接口实现完成
- [ ] 权限验证集成到所有接口
- [ ] API测试通过

### 阶段五完成标准
- [ ] 所有面板组件权限控制完成
- [ ] 模拟数据替换为真实API
- [ ] 用户交互正常

### 阶段六完成标准
- [ ] 所有功能测试通过
- [ ] 性能满足要求
- [ ] 用户体验良好

## 🚀 开始实施

**请确认以上问题后，我将开始具体的代码实施工作。建议按阶段逐步进行，每个阶段完成后进行验证再继续下一阶段。**

---

**文档版本**：v1.0  
**创建时间**：2025-01-14  
**预计完成时间**：7个工作日  
**风险等级**：中等
