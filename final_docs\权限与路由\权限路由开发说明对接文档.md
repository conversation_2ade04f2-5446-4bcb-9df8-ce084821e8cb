# 权限路由开发说明对接文档

## 📋 文档概述

本文档为团队开发提供权限系统的完整对接指南，包括前后端权限控制、路由配置、数据库设计等全方位的开发规范和最佳实践。

**文档版本**: v2.0
**更新日期**: 2025-01-31
**适用系统**: CRM管理系统
**技术栈**: ThinkPHP 8 + Vue 3 + TypeScript

**v2.0 更新内容**:
- ✅ 控制器命名规范统一（Controller后缀）
- ✅ 权限解析逻辑优化
- ✅ 路由文件组织规范
- ✅ 中间件配置标准化

---

## 🏗️ 权限系统架构

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端权限控制   │    │   后端权限验证   │    │   数据库权限配置 │
│                │    │                │    │                │
│ • v-auth指令    │◄──►│ • 权限中间件    │◄──►│ • system_menu   │
│ • useAuth组合   │    │ • 路由保护      │    │ • 权限标识      │
│ • 按钮控制      │    │ • API验证       │    │ • 角色分配      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 权限控制层级

1. **路由级权限**: 控制页面访问
2. **功能级权限**: 控制按钮显示
3. **数据级权限**: 控制数据访问范围
4. **字段级权限**: 控制字段显示/编辑

---

## 🗄️ 数据库设计

### system_menu 表结构

```sql
CREATE TABLE `system_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `title` varchar(50) NOT NULL COMMENT '菜单标题',
  `name` varchar(100) NOT NULL COMMENT '权限标识',
  `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '类型:0=目录,1=菜单,2=按钮',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `external` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否外链',
  `keep_alive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缓存',
  `visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  `remark` text COMMENT '备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_name` (`name`),
  KEY `idx_status_deleted` (`status`, `deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统菜单权限表';
```

### 权限标识规范

```
格式: 模块:子模块:操作
示例:
- crm:crm_product:add        # CRM产品新增
- crm:crm_product:edit       # CRM产品编辑
- crm:crm_product:delete     # CRM产品删除
- crm:crm_product:detail     # CRM产品详情
- system:permission:role:add # 系统角色新增
```

---

## 🔧 后端开发指南

### 1. 控制器命名规范（v2.0 统一规范）

#### 控制器文件命名
```php
// ✅ 正确命名格式（必须包含Controller后缀）
app/{模块}/controller/{功能}Controller.php

// 示例
app/crm/controller/CrmCustomerMyController.php
app/system/controller/AuthController.php
app/hr/controller/HrMonthlyStatsController.php

// 子目录控制器
app/system/controller/permission/AdminController.php
app/system/controller/log/LoginController.php
app/system/controller/tenant/TenantConfigController.php
```

#### 控制器类名规范
```php
// ✅ 正确类名（必须包含Controller后缀）
class CrmCustomerMyController extends BaseController
class AuthController extends BaseController
class AdminController extends BaseController
```

#### 路由引用格式
```php
// ✅ 正确路由引用
Route::get('index', 'app\crm\controller\CrmCustomerMyController@index');
Route::post('login', 'app\system\controller\AuthController@login');
Route::get('admin/list', 'app\system\controller\permission\AdminController@index');
```

#### 权限标识解析
控制器命名规范化后，权限解析逻辑已优化：
```php
// 解析示例
app\crm\controller\CrmCustomerMyController@index → crm:crmcustomermy:index
app\system\controller\permission\AdminController@index → system:permission:admin:index
app\system\controller\log\LoginController@index → system:log:login:index
```

### 2. 权限中间件配置

#### 启用权限中间件

```php
// route/crm_product.php
use app\common\middleware\PermissionMiddleware;

Route::group('crm_product', function () {
    Route::get('list', 'list');
    Route::post('add', 'add');
    Route::put('edit', 'edit');
    Route::delete('delete', 'delete');
})->middleware(PermissionMiddleware::class);
```

#### 中间件配置规范（v2.0 标准化）

根据接口类型选择合适的中间件配置：

```php
// 1. 公开接口（无中间件）
Route::group('api/public', function () {
    Route::get('captcha', 'getCaptcha');
    Route::get('config', 'getConfig');
});

// 2. 认证接口（特殊中间件）
Route::group('api/auth', function () {
    Route::post('login', 'login');
    Route::post('register', 'register');
})->middleware([CheckLoginAttempts::class]);

// 3. 公共接口（仅Token认证）
Route::group('api/common', function () {
    Route::get('options', 'getOptions');
    Route::get('dict', 'getDictData');
})->middleware([TokenAuthMiddleware::class]);

// 4. 业务接口（Token + 权限验证）
Route::group('api/business', function () {
    Route::get('crm/customer/list', 'list');
    Route::post('crm/customer/add', 'add');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);

// 5. 管理接口（Token + 权限 + 操作日志）
Route::group('api/admin', function () {
    Route::get('system/admin/list', 'list');
    Route::post('system/role/add', 'add');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class,
    OperationLogMiddleware::class
]);
```

### 3. 路由文件组织规范（v2.0 优化方案）

#### 当前问题
- 路由文件过多（38个文件）
- CRM模块分散（22个独立文件）
- 中间件配置重复

#### 优化方案
```
route/
├── modules/
│   ├── crm.php          # 合并所有CRM路由（22→1）
│   ├── project.php      # 合并所有项目路由（5→1）
│   ├── system.php       # 系统管理路由
│   ├── workflow.php     # 工作流路由
│   ├── notice.php       # 通知路由
│   ├── hr.php           # 人事路由
│   └── daily.php        # 每日报价路由
├── common.php           # 公共接口
├── auth.php             # 认证接口
└── public.php           # 公开接口
```

#### 模块路由文件示例
```php
// route/modules/crm.php
<?php
use think\facade\Route;
use app\common\middleware\TokenAuthMiddleware;
use app\common\middleware\PermissionMiddleware;

$nameSpace = 'app\crm\controller';

// CRM客户管理
Route::group('crm/customer', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmCustomerMyController@index');
    Route::post('add', $nameSpace . '\CrmCustomerMyController@add');
    Route::put('edit', $nameSpace . '\CrmCustomerMyController@edit');
    Route::delete('delete', $nameSpace . '\CrmCustomerMyController@delete');
});

// CRM线索管理
Route::group('crm/lead', function () use ($nameSpace) {
    Route::get('index', $nameSpace . '\CrmLeadController@index');
    Route::post('add', $nameSpace . '\CrmLeadController@add');
});

// 统一中间件配置
Route::group('', function () {
    // 包含上述所有路由
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);
```

#### 权限中间件实现

```php
// app/common/middleware/PermissionMiddleware.php
<?php
namespace app\common\middleware;

use think\Request;
use think\Response;

class PermissionMiddleware
{
    public function handle(Request $request, \Closure $next): Response
    {
        // 获取当前路由权限标识
        $permission = $this->getRoutePermission($request);
        
        // 验证用户权限
        if (!$this->checkUserPermission($permission)) {
            return json(['code' => 403, 'message' => '无权限访问']);
        }
        
        return $next($request);
    }
    
    private function getRoutePermission(Request $request): string
    {
        $module = $request->controller();
        $action = $request->action();
        return strtolower("{$module}:{$action}");
    }
    
    private function checkUserPermission(string $permission): bool
    {
        // 实现权限验证逻辑
        return true;
    }
}
```

### 4. 控制器权限验证（v2.0 规范）

```php
// app/crm/controller/CrmProductController.php
<?php
namespace app\crm\controller;

use app\common\controller\BaseController;

class CrmProductController extends BaseController
{
    /**
     * 产品列表
     * 权限: crm:crm_product:list
     */
    public function list()
    {
        // 业务逻辑
    }
    
    /**
     * 新增产品
     * 权限: crm:crm_product:add
     */
    public function add()
    {
        // 业务逻辑
    }
    
    /**
     * 编辑产品
     * 权限: crm:crm_product:edit
     */
    public function edit()
    {
        // 业务逻辑
    }
    
    /**
     * 删除产品
     * 权限: crm:crm_product:delete
     */
    public function delete()
    {
        // 业务逻辑
    }
}
```

### 3. 数据权限控制

```php
// 在模型中实现数据权限
class CrmProduct extends BaseModel
{
    public function scopeDataPermission($query)
    {
        $user = request()->user;
        
        // 根据用户角色控制数据范围
        if ($user->is_super_admin) {
            // 超级管理员看所有数据
            return $query;
        } elseif ($user->is_tenant_super_admin) {
            // 租户管理员看租户数据
            return $query->where('tenant_id', request()->tenant_id);
        } else {
            // 普通用户只看自己的数据
            return $query->where('owner_id', $user->id);
        }
    }
}
```

---

## 🎨 前端开发指南

### 1. 权限组合函数

#### useAuth 通用权限验证

```typescript
// src/composables/useAuth.ts
import { computed } from 'vue'
import { useUserStore } from '@/store/modules/user'

export function useAuth() {
  const userStore = useUserStore()
  
  /**
   * 检查用户是否有指定权限
   * @param permission 权限标识，如 'crm:crm_product:add'
   * @returns boolean
   */
  const hasAuth = (permission: string): boolean => {
    if (!permission) return true
    
    // 超级管理员拥有所有权限
    if (userStore.user?.is_super_admin) return true
    
    // 检查用户权限列表
    return userStore.permissions?.includes(permission) || false
  }
  
  /**
   * 检查用户是否有任一权限
   * @param permissions 权限数组
   * @returns boolean
   */
  const hasAnyAuth = (permissions: string[]): boolean => {
    return permissions.some(permission => hasAuth(permission))
  }
  
  /**
   * 检查用户是否拥有所有权限
   * @param permissions 权限数组
   * @returns boolean
   */
  const hasAllAuth = (permissions: string[]): boolean => {
    return permissions.every(permission => hasAuth(permission))
  }
  
  return {
    hasAuth,
    hasAnyAuth,
    hasAllAuth
  }
}
```

#### useCustomerPermission 业务权限验证

```typescript
// src/composables/useCustomerPermission.ts
import { useAuth } from './useAuth'

export function useCustomerPermission() {
  const { hasAuth } = useAuth()
  
  /**
   * 检查按钮权限
   * @param action 操作类型
   * @param module 模块名称，默认为 'crm_customer_my'
   * @returns boolean
   */
  const hasButtonPermission = (action: string, module: string = 'crm_customer_my'): boolean => {
    return hasAuth(`crm:${module}:${action}`)
  }
  
  /**
   * 检查客户操作权限
   * @param customer 客户数据
   * @param action 操作类型
   * @returns boolean
   */
  const hasCustomerPermission = (customer: any, action: string): boolean => {
    // 基础权限检查
    if (!hasButtonPermission(action)) return false
    
    // 业务逻辑权限检查
    switch (action) {
      case 'edit':
        // 只有客户负责人可以编辑
        return customer.owner_id === getCurrentUserId()
      case 'delete':
        // 只有创建者可以删除
        return customer.creator_id === getCurrentUserId()
      default:
        return true
    }
  }
  
  return {
    hasButtonPermission,
    hasCustomerPermission
  }
}
```

### 2. 权限指令

#### v-auth 指令实现

```typescript
// src/directives/auth.ts
import type { Directive } from 'vue'
import { useAuth } from '@/composables/useAuth'

export const authDirective: Directive = {
  mounted(el: HTMLElement, binding) {
    const { hasAuth } = useAuth()
    const permission = binding.value
    
    if (!hasAuth(permission)) {
      // 移除元素或隐藏
      el.style.display = 'none'
      // 或者完全移除: el.remove()
    }
  },
  
  updated(el: HTMLElement, binding) {
    const { hasAuth } = useAuth()
    const permission = binding.value
    
    if (!hasAuth(permission)) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 注册指令
// src/main.ts
import { authDirective } from '@/directives/auth'

app.directive('auth', authDirective)
```

### 3. 组件权限控制

#### 按钮权限控制

```vue
<!-- 基础用法 -->
<template>
  <div>
    <!-- 使用 v-auth 指令 -->
    <ElButton v-auth="'crm:crm_product:add'" type="primary" @click="handleAdd">
      新增产品
    </ElButton>
    
    <!-- 使用 hasAuth 函数 -->
    <ElButton v-if="hasAuth('crm:crm_product:edit')" type="success" @click="handleEdit">
      编辑产品
    </ElButton>
    
    <!-- 组合权限控制 -->
    <ElButton 
      v-if="hasAuth('crm:crm_product:delete') && canDelete(row)" 
      type="danger" 
      @click="handleDelete"
    >
      删除产品
    </ElButton>
  </div>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'

const { hasAuth } = useAuth()

// 业务逻辑权限判断
const canDelete = (row: any) => {
  return row.status === 0 // 只能删除草稿状态
}
</script>
```

#### 表格操作列权限控制

```vue
<template>
  <ElTableColumn prop="operation" label="操作" fixed="right" width="240">
    <template #default="scope">
      <div class="operation-buttons">
        <!-- 详情按钮 -->
        <ArtButtonTable
          v-auth="'crm:crm_product:detail'"
          text="详情"
          :iconClass="BgColorEnum.SECONDARY"
          @click="showDetail(scope.row.id)"
        />
        
        <!-- 编辑按钮 -->
        <ArtButtonTable
          v-auth="'crm:crm_product:edit'"
          text="编辑"
          :iconClass="BgColorEnum.PRIMARY"
          @click="showFormDialog('edit', scope.row.id)"
        />
        
        <!-- 更多操作下拉菜单 -->
        <ElDropdown @command="(command) => handleMoreAction(command, scope.row)">
          <ArtButtonTable text="更多" type="more" />
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem 
                v-if="hasAuth('crm:crm_product:copy')" 
                command="copy"
              >
                复制
              </ElDropdownItem>
              <ElDropdownItem 
                v-if="hasAuth('crm:crm_product:delete') && scope.row.status === 0" 
                command="delete"
              >
                删除
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
    </template>
  </ElTableColumn>
</template>
```

#### 动态渲染权限控制

```typescript
// 在 formatter 中使用权限控制
const columns = computed(() => [
  {
    prop: 'operation',
    label: '操作',
    formatter: (row: any) => {
      const buttons = []
      
      // 详情按钮
      if (hasAuth('crm:crm_product:detail')) {
        buttons.push(h(ArtButtonTable, {
          text: '详情',
          onClick: () => showDetail(row.id)
        }))
      }
      
      // 编辑按钮
      if (hasAuth('crm:crm_product:edit')) {
        buttons.push(h(ArtButtonTable, {
          text: '编辑',
          onClick: () => showEdit(row.id)
        }))
      }
      
      // 删除按钮 - 带业务逻辑判断
      if (hasAuth('crm:crm_product:delete') && row.status === 0) {
        buttons.push(h(ArtButtonTable, {
          text: '删除',
          type: 'delete',
          onClick: () => handleDelete(row.id)
        }))
      }
      
      return h('div', buttons)
    }
  }
])
```

---

## 📝 开发规范

### 1. 权限标识命名规范

```
模块:子模块:操作

模块命名:
- crm: CRM客户关系管理
- system: 系统管理
- project: 项目管理
- workflow: 工作流
- ims: 进销存管理

操作命名:
- list: 列表查看
- detail: 详情查看
- add: 新增
- edit: 编辑
- delete: 删除
- import: 导入
- export: 导出
- approve: 审批
- reject: 驳回
- submit: 提交
- withdraw: 撤回
- copy: 复制
- move: 移动
- assign: 分配
- claim: 认领
- lock: 锁定
- unlock: 解锁
- void: 作废
- convert: 转化
```

### 2. 前端组件权限规范

```vue
<!-- ✅ 推荐写法 -->
<template>
  <!-- 1. 优先使用 v-auth 指令 -->
  <ElButton v-auth="'crm:crm_product:add'" @click="handleAdd">新增</ElButton>
  
  <!-- 2. 复杂逻辑使用 hasAuth 函数 -->
  <ElButton v-if="hasAuth('crm:crm_product:edit') && canEdit(row)" @click="handleEdit">
    编辑
  </ElButton>
  
  <!-- 3. 业务权限组合使用专用 composable -->
  <ElButton v-if="hasButtonPermission('delete')" @click="handleDelete">删除</ElButton>
</template>

<!-- ❌ 不推荐写法 -->
<template>
  <!-- 硬编码权限判断 -->
  <ElButton v-if="userStore.user.role === 'admin'" @click="handleAdd">新增</ElButton>
  
  <!-- 权限标识拼写错误 -->
  <ElButton v-auth="'crm:product:add'" @click="handleAdd">新增</ElButton>
  
  <!-- 缺少权限控制 -->
  <ElButton @click="handleDelete">删除</ElButton>
</template>
```

### 3. 后端路由权限规范

```php
// ✅ 推荐写法
Route::group('crm_product', function () {
    Route::get('list', 'list');           // crm:crm_product:list
    Route::get('detail/:id', 'detail');   // crm:crm_product:detail
    Route::post('add', 'add');            // crm:crm_product:add
    Route::put('edit/:id', 'edit');       // crm:crm_product:edit
    Route::delete('delete/:id', 'delete'); // crm:crm_product:delete
})->middleware(PermissionMiddleware::class);

// ❌ 不推荐写法
Route::group('crm_product', function () {
    Route::get('list', 'list');
    Route::post('add', 'add');
    Route::put('edit/:id', 'edit');
    Route::delete('delete/:id', 'delete');
}); // 缺少权限中间件
```

---

## 🚀 最佳实践

### 1. 权限粒度控制

```typescript
// 细粒度权限控制示例
export function useProductPermission() {
  const { hasAuth } = useAuth()
  
  // 基础权限
  const canView = computed(() => hasAuth('crm:crm_product:list'))
  const canAdd = computed(() => hasAuth('crm:crm_product:add'))
  const canEdit = computed(() => hasAuth('crm:crm_product:edit'))
  const canDelete = computed(() => hasAuth('crm:crm_product:delete'))
  
  // 业务权限
  const canEditProduct = (product: any) => {
    return canEdit.value && (
      product.status === 0 || // 草稿状态
      product.owner_id === getCurrentUserId() // 负责人
    )
  }
  
  const canDeleteProduct = (product: any) => {
    return canDelete.value && 
           product.status === 0 && // 只能删除草稿
           product.creator_id === getCurrentUserId() // 只能删除自己创建的
  }
  
  return {
    canView,
    canAdd,
    canEdit,
    canDelete,
    canEditProduct,
    canDeleteProduct
  }
}
```

### 2. 权限缓存优化

```typescript
// 权限缓存实现
class PermissionCache {
  private cache = new Map<string, boolean>()
  private cacheTime = new Map<string, number>()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5分钟
  
  has(permission: string): boolean {
    const now = Date.now()
    const cacheTime = this.cacheTime.get(permission)
    
    if (!cacheTime || now - cacheTime > this.CACHE_DURATION) {
      return false
    }
    
    return this.cache.has(permission)
  }
  
  get(permission: string): boolean | undefined {
    if (!this.has(permission)) return undefined
    return this.cache.get(permission)
  }
  
  set(permission: string, value: boolean): void {
    this.cache.set(permission, value)
    this.cacheTime.set(permission, Date.now())
  }
  
  clear(): void {
    this.cache.clear()
    this.cacheTime.clear()
  }
}

export const permissionCache = new PermissionCache()
```

### 3. 错误处理

```typescript
// 权限错误处理
export function useAuthError() {
  const handleAuthError = (error: any) => {
    if (error.code === 403) {
      ElMessage.error('您没有权限执行此操作')
      // 可以跳转到无权限页面
      // router.push('/403')
    } else if (error.code === 401) {
      ElMessage.error('登录已过期，请重新登录')
      // 跳转到登录页
      router.push('/login')
    }
  }
  
  return {
    handleAuthError
  }
}
```

---

## 🔍 调试与测试

### 1. 权限调试工具

```typescript
// 开发环境权限调试
export function usePermissionDebug() {
  const debugPermission = (permission: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔐 权限调试: ${permission}`)
      console.log('用户权限列表:', userStore.permissions)
      console.log('权限检查结果:', hasAuth(permission))
      console.log('用户角色:', userStore.user?.role)
      console.groupEnd()
    }
  }
  
  return {
    debugPermission
  }
}
```

### 2. 权限测试用例

```typescript
// 权限测试示例
describe('权限系统测试', () => {
  test('hasAuth 函数测试', () => {
    const { hasAuth } = useAuth()
    
    // 模拟用户权限
    userStore.permissions = ['crm:crm_product:add', 'crm:crm_product:edit']
    
    expect(hasAuth('crm:crm_product:add')).toBe(true)
    expect(hasAuth('crm:crm_product:delete')).toBe(false)
  })
  
  test('超级管理员权限测试', () => {
    const { hasAuth } = useAuth()
    
    // 模拟超级管理员
    userStore.user = { is_super_admin: true }
    
    expect(hasAuth('any:permission:here')).toBe(true)
  })
})
```

---

## 📚 常见问题

### Q1: 权限不生效怎么办？

**A**: 检查以下几点：
1. 确认权限中间件已启用
2. 检查权限标识是否正确
3. 验证用户是否有对应权限
4. 检查前端权限缓存是否过期

### Q2: 如何处理动态权限？

**A**: 使用计算属性和响应式数据：
```typescript
const dynamicPermission = computed(() => {
  return `crm:${currentModule.value}:${currentAction.value}`
})

const canOperate = computed(() => hasAuth(dynamicPermission.value))
```

### Q3: 如何实现数据权限？

**A**: 在后端模型中实现作用域：
```php
public function scopeDataPermission($query)
{
    // 根据用户角色和数据权限规则过滤数据
    return $query->where('owner_id', getCurrentUserId());
}
```

---

## 🛠️ 开发工具与脚本

### 1. 权限生成脚本

```php
// scripts/generate_permissions.php
<?php
/**
 * 权限自动生成脚本
 */

class PermissionGenerator
{
    private $modules = [
        'crm' => [
            'crm_product' => ['list', 'detail', 'add', 'edit', 'delete', 'import', 'export'],
            'crm_customer' => ['list', 'detail', 'add', 'edit', 'delete', 'assign', 'claim'],
            'crm_contract' => ['list', 'detail', 'add', 'edit', 'delete', 'approve', 'void']
        ],
        'system' => [
            'permission' => [
                'role' => ['add', 'edit', 'delete'],
                'department' => ['add', 'edit', 'delete'],
                'admin' => ['add', 'edit', 'delete']
            ]
        ]
    ];

    public function generate()
    {
        foreach ($this->modules as $module => $subModules) {
            foreach ($subModules as $subModule => $actions) {
                if (is_array($actions)) {
                    foreach ($actions as $action) {
                        $this->createPermission($module, $subModule, $action);
                    }
                } else {
                    // 处理嵌套结构
                    foreach ($actions as $nestedModule => $nestedActions) {
                        foreach ($nestedActions as $action) {
                            $this->createPermission($module, "{$subModule}:{$nestedModule}", $action);
                        }
                    }
                }
            }
        }
    }

    private function createPermission($module, $subModule, $action)
    {
        $name = "{$module}:{$subModule}:{$action}";
        $title = $this->getActionTitle($action);

        echo "INSERT INTO system_menu (parent_id, title, name, type, status) VALUES ";
        echo "(0, '{$title}', '{$name}', 2, 1);\n";
    }

    private function getActionTitle($action)
    {
        $titles = [
            'list' => '列表',
            'detail' => '详情',
            'add' => '新增',
            'edit' => '编辑',
            'delete' => '删除',
            'import' => '导入',
            'export' => '导出',
            'approve' => '审批',
            'void' => '作废',
            'assign' => '分配',
            'claim' => '认领'
        ];

        return $titles[$action] ?? $action;
    }
}

$generator = new PermissionGenerator();
$generator->generate();
```

### 2. 前端权限检查工具

```typescript
// src/utils/permissionChecker.ts
export class PermissionChecker {
  private static instance: PermissionChecker
  private permissions: Set<string> = new Set()

  static getInstance(): PermissionChecker {
    if (!this.instance) {
      this.instance = new PermissionChecker()
    }
    return this.instance
  }

  /**
   * 记录使用的权限
   */
  recordPermission(permission: string): void {
    this.permissions.add(permission)

    if (process.env.NODE_ENV === 'development') {
      console.log(`🔐 权限使用记录: ${permission}`)
    }
  }

  /**
   * 获取所有使用的权限
   */
  getUsedPermissions(): string[] {
    return Array.from(this.permissions)
  }

  /**
   * 检查权限格式是否正确
   */
  validatePermissionFormat(permission: string): boolean {
    const pattern = /^[a-z]+:[a-z_]+:[a-z_]+$/
    return pattern.test(permission)
  }

  /**
   * 生成权限使用报告
   */
  generateReport(): void {
    if (process.env.NODE_ENV === 'development') {
      console.group('📊 权限使用报告')
      console.log('总权限数:', this.permissions.size)
      console.log('权限列表:', this.getUsedPermissions())

      // 检查格式错误
      const invalidPermissions = this.getUsedPermissions().filter(
        p => !this.validatePermissionFormat(p)
      )

      if (invalidPermissions.length > 0) {
        console.warn('⚠️ 格式错误的权限:', invalidPermissions)
      }

      console.groupEnd()
    }
  }
}
```

---

## 📋 权限配置清单

### 当前系统权限统计

| 模块 | 子模块 | 权限数量 | 状态 |
|------|--------|----------|------|
| **CRM** | 产品管理 | 12个 | ✅ 完成 |
| **CRM** | 客户管理 | 15个 | ✅ 完成 |
| **CRM** | 线索管理 | 10个 | ✅ 完成 |
| **CRM** | 合同管理 | 8个 | ✅ 完成 |
| **CRM** | 回款管理 | 8个 | ✅ 完成 |
| **系统管理** | 角色管理 | 6个 | ✅ 完成 |
| **系统管理** | 部门管理 | 6个 | ✅ 完成 |
| **系统管理** | 岗位管理 | 6个 | ✅ 完成 |
| **IMS** | 供应商管理 | 6个 | ✅ 完成 |
| **总计** | - | **307个** | ✅ 完成 |

### 权限配置模板

```sql
-- CRM模块权限配置模板
INSERT INTO `system_menu` (`parent_id`, `title`, `name`, `type`, `status`) VALUES
-- 产品管理
(0, 'CRM产品管理', 'crm:crm_product:index', 1, 1),
(LAST_INSERT_ID(), '产品列表', 'crm:crm_product:list', 2, 1),
(LAST_INSERT_ID()-1, '产品详情', 'crm:crm_product:detail', 2, 1),
(LAST_INSERT_ID()-2, '新增产品', 'crm:crm_product:add', 2, 1),
(LAST_INSERT_ID()-3, '编辑产品', 'crm:crm_product:edit', 2, 1),
(LAST_INSERT_ID()-4, '删除产品', 'crm:crm_product:delete', 2, 1),
(LAST_INSERT_ID()-5, '导入产品', 'crm:crm_product:import', 2, 1),
(LAST_INSERT_ID()-6, '导出产品', 'crm:crm_product:export', 2, 1);
```

---

## 🔄 版本更新记录

### v1.0 (2025-01-25)
- ✅ 完成权限系统架构设计
- ✅ 实现前后端权限控制
- ✅ 完成14个核心模块权限适配
- ✅ 建立权限开发规范
- ✅ 提供完整的开发工具

### 后续版本规划

#### v1.1 (计划)
- 🔄 权限缓存优化
- 🔄 权限审计日志
- 🔄 动态权限配置
- 🔄 权限可视化管理

#### v1.2 (计划)
- 🔄 字段级权限控制
- 🔄 数据权限细化
- 🔄 权限性能优化
- 🔄 权限测试自动化

---

## 📞 技术支持

### 联系方式
- **技术负责人**: 开发团队
- **文档维护**: 系统架构师
- **问题反馈**: 通过项目管理系统提交
- **最后更新**: 2025-01-25

### 相关资源
- **项目仓库**: [Git仓库地址]
- **API文档**: [API文档地址]
- **测试环境**: http://localhost:3006
- **生产环境**: [生产环境地址]

---

**© 2025 CRM管理系统开发团队 | 权限系统开发指南 v1.0**
