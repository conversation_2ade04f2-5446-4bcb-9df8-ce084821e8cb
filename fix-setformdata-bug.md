# 修复FormManager setFormData方法缺失的Bug

## 🚨 问题描述

在编辑申请时出现以下错误：
```
FormManager收到编辑数据: {id: 127, formData: {…}}
form-manager.vue:226 表单组件未挂载或没有setFormData方法
hr_leave-form.vue:529 hr_leave-form showForm called with id: undefined
```

## 🔍 问题分析

### 错误原因
1. **缺少方法**：`hr_leave-form.vue` 中没有 `setFormData` 方法
2. **方法调用错误**：`form-manager.vue` 尝试调用不存在的 `setFormData` 方法
3. **数据传递问题**：即使有 `showForm` 方法，传递的ID也是 `undefined`

### 调用链分析
```
Application.vue (editApplication)
  ↓ 调用 setEditData({id: 127, formData: {...}})
FormManager.vue (setEditData)
  ↓ 尝试调用 setFormData(formDataWithId)
hr_leave-form.vue
  ✗ 没有 setFormData 方法
```

## ✅ 修复方案

### 1. 添加 setFormData 方法
在 `hr_leave-form.vue` 中添加缺失的 `setFormData` 方法：

```typescript
/**
 * 设置表单数据（供FormManager调用）
 */
const setFormData = (data: any) => {
  console.log('hr_leave-form setFormData called with data:', data)
  if (data) {
    // 重置表单数据
    resetForm()
    // 设置新数据
    Object.assign(formData, data)
    // 显示对话框
    dialogVisible.value = true
    console.log('hr_leave-form 表单数据已设置，ID:', formData.id)
  }
}
```

### 2. 暴露 setFormData 方法
在 `defineExpose` 中添加 `setFormData` 方法：

```typescript
// 暴露方法供FormManager调用
defineExpose({
  showForm,
  resetForm,
  loadFormData,
  updateFormData,
  setFormData  // 新增
})
```

### 3. 优化 watchEffect 监听
在 `form-manager.vue` 中优化延迟数据传递的监听逻辑：

```typescript
// 如果组件未挂载，则监听变化
const stopWatcher = watchEffect(() => {
  if (
    formComponentRef.value &&
    formComponentRef.value.setFormData &&
    preloadedEditData.value
  ) {
    console.log('延迟传递表单数据到业务组件')

    // 同样确保带有ID
    const formDataWithId = {
      ...preloadedEditData.value.formData,
      id: preloadedEditData.value.id
    }

    formComponentRef.value.setFormData(formDataWithId)
    preloadedEditData.value = null
    
    // 数据传递完成后停止监听
    stopWatcher()
  }
})
```

## 🎯 修复要点

### 1. 方法命名统一
- `setFormData`: 用于设置完整的表单数据对象
- `showForm`: 用于显示表单（可选传入ID）
- `updateFormData`: 用于更新部分表单数据

### 2. 数据处理逻辑
- 重置表单后再设置新数据，避免数据污染
- 确保ID字段正确传递和设置
- 自动显示对话框，提供良好的用户体验

### 3. 内存管理
- 使用 `stopWatcher()` 停止不需要的监听
- 及时清理 `preloadedEditData.value`

## 🧪 测试验证

### 测试步骤
1. 访问工作流申请页面
2. 找到一个草稿状态的申请
3. 点击"更多" → "编辑"
4. 观察控制台日志和表单显示

### 预期结果
- ✅ 不再出现 "表单组件未挂载或没有setFormData方法" 错误
- ✅ 表单正确显示并填充数据
- ✅ 表单ID正确设置（不再是 undefined）
- ✅ 控制台显示正确的调试信息

## 📋 修复清单

- ✅ 添加 `setFormData` 方法到 `hr_leave-form.vue`
- ✅ 在 `defineExpose` 中暴露 `setFormData` 方法
- ✅ 优化 `form-manager.vue` 中的 watchEffect 监听
- ✅ 添加调试日志用于验证修复效果
- ✅ 确保数据重置和设置的正确顺序

## 🔄 相关文件修改

1. `frontend/src/views/workflow/components/business-forms/hr_leave-form.vue`
   - 添加 `setFormData` 方法
   - 在 `defineExpose` 中暴露该方法

2. `frontend/src/views/workflow/components/form-manager.vue`
   - 优化 watchEffect 监听逻辑
   - 添加 stopWatcher 防止内存泄漏

修复完成后，编辑申请功能应该能正常工作，表单能正确显示和填充数据。
