<template>
  <el-dialog
    v-model="visible"
    title="添加评论"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="comment-dialog-content">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="0">
        <el-form-item prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="6"
            placeholder="添加评论..."
            maxlength="1000"
            show-word-limit
            resize="none"
          />
        </el-form-item>
        
        <el-form-item prop="attachments">
          <el-upload
            v-model:file-list="fileList"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            multiple
            :limit="5"
            class="upload-demo"
          >
            <el-button size="small" link>
              <el-icon><Paperclip /></el-icon>
              添加附件
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="default">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="loading"
          size="default"
        >
          发布评论
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElUpload, ElIcon } from 'element-plus'
import { Paperclip } from '@element-plus/icons-vue'
import { ProjectApi } from '@/api/project/projectApi'

// Props
interface Props {
  modelValue: boolean
  taskId: number | string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskId: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': []
}>()

// 响应式数据
const visible = ref(props.modelValue)
const loading = ref(false)
const formRef = ref()
const fileList = ref([])

// 表单数据
const formData = reactive({
  content: '',
  attachments: []
})

// 表单验证规则
const rules = {
  content: [
    { required: true, message: '请输入评论内容', trigger: 'blur' },
    { min: 1, max: 1000, message: '评论内容长度在 1 到 1000 个字符', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  formData.content = ''
  formData.attachments = []
  fileList.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 文件上传处理
const handleFileChange = (file: any) => {
  console.log('文件变化:', file)
}

const handleFileRemove = (file: any) => {
  console.log('移除文件:', file)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const submitData = {
      task_id: props.taskId,
      content: formData.content,
      attachments: formData.attachments
    }
    
    await ProjectApi.addTaskComment(submitData)
    
    ElMessage.success('评论发布成功')
    emit('success')
    visible.value = false
    
  } catch (error) {
    console.error('发布评论失败:', error)
    ElMessage.error('发布评论失败')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style lang="scss" scoped>
.comment-dialog-content {
  padding: 0;
  
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .el-textarea {
      :deep(.el-textarea__inner) {
        font-family: inherit;
        line-height: 1.5;
      }
    }
    
    .upload-demo {
      :deep(.el-upload-list) {
        margin-top: 10px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
