/**
 * 通用组件导出文件
 */

// 部门选择组件
import DepartmentTreeSelect from './DepartmentTreeSelect.vue'

// 部门人员选择组件
import DepartmentPersonSelector from './DepartmentPersonSelector.vue'
import DepartmentPersonForm from './DepartmentPersonForm.vue'
import DepartmentPersonSearch from './DepartmentPersonSearch.vue'

// 组件列表
const components = [
  DepartmentTreeSelect,
  DepartmentPersonSelector,
  DepartmentPersonForm,
  DepartmentPersonSearch
]

// 全局注册函数
export function registerCustomComponents(app: any) {
  components.forEach((component) => {
    // 使用组件的 name 属性或文件名作为组件名
    const componentName = component.name || component.__name
    if (componentName) {
      app.component(componentName, component)
    }
  })
}

// 按需导入
export {
  DepartmentTreeSelect,
  DepartmentPersonSelector,
  DepartmentPersonForm,
  DepartmentPersonSearch
}

// 默认导出
export default {
  install(app: any) {
    registerCustomComponents(app)
  }
}

// 类型导出
export type { PersonItem, DepartmentItem } from './types'
