<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑项目' : '新建项目'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" @submit.prevent>
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入项目名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="项目描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入项目描述"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="项目状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择项目状态" style="width: 100%">
              <el-option label="进行中" :value="1" />
              <el-option label="已完成" :value="2" />
              <el-option label="已暂停" :value="3" />
              <el-option label="已取消" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
              <el-option label="紧急" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="start_date">
            <el-date-picker
              v-model="formData.start_date"
              type="date"
              placeholder="选择开始时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止时间" prop="end_date">
            <el-date-picker
              v-model="formData.end_date"
              type="date"
              placeholder="选择截止时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="项目负责人" prop="owner">
        <DepartmentPersonForm
          v-model="formData.owner"
          :multiple="false"
          placeholder="请选择项目负责人"
          :department-api="getDepartmentList"
          :user-api="getUserList"
        />
      </el-form-item>

      <el-form-item label="预算金额" prop="budget">
        <el-input-number
          v-model="formData.budget"
          placeholder="请输入预算金额"
          style="width: 100%"
          :min="0"
          :precision="2"
          controls-position="right"
        />
      </el-form-item>

      <!--      <el-form-item label="项目标签" prop="tags">
              <el-select
                v-model="formData.tags"
                placeholder="请选择项目标签"
                style="width: 100%"
                multiple
                filterable
                allow-create
              >
                <el-option v-for="tag in tagOptions" :key="tag" :label="tag" :value="tag" />
              </el-select>
            </el-form-item>-->

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          placeholder="请输入备注信息"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch, nextTick } from 'vue'
  import { ElMessage, FormInstance, FormRules } from 'element-plus'
  import { ProjectApi } from '@/api/project/projectApi'
  import { ApiStatus } from '@/utils/http/status'
  import DepartmentPersonForm from '@/components/custom/DepartmentPersonForm.vue'
  import { WorkflowApi } from '@/components/custom/workflow/api/workflowApi'

  // Props
  interface Props {
    visible: boolean
    projectData?: any
  }

  const props = withDefaults(defineProps<Props>(), {
    projectData: null
  })

  // Emits
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  // const tagOptions = ref(['前端开发', '后端开发', '移动端', 'UI设计', '测试', '运维'])

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const isEdit = computed(() => !!props.projectData?.id)

  // 表单数据
  const formData = reactive({
    id: null,
    name: '',
    description: '',
    status: 1,
    priority: 2,
    start_date: '',
    end_date: '',
    owner: null, // { id: number } | null - 用于DepartmentPersonForm
    owner_id: null, // number | null - 用于后端提交
    budget: null,
    // tags: [],
    remark: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入项目名称', trigger: 'blur' },
      { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    status: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
    start_date: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
    end_date: [{ required: true, message: '请选择截止时间', trigger: 'change' }],
    owner: [{ required: true, message: '请选择项目负责人', trigger: 'change' }]
  }

  // 方法
  const resetForm = () => {
    Object.assign(formData, {
      id: null,
      name: '',
      description: '',
      status: 1,
      priority: 2,
      start_date: '',
      end_date: '',
      owner: null, // 重置owner字段
      owner_id: null,
      budget: null,
      // tags: [],
      remark: ''
    })
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 监听项目数据变化
  watch(
    () => props.projectData,
    (newData) => {
      if (newData) {
        Object.assign(formData, {
          id: newData.id,
          name: newData.name || '',
          description: newData.description || '',
          status: newData.status || 1,
          priority: newData.priority || 2,
          start_date: newData.start_date || '',
          end_date: newData.end_date || '',
          owner: newData.owner_id ? { id: newData.owner_id } : null, // 转换为DepartmentPersonForm需要的格式
          owner_id: newData.owner_id || null, // 保留原始ID用于后端提交
          budget: newData.budget || null,
          // tags: newData.tags || [],
          remark: newData.remark || ''
        })
      } else {
        resetForm()
      }
    },
    { immediate: true }
  )

  // 监听owner变化，同步到owner_id
  watch(
    () => formData.owner,
    (newOwner) => {
      console.log('Owner changed:', newOwner)
      formData.owner_id = newOwner?.id || null
      console.log('Owner ID set to:', formData.owner_id)
    },
    { deep: true }
  )

  // API方法 - 用于DepartmentPersonForm组件
  const getDepartmentList = async () => {
    const res = await WorkflowApi.getDepartmentList()
    if (res.code === 1 && res.data) {
      return res.data
    }
    throw new Error(res.message || '获取部门列表失败')
  }

  const getUserList = async (params?: any) => {
    const res = await WorkflowApi.getUserList(params)
    if (res.code === 1 && res.data) {
      return res.data
    }
    throw new Error(res.message || '获取用户列表失败')
  }

  const handleClose = () => {
    dialogVisible.value = false
    resetForm()
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 准备提交数据，排除id和owner字段（只保留owner_id）
      const { id, owner, ...submitData } = formData

      console.log('Form data before submit:', formData)
      console.log('Submit data:', submitData)

      let result
      if (isEdit.value) {
        // 编辑时，id作为路径参数传递
        result = await ProjectApi.update(id, submitData)
      } else {
        // 新增时，不传递id
        result = await ProjectApi.add(submitData)
      }
      if (result.code === ApiStatus.success) {
        emit('success')
        ElMessage.success(isEdit.value ? '项目更新成功' : '项目创建成功')
        handleClose()
      }
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped lang="scss">
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
