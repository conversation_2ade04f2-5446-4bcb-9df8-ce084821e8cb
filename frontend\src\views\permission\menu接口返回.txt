{"code": 1, "message": "获取成功", "data": [{"id": 77, "title": "办公", "name": "office", "path": "/office", "component": "/index/index", "meta": {"title": "办公", "icon": "&#xe62c;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 78, "title": "工作台", "name": "office:workbench", "path": "/workbench", "component": "/dashboard/console", "meta": {"title": "工作台", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 79, "title": "办公审批", "name": "office:workflow", "path": "/workflow", "component": "", "meta": {"title": "办公审批", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 80, "title": "我的审批", "name": "workflow:task", "path": "/task", "component": "/workflow/WorkflowTask", "meta": {"title": "我的审批", "icon": "", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": [{"id": 159, "title": "列表", "auth_mark": "workflow:task:index", "name": "workflow:task:index"}, {"id": 160, "title": "详情", "auth_mark": "workflow:task:detail", "name": "workflow:task:detail"}, {"id": 161, "title": "同意", "auth_mark": "workflow:task:approve", "name": "workflow:task:approve"}, {"id": 162, "title": "拒绝", "auth_mark": "workflow:task:reject", "name": "workflow:task:reject"}, {"id": 163, "title": "转交", "auth_mark": "workflow:task:transfer", "name": "workflow:task:transfer"}, {"id": 164, "title": "终止", "auth_mark": "workflow:task:terminate", "name": "workflow:task:terminate"}]}, "visible": 1, "children": []}, {"id": 81, "title": "我的申请", "name": "workflow:application", "path": "/application", "component": "/workflow/Application", "meta": {"title": "我的申请", "icon": "", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": [{"id": 145, "title": "新增", "auth_mark": "workflow:application:create", "name": "workflow:application:create"}, {"id": 146, "title": "详情", "auth_mark": "workflow:application:detail", "name": "workflow:application:detail"}, {"id": 147, "title": "撤回", "auth_mark": "workflow:application:withdraw", "name": "workflow:application:withdraw"}, {"id": 149, "title": "提交", "auth_mark": "workflow:application:submit", "name": "workflow:application:submit"}, {"id": 151, "title": "编辑", "auth_mark": "workflow:application:edit", "name": "workflow:application:edit"}, {"id": 152, "title": "列表", "auth_mark": "workflow:application:index", "name": "workflow:application:index"}, {"id": 165, "title": "催办", "auth_mark": "workflow:application:urge", "name": "workflow:application:urge"}]}, "visible": 1, "children": []}, {"id": 156, "title": "抄送我的", "name": "workflow:cc", "path": "/cc", "component": "/workflow/WorkflowCc", "meta": {"title": "抄送我的", "icon": "", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": [{"id": 157, "title": "列表", "auth_mark": "workflow:cc:index", "name": "workflow:cc:index"}, {"id": 158, "title": "详情", "auth_mark": "workflow:cc:detail", "name": "workflow:cc:detail"}]}, "visible": 1, "children": []}]}, {"id": 101, "title": "考勤", "name": "office:attendance", "path": "/attendance", "component": "", "meta": {"title": "考勤", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 102, "title": "考勤设置", "name": "user:attendance:attendance_config", "path": "/attendance_config", "component": "/attendance_config", "meta": {"title": "考勤设置", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 103, "title": "考勤统计", "name": "system:user:attendance_data", "path": "/attendance_data", "component": "", "meta": {"title": "考勤统计", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 104, "title": "每日统计", "name": "system:user:attendance_data:daily", "path": "/daily", "component": "/user/attendance/attendance_data/daily", "meta": {"title": "每日统计", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 105, "title": "月度统计", "name": "system:user:attendance:attendance_data:monthly", "path": "/monthly", "component": "/attendance/attendance_data/monthly", "meta": {"title": "月度统计", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 106, "title": "打卡记录", "name": "system:user:attendance:attendance_data:clockIn", "path": "/clockIn", "component": "/attendance/attendance_data/clock_in", "meta": {"title": "打卡记录", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}]}]}, {"id": 2581, "title": "工作汇报", "name": "crm:work_report/index", "path": "/crm_work_report", "component": "/crm/crm_work_report/list", "meta": {"title": "工作汇报", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}]}, {"id": 2582, "title": "文章管理", "name": "article", "path": "/article", "component": "/index/index", "meta": {"title": "文章管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 166, "title": "文章分类", "name": "system:article_category:index", "path": "/article_cat", "component": "/system/article_category/list", "meta": {"title": "文章分类", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 727, "title": "获取选项", "auth_mark": "system:article_category:options", "name": "system:article_category:options"}, {"id": 726, "title": "更新字段", "auth_mark": "system:article_category:updateField", "name": "system:article_category:updateField"}, {"id": 725, "title": "删除", "auth_mark": "system:article_category:delete", "name": "system:article_category:delete"}, {"id": 724, "title": "编辑", "auth_mark": "system:article_category:edit", "name": "system:article_category:edit"}, {"id": 723, "title": "新增", "auth_mark": "system:article_category:add", "name": "system:article_category:add"}, {"id": 722, "title": "查看", "auth_mark": "system:article_category:index", "name": "system:article_category:index"}]}, "visible": 1, "children": []}, {"id": 107, "title": "文章列表", "name": "system:article:index", "path": "/article_list", "component": "/system/article/list", "meta": {"title": "文章列表", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 733, "title": "详情", "auth_mark": "system:article:detail", "name": "system:article:detail"}, {"id": 732, "title": "更新字段", "auth_mark": "system:article:updateField", "name": "system:article:updateField"}, {"id": 731, "title": "删除", "auth_mark": "system:article:delete", "name": "system:article:delete"}, {"id": 730, "title": "编辑", "auth_mark": "system:article:edit", "name": "system:article:edit"}, {"id": 729, "title": "新增", "auth_mark": "system:article:add", "name": "system:article:add"}, {"id": 728, "title": "查看", "auth_mark": "system:article:index", "name": "system:article:index"}]}, "visible": 1, "children": []}]}, {"id": 1, "title": "权限管理", "name": "system:permission::index", "path": "/permission", "component": "/index/index", "meta": {"title": "权限管理", "icon": "&#xe86e;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 6, "title": "角色管理", "name": "system:permission:role", "path": "/role", "component": "/permission/Role", "meta": {"title": "角色管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 35, "title": "列表", "auth_mark": "system:permission:role:index", "name": "system:permission:role:index"}, {"id": 36, "title": "详情", "auth_mark": "system:permission:role:detail", "name": "system:permission:role:detail"}, {"id": 19, "title": "新增", "auth_mark": "system:permission:role:add", "name": "system:permission:role:add"}, {"id": 20, "title": "编辑", "auth_mark": "system:permission:role:edit", "name": "system:permission:role:edit"}, {"id": 21, "title": "删除", "auth_mark": "system:permission:role:delete", "name": "system:permission:role:delete"}]}, "visible": 1, "children": []}, {"id": 3, "title": "管理员管理", "name": "system:permission:admin", "path": "/admin", "component": "/permission/Admin", "meta": {"title": "管理员管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 37, "title": "列表", "auth_mark": "system:permission:admin:index", "name": "system:permission:admin:index"}, {"id": 38, "title": "详情", "auth_mark": "system:permission:admin:detail", "name": "system:permission:admin:detail"}, {"id": 16, "title": "新增", "auth_mark": "system:permission:admin:add", "name": "system:permission:admin:add"}, {"id": 17, "title": "编辑", "auth_mark": "system:permission:admin:edit", "name": "system:permission:admin:edit"}, {"id": 18, "title": "删除", "auth_mark": "system:permission:admin:delete", "name": "system:permission:admin:delete"}, {"id": 31, "title": "重置密码", "auth_mark": "system:permission:admin:reset_password", "name": "system:permission:admin:reset_password"}]}, "visible": 1, "children": []}, {"id": 2, "title": "菜单管理", "name": "system:permission:menu", "path": "/menu", "component": "/permission/Menu", "meta": {"title": "菜单管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 32, "title": "列表", "auth_mark": "system:permission:menu:index", "name": "system:permission:menu:index"}, {"id": 33, "title": "详情", "auth_mark": "system:permission:menu:detail", "name": "system:permission:menu:detail"}, {"id": 13, "title": "新增", "auth_mark": "system:permission:menu:add", "name": "system:permission:menu:add"}, {"id": 14, "title": "编辑", "auth_mark": "system:permission:menu:edit", "name": "system:permission:menu:edit"}, {"id": 15, "title": "删除", "auth_mark": "system:permission:menu:delete", "name": "system:permission:menu:delete"}]}, "visible": 1, "children": []}, {"id": 7, "title": "部门管理", "name": "system:permission:department", "path": "/dept", "component": "/permission/Dept", "meta": {"title": "部门管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 39, "title": "列表", "auth_mark": "system:permission:department:index", "name": "system:permission:department:index"}, {"id": 40, "title": "详情", "auth_mark": "system:permission:department:detail", "name": "system:permission:department:detail"}, {"id": 22, "title": "新增", "auth_mark": "system:permission:department:add", "name": "system:permission:department:add"}, {"id": 23, "title": "编辑", "auth_mark": "system:permission:department:edit", "name": "system:permission:department:edit"}, {"id": 24, "title": "删除", "auth_mark": "system:permission:department:delete", "name": "system:permission:department:delete"}]}, "visible": 1, "children": []}, {"id": 8, "title": "岗位管理", "name": "system:permission:post", "path": "/post", "component": "/permission/Post", "meta": {"title": "岗位管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 41, "title": "列表", "auth_mark": "system:permission:post:index", "name": "system:permission:post:index"}, {"id": 42, "title": "详情", "auth_mark": "system:permission:post:detail", "name": "system:permission:post:detail"}, {"id": 25, "title": "新增", "auth_mark": "system:permission:post:add", "name": "system:permission:post:add"}, {"id": 26, "title": "编辑", "auth_mark": "system:permission:post:edit", "name": "system:permission:post:edit"}, {"id": 27, "title": "删除", "auth_mark": "system:permission:post:delete", "name": "system:permission:post:delete"}]}, "visible": 1, "children": []}]}, {"id": 210, "title": "产品管理", "name": "crm:product", "path": "/product", "component": "/index/index", "meta": {"title": "产品管理", "icon": "&#xe83d;", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 212, "title": "产品分类", "name": "crm:crm_product_category:index", "path": "/crm/crm_product_category", "component": "/crm/crm_product_category/list", "meta": {"title": "产品分类", "icon": "&#xe61a;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2126, "title": "导入", "auth_mark": "crm:crm_product_category:import", "name": "crm:crm_product_category:import"}, {"id": 2125, "title": "导出", "auth_mark": "crm:crm_product_category:export", "name": "crm:crm_product_category:export"}, {"id": 2123, "title": "删除", "auth_mark": "crm:crm_product_category:delete", "name": "crm:crm_product_category:delete"}, {"id": 2122, "title": "编辑", "auth_mark": "crm:crm_product_category:edit", "name": "crm:crm_product_category:edit"}, {"id": 2121, "title": "新增", "auth_mark": "crm:crm_product_category:add", "name": "crm:crm_product_category:add"}]}, "visible": 1, "children": []}, {"id": 213, "title": "产品单位", "name": "crm:crm_product_unit:index", "path": "/crm/crm_product_unit", "component": "/crm/crm_product_unit/list", "meta": {"title": "产品单位", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2134, "title": "详情", "auth_mark": "crm:crm_product_unit:detail", "name": "crm:crm_product_unit:detail"}, {"id": 2133, "title": "删除", "auth_mark": "crm:crm_product_unit:delete", "name": "crm:crm_product_unit:delete"}, {"id": 2132, "title": "编辑", "auth_mark": "crm:crm_product_unit:edit", "name": "crm:crm_product_unit:edit"}]}, "visible": 1, "children": []}, {"id": 211, "title": "产品列表", "name": "crm:crm_product:index", "path": "/crm/crm_product", "component": "/crm/crm_product/list", "meta": {"title": "产品列表", "icon": "&#xe64e;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2119, "title": "设置价格", "auth_mark": "crm:crm_product:set_price", "name": "crm:crm_product:set_price"}, {"id": 2118, "title": "下架", "auth_mark": "crm:crm_product:offline", "name": "crm:crm_product:offline"}, {"id": 2117, "title": "上架", "auth_mark": "crm:crm_product:online", "name": "crm:crm_product:online"}, {"id": 2116, "title": "导入", "auth_mark": "crm:crm_product:import", "name": "crm:crm_product:import"}, {"id": 2115, "title": "导出", "auth_mark": "crm:crm_product:export", "name": "crm:crm_product:export"}, {"id": 2114, "title": "详情", "auth_mark": "crm:crm_product:detail", "name": "crm:crm_product:detail"}, {"id": 2113, "title": "删除", "auth_mark": "crm:crm_product:delete", "name": "crm:crm_product:delete"}, {"id": 2112, "title": "编辑", "auth_mark": "crm:crm_product:edit", "name": "crm:crm_product:edit"}, {"id": 2111, "title": "新增", "auth_mark": "crm:crm_product:add", "name": "crm:crm_product:add"}]}, "visible": 1, "children": []}, {"id": 215, "title": "产品规格", "name": "crm:crm_product_spec:index", "path": "/crm/crm_product_spec", "component": "/crm/crm_product_spec/list", "meta": {"title": "产品规格", "icon": "&#xe65f;", "keepAlive": 1, "isHide": true, "isIframe": false, "authList": [{"id": 2367, "title": "新增", "auth_mark": "crm:crm_product_spec:add", "name": "crm:crm_product_spec:add"}, {"id": 2368, "title": "编辑", "auth_mark": "crm:crm_product_spec:edit", "name": "crm:crm_product_spec:edit"}, {"id": 2369, "title": "删除", "auth_mark": "crm:crm_product_spec:delete", "name": "crm:crm_product_spec:delete"}]}, "visible": 0, "children": []}]}, {"id": 189, "title": "CRM管理", "name": "crm:index", "path": "/crm", "component": "/index/index", "meta": {"title": "CRM管理", "icon": "", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 220, "title": "客户管理", "name": "crm:crm_customer", "path": "/crm_customer", "component": "", "meta": {"title": "客户管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 2370, "title": "公海客户", "name": "crm:customer_sea:index", "path": "/crm_sea", "component": "/crm/crm_customer_sea/list", "meta": {"title": "公海客户", "icon": "&#xe70a;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2382, "title": "分配", "auth_mark": "crm:customer_sea:assign", "name": "crm:customer_sea:assign"}, {"id": 2381, "title": "解锁", "auth_mark": "crm:customer_sea:unlock", "name": "crm:customer_sea:unlock"}, {"id": 2380, "title": "锁定", "auth_mark": "crm:customer_sea:lock", "name": "crm:customer_sea:lock"}, {"id": 2378, "title": "详情", "auth_mark": "crm:customer_sea:detail", "name": "crm:customer_sea:detail"}, {"id": 2377, "title": "认领", "auth_mark": "crm:customer_sea:claim", "name": "crm:customer_sea:claim"}, {"id": 2436, "title": "编辑", "auth_mark": "crm:customer_sea:edit", "name": "crm:customer_sea:edit"}, {"id": 2437, "title": "删除", "auth_mark": "crm:customer_sea:delete", "name": "crm:customer_sea:delete"}]}, "visible": 1, "children": []}, {"id": 192, "title": "我的客户", "name": "crm:crm_customer_my:index", "path": "/crm_customer_my", "component": "/crm/crm_customer_my/list", "meta": {"title": "我的客户", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2507, "title": "回收客户", "auth_mark": "crm:crm_customer_my:recycle_customer", "name": "crm:crm_customer_my:recycle_customer"}, {"id": 2506, "title": "共享客户", "auth_mark": "crm:crm_customer_my:share_customer", "name": "crm:crm_customer_my:share_customer"}, {"id": 2505, "title": "转移客户", "auth_mark": "crm:crm_customer_my:transfer_customer", "name": "crm:crm_customer_my:transfer_customer"}, {"id": 2504, "title": "删除跟进", "auth_mark": "crm:crm_customer_my:delete_follow", "name": "crm:crm_customer_my:delete_follow"}, {"id": 2503, "title": "编辑跟进", "auth_mark": "crm:crm_customer_my:edit_follow", "name": "crm:crm_customer_my:edit_follow"}, {"id": 2502, "title": "跟进详情", "auth_mark": "crm:crm_customer_my:follow_detail", "name": "crm:crm_customer_my:follow_detail"}, {"id": 2501, "title": "新增跟进", "auth_mark": "crm:crm_customer_my:add_follow", "name": "crm:crm_customer_my:add_follow"}, {"id": 2500, "title": "回款详情", "auth_mark": "crm:crm_customer_my:receivable_detail", "name": "crm:crm_customer_my:receivable_detail"}, {"id": 2499, "title": "新增回款", "auth_mark": "crm:crm_customer_my:add_receivable_more", "name": "crm:crm_customer_my:add_receivable_more"}, {"id": 2498, "title": "回款列表", "auth_mark": "crm:crm_customer_my:receivable_list", "name": "crm:crm_customer_my:receivable_list"}, {"id": 2497, "title": "提交审批", "auth_mark": "crm:crm_customer_my:submit_receivable_approval", "name": "crm:crm_customer_my:submit_receivable_approval"}, {"id": 2496, "title": "删除回款", "auth_mark": "crm:crm_customer_my:delete_receivable", "name": "crm:crm_customer_my:delete_receivable"}, {"id": 2495, "title": "编辑回款", "auth_mark": "crm:crm_customer_my:edit_receivable", "name": "crm:crm_customer_my:edit_receivable"}, {"id": 2494, "title": "新增回款", "auth_mark": "crm:crm_customer_my:add_receivable", "name": "crm:crm_customer_my:add_receivable"}, {"id": 2493, "title": "删除合同", "auth_mark": "crm:crm_customer_my:delete_contract", "name": "crm:crm_customer_my:delete_contract"}, {"id": 2492, "title": "编辑合同", "auth_mark": "crm:crm_customer_my:edit_contract", "name": "crm:crm_customer_my:edit_contract"}, {"id": 2491, "title": "合同列表", "auth_mark": "crm:crm_customer_my:contract_list", "name": "crm:crm_customer_my:contract_list"}, {"id": 2490, "title": "提交审批", "auth_mark": "crm:crm_customer_my:submit_approval", "name": "crm:crm_customer_my:submit_approval"}, {"id": 2489, "title": "新增合同", "auth_mark": "crm:crm_customer_my:add_contract", "name": "crm:crm_customer_my:add_contract"}, {"id": 2488, "title": "合同详情", "auth_mark": "crm:crm_customer_my:contract_detail", "name": "crm:crm_customer_my:contract_detail"}, {"id": 2487, "title": "删除联系人", "auth_mark": "crm:crm_customer_my:delete_contact", "name": "crm:crm_customer_my:delete_contact"}, {"id": 2486, "title": "编辑联系人", "auth_mark": "crm:crm_customer_my:edit_contact", "name": "crm:crm_customer_my:edit_contact"}, {"id": 2485, "title": "联系人列表", "auth_mark": "crm:crm_customer_my:contact_list", "name": "crm:crm_customer_my:contact_list"}, {"id": 2484, "title": "新增联系人", "auth_mark": "crm:crm_customer_my:add_contact", "name": "crm:crm_customer_my:add_contact"}, {"id": 2432, "title": "共享", "auth_mark": "crm:crm_customer_my:share", "name": "crm:crm_customer_my:share"}, {"id": 2430, "title": "回收", "auth_mark": "crm:crm_customer_my:recycle", "name": "crm:crm_customer_my:recycle"}, {"id": 2426, "title": "转移", "auth_mark": "crm:crm_customer_my:transfer", "name": "crm:crm_customer_my:transfer"}, {"id": 2425, "title": "导出", "auth_mark": "crm:crm_customer_my:export", "name": "crm:crm_customer_my:export"}, {"id": 2424, "title": "导入", "auth_mark": "crm:crm_customer_my:import", "name": "crm:crm_customer_my:import"}, {"id": 2423, "title": "详情", "auth_mark": "crm:crm_customer_my:detail", "name": "crm:crm_customer_my:detail"}, {"id": 2422, "title": "删除", "auth_mark": "crm:crm_customer_my:delete", "name": "crm:crm_customer_my:delete"}, {"id": 2421, "title": "编辑", "auth_mark": "crm:crm_customer_my:edit", "name": "crm:crm_customer_my:edit"}, {"id": 2420, "title": "新增", "auth_mark": "crm:crm_customer_my:add", "name": "crm:crm_customer_my:add"}]}, "visible": 1, "children": []}, {"id": 193, "title": "联系人管理", "name": "crm:crm_contact:index", "path": "/crm_contact", "component": "/crm/crm_contact/list", "meta": {"title": "联系人管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 1955, "title": "导入", "auth_mark": "crm:crm_contact:import", "name": "crm:crm_contact:import"}, {"id": 1954, "title": "导出", "auth_mark": "crm:crm_contact:export", "name": "crm:crm_contact:export"}, {"id": 1953, "title": "详情", "auth_mark": "crm:crm_contact:detail", "name": "crm:crm_contact:detail"}, {"id": 1952, "title": "删除", "auth_mark": "crm:crm_contact:delete", "name": "crm:crm_contact:delete"}, {"id": 1951, "title": "编辑", "auth_mark": "crm:crm_contact:edit", "name": "crm:crm_contact:edit"}, {"id": 1950, "title": "新增", "auth_mark": "crm:crm_contact:add", "name": "crm:crm_contact:add"}]}, "visible": 1, "children": []}, {"id": 2371, "title": "跟进记录", "name": "crm:follow:index", "path": "/crm_follow", "component": "/crm/crm_follow_record/list", "meta": {"title": "跟进记录", "icon": "&#xe706;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2386, "title": "详情", "auth_mark": "crm:follow_record:detail", "name": "crm:follow_record:detail"}, {"id": 2385, "title": "删除", "auth_mark": "crm:follow_record:delete", "name": "crm:follow_record:delete"}, {"id": 2384, "title": "编辑", "auth_mark": "crm:follow_record:edit", "name": "crm:follow_record:edit"}, {"id": 2383, "title": "新增", "auth_mark": "crm:follow_record:add", "name": "crm:follow_record:add"}]}, "visible": 1, "children": []}]}, {"id": 190, "title": "销售管理", "name": "crm:crm_sales", "path": "/crm_sales", "component": "", "meta": {"title": "销售管理", "icon": "", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 191, "title": "我的线索", "name": "crm:crm_lead:index", "path": "/crm_lead", "component": "/crm/crm_lead/list", "meta": {"title": "我的线索", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 1919, "title": "跟进", "auth_mark": "crm:crm_lead:follow", "name": "crm:crm_lead:follow"}, {"id": 1917, "title": "转化", "auth_mark": "crm:crm_lead:convert", "name": "crm:crm_lead:convert"}, {"id": 1916, "title": "导入", "auth_mark": "crm:crm_lead:import", "name": "crm:crm_lead:import"}, {"id": 1915, "title": "导出", "auth_mark": "crm:crm_lead:export", "name": "crm:crm_lead:export"}, {"id": 1914, "title": "详情", "auth_mark": "crm:crm_lead:detail", "name": "crm:crm_lead:detail"}, {"id": 1913, "title": "删除", "auth_mark": "crm:crm_lead:delete", "name": "crm:crm_lead:delete"}, {"id": 1912, "title": "编辑", "auth_mark": "crm:crm_lead:edit", "name": "crm:crm_lead:edit"}, {"id": 1911, "title": "新增", "auth_mark": "crm:crm_lead:add", "name": "crm:crm_lead:add"}, {"id": 2439, "title": "分配", "auth_mark": "crm:crm_lead:assign", "name": "crm:crm_lead:assign"}]}, "visible": 1, "children": []}, {"id": 194, "title": "商机列表", "name": "crm:crm_business:index", "path": "/crm_business", "component": "/crm/crm_business/list", "meta": {"title": "商机列表", "icon": "", "keepAlive": 1, "isHide": true, "isIframe": false, "authList": [{"id": 1948, "title": "转合同", "auth_mark": "crm:crm_business:convert_contract", "name": "crm:crm_business:convert_contract"}, {"id": 1947, "title": "阶段流转", "auth_mark": "crm:crm_business:transfer", "name": "crm:crm_business:transfer"}, {"id": 1944, "title": "详情", "auth_mark": "crm:crm_business:detail", "name": "crm:crm_business:detail"}, {"id": 1943, "title": "删除", "auth_mark": "crm:crm_business:delete", "name": "crm:crm_business:delete"}, {"id": 1942, "title": "编辑", "auth_mark": "crm:crm_business:edit", "name": "crm:crm_business:edit"}, {"id": 1941, "title": "新增", "auth_mark": "crm:crm_business:add", "name": "crm:crm_business:add"}]}, "visible": 0, "children": []}, {"id": 202, "title": "合同管理", "name": "crm:crm_contract:index", "path": "/crm_contract", "component": "/crm/crm_contract/list", "meta": {"title": "合同管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2029, "title": "作废", "auth_mark": "crm:crm_contract:void", "name": "crm:crm_contract:void"}, {"id": 2028, "title": "提交", "auth_mark": "crm:crm_contract:approve", "name": "crm:crm_contract:approve"}, {"id": 2024, "title": "详情", "auth_mark": "crm:crm_contract:detail", "name": "crm:crm_contract:detail"}, {"id": 2023, "title": "删除", "auth_mark": "crm:crm_contract:delete", "name": "crm:crm_contract:delete"}, {"id": 2022, "title": "编辑", "auth_mark": "crm:crm_contract:edit", "name": "crm:crm_contract:edit"}, {"id": 2021, "title": "新增", "auth_mark": "crm:crm_contract:add", "name": "crm:crm_contract:add"}]}, "visible": 1, "children": []}, {"id": 203, "title": "回款管理", "name": "crm:crm_contract_receivable:index", "path": "/crm_contract_receivable", "component": "/crm/crm_contract_receivable/list", "meta": {"title": "回款管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2038, "title": "作废", "auth_mark": "crm:crm_contract_receivable:void", "name": "crm:crm_contract_receivable:void"}, {"id": 2037, "title": "提交", "auth_mark": "crm:crm_contract_receivable:approve", "name": "crm:crm_contract_receivable:approve"}, {"id": 2034, "title": "详情", "auth_mark": "crm:crm_contract_receivable:detail", "name": "crm:crm_contract_receivable:detail"}, {"id": 2033, "title": "删除", "auth_mark": "crm:crm_contract_receivable:delete", "name": "crm:crm_contract_receivable:delete"}, {"id": 2032, "title": "编辑", "auth_mark": "crm:crm_contract_receivable:edit", "name": "crm:crm_contract_receivable:edit"}, {"id": 2031, "title": "新增", "auth_mark": "crm:crm_contract_receivable:add", "name": "crm:crm_contract_receivable:add"}]}, "visible": 1, "children": []}]}, {"id": 230, "title": "管理配置", "name": "crm:crm_config", "path": "/crm_config", "component": "", "meta": {"title": "管理配置", "icon": "&#xe61a;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 233, "title": "公海规则", "name": "crm:crm_sea_rule:index", "path": "/crm_sea_rule", "component": "/crm/crm_sea_rule/list", "meta": {"title": "公海规则", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2339, "title": "测试规则", "auth_mark": "crm:crm_sea_rule:test", "name": "crm:crm_sea_rule:test"}, {"id": 2338, "title": "禁用", "auth_mark": "crm:crm_sea_rule:disable", "name": "crm:crm_sea_rule:disable"}, {"id": 2337, "title": "启用", "auth_mark": "crm:crm_sea_rule:enable", "name": "crm:crm_sea_rule:enable"}, {"id": 2336, "title": "导入", "auth_mark": "crm:crm_sea_rule:import", "name": "crm:crm_sea_rule:import"}, {"id": 2335, "title": "导出", "auth_mark": "crm:crm_sea_rule:export", "name": "crm:crm_sea_rule:export"}, {"id": 2334, "title": "详情", "auth_mark": "crm:crm_sea_rule:detail", "name": "crm:crm_sea_rule:detail"}, {"id": 2333, "title": "删除", "auth_mark": "crm:crm_sea_rule:delete", "name": "crm:crm_sea_rule:delete"}, {"id": 2332, "title": "编辑", "auth_mark": "crm:crm_sea_rule:edit", "name": "crm:crm_sea_rule:edit"}, {"id": 2331, "title": "新增", "auth_mark": "crm:crm_sea_rule:add", "name": "crm:crm_sea_rule:add"}]}, "visible": 1, "children": []}, {"id": 231, "title": "标签管理", "name": "crm:crm_tag:index", "path": "/crm_tag", "component": "/crm/crm_tag/list", "meta": {"title": "标签管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2313, "title": "删除", "auth_mark": "crm:crm_tag:delete", "name": "crm:crm_tag:delete"}, {"id": 2312, "title": "编辑", "auth_mark": "crm:crm_tag:edit", "name": "crm:crm_tag:edit"}, {"id": 2311, "title": "新增", "auth_mark": "crm:crm_tag:add", "name": "crm:crm_tag:add"}]}, "visible": 1, "children": []}]}]}, {"id": 2440, "title": "项目管理", "name": "project", "path": "/project", "component": "/index/index", "meta": {"title": "项目管理", "icon": "&#xe627;", "keepAlive": 0, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 2441, "title": "项目列表", "name": "project:project:index", "path": "/project_list", "component": "/project/ProjectList", "meta": {"title": "项目列表", "icon": "&#xe61a;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2464, "title": "删除成员", "auth_mark": "project:member:delete", "name": "project:member:delete"}, {"id": 2463, "title": "编辑成员", "auth_mark": "project:member:edit", "name": "project:member:edit"}, {"id": 2462, "title": "成员管理", "auth_mark": "project:member:index", "name": "project:member:index"}, {"id": 2461, "title": "查看成员", "auth_mark": "project:member:detail", "name": "project:member:detail"}, {"id": 2460, "title": "移除成员", "auth_mark": "project:project:removemember", "name": "project:project:removemember"}, {"id": 2459, "title": "添加成员", "auth_mark": "project:project:addmember", "name": "project:project:addmember"}, {"id": 2455, "title": "项目状态", "auth_mark": "project:project:status", "name": "project:project:status"}, {"id": 2454, "title": "更新项目字段", "auth_mark": "project:project:updatefield", "name": "project:project:updatefield"}, {"id": 2453, "title": "批量删除项目", "auth_mark": "project:project:batchdelete", "name": "project:project:batchdelete"}, {"id": 2452, "title": "删除", "auth_mark": "project:project:delete", "name": "project:project:delete"}, {"id": 2451, "title": "编辑", "auth_mark": "project:project:edit", "name": "project:project:edit"}, {"id": 2450, "title": "新增", "auth_mark": "project:project:add", "name": "project:project:add"}]}, "visible": 1, "children": []}, {"id": 2442, "title": "任务管理", "name": "project:task:index", "path": "/project_tasks", "component": "/project/TaskManagement", "meta": {"title": "任务管理", "icon": "&#xe626;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 2483, "title": "删除评论", "auth_mark": "project:taskcomment:delete", "name": "project:taskcomment:delete"}, {"id": 2482, "title": "编辑评论", "auth_mark": "project:taskcomment:edit", "name": "project:taskcomment:edit"}, {"id": 2481, "title": "评论管理", "auth_mark": "project:taskcomment:index", "name": "project:taskcomment:index"}, {"id": 2480, "title": "查看评论", "auth_mark": "project:taskcomment:detail", "name": "project:taskcomment:detail"}, {"id": 2479, "title": "添加评论", "auth_mark": "project:task:addcomment", "name": "project:task:addcomment"}, {"id": 2478, "title": "分配任务", "auth_mark": "project:task:assign", "name": "project:task:assign"}, {"id": 2477, "title": "更新状态", "auth_mark": "project:task:<PERSON><PERSON><PERSON>", "name": "project:task:<PERSON><PERSON><PERSON>"}, {"id": 2476, "title": "我的任务", "auth_mark": "project:task:mytasks", "name": "project:task:mytasks"}, {"id": 2475, "title": "任务状态", "auth_mark": "project:task:status", "name": "project:task:status"}, {"id": 2474, "title": "更新任务字段", "auth_mark": "project:task:updatefield", "name": "project:task:updatefield"}, {"id": 2473, "title": "批量删除任务", "auth_mark": "project:task:batchdelete", "name": "project:task:batchdelete"}, {"id": 2472, "title": "删除任务", "auth_mark": "project:task:delete", "name": "project:task:delete"}, {"id": 2471, "title": "编辑任务", "auth_mark": "project:task:edit", "name": "project:task:edit"}, {"id": 2470, "title": "新增任务", "auth_mark": "project:task:add", "name": "project:task:add"}, {"id": 2469, "title": "查看任务", "auth_mark": "project:task:detail", "name": "project:task:detail"}]}, "visible": 1, "children": []}, {"id": 2449, "title": "项目详情", "name": "project:project:projectdetail", "path": "/detail/:id", "component": "/project/ProjectDetail", "meta": {"title": "项目详情", "icon": "", "keepAlive": 1, "isHide": true, "isIframe": false, "authList": []}, "visible": 0, "children": []}]}, {"id": 93, "title": "进销存", "name": "system:inventory", "path": "/inventory", "component": "/index/index", "meta": {"title": "进销存", "icon": "&#xe667;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 96, "title": "仓库管理", "name": "system:inventory:warehouse", "path": "/warehouse", "component": "/inventory/warehouse", "meta": {"title": "仓库管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 97, "title": "供应商管理", "name": "system:inventory:supplier", "path": "/supplier", "component": "/inventory/supplier", "meta": {"title": "供应商管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 94, "title": "产品分类", "name": "system:inventory:goodCat", "path": "/goodCat", "component": "/inventory/good_cat", "meta": {"title": "产品分类", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 95, "title": "产品管理", "name": "system:inventory:goodList", "path": "/goodList", "component": "/inventory/good_list", "meta": {"title": "产品管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 98, "title": "出库", "name": "system:inventory:outbound", "path": "/outbound", "component": "/inventory/outbound", "meta": {"title": "出库", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 99, "title": "入库", "name": "system:inventory:inbound", "path": "/inbound", "component": "/inventory/inbound", "meta": {"title": "入库", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}, {"id": 100, "title": "盘点", "name": "system:inventory:stocktaking", "path": "/stocktaking", "component": "/inventory/stocktaking", "meta": {"title": "盘点", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}]}, {"id": 34, "title": "系统管理", "name": "system", "path": "/system", "component": "/index/index", "meta": {"title": "系统管理", "icon": "&#xe7b9;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 50, "title": "系统配置", "name": "system:config", "path": "/config", "component": "/system/Config", "meta": {"title": "系统配置", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 51, "title": "设置", "auth_mark": "system:config:save", "name": "system:config:save"}, {"id": 52, "title": "读取", "auth_mark": "system:config:detail", "name": "system:config:detail"}]}, "visible": 1, "children": []}, {"id": 48, "title": "附件分类", "name": "system:attachmentCat", "path": "/attachmentCat", "component": "", "meta": {"title": "附件分类", "icon": "", "keepAlive": 1, "isHide": true, "isIframe": false, "authList": [{"id": 56, "title": "新增", "auth_mark": "system:attachmentCat:add", "name": "system:attachmentCat:add"}, {"id": 57, "title": "编辑", "auth_mark": "system:attachmentCat:edit", "name": "system:attachmentCat:edit"}, {"id": 58, "title": "删除", "auth_mark": "system:attachmentCat:delete", "name": "system:attachmentCat:delete"}]}, "visible": 0, "children": []}, {"id": 47, "title": "附件管理", "name": "system:attachment", "path": "/attachment", "component": "/system/attachment", "meta": {"title": "附件管理", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 53, "title": "列表", "auth_mark": "system:attachment:index", "name": "system:attachment:index"}, {"id": 54, "title": "移动分类", "auth_mark": "system:attachment:move", "name": "system:attachment:move"}, {"id": 55, "title": "删除", "auth_mark": "system:attachment:delete", "name": "system:attachment:delete"}]}, "visible": 1, "children": []}, {"id": 49, "title": "个人中心", "name": "UserCenter", "path": "/user-center", "component": "/system/UserCenter", "meta": {"title": "个人中心", "icon": "", "keepAlive": 1, "isHide": true, "isIframe": false, "authList": []}, "visible": 0, "children": []}]}, {"id": 122, "title": "流程配置", "name": "workflow", "path": "/process", "component": "/index/index", "meta": {"title": "流程配置", "icon": "&#xe741;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 121, "title": "流程类型", "name": "workflow:type", "path": "/flow_type", "component": "/workflow/workflow_type/list", "meta": {"title": "流程类型", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 123, "title": "列表", "auth_mark": "workflow:formType:index", "name": "workflow:formType:index"}, {"id": 124, "title": "编辑", "auth_mark": "workflow:formType:edit", "name": "workflow:formType:edit"}, {"id": 125, "title": "新增", "auth_mark": "workflow:formType:add", "name": "workflow:formType:add"}, {"id": 126, "title": "删除", "auth_mark": "workflow:formType:delete", "name": "workflow:formType:delete"}]}, "visible": 1, "children": []}, {"id": 75, "title": "流程列表", "name": "workflow:definition", "path": "/definition", "component": "/workflow/workflow_definition/list", "meta": {"title": "流程列表", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 113, "title": "列表", "auth_mark": "workflow:definition:index", "name": "workflow:definition:index"}, {"id": 114, "title": "新增", "auth_mark": "workflow:definition:add", "name": "workflow:definition:add"}, {"id": 115, "title": "编辑", "auth_mark": "workflow:definition:edit", "name": "workflow:definition:edit"}, {"id": 116, "title": "详情", "auth_mark": "workflow:definition:detail", "name": "workflow:definition:detail"}, {"id": 117, "title": "删除", "auth_mark": "workflow:definition:delete", "name": "workflow:definition:delete"}, {"id": 120, "title": "设计表单", "auth_mark": "workflow:definition:design", "name": "workflow:definition:design"}]}, "visible": 1, "children": []}]}, {"id": 127, "title": "消息管理", "name": "notice", "path": "/notice", "component": "/index/index", "meta": {"title": "消息管理", "icon": "&#xe6e9;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 134, "title": "消息模板", "name": "notice:template", "path": "/notice_template", "component": "/notice/Template", "meta": {"title": "消息模板", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 135, "title": "列表", "auth_mark": "notice:template:index", "name": "notice:template:index"}, {"id": 136, "title": "新增", "auth_mark": "notice:template:add", "name": "notice:template:add"}, {"id": 137, "title": "编辑", "auth_mark": "notice:template:edit", "name": "notice:template:edit"}, {"id": 138, "title": "详情", "auth_mark": "notice:template:detail", "name": "notice:template:detail"}, {"id": 139, "title": "删除", "auth_mark": "notice:template:delete", "name": "notice:template:delete"}, {"id": 143, "title": "预览", "auth_mark": "notice:template:preview", "name": "notice:template:preview"}, {"id": 144, "title": "状态", "auth_mark": "notice:template:status", "name": "notice:template:status"}]}, "visible": 1, "children": []}, {"id": 141, "title": "模板配置", "name": "notice:tenant:templateConfig", "path": "/templateConfig", "component": "/notice/tenant/TemplateConfig", "meta": {"title": "模板配置", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": []}]}, {"id": 4, "title": "日志管理", "name": "log", "path": "/log", "component": "/index/index", "meta": {"title": "日志管理", "icon": "&#xe76c;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 5, "title": "登录日志", "name": "log:login", "path": "/login_log", "component": "/log/LoginLog", "meta": {"title": "登录日志", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 43, "title": "列表", "auth_mark": "system:log:login:index", "name": "system:log:login:index"}, {"id": 44, "title": "详情", "auth_mark": "system:log:login:detail", "name": "system:log:login:detail"}, {"id": 28, "title": "删除", "auth_mark": "system:log:login:delete", "name": "system:log:login:delete"}]}, "visible": 1, "children": []}, {"id": 29, "title": "操作日志", "name": "log:operation", "path": "/operation_log", "component": "/log/OperationLog", "meta": {"title": "操作日志", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 45, "title": "列表", "auth_mark": "system:log:operation:index", "name": "system:log:operation:index"}, {"id": 46, "title": "详情", "auth_mark": "system:log:operation:detail", "name": "system:log:operation:detail"}, {"id": 30, "title": "删除", "auth_mark": "system:log:operation:delete", "name": "system:log:operation:delete"}]}, "visible": 1, "children": []}]}, {"id": 59, "title": "租户管理", "name": "system:tenant", "path": "/tenant", "component": "/index/index", "meta": {"title": "租户管理", "icon": "&#xe753;", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": []}, "visible": 1, "children": [{"id": 60, "title": "租户列表", "name": "system:tenant:tenantlist", "path": "/tenant_list", "component": "/tenant/list", "meta": {"title": "租户列表", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 61, "title": "列表", "auth_mark": "system:tenant:index", "name": "system:tenant:index"}, {"id": 62, "title": "新增", "auth_mark": "system:tenant:add", "name": "system:tenant:add"}, {"id": 63, "title": "详情", "auth_mark": "system:tenant:detail", "name": "system:tenant:detail"}, {"id": 64, "title": "编辑", "auth_mark": "system:tenant:edit", "name": "system:tenant:edit"}, {"id": 65, "title": "删除", "auth_mark": "system:tenant:delete", "name": "system:tenant:delete"}]}, "visible": 1, "children": []}, {"id": 66, "title": "租户套餐", "name": "system:tenantPackage", "path": "/tenantPackage", "component": "/tenant/package", "meta": {"title": "租户套餐", "icon": "", "keepAlive": 1, "isHide": false, "isIframe": false, "authList": [{"id": 67, "title": "列表", "auth_mark": "system:tenantPackage:index", "name": "system:tenantPackage:index"}, {"id": 68, "title": "新增", "auth_mark": "system:tenantPackage:add", "name": "system:tenantPackage:add"}, {"id": 69, "title": "详情", "auth_mark": "system:tenantPackage:detail", "name": "system:tenantPackage:detail"}, {"id": 70, "title": "编辑", "auth_mark": "system:tenantPackage:edit", "name": "system:tenantPackage:edit"}, {"id": 71, "title": "删除", "auth_mark": "system:tenantPackage:delete", "name": "system:tenantPackage:delete"}]}, "visible": 1, "children": []}]}, {"id": 140, "title": "消息中心", "name": "message", "path": "/message", "component": "/notice/Message", "meta": {"title": "消息中心", "icon": "", "keepAlive": 0, "isHide": true, "isIframe": false, "authList": [{"id": 154, "title": "批量删除", "auth_mark": "notice:message:batchDelete", "name": "notice:message:batchDelete"}, {"id": 155, "title": "删除", "auth_mark": "notice:message:delete", "name": "notice:message:delete"}]}, "visible": 0, "children": []}], "time": 1752825674}