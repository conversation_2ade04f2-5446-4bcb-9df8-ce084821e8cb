# Form-Create集成方案

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 集成目标

### 核心目标
1. **无缝集成**：Form-Create与现有统一架构完美融合
2. **可视化设计**：提供直观的表单设计器界面
3. **动态渲染**：支持运行时动态表单渲染
4. **配置驱动**：通过配置实现表单的创建和管理

### 技术优势
- **成熟稳定**：基于成熟的Form-Create框架
- **功能丰富**：支持丰富的表单组件和高级功能
- **易于扩展**：支持自定义组件和业务逻辑
- **性能优秀**：经过优化的渲染性能

## 🏗️ 集成架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "设计器层"
        A1[表单设计器<br/>FormDesigner] --> A2[组件库<br/>ComponentLibrary]
        A2 --> A3[配置管理<br/>ConfigManager]
    end
    
    subgraph "渲染层"
        B1[Form-Create渲染器<br/>FormCreateRenderer] --> B2[Form-Create查看器<br/>FormCreateViewer]
        B2 --> B3[动态组件<br/>DynamicComponent]
    end
    
    subgraph "统一组件层"
        C1[FormManager<br/>表单管理器] --> C2[FormDataViewer<br/>详情查看器]
        C2 --> C3[ComponentMapper<br/>组件映射器]
    end
    
    subgraph "配置层"
        D1[表单配置<br/>FormConfig] --> D2[字段配置<br/>FieldConfig]
        D2 --> D3[验证规则<br/>ValidationRules]
    end
    
    subgraph "数据层"
        E1[form_create_definition<br/>表单定义表] --> E2[form_create_data<br/>表单数据表]
        E2 --> E3[workflow_instance<br/>工作流实例表]
    end
    
    A1 --> B1
    A3 --> D1
    B1 --> C1
    B2 --> C2
    C3 --> B1
    C3 --> B2
    D1 --> E1
    D2 --> E2
    D3 --> E3
```

### Form-Create组件映射

```mermaid
graph LR
    subgraph "业务代码识别"
        A[businessCode] --> B{代码模式}
        B -->|fc_*| C[Form-Create表单]
        B -->|其他| D[静态/动态表单]
    end
    
    subgraph "Form-Create处理"
        C --> E[提取formId]
        E --> F[加载配置]
        F --> G[渲染组件]
    end
    
    subgraph "组件选择"
        G --> H{场景判断}
        H -->|申请表单| I[FormCreateRenderer]
        H -->|详情查看| J[FormCreateViewer]
        H -->|设计器| K[FormCreateDesigner]
    end
    
    I --> L[表单渲染]
    J --> M[详情展示]
    K --> N[可视化设计]
```

## 📊 数据结构设计

### Form-Create配置表

```sql
-- Form-Create表单定义表
CREATE TABLE form_create_definition (
  id int PRIMARY KEY AUTO_INCREMENT,
  name varchar(100) NOT NULL COMMENT '表单名称',
  business_code varchar(50) NOT NULL COMMENT '业务代码 fc_001',
  form_rule JSON NOT NULL COMMENT 'Form-Create规则配置',
  form_option JSON COMMENT 'Form-Create选项配置',
  designer_config JSON COMMENT '设计器配置',
  template_id int DEFAULT 0 COMMENT '模板ID',
  category varchar(50) DEFAULT '' COMMENT '表单分类',
  description text COMMENT '表单描述',
  preview_image varchar(255) COMMENT '预览图片',
  status tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  version varchar(20) DEFAULT '1.0' COMMENT '版本号',
  is_template tinyint DEFAULT 0 COMMENT '是否为模板',
  sort_order int DEFAULT 0 COMMENT '排序',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  UNIQUE KEY uk_business_code (business_code, tenant_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_template (is_template),
  INDEX idx_tenant_id (tenant_id)
);

-- Form-Create表单数据表
CREATE TABLE form_create_data (
  id int PRIMARY KEY AUTO_INCREMENT,
  form_definition_id int NOT NULL COMMENT '表单定义ID',
  business_code varchar(50) NOT NULL COMMENT '业务代码',
  form_data JSON NOT NULL COMMENT '表单数据JSON',
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitter_name varchar(50) COMMENT '提交人姓名',
  submit_time datetime COMMENT '提交时间',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  INDEX idx_form_definition_id (form_definition_id),
  INDEX idx_business_code (business_code),
  INDEX idx_instance_id (instance_id),
  INDEX idx_approval_status (approval_status),
  INDEX idx_submitter_id (submitter_id),
  INDEX idx_tenant_id (tenant_id)
);

-- Form-Create模板表
CREATE TABLE form_create_template (
  id int PRIMARY KEY AUTO_INCREMENT,
  name varchar(100) NOT NULL COMMENT '模板名称',
  category varchar(50) DEFAULT '' COMMENT '模板分类',
  description text COMMENT '模板描述',
  preview_image varchar(255) COMMENT '预览图片',
  form_rule JSON NOT NULL COMMENT '模板规则配置',
  form_option JSON COMMENT '模板选项配置',
  tags varchar(255) COMMENT '标签',
  download_count int DEFAULT 0 COMMENT '下载次数',
  is_public tinyint DEFAULT 1 COMMENT '是否公开',
  sort_order int DEFAULT 0 COMMENT '排序',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  INDEX idx_category (category),
  INDEX idx_public (is_public),
  INDEX idx_tenant_id (tenant_id)
);
```

### Form-Create配置结构

```typescript
// Form-Create配置接口
interface FormCreateConfig {
  // 基本信息
  id: number
  name: string
  businessCode: string
  description?: string
  
  // Form-Create配置
  rule: FormCreateRule[]
  option: FormCreateOption
  
  // 设计器配置
  designerConfig?: {
    width?: string
    height?: string
    components?: string[]
    plugins?: string[]
  }
  
  // 元数据
  version: string
  status: number
  isTemplate: boolean
  templateId?: number
  category?: string
  tags?: string[]
}

// Form-Create规则
interface FormCreateRule {
  type: string                    // 组件类型
  field: string                   // 字段名
  title: string                   // 字段标题
  value?: any                     // 默认值
  props?: Record<string, any>     // 组件属性
  validate?: FormCreateValidate[] // 验证规则
  children?: FormCreateRule[]     // 子组件
  control?: FormCreateControl[]   // 联动控制
}

// Form-Create选项
interface FormCreateOption {
  form?: {
    labelWidth?: string
    size?: string
    disabled?: boolean
    inline?: boolean
  }
  row?: {
    gutter?: number
    type?: string
  }
  submitBtn?: boolean | {
    text?: string
    type?: string
    size?: string
    loading?: boolean
  }
  resetBtn?: boolean | {
    text?: string
    type?: string
    size?: string
  }
}

// 验证规则
interface FormCreateValidate {
  type: string
  message: string
  trigger?: string
  required?: boolean
  pattern?: string
  min?: number
  max?: number
  validator?: string
}

// 联动控制
interface FormCreateControl {
  handle: string
  condition: string
  rule: FormCreateRule[]
}
```

## 🔧 核心组件实现

### Form-Create渲染器

```vue
<!-- FormCreateRenderer.vue -->
<template>
  <div class="form-create-renderer" v-loading="loading">
    <!-- Form-Create表单渲染 -->
    <form-create
      ref="formCreateRef"
      v-model="formData"
      :rule="formRule"
      :option="formOption"
      @submit="handleSubmit"
      @change="handleChange"
      @mounted="handleMounted"
    />

    <!-- 表单操作按钮 -->
    <div class="form-actions" v-if="showActions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSave" :loading="saving">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交申请
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FormCreate } from '@form-create/element-ui'
import { CustomFormApi } from '@/api/custom/form'

interface Props {
  formConfig?: FormCreateConfig  // Form-Create配置
  formId?: number               // 表单ID（编辑时）
  data?: any                   // 初始数据
  mode?: 'create' | 'edit' | 'view'
  showActions?: boolean
  definitionId?: number        // 工作流定义ID
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  showActions: true,
  data: () => ({})
})

const emit = defineEmits<{
  submit: [data: any]
  save: [data: any]
  cancel: []
  change: [data: any]
  mounted: []
}>()

// Form-Create实例引用
const formCreateRef = ref<FormCreate>()

// 状态管理
const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)

// 表单数据
const formData = ref<Record<string, any>>({})

// Form-Create规则配置
const formRule = ref<FormCreateRule[]>([])

// Form-Create选项配置
const formOption = ref<FormCreateOption>({
  form: {
    labelWidth: '120px',
    size: 'default'
  },
  submitBtn: false,  // 禁用默认提交按钮
  resetBtn: false    // 禁用默认重置按钮
})

// 加载表单配置
const loadFormConfig = async () => {
  if (props.formConfig) {
    // 直接使用传入的配置
    formRule.value = props.formConfig.rule || []
    formOption.value = { ...formOption.value, ...props.formConfig.option }
  } else if (props.formId) {
    // 从API加载配置
    try {
      loading.value = true
      const res = await CustomFormApi.getFormCreateConfig(props.formId)
      if (res.code === 1) {
        formRule.value = res.data.rule || []
        formOption.value = { ...formOption.value, ...res.data.option }
      }
    } catch (error) {
      console.error('加载Form-Create配置失败:', error)
      ElMessage.error('加载表单配置失败')
    } finally {
      loading.value = false
    }
  }
}

// 表单挂载完成
const handleMounted = () => {
  console.log('Form-Create表单挂载完成')
  emit('mounted')
  
  // 设置初始数据
  if (props.data && Object.keys(props.data).length > 0) {
    formData.value = { ...props.data }
  }
}

// 表单提交
const handleSubmit = async () => {
  if (submitting.value) return
  
  try {
    submitting.value = true
    
    // Form-Create表单验证
    const valid = await formCreateRef.value?.validate()
    if (valid) {
      emit('submit', formData.value)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    submitting.value = false
  }
}

// 表单保存
const handleSave = async () => {
  if (saving.value) return
  
  try {
    saving.value = true
    emit('save', formData.value)
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 表单取消
const handleCancel = () => {
  emit('cancel')
}

// 表单变化
const handleChange = (field: string, value: any, formData: any) => {
  emit('change', formData)
}

// 显示表单（编辑模式）
const showForm = async (id?: number) => {
  if (id && props.mode === 'edit') {
    try {
      loading.value = true
      const res = await ApplicationApi.detail(id)
      if (res.code === 1 && res.data.form_data) {
        formData.value = { ...res.data.form_data }
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
      ElMessage.error('加载表单数据失败')
    } finally {
      loading.value = false
    }
  }
}

// 重置表单
const resetForm = () => {
  formCreateRef.value?.resetFields()
  formData.value = {}
}

// 获取表单数据
const getFormData = () => {
  return formData.value
}

// 设置表单数据
const setFormData = (data: any) => {
  formData.value = { ...data }
}

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    return await formCreateRef.value?.validate() || false
  } catch (error) {
    return false
  }
}

// 初始化
onMounted(() => {
  loadFormConfig()
})

// 暴露方法
defineExpose({
  showForm,
  resetForm,
  getFormData,
  setFormData,
  validate
})
</script>

<style scoped>
.form-create-renderer {
  padding: 20px;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
```

### Form-Create详情查看器

```vue
<!-- FormCreateViewer.vue -->
<template>
  <div class="form-create-viewer" v-loading="loading">
    <!-- 使用Form-Create的只读模式 -->
    <form-create
      v-model="viewData"
      :rule="viewRule"
      :option="viewOption"
    />
  </div>
</template>

<script setup lang="ts">
interface Props {
  formConfig: FormCreateConfig  // Form-Create配置
  data: any                    // 表单数据
}

const props = defineProps<Props>()

// 状态管理
const loading = ref(false)

// 查看数据
const viewData = ref<Record<string, any>>({})

// 只读规则配置
const viewRule = computed(() => {
  return props.formConfig.rule?.map(rule => ({
    ...rule,
    props: {
      ...rule.props,
      disabled: true,  // 设置为只读
      readonly: true
    }
  })) || []
})

// 只读选项配置
const viewOption = ref<FormCreateOption>({
  form: {
    labelWidth: '120px',
    size: 'default',
    disabled: true
  },
  submitBtn: false,
  resetBtn: false
})

// 初始化数据
onMounted(() => {
  viewData.value = { ...props.data }
})

// 监听数据变化
watch(() => props.data, (newData) => {
  viewData.value = { ...newData }
}, { deep: true })
</script>

<style scoped>
.form-create-viewer {
  padding: 20px;
}

/* 只读模式样式优化 */
.form-create-viewer :deep(.el-form-item__content) {
  color: #606266;
}

.form-create-viewer :deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}
</style>
```

### Form-Create设计器

```vue
<!-- FormCreateDesigner.vue -->
<template>
  <div class="form-create-designer">
    <!-- 设计器工具栏 -->
    <div class="designer-toolbar">
      <el-button-group>
        <el-button @click="handlePreview" icon="View">预览</el-button>
        <el-button @click="handleSave" icon="Document" type="primary">保存</el-button>
        <el-button @click="handleClear" icon="Delete">清空</el-button>
      </el-button-group>
      
      <div class="toolbar-right">
        <el-button @click="handleImportTemplate" icon="Upload">导入模板</el-button>
        <el-button @click="handleExportTemplate" icon="Download">导出模板</el-button>
      </div>
    </div>
    
    <!-- Form-Create设计器 -->
    <form-create-designer
      ref="designerRef"
      :config="designerConfig"
      @save="handleDesignerSave"
      @preview="handleDesignerPreview"
    />

    <!-- 预览对话框 -->
    <el-dialog v-model="previewVisible" title="表单预览" width="80%">
      <FormCreateRenderer
        :form-config="previewConfig"
        :show-actions="false"
        mode="preview"
      />
    </el-dialog>
    
    <!-- 保存对话框 -->
    <el-dialog v-model="saveVisible" title="保存表单" width="500px">
      <el-form ref="saveFormRef" :model="saveForm" :rules="saveRules" label-width="100px">
        <el-form-item label="表单名称" prop="name">
          <el-input v-model="saveForm.name" placeholder="请输入表单名称" />
        </el-form-item>
        <el-form-item label="表单分类" prop="category">
          <el-select v-model="saveForm.category" placeholder="请选择分类">
            <el-option label="通用表单" value="general" />
            <el-option label="业务表单" value="business" />
            <el-option label="审批表单" value="approval" />
          </el-select>
        </el-form-item>
        <el-form-item label="表单描述" prop="description">
          <el-input
            v-model="saveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入表单描述"
          />
        </el-form-item>
        <el-form-item label="保存为模板">
          <el-switch v-model="saveForm.isTemplate" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="saveVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmSave" :loading="saving">
          确定保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import FormCreateDesigner from '@form-create/designer'
import FormCreateRenderer from './FormCreateRenderer.vue'
import { CustomFormApi } from '@/api/custom/form'

// 设计器引用
const designerRef = ref<any>()

// 状态管理
const previewVisible = ref(false)
const saveVisible = ref(false)
const saving = ref(false)

// 预览配置
const previewConfig = ref<FormCreateConfig>({
  id: 0,
  name: '',
  businessCode: '',
  rule: [],
  option: {},
  version: '1.0',
  status: 1,
  isTemplate: false
})

// 保存表单
const saveForm = reactive({
  name: '',
  category: 'general',
  description: '',
  isTemplate: false
})

// 保存表单验证规则
const saveRules = {
  name: [
    { required: true, message: '请输入表单名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择表单分类', trigger: 'change' }
  ]
}

// 设计器配置
const designerConfig = ref({
  // Form-Create设计器配置
  height: '600px',
  mask: false,
  preview: true,
  // 自定义字段组件
  components: [
    // 基础组件
    'input', 'textarea', 'select', 'radio', 'checkbox',
    'date', 'time', 'number', 'rate', 'slider', 'switch',
    // 高级组件
    'upload', 'tree', 'cascader', 'transfer',
    // 布局组件
    'row', 'col', 'divider', 'space',
    // 自定义业务组件
    'department-select', 'user-select', 'customer-select'
  ],
  // 组件配置
  componentConfig: {
    'department-select': {
      label: '部门选择',
      icon: 'el-icon-office-building',
      props: {
        placeholder: '请选择部门'
      }
    },
    'user-select': {
      label: '用户选择',
      icon: 'el-icon-user',
      props: {
        placeholder: '请选择用户'
      }
    },
    'customer-select': {
      label: '客户选择',
      icon: 'el-icon-user-solid',
      props: {
        placeholder: '请选择客户'
      }
    }
  }
})

// 处理预览
const handlePreview = () => {
  const config = designerRef.value?.getRule()
  if (config) {
    previewConfig.value = {
      id: 0,
      name: '预览表单',
      businessCode: 'preview',
      rule: config.rule || [],
      option: config.option || {},
      version: '1.0',
      status: 1,
      isTemplate: false
    }
    previewVisible.value = true
  }
}

// 处理保存
const handleSave = () => {
  const config = designerRef.value?.getRule()
  if (config && config.rule && config.rule.length > 0) {
    saveVisible.value = true
  } else {
    ElMessage.warning('请先设计表单内容')
  }
}

// 确认保存
const handleConfirmSave = async () => {
  try {
    const valid = await saveFormRef.value?.validate()
    if (!valid) return
    
    saving.value = true
    
    const config = designerRef.value?.getRule()
    const businessCode = `fc_${Date.now()}`
    
    const res = await CustomFormApi.saveFormCreateConfig({
      name: saveForm.name,
      business_code: businessCode,
      category: saveForm.category,
      description: saveForm.description,
      rule: config.rule,
      option: config.option,
      is_template: saveForm.isTemplate ? 1 : 0
    })
    
    if (res.code === 1) {
      ElMessage.success('表单保存成功')
      saveVisible.value = false
      emit('save-success', res.data)
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 处理清空
const handleClear = () => {
  ElMessageBox.confirm('确定要清空当前设计的表单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    designerRef.value?.clear()
    ElMessage.success('已清空表单')
  })
}

// 导入模板
const handleImportTemplate = () => {
  // 实现模板导入逻辑
  ElMessage.info('模板导入功能开发中')
}

// 导出模板
const handleExportTemplate = () => {
  const config = designerRef.value?.getRule()
  if (config) {
    const blob = new Blob([JSON.stringify(config, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `form-template-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
    ElMessage.success('模板导出成功')
  } else {
    ElMessage.warning('请先设计表单内容')
  }
}

// 设计器保存事件
const handleDesignerSave = (config: any) => {
  console.log('设计器保存事件:', config)
}

// 设计器预览事件
const handleDesignerPreview = (config: any) => {
  previewConfig.value = {
    id: 0,
    name: '预览表单',
    businessCode: 'preview',
    rule: config.rule || [],
    option: config.option || {},
    version: '1.0',
    status: 1,
    isTemplate: false
  }
  previewVisible.value = true
}

const emit = defineEmits(['save-success'])
</script>

<style scoped>
.form-create-designer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}
</style>
```

## 🔄 组件映射集成

### 扩展组件映射器

```typescript
// utils/formCreateMapper.ts
class FormCreateComponentMapper {
  // 获取Form-Create申请表单组件
  async getFormCreateFormComponent(businessCode: string): Promise<ComponentMapping> {
    const formId = this.extractFormId(businessCode)
    
    try {
      const formConfig = await CustomFormApi.getFormCreateConfig(formId)
      
      return {
        component: () => import('@/components/business-forms/form-create-renderer.vue'),
        props: { 
          formConfig: formConfig.data,
          formId
        }
      }
    } catch (error) {
      console.error(`加载Form-Create配置失败: ${businessCode}`, error)
      return this.getFallbackComponent()
    }
  }
  
  // 获取Form-Create详情组件
  async getFormCreateDetailComponent(businessCode: string): Promise<ComponentMapping> {
    const formId = this.extractFormId(businessCode)
    
    try {
      const formConfig = await CustomFormApi.getFormCreateConfig(formId)
      
      return {
        component: () => import('@/components/business-detail/form-create-viewer.vue'),
        props: { 
          formConfig: formConfig.data
        }
      }
    } catch (error) {
      console.error(`加载Form-Create详情配置失败: ${businessCode}`, error)
      return this.getFallbackDetailComponent()
    }
  }
  
  // 提取表单ID
  private extractFormId(businessCode: string): number {
    const match = businessCode.match(/^fc_(\d+)$/)
    return match ? parseInt(match[1]) : 0
  }
  
  // 获取兜底组件
  private getFallbackComponent(): ComponentMapping {
    return {
      component: () => import('@/components/business-forms/generic-form.vue')
    }
  }
  
  // 获取兜底详情组件
  private getFallbackDetailComponent(): ComponentMapping {
    return {
      component: () => import('@/components/business-detail/generic-detail.vue')
    }
  }
}

export const formCreateMapper = new FormCreateComponentMapper()
```

### 集成到现有映射器

```typescript
// 在FormManager中集成Form-Create
const currentFormComponent = computed(async () => {
  if (!formState.type) return null

  try {
    let mapping: ComponentMapping
    
    // Form-Create表单映射
    if (formState.type.startsWith('fc_')) {
      mapping = await formCreateMapper.getFormCreateFormComponent(formState.type)
    }
    // 其他表单类型...
    else {
      mapping = await businessFormMapper.getBusinessFormComponent(formState.type)
    }
    
    return markRaw(defineAsyncComponent(mapping.component))
  } catch (error) {
    console.error('组件映射失败:', error)
    return null
  }
})

// 在FormDataViewer中集成Form-Create
const businessDetailComponent = computed(async () => {
  if (!props.businessCode) return null
  
  try {
    let mapping: ComponentMapping
    
    // Form-Create详情映射
    if (props.businessCode.startsWith('fc_')) {
      mapping = await formCreateMapper.getFormCreateDetailComponent(props.businessCode)
    }
    // 其他详情类型...
    else {
      mapping = await businessDetailMapper.getBusinessDetailComponent(props.businessCode)
    }
    
    return markRaw(defineAsyncComponent(mapping.component))
  } catch (error) {
    console.error('详情组件映射失败:', error)
    return null
  }
})
```

## 🎨 自定义组件扩展

### 业务组件注册

```typescript
// components/form-create/business-components.ts
import { FormCreate } from '@form-create/element-ui'

// 部门选择组件
const DepartmentSelect = {
  name: 'DepartmentSelect',
  props: {
    modelValue: [String, Number, Array],
    multiple: Boolean,
    placeholder: String
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const departments = ref([])
    
    const loadDepartments = async () => {
      try {
        const res = await WorkflowApi.getDepartmentList()
        departments.value = res.data || []
      } catch (error) {
        console.error('加载部门列表失败:', error)
      }
    }
    
    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }
    
    onMounted(() => {
      loadDepartments()
    })
    
    return {
      departments,
      handleChange
    }
  },
  template: `
    <el-tree-select
      :model-value="modelValue"
      :data="departments"
      :props="{ label: 'name', value: 'id', children: 'children' }"
      :placeholder="placeholder || '请选择部门'"
      :multiple="multiple"
      check-strictly
      @update:model-value="handleChange"
    />
  `
}

// 用户选择组件
const UserSelect = {
  name: 'UserSelect',
  props: {
    modelValue: [String, Number, Array],
    multiple: Boolean,
    placeholder: String,
    departmentId: [String, Number]
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const users = ref([])
    
    const loadUsers = async () => {
      try {
        const params = props.departmentId ? { department_id: props.departmentId } : {}
        const res = await WorkflowApi.getUserList(params)
        users.value = res.data || []
      } catch (error) {
        console.error('加载用户列表失败:', error)
      }
    }
    
    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }
    
    watch(() => props.departmentId, () => {
      loadUsers()
    })
    
    onMounted(() => {
      loadUsers()
    })
    
    return {
      users,
      handleChange
    }
  },
  template: `
    <el-select
      :model-value="modelValue"
      :placeholder="placeholder || '请选择用户'"
      :multiple="multiple"
      filterable
      @update:model-value="handleChange"
    >
      <el-option
        v-for="user in users"
        :key="user.id"
        :label="user.name"
        :value="user.id"
      />
    </el-select>
  `
}

// 注册自定义组件
export function registerBusinessComponents() {
  FormCreate.component('department-select', DepartmentSelect)
  FormCreate.component('user-select', UserSelect)
  
  // 注册组件配置
  FormCreate.componentConfig('department-select', {
    label: '部门选择',
    icon: 'el-icon-office-building',
    props: {
      placeholder: '请选择部门',
      multiple: false
    },
    validate: [
      { type: 'string', message: '请选择部门', trigger: 'change' }
    ]
  })
  
  FormCreate.componentConfig('user-select', {
    label: '用户选择',
    icon: 'el-icon-user',
    props: {
      placeholder: '请选择用户',
      multiple: false
    },
    validate: [
      { type: 'string', message: '请选择用户', trigger: 'change' }
    ]
  })
}
```

## 📊 性能优化

### 配置缓存优化

```typescript
// utils/formCreateCache.ts
class FormCreateCache {
  private configCache = new Map<number, FormCreateConfig>()
  private ttl = 10 * 60 * 1000 // 10分钟缓存
  
  // 缓存配置
  cacheConfig(formId: number, config: FormCreateConfig) {
    this.configCache.set(formId, {
      ...config,
      _timestamp: Date.now()
    })
  }
  
  // 获取缓存配置
  getCachedConfig(formId: number): FormCreateConfig | null {
    const cached = this.configCache.get(formId)
    if (cached && Date.now() - cached._timestamp < this.ttl) {
      return cached
    }
    
    this.configCache.delete(formId)
    return null
  }
  
  // 清除缓存
  clearCache(formId?: number) {
    if (formId) {
      this.configCache.delete(formId)
    } else {
      this.configCache.clear()
    }
  }
  
  // 预加载常用配置
  async preloadConfigs(formIds: number[]) {
    const promises = formIds.map(async (formId) => {
      if (!this.getCachedConfig(formId)) {
        try {
          const res = await CustomFormApi.getFormCreateConfig(formId)
          this.cacheConfig(formId, res.data)
        } catch (error) {
          console.error(`预加载配置失败: ${formId}`, error)
        }
      }
    })
    
    await Promise.allSettled(promises)
  }
}

export const formCreateCache = new FormCreateCache()
```

### 组件懒加载

```typescript
// utils/formCreateLazy.ts
export function createLazyFormCreateComponent(loader: () => Promise<any>) {
  return defineAsyncComponent({
    loader,
    loadingComponent: FormCreateSkeleton,
    errorComponent: FormCreateError,
    delay: 200,
    timeout: 5000
  })
}

// 骨架屏组件
const FormCreateSkeleton = {
  template: `
    <div class="form-create-skeleton">
      <el-skeleton :rows="5" animated />
    </div>
  `
}

// 错误组件
const FormCreateError = {
  template: `
    <div class="form-create-error">
      <el-alert
        title="表单加载失败"
        type="error"
        description="请检查网络连接或联系管理员"
        show-icon
      />
    </div>
  `
}
```

## 🔍 监控与调试

### Form-Create性能监控

```typescript
// utils/formCreateMonitor.ts
class FormCreateMonitor {
  private metrics = new Map<string, any>()
  
  // 监控表单渲染性能
  monitorFormRender(formId: number, startTime: number) {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.recordMetric('form_render', {
      formId,
      duration,
      timestamp: Date.now()
    })
  }
  
  // 监控配置加载性能
  monitorConfigLoad(formId: number, duration: number, success: boolean) {
    this.recordMetric('config_load', {
      formId,
      duration,
      success,
      timestamp: Date.now()
    })
  }
  
  // 监控表单提交性能
  monitorFormSubmit(formId: number, duration: number, success: boolean) {
    this.recordMetric('form_submit', {
      formId,
      duration,
      success,
      timestamp: Date.now()
    })
  }
  
  // 记录指标
  private recordMetric(type: string, data: any) {
    // 发送到监控系统
    if (window.monitoringService) {
      window.monitoringService.record(`form_create_${type}`, data)
    }
    
    // 开发环境日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`Form-Create监控 - ${type}:`, data)
    }
  }
}

export const formCreateMonitor = new FormCreateMonitor()
```

---

**注意：** Form-Create集成是动态表单功能的核心，确保所有配置和组件都经过充分测试。