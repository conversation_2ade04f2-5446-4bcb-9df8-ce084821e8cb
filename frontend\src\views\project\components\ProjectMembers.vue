<template>
  <div class="project-members">
    <div class="members-header">
      <div class="header-left">
        <h3>项目成员</h3>
        <span class="member-count">({{ members.length }}人)</span>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAddMember">
          <el-icon>
            <Plus />
          </el-icon>
          添加成员
        </el-button>
      </div>
    </div>

    <div class="members-content">
      <div class="member-grid">
        <div v-for="member in members" :key="member.id" class="member-card">
          <div class="member-avatar">
            <el-avatar :size="48" :src="member.avatar">
              {{ member.real_name?.charAt(0) || member.username?.charAt(0) }}
            </el-avatar>
          </div>
          <div class="member-info">
            <div class="member-name">{{ member.real_name || member.username }}</div>
            <div class="member-role">{{ member.dept_name || '未分配部门' }}</div>
            <div class="member-stats">
              <span class="stat-item">
                <el-icon><Tickets /></el-icon>
                {{ member.task_count || 0 }}个任务
              </span>
            </div>
          </div>
          <div class="member-actions">
            <el-dropdown trigger="click">
              <el-button text circle>
                <el-icon>
                  <MoreFilled />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleViewMember(member)">
                    <el-icon>
                      <View />
                    </el-icon>
                    查看详情
                  </el-dropdown-item>
                  <!--                  <el-dropdown-item @click="handleUpdateRole(member)">
                                      <el-icon>
                                        <Edit />
                                      </el-icon>
                                      修改角色
                                    </el-dropdown-item>-->
                  <el-dropdown-item divided @click="handleRemoveMember(member)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    移除成员
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <div v-if="members.length === 0" class="empty-members">
        <el-empty description="暂无项目成员">
          <el-button type="primary" @click="handleAddMember"> 添加成员</el-button>
        </el-empty>
      </div>
    </div>

    <!-- 添加成员弹窗 -->
    <el-dialog v-model="addMemberVisible" title="添加项目成员" width="500px">
      <el-form
        ref="addMemberFormRef"
        :model="addMemberForm"
        :rules="addMemberRules"
        label-width="80px"
      >
        <el-form-item label="选择用户" prop="user_id">
          <el-select
            v-model="addMemberForm.user_id"
            placeholder="请选择用户"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
        </el-form-item>
        <!-- 暂时注释角色选择，添加的成员统一为项目执行人 -->
        <!-- <el-form-item label="角色" prop="role">
          <el-select
            v-model="addMemberForm.role"
            placeholder="请选择角色"
            style="width: 100%"
          >
            <el-option label="项目负责人" value="owner" />
            <el-option label="开发成员" value="developer" />
            <el-option label="测试成员" value="tester" />
            <el-option label="观察者" value="observer" />
          </el-select>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addMemberVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmAddMember"> 确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessageBox } from 'element-plus'
  import { Plus, MoreFilled, View, Delete, Tickets } from '@element-plus/icons-vue'
  import { ProjectMemberApi } from '@/api/project/projectMember'
  import { ApiStatus } from '@/utils/http/status'

  // Props
  interface Props {
    projectId: number
    members: any[]
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    'add-member': [memberData: any]
    'remove-member': [userId: number]
    'update-role': [userId: number, role: string]
  }>()

  // 响应式数据
  const addMemberVisible = ref(false)
  const availableUsers = ref<
    Array<{ value: number; label: string; username?: string; email?: string }>
  >([])
  const addMemberFormRef = ref()

  const addMemberForm = reactive({
    user_id: undefined as number | undefined,
    role: 'member' // 统一设置为项目执行人
  })

  const addMemberRules = {
    user_id: [{ required: true, message: '请选择用户', trigger: 'change' }]
    // 角色已固定为member，无需验证
  }

  // 方法
  const handleAddMember = () => {
    addMemberVisible.value = true
    loadAvailableUsers()
  }

  const handleViewMember = (member: any) => {
    // 显示成员详细信息
    const memberInfo = `
      <div style="text-align: left;">
        <p><strong>姓名：</strong>${member.real_name || member.username}</p>
        <p><strong>用户名：</strong>${member.username}</p>
        <p><strong>部门：</strong>${member.dept_name || '未分配部门'}</p>
        <p><strong>手机号：</strong>${member.mobile || '未填写'}</p>
        <p><strong>角色：</strong>${getRoleText(member.role)}</p>
        <p><strong>加入时间：</strong>${member.joined_at || '未知'}</p>
        <p><strong>任务数量：</strong>${member.task_count || 0}个</p>
      </div>
    `

    ElMessageBox.alert(memberInfo, '成员详情', {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      customStyle: {
        width: '400px'
      }
    })
  }

  /*const handleUpdateRole = async (member: any) => {
    try {
      const { value: newRole } = await ElMessageBox.prompt('请选择新的角色', '修改角色', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: [
          { label: '项目负责人', value: 'owner' },
          { label: '开发成员', value: 'developer' },
          { label: '测试成员', value: 'tester' },
          { label: '观察者', value: 'observer' }
        ]
      })

      emit('update-role', member.user_id, newRole)
    } catch (error) {
      // 用户取消
    }
  }*/

  const handleRemoveMember = async (member: any) => {
    await ElMessageBox.confirm(`确定要移除成员"${member.real_name || member.username}"吗？`, '移除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    emit('remove-member', member.user_id)
  }

  const handleConfirmAddMember = async () => {
    if (!addMemberFormRef.value) return

    try {
      await addMemberFormRef.value.validate()

      emit('add-member', {
        user_id: addMemberForm.user_id,
        role: addMemberForm.role
      })

      addMemberVisible.value = false
      addMemberForm.user_id = undefined
      addMemberForm.role = 'member'
    } catch (error) {
      // 验证失败
      console.error('表单验证失败:', error)
    }
  }

  const getRoleText = (role: string) => {
    const roleMap: Record<string, string> = {
      owner: '项目负责人',
      member: '项目执行人'
      // 暂时注释其他角色
      // developer: '开发成员',
      // tester: '测试成员',
      // observer: '观察者'
    }
    return roleMap[role] || '项目执行人'
  }

  const loadAvailableUsers = async () => {
    try {
      const response = await ProjectMemberApi.availableUsers(props.projectId)
      if (response.code === ApiStatus.success) {
        // 后端返回的数据格式：{ value: id, label: name, username, email }
        // 转换为前端需要的格式：{ id, name }
        availableUsers.value = response.data.map((user: any) => ({
          value: user.value,
          label: user.label
        }))
      }
    } catch (error) {
      console.error('加载用户列表失败:', error)
      availableUsers.value = []
    }
  }

  // 初始化
  onMounted(() => {
    // 组件初始化
  })
</script>

<style scoped lang="scss">
  .project-members {
    .members-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        h3 {
          margin: 0;
          color: #1f2329;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }

        .member-count {
          color: #86909c;
          font-size: 14px;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }
      }
    }

    .members-content {
      .member-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 16px;

        .member-card {
          background: white;
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          padding: 16px;
          display: flex;
          align-items: center;

          // 黑暗模式适配
          html.dark & {
            background: var(--art-main-bg-color);
            border-color: var(--art-border-color);
          }

          gap: 12px;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .member-avatar {
            flex-shrink: 0;
          }

          .member-info {
            flex: 1;

            .member-name {
              font-weight: 500;
              color: #1f2329;
              margin-bottom: 4px;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-900);
              }
            }

            .member-role {
              color: #86909c;
              font-size: 12px;
              margin-bottom: 6px;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-600);
              }
            }

            .member-stats {
              .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #86909c;

                .el-icon {
                  font-size: 12px;
                }

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-600);
                }
              }
            }
          }

          .member-actions {
            flex-shrink: 0;
          }
        }
      }

      .empty-members {
        text-align: center;
        padding: 48px 0;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
</style>
