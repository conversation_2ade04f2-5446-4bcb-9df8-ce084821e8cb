<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 客户表路由
Route::group('api/crm/crm_customer_my', function () {
	Route::get('index', 'app\crm\controller\CrmCustomerMyController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmCustomerMyController@detail');
	Route::post('add', 'app\crm\controller\CrmCustomerMyController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmCustomerMyController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmCustomerMyController@delete');
	//	Route::post('batchDelete', 'app\crm\controller\CrmCustomerMyController@batchDelete');
	//	Route::post('updateField', 'app\crm\controller\CrmCustomerMyController@updateField');
	Route::post('status/:id', 'app\crm\controller\CrmCustomerMyController@status');
	Route::post('import', 'app\crm\controller\CrmCustomerMyController@import');
	//	Route::get('importTemplate', 'app\crm\controller\CrmCustomerMyController@importTemplate');
	//	Route::get('downloadTemplate', 'app\crm\controller\CrmCustomerMyController@downloadTemplate');
	//	Route::get('export', 'app\crm\controller\CrmCustomerMyController@export');
	Route::get('options', 'app\crm\controller\CrmCustomerMyController@options');
	
	// ==================== 客户详情页面操作路由 ====================
	
	// 联系人操作路由 (5个)
	Route::post('add_contact', 'app\crm\controller\CrmCustomerMyController@addContact');
	Route::post('edit_contact', 'app\crm\controller\CrmCustomerMyController@editContact');
	Route::post('delete_contact', 'app\crm\controller\CrmCustomerMyController@deleteContact');
	Route::get('contact_detail', 'app\crm\controller\CrmCustomerMyController@contactDetail');
	Route::get('contact_list', 'app\crm\controller\CrmCustomerMyController@contactList');
	
	// 合同操作路由 (8个)
	Route::post('add_contract', 'app\crm\controller\CrmCustomerMyController@addContract');
	Route::post('edit_contract', 'app\crm\controller\CrmCustomerMyController@editContract');
	Route::post('delete_contract', 'app\crm\controller\CrmCustomerMyController@deleteContract');
	Route::get('contract_detail', 'app\crm\controller\CrmCustomerMyController@contractDetail');
	Route::get('contract_list', 'app\crm\controller\CrmCustomerMyController@contractList');
	Route::post('submit_contract_approval', 'app\crm\controller\CrmCustomerMyController@submitContractApproval');
	Route::post('withdraw_contract_approval', 'app\crm\controller\CrmCustomerMyController@withdrawContractApproval');
	
	// 回款操作路由 (8个)
	Route::post('add_receivable', 'app\crm\controller\CrmCustomerMyController@addReceivable');
	Route::post('edit_receivable', 'app\crm\controller\CrmCustomerMyController@editReceivable');
	Route::post('delete_receivable', 'app\crm\controller\CrmCustomerMyController@deleteReceivable');
	Route::get('receivable_detail', 'app\crm\controller\CrmCustomerMyController@receivableDetail');
	Route::get('receivable_list', 'app\crm\controller\CrmCustomerMyController@receivableList');
	Route::post('submit_receivable_approval', 'app\crm\controller\CrmCustomerMyController@submitReceivableApproval');
	Route::post('withdraw_receivable_approval', 'app\crm\controller\CrmCustomerMyController@withdrawReceivableApproval');
	Route::post('add_receivable_more', 'app\crm\controller\CrmCustomerMyController@addReceivableMore');
	
	// 跟进记录路由 (5个)
	Route::post('add_follow', 'app\crm\controller\CrmCustomerMyController@addFollow');
	Route::post('edit_follow', 'app\crm\controller\CrmCustomerMyController@editFollow');
	Route::post('delete_follow', 'app\crm\controller\CrmCustomerMyController@deleteFollow');
	Route::get('follow_detail', 'app\crm\controller\CrmCustomerMyController@followDetail');
	Route::get('follow_list', 'app\crm\controller\CrmCustomerMyController@followList');
	
	// 客户操作路由 (1个)
	// 回收公海
	Route::post('recycle_customer/:id', 'app\crm\controller\CrmCustomerMyController@recycle_customer');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);