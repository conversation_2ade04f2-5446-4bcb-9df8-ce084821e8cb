import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 线索池相关接口
 */
export class CrmLeadPoolApi {
  /**
   * 获取线索表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_lead_pool/index',
      params
    })
  }

  /**
   * 获取详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/crm/crm_lead_pool/detail/${id}`,
      params: options
    })
  }

  /**
   * 认领线索
   * @param id 线索ID
   */
  static claimLead(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_pool/claimLead/${id}`
    })
  }

  /**
   * 分配线索
   * @param id 线索ID
   * @param data 分配数据
   */
  static assign(id: number | string, data: any) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_pool/assign/${id}`,
      data
    })
  }

  /**
   * 获取下拉选项
   */
  static options() {
    return request.get<BaseResult>({
      url: `/crm/crm_lead_pool/options`
    })
  }

  /**
   * 导出线索池数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/crm/crm_lead_pool/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 批量认领线索
   * @param ids 线索ID数组
   */
  static batchClaim(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_pool/batchClaim`,
      data: { ids }
    })
  }

  /**
   * 批量分配线索
   * @param ids 线索ID数组
   * @param toUserId 目标用户ID
   * @param reason 分配原因
   */
  static batchAssign(ids: (number | string)[], toUserId: number | string, reason?: string) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_pool/batchAssign`,
      data: {
        ids,
        to_user_id: toUserId,
        reason: reason || ''
      }
    })
  }
}
