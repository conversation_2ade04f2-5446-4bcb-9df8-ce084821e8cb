# AUGMENT分阶段执行提示词 - CRM工作报告模块

## 🎯 执行说明

请按照以下顺序逐个执行提示词，每个阶段完成后进行验收，确认无误后再进行下一阶段。

---

## 📋 阶段一：后端功能定制

### 提示词1.1：检查生成的后端代码并添加复制功能

```
我需要你帮我检查和优化CRM工作报告模块的后端代码。

背景信息：
- 项目路径：e:\项目\self_admin\base_admin
- 已使用CRUD生成器生成了基础代码（已确认文件存在）
- 表名：crm_work_report，模块：crm
- 需要添加复制汇报功能
- MCP工具已验证可用

请执行以下任务：

1. 首先使用view工具查看生成的后端文件：
   - app/crm/controller/CrmWorkReportController.php
   - app/crm/model/CrmWorkReport.php
   - app/crm/service/CrmWorkReportService.php

2. 在CrmWorkReportService.php中添加复制功能：
   ```php
   /**
    * 复制汇报
    */
   public function copy($id)
   {
       $original = $this->detail($id);
       $data = $original->toArray();
       
       // 移除主键和时间戳
       unset($data['id'], $data['created_at'], $data['updated_at']);
       
       // 修改标题和日期
       $data['title'] = '复制-' . $data['title'];
       $data['report_date'] = date('Y-m-d');
       
       return $this->create($data);
   }
   ```

3. 在CrmWorkReportController.php中添加复制接口：
   ```php
   /**
    * 复制汇报
    */
   public function copy($id)
   {
       try {
           $result = $this->service->copy($id);
           return $this->success('复制成功', $result);
       } catch (\Exception $e) {
           return $this->error('复制失败：' . $e->getMessage());
       }
   }
   ```

4. 在CrmWorkReport.php模型中添加附件处理：
   ```php
   // 附件处理访问器
   public function getAttachmentListAttr($value, $data)
   {
       return $data['attachments'] ? json_decode($data['attachments'], true) : [];
   }
   
   // 附件处理修改器
   public function setAttachmentsAttr($value)
   {
       return is_array($value) ? json_encode($value) : $value;
   }
   ```

5. 检查路由配置，确保复制接口路由正确

请使用view工具查看文件，使用str-replace-editor工具进行修改。完成后告诉我结果。
```

### 提示词1.2：测试后端API接口

```
现在请帮我测试CRM工作报告模块的后端API接口功能。

请执行以下测试：

1. 检查后端服务是否正常运行

2. 创建测试数据，测试以下API接口：
   - GET /crm/work-report - 列表查询
   - POST /crm/work-report - 创建汇报
   - GET /crm/work-report/{id} - 详情查询
   - PUT /crm/work-report/{id} - 更新汇报
   - POST /crm/work-report/{id}/copy - 复制汇报（新增功能）
   - DELETE /crm/work-report/{id} - 删除汇报

3. 测试搜索筛选功能：
   - 按标题模糊搜索
   - 按类型精确搜索
   - 按日期范围搜索

4. 验证数据验证规则：
   - 必填字段验证（title, type, report_date, content）
   - 字段长度验证（title最大200字符）

使用适当的测试数据，记录测试结果。如果发现问题请修复。
```

---

## 🎨 阶段二：前端UI定制开发

### 提示词2.1：优化列表页面为飞书风格卡片布局

```
请帮我优化CRM工作报告模块的列表页面，实现飞书风格的卡片布局。

背景信息：
- 文件路径：frontend/src/views/crm/crm_work_report/list.vue
- 已有CRUD生成器生成的基础代码
- 需要改为卡片式布局，参考飞书设计风格

请执行以下任务：

1. 首先查看现有的list.vue文件结构

2. 保留以下生成的功能：
   - 搜索筛选栏（ArtSearchBar）
   - 操作栏（ArtTableHeader）
   - 分页组件
   - 表单对话框
   - 基础的数据获取逻辑

3. 替换表格为卡片布局，参考以下设计：
   ```vue
   <!-- 卡片列表区域 -->
   <div class="report-card-list">
     <div v-for="item in tableData" :key="item.id" class="report-card">
       <div class="card-header">
         <span class="report-date">📅 {{ item.report_date }}</span>
         <el-tag :type="getTypeColor(item.type)">{{ getTypeText(item.type) }}</el-tag>
         <span class="creator">👤 {{ item.creator_name }}</span>
       </div>
       <div class="card-title">📝 {{ item.title }}</div>
       <div class="card-content">💬 {{ item.content ? item.content.substring(0, 50) + '...' : '' }}</div>
       <div class="card-footer">
         <span class="attachment-count">📎 {{ getAttachmentCount(item.attachments) }}个附件</span>
         <span class="create-time">⏰ {{ formatTime(item.created_at) }}</span>
         <div class="actions">
           <el-button type="primary" link @click="handleDetail(item)">👁️ 查看详情</el-button>
           <el-button type="primary" link @click="handleEdit(item)">✏️ 编辑</el-button>
           <el-button type="primary" link @click="handleCopy(item.id)">📋 复制</el-button>
         </div>
       </div>
     </div>
   </div>
   ```

4. 添加飞书风格样式：
   ```css
   .report-card-list {
     display: grid;
     grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
     gap: 16px;
     margin-top: 16px;
   }
   
   .report-card {
     background: white;
     border-radius: 8px;
     padding: 16px;
     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
     transition: all 0.3s ease;
   }
   
   .report-card:hover {
     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
     transform: translateY(-2px);
   }
   ```

5. 添加复制功能的前端逻辑：
   ```javascript
   const handleCopy = async (id) => {
     try {
       await CrmWorkReportApi.copy(id)
       ElMessage.success('复制成功')
       await fetchData()
     } catch (error) {
       ElMessage.error('复制失败')
     }
   }
   ```

6. 添加辅助方法：
   - getTypeColor() - 获取类型对应的颜色
   - getTypeText() - 获取类型对应的文本
   - getAttachmentCount() - 获取附件数量
   - formatTime() - 格式化时间

请确保保持现有的数据获取和分页逻辑，只替换展示部分。
```

### 提示词2.2：优化表单页面并集成富文本编辑器

```
请帮我优化CRM工作报告模块的表单页面，集成富文本编辑器并改进布局设计。

背景信息：
- 文件路径：frontend/src/views/crm/crm_work_report/form-dialog.vue
- 需要集成富文本编辑器替换textarea
- 需要实现分区布局设计

请执行以下任务：

1. 首先检查现有的form-dialog.vue文件

2. 安装富文本编辑器依赖：
   ```bash
   npm install @vueup/vue-quill
   ```

3. 优化表单布局为分区设计：
   ```vue
   <template>
     <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1200px">
       <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
         <!-- 基本信息区域 -->
         <div class="form-section">
           <div class="section-title">📋 基本信息</div>
           <!-- 基本字段 -->
         </div>
         
         <!-- 内容区域 -->
         <div class="form-section">
           <div class="section-title">📝 汇报内容</div>
           <el-form-item label="工作内容" prop="content">
             <QuillEditor v-model:content="formData.content" content-type="html" />
           </el-form-item>
           <el-form-item label="工作总结">
             <QuillEditor v-model:content="formData.summary" content-type="html" />
           </el-form-item>
           <el-form-item label="下期计划">
             <QuillEditor v-model:content="formData.plan" content-type="html" />
           </el-form-item>
         </div>
         
         <!-- 附件区域 -->
         <div class="form-section">
           <div class="section-title">📎 附件上传</div>
           <!-- 附件上传组件 -->
         </div>
       </el-form>
       
       <template #footer>
         <el-button @click="dialogVisible = false">取消</el-button>
         <el-button @click="handleSaveDraft">💾 保存草稿</el-button>
         <el-button type="primary" @click="handleSubmit">📤 提交汇报</el-button>
       </template>
     </el-dialog>
   </template>
   ```

4. 添加富文本编辑器导入和样式：
   ```javascript
   import { QuillEditor } from '@vueup/vue-quill'
   import '@vueup/vue-quill/dist/vue-quill.snow.css'
   ```

5. 添加分区样式：
   ```css
   .form-section {
     margin-bottom: 24px;
     padding: 16px;
     background: #f7f8fa;
     border-radius: 8px;
   }
   
   .section-title {
     font-size: 16px;
     font-weight: 600;
     margin-bottom: 16px;
     color: #1f2329;
   }
   ```

6. 优化附件上传组件，支持拖拽上传

7. 添加草稿保存功能

请确保富文本编辑器正常工作，表单验证正确。
```

### 提示词2.3：创建详情页面

```
请帮我创建CRM工作报告模块的详情页面。

背景信息：
- CRUD生成器没有生成详情页面，需要新建
- 可以创建独立页面或在list.vue中添加详情抽屉
- 需要展示完整的汇报信息并支持富文本内容展示

请执行以下任务：

1. 在list.vue中添加详情抽屉组件：
   ```vue
   <!-- 详情抽屉 -->
   <el-drawer v-model="detailDrawerVisible" title="汇报详情" size="60%">
     <div class="detail-content">
       <!-- 头部信息 -->
       <div class="detail-header">
         <div class="report-info">
           <h2>{{ currentDetail.title }}</h2>
           <div class="meta-info">
             <el-tag :type="getTypeColor(currentDetail.type)">{{ getTypeText(currentDetail.type) }}</el-tag>
             <span>📅 {{ currentDetail.report_date }}</span>
             <span>👤 {{ currentDetail.creator_name }}</span>
             <span>⏰ {{ formatTime(currentDetail.created_at) }}</span>
           </div>
         </div>
         <div class="actions">
           <el-button type="primary" @click="handleEdit(currentDetail)">✏️ 编辑</el-button>
           <el-button @click="handleCopy(currentDetail.id)">📋 复制</el-button>
           <el-button type="danger" @click="handleDelete(currentDetail)">🗑️ 删除</el-button>
         </div>
       </div>
       
       <!-- 内容区域 -->
       <div class="detail-body">
         <div class="content-section">
           <h3>📝 工作内容</h3>
           <div class="rich-content" v-html="currentDetail.content"></div>
         </div>
         
         <div class="content-section" v-if="currentDetail.summary">
           <h3>📊 工作总结</h3>
           <div class="rich-content" v-html="currentDetail.summary"></div>
         </div>
         
         <div class="content-section" v-if="currentDetail.plan">
           <h3>📅 下期计划</h3>
           <div class="rich-content" v-html="currentDetail.plan"></div>
         </div>
         
         <div class="content-section" v-if="currentDetail.attachments">
           <h3>📎 附件列表</h3>
           <div class="attachment-list">
             <!-- 附件列表 -->
           </div>
         </div>
       </div>
     </div>
   </el-drawer>
   ```

2. 添加详情相关的数据和方法：
   ```javascript
   const detailDrawerVisible = ref(false)
   const currentDetail = ref({})
   
   const handleDetail = async (row) => {
     try {
       const result = await CrmWorkReportApi.detail(row.id)
       currentDetail.value = result.data
       detailDrawerVisible.value = true
     } catch (error) {
       ElMessage.error('获取详情失败')
     }
   }
   ```

3. 添加详情页面样式：
   ```css
   .detail-content {
     padding: 20px;
   }
   
   .detail-header {
     display: flex;
     justify-content: space-between;
     align-items: flex-start;
     margin-bottom: 24px;
     padding-bottom: 16px;
     border-bottom: 1px solid #e4e7ed;
   }
   
   .report-info h2 {
     margin: 0 0 8px 0;
     color: #1f2329;
   }
   
   .meta-info {
     display: flex;
     gap: 12px;
     align-items: center;
     color: #646a73;
   }
   
   .content-section {
     margin-bottom: 24px;
   }
   
   .content-section h3 {
     margin: 0 0 12px 0;
     color: #1f2329;
     font-size: 16px;
   }
   
   .rich-content {
     background: #f7f8fa;
     padding: 16px;
     border-radius: 8px;
     line-height: 1.6;
   }
   ```

4. 确保富文本内容正确显示，注意XSS防护

5. 实现附件列表展示和下载功能

请确保详情页面信息完整，操作便捷。
```

---

## 🧪 阶段三：功能完善和测试

### 提示词3.1：前端功能集成测试

```
现在请帮我测试CRM工作报告模块的前端功能。

请执行以下测试：

1. 启动前端开发服务器：
   - 进入frontend目录
   - 运行npm run dev

2. 访问页面测试基础功能：
   - 访问列表页面：http://localhost:3006/crm/work-report
   - 检查页面是否正常加载
   - 测试卡片布局是否正确显示

3. 测试CRUD操作：
   - 点击"写汇报"按钮，测试表单对话框
   - 测试富文本编辑器是否正常工作
   - 创建一个测试汇报
   - 测试编辑功能，检查数据回显
   - 测试查看详情功能
   - 测试复制功能
   - 测试删除功能

4. 测试搜索筛选：
   - 测试按标题搜索
   - 测试按类型筛选
   - 测试按日期范围筛选
   - 测试分页功能

5. 测试UI体验：
   - 检查卡片悬停效果
   - 检查响应式布局
   - 检查表单验证提示
   - 检查加载状态显示

6. 测试浏览器兼容性（如果可能）

请记录测试结果，发现问题及时修复。使用open-browser工具打开页面进行测试。
```

### 提示词3.2：性能优化和最终检查

```
请帮我对CRM工作报告模块进行最终的性能优化和代码检查。

请执行以下优化：

1. 性能检查：
   - 检查页面加载时间
   - 检查富文本编辑器性能
   - 检查内存使用情况
   - 优化不必要的重复渲染

2. 代码质量检查：
   - 使用diagnostics工具检查TypeScript类型错误
   - 检查ESLint警告
   - 检查代码格式
   - 检查注释完整性

3. 用户体验优化：
   - 添加适当的加载状态
   - 优化错误提示信息
   - 改进操作反馈
   - 检查表单验证体验

4. 功能完整性检查：
   - 确认所有CRUD操作正常
   - 确认复制功能正常
   - 确认富文本编辑器功能完整
   - 确认搜索筛选功能正确
   - 确认导入导出功能正常

5. 样式最终调整：
   - 确保飞书风格设计一致
   - 检查响应式布局
   - 优化色彩和间距
   - 确保与现有CRM系统风格统一

6. 安全检查：
   - 检查富文本内容的XSS防护
   - 检查文件上传安全
   - 检查API调用安全

请提供最终的测试报告和优化建议。
```

---

## ✅ 验收清单

每个阶段完成后，请确认以下项目：

### 阶段一验收
- [ ] 后端API接口全部正常响应
- [ ] 复制功能API测试通过
- [ ] 数据验证规则正确
- [ ] 权限控制正常

### 阶段二验收
- [ ] 列表页面卡片布局实现
- [ ] 富文本编辑器集成成功
- [ ] 表单分区布局完成
- [ ] 详情页面功能完整

### 阶段三验收
- [ ] 前后端集成测试通过
- [ ] UI设计符合飞书风格
- [ ] 性能指标达标
- [ ] 代码质量良好

## 🔧 MCP工具验证状态

### ✅ 已验证可用的MCP工具
1. **codebase-retrieval**: 代码库检索和分析 ✅
2. **view**: 查看文件和目录结构 ✅
3. **str-replace-editor**: 精确编辑代码文件 ✅
4. **save-file**: 创建新文件 ✅
5. **launch-process**: 启动开发服务器 ✅
6. **read-process**: 读取进程输出 ✅
7. **open-browser**: 打开浏览器测试 ✅
8. **diagnostics**: 检查代码问题 ✅

### 🗂️ 项目环境信息
- **项目路径**: `e:\项目\self_admin\base_admin`
- **前端路径**: `frontend/src/`
- **后端路径**: `app/crm/`
- **数据库**: MySQL (已配置MCP连接)
- **开发服务器**: 前端 localhost:3006, 后端 www.bs.com

### 📋 CRUD生成器状态确认
✅ **已完成的生成文件**:
- `app/crm/controller/CrmWorkReportController.php` - 控制器已生成
- `app/crm/model/CrmWorkReport.php` - 模型已生成
- `app/crm/service/CrmWorkReportService.php` - 服务类已生成
- `frontend/src/views/crm/crm_work_report/list.vue` - 列表页面已生成
- `frontend/src/views/crm/crm_work_report/form-dialog.vue` - 表单页面已生成
- `frontend/src/views/crm/crm_work_report/import-export-dialog.vue` - 导入导出已生成

## 🎯 最终目标

完成后应该实现：
1. 功能完整的CRM工作报告模块
2. 飞书风格的现代化UI设计
3. 流畅的用户体验
4. 高质量的代码实现
