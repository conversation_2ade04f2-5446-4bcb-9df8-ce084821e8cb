<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑联系人' : '新增联系人'"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div v-loading="loading" class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="formData.gender" placeholder="请选择性别">
                <el-option label="保密" :value="0" />
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input v-model="formData.position" placeholder="请输入职位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门" prop="department">
              <el-input v-model="formData.department" placeholder="请输入部门" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="formData.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信" prop="wechat">
              <el-input v-model="formData.wechat" placeholder="请输入微信号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重要程度" prop="importance">
              <el-select v-model="formData.importance" placeholder="请选择重要程度">
                <el-option label="普通" :value="0" />
                <el-option label="重要" :value="1" />
                <el-option label="核心" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色类型" prop="role_type">
              <el-select v-model="formData.role_type" placeholder="请选择角色类型">
                <el-option label="决策者" value="decision" />
                <el-option label="使用者" value="user" />
                <el-option label="影响者" value="influence" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生日" prop="birthday">
              <el-date-picker
                v-model="formData.birthday"
                type="date"
                placeholder="请选择生日"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主要联系人" prop="is_primary">
              <el-switch v-model="formData.is_primary" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="地址" prop="address">
          <el-input v-model="formData.address" type="textarea" :rows="2" placeholder="请输入地址" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number
    contactId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: 0,
    contactId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const isEdit = computed(() => !!props.contactId)

  // 表单数据
  const formData = reactive({
    name: '',
    gender: 0,
    position: '',
    department: '',
    mobile: '',
    phone: '',
    email: '',
    wechat: '',
    qq: '',
    importance: 0,
    role_type: '',
    birthday: null,
    address: '',
    remark: '',
    is_primary: 0
  })

  // 表单验证规则
  const formRules: FormRules = {
    name: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
    mobile: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }]
  }

  // 监听联系人ID变化，加载联系人详情
  watch(
    () => props.contactId,
    (newId) => {
      if (newId && props.modelValue) {
        loadContactDetail()
      }
    },
    { immediate: true }
  )

  // 监听对话框显示状态
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal && props.contactId) {
        loadContactDetail()
      } else if (newVal && !props.contactId) {
        // 新增模式，重置表单
        resetFormData()
      }
    }
  )

  // 加载联系人详情
  const loadContactDetail = async () => {
    if (!props.contactId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getContactDetail(props.contactId)

      if (res.code === ApiStatus.success) {
        const data = res.data
        Object.assign(formData, {
          name: data.name || '',
          gender: data.gender || 0,
          position: data.position || '',
          department: data.department || '',
          mobile: data.mobile || '',
          phone: data.phone || '',
          email: data.email || '',
          wechat: data.wechat || '',
          qq: data.qq || '',
          importance: data.importance || 0,
          role_type: data.role_type || '',
          birthday: data.birthday || null,
          address: data.address || '',
          remark: data.remark || '',
          is_primary: data.is_primary || 0
        })
      } else {
        ElMessage.error(res.message || '加载联系人详情失败')
      }
    } catch (error) {
      console.error('加载联系人详情失败:', error)
      ElMessage.error('加载联系人详情失败')
    } finally {
      loading.value = false
    }
  }

  // 重置表单数据
  const resetFormData = () => {
    Object.assign(formData, {
      name: '',
      gender: 0,
      position: '',
      department: '',
      mobile: '',
      phone: '',
      email: '',
      wechat: '',
      qq: '',
      importance: 0,
      role_type: '',
      birthday: null,
      address: '',
      remark: '',
      is_primary: 0
    })
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editContact(props.contactId!, formData)
      } else {
        res = await CrmCustomerDetailApi.addContact(props.customerId!, formData)
      }

      if (res.code === ApiStatus.success) {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        visible.value = false
        emit('success')
      } else {
        ElMessage.error(res.message || '操作失败')
      }
    } catch (error) {
      console.error('提交表单失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭处理
  const handleClosed = () => {
    formRef.value?.resetFields()
    resetFormData()
  }
</script>

<style scoped lang="scss">
  .form-container {
    min-height: 200px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
