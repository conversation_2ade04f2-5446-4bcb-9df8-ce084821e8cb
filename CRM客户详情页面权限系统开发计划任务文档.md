# CRM客户详情页面权限系统开发计划任务文档

## 📋 项目信息

**项目名称**：CRM客户详情页面权限系统
**项目周期**：预计18个工作日
**开发人员**：后端开发1人 + 前端开发1人
**项目优先级**：高

## 🎯 项目目标

1. 为客户详情页面添加22个细粒度按钮权限（21个核心功能 + 1个回收客户）
2. 实现安全的客户数据访问控制
3. 优化代码结构，避免控制器过长
4. 为客户转移和共享功能预留完整框架
5. 完善前端权限控制和API对接
6. 暂缓实施商机功能和客户转移/共享功能

## 📅 开发计划总览

### 阶段一：基础架构搭建 (3天)
- 数据库权限配置
- 权限验证服务开发
- 控制器架构重构

### 阶段二：后端API开发 (6天)
- 联系人操作API (1.5天)
- 合同操作API (2天)
- 回款操作API (2天)
- 跟进记录API (0.5天)

### 阶段三：前端权限集成 (4天)
- 权限指令优化
- 详情页面权限控制
- API接口对接

### 阶段四：前端代码深度对接 (3天)
- 权限指令系统优化
- 详情页面组件权限集成
- API调用模式统一
- 组件数据加载优化
- 事件处理机制优化

### 阶段五：测试与优化 (2天)
- 功能测试
- 权限测试
- 性能优化

## 📋 详细任务清单

### 🏗️ 阶段一：基础架构搭建

#### 任务1.1：数据库权限配置 (0.5天)
**负责人**：后端开发
**任务内容**：
- [ ] 执行权限SQL文件 `crm_customer_detail_permissions.sql`
- [ ] 验证22个权限是否正确添加到system_menu表（21个核心功能 + 1个回收客户）
- [ ] 确认预留权限（转移客户、共享客户）已注释保存
- [ ] 配置测试角色的权限分配
- [ ] 验证权限中间件是否正常工作

**交付物**：
- 权限配置完成确认文档
- 测试角色权限配置截图

#### 任务1.2：权限验证服务开发 (1天)
**负责人**：后端开发  
**任务内容**：
- [ ] 创建 `CustomerPermissionService` 权限验证服务
- [ ] 实现客户访问权限验证方法
- [ ] 实现数据权限范围验证
- [ ] 为共享权限预留接口

**关键方法**：
```php
// app/crm/service/CustomerPermissionService.php
class CustomerPermissionService
{
    public function validateCustomerAccess(int $customerId, string $operation): bool
    public function hasSharedAccess(int $customerId, int $userId): bool  // 预留
    public function checkSharedPermission(int $customerId, int $userId, string $operation): bool  // 预留
    public function inDataPermissionScope(int $creatorId, int $userId): bool
}
```

#### 任务1.3：权限验证中间件开发 (0.5天)
**负责人**：后端开发  
**任务内容**：
- [ ] 创建 `CustomerAccessMiddleware` 数据权限中间件
- [ ] 集成到路由配置中
- [ ] 测试中间件权限拦截功能

**文件位置**：
- `app/common/middleware/CustomerAccessMiddleware.php`

#### 任务1.4：控制器架构重构 (1天)
**负责人**：后端开发  
**任务内容**：
- [ ] 创建 `CrmCustomerDetailController` 详情操作控制器
- [ ] 创建功能Trait文件
  - [ ] `CustomerContactTrait` - 联系人操作
  - [ ] `CustomerContractTrait` - 合同操作
  - [ ] `CustomerReceivableTrait` - 回款操作
  - [ ] `CustomerFollowTrait` - 跟进操作
- [ ] 配置新的路由组

**文件结构**：
```
app/crm/controller/
├── CrmCustomerDetailController.php
└── traits/
    ├── CustomerContactTrait.php
    ├── CustomerContractTrait.php
    ├── CustomerReceivableTrait.php
    └── CustomerFollowTrait.php
```

### 🔧 阶段二：后端API开发

#### 任务2.1：联系人操作API开发 (1.5天)
**负责人**：后端开发  
**任务内容**：
- [ ] `addContact()` - 新增联系人
- [ ] `editContact()` - 编辑联系人  
- [ ] `deleteContact()` - 删除联系人
- [ ] `getContactList()` - 获取联系人列表

**权限验证要求**：
- 验证客户访问权限
- 验证联系人归属关系
- 记录操作日志

**API路径**：
```
POST /api/crm/customer_detail/add_contact/{customer_id}
PUT  /api/crm/customer_detail/edit_contact/{contact_id}
DELETE /api/crm/customer_detail/delete_contact/{contact_id}
GET  /api/crm/customer_detail/contact_list/{customer_id}
```

#### 任务2.2：合同操作API开发 (2天)
**负责人**：后端开发  
**任务内容**：
- [ ] `addContract()` - 新增合同
- [ ] `editContract()` - 编辑合同
- [ ] `deleteContract()` - 删除合同
- [ ] `getContractDetail()` - 合同详情
- [ ] `getContractList()` - 合同列表
- [ ] `submitApproval()` - 提交审批

**特殊处理**：
- 合同状态验证
- 审批流程集成
- 合同编号自动生成

#### 任务2.3：回款操作API开发 (2天)
**负责人**：后端开发  
**任务内容**：
- [ ] `addReceivable()` - 新增回款
- [ ] `editReceivable()` - 编辑回款
- [ ] `deleteReceivable()` - 删除回款
- [ ] `getReceivableDetail()` - 回款详情
- [ ] `getReceivableList()` - 回款列表
- [ ] `submitReceivableApproval()` - 提交回款审批
- [ ] `addReceivableMore()` - 更多操作新增回款

**特殊处理**：
- 回款金额验证
- 合同关联验证
- 审批状态控制

#### 任务2.4：跟进记录API开发 (0.5天)
**负责人**：后端开发  
**任务内容**：
- [ ] `addFollow()` - 新增跟进
- [ ] `editFollow()` - 编辑跟进
- [ ] `deleteFollow()` - 删除跟进
- [ ] `getFollowDetail()` - 跟进详情

**特殊处理**：
- 跟进记录关联类型处理
- 附件上传支持
- 下次跟进时间提醒

### 🎨 阶段三：前端权限集成

#### 任务3.1：权限指令优化 (0.5天)
**负责人**：前端开发  
**任务内容**：
- [ ] 优化 `v-permission` 指令
- [ ] 添加客户访问权限验证指令
- [ ] 实现权限缓存机制

#### 任务3.2：详情页面权限控制 (2天)
**负责人**：前端开发  
**任务内容**：
- [ ] 联系人面板权限控制
- [ ] 合同面板权限控制
- [ ] 回款面板权限控制
- [ ] 跟进记录面板权限控制
- [ ] 客户操作按钮权限控制

**权限控制示例**：
```vue
<!-- 联系人操作按钮 -->
<el-button v-permission="'crm:crm_customer_my:add_contact'" 
           v-if="hasCustomerAccess(customerId, 'edit')"
           @click="handleAddContact">
  新增联系人
</el-button>
```

#### 任务3.3：API接口对接 (1.5天)
**负责人**：前端开发
**任务内容**：
- [ ] 创建详情操作API接口文件
- [ ] 对接联系人操作接口
- [ ] 对接合同操作接口
- [ ] 对接回款操作接口
- [ ] 对接跟进记录接口

**API文件**：
```typescript
// frontend/src/api/crm/crmCustomerDetail.ts
export class CrmCustomerDetailApi {
  // 联系人操作
  static addContact(customerId: number, data: any)
  static editContact(contactId: number, data: any)
  static deleteContact(contactId: number)
  static getContactList(customerId: number)

  // 合同操作
  static addContract(customerId: number, data: any)
  // ... 其他方法
}
```

### 🔧 阶段四：前端代码深度分析与对接

#### 任务4.1：权限指令系统优化 (1天)
**负责人**：前端开发
**发现的问题**：
- 现有权限指令为 `v-auth`，需要统一为 `v-permission`
- 权限验证基于路由meta.authList，需要扩展支持客户数据权限
- 缺少组合权限验证（功能权限+数据权限）

**任务内容**：
- [ ] 扩展权限指令支持数据权限验证
- [ ] 创建客户访问权限验证composable
- [ ] 统一权限指令命名和使用方式

**实现方案**：
```typescript
// frontend/src/composables/useCustomerPermission.ts
export const useCustomerPermission = () => {
  const hasCustomerAccess = (customerId: number, operation: string): boolean => {
    // 验证客户数据权限
    return true // 实现逻辑
  }

  const hasPermissionAndAccess = (permission: string, customerId?: number, operation?: string): boolean => {
    // 组合权限验证：功能权限 + 数据权限
    return hasAuth(permission) && (!customerId || hasCustomerAccess(customerId, operation))
  }

  return { hasCustomerAccess, hasPermissionAndAccess }
}
```

#### 任务4.2：详情页面组件权限集成 (2天)
**负责人**：前端开发
**基于代码分析发现**：
- CustomerDetailDrawer使用CrmDetailDrawer作为基础组件
- 各面板组件通过emit('action')方式触发操作
- 现有按钮缺少权限控制指令

**任务内容**：
- [ ] 为CustomerDetailDrawer头部操作按钮添加权限控制
- [ ] 为各面板组件的操作按钮添加权限控制
- [ ] 实现权限状态的响应式更新

**具体修改点**：
```vue
<!-- CustomerDetailDrawer/index.vue 头部操作 -->
<div class="customer-actions">
  <el-button v-permission="'crm:crm_customer_my:transfer_customer'"
             v-if="hasCustomerAccess(customerId, 'transfer')"
             @click="handleTransfer">转移客户</el-button>
  <el-button v-permission="'crm:crm_customer_my:share_customer'"
             v-if="hasCustomerAccess(customerId, 'share')"
             @click="handleShare">共享客户</el-button>
  <el-button v-permission="'crm:crm_customer_my:recycle_customer'"
             v-if="hasCustomerAccess(customerId, 'recycle')"
             @click="handleMoveToSea">回收</el-button>
</div>

<!-- CustomerContactPanel.vue 联系人操作 -->
<el-button v-permission="'crm:crm_customer_my:add_contact'"
           v-if="hasCustomerAccess(businessId, 'edit')"
           @click="handleAddContact">新增联系人</el-button>

<!-- CustomerContractPanel.vue 合同操作 -->
<ArtButtonTable v-permission="'crm:crm_customer_my:contract_detail'"
                text="详情" @click="handleViewContract(row)" />
<ArtButtonTable v-permission="'crm:crm_customer_my:edit_contract'"
                v-if="canEditContract(row)"
                text="编辑" @click="handleEditContract(row)" />
```

#### 任务4.3：API调用模式统一 (1天)
**负责人**：前端开发
**基于现有API分析**：
- 使用统一的request工具，支持BaseResult和PaginationResult类型
- API调用采用静态方法模式
- 错误处理通过try-catch和ElMessage实现

**任务内容**：
- [ ] 创建CrmCustomerDetailApi接口文件
- [ ] 替换各面板的模拟数据为真实API调用
- [ ] 统一错误处理和加载状态管理

**API接口设计**：
```typescript
// frontend/src/api/crm/crmCustomerDetail.ts
import request from '@/utils/http'
import { BaseResult, PaginationResult } from '@/types/axios'

export class CrmCustomerDetailApi {
  // 联系人操作
  static addContact(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_contact/${customerId}`,
      data
    })
  }

  static editContact(contactId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_contact/${contactId}`,
      data
    })
  }

  static deleteContact(contactId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_contact/${contactId}`
    })
  }

  static getContactList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/contact_list/${customerId}`,
      params
    })
  }

  // 合同操作
  static addContract(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_contract/${customerId}`,
      data
    })
  }

  static editContract(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_contract/${contractId}`,
      data
    })
  }

  static deleteContract(contractId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_contract/${contractId}`
    })
  }

  static getContractDetail(contractId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/contract_detail/${contractId}`
    })
  }

  static getContractList(customerId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/contract_list/${customerId}`,
      params
    })
  }

  static submitApproval(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/submit_approval/${contractId}`,
      data
    })
  }

  // 回款操作
  static addReceivable(contractId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_receivable/${contractId}`,
      data
    })
  }

  static editReceivable(receivableId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_receivable/${receivableId}`,
      data
    })
  }

  static deleteReceivable(receivableId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_receivable/${receivableId}`
    })
  }

  static getReceivableDetail(receivableId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/receivable_detail/${receivableId}`
    })
  }

  static getReceivableList(contractId: number, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: `/crm/customer_detail/receivable_list/${contractId}`,
      params
    })
  }

  static submitReceivableApproval(receivableId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/submit_receivable_approval/${receivableId}`,
      data
    })
  }

  // 跟进记录操作
  static addFollow(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/add_follow/${customerId}`,
      data
    })
  }

  static editFollow(followId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/edit_follow/${followId}`,
      data
    })
  }

  static deleteFollow(followId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/delete_follow/${followId}`
    })
  }

  static getFollowDetail(followId: number) {
    return request.get<BaseResult>({
      url: `/crm/customer_detail/follow_detail/${followId}`
    })
  }

  // 客户操作
  static transferCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/transfer_customer/${customerId}`,
      data
    })
  }

  static shareCustomer(customerId: number, data: any) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/share_customer/${customerId}`,
      data
    })
  }

  static recycleCustomer(customerId: number) {
    return request.post<BaseResult>({
      url: `/crm/customer_detail/recycle_customer/${customerId}`
    })
  }
}
```

#### 任务4.4：组件数据加载优化 (1天)
**负责人**：前端开发
**基于现有组件分析**：
- CustomerContactPanel、CustomerContractPanel等使用模拟数据
- 缺少统一的加载状态管理
- 需要实现数据刷新机制

**任务内容**：
- [ ] 替换所有面板的模拟数据为真实API调用
- [ ] 实现统一的加载状态和错误处理
- [ ] 添加数据刷新和乐观更新机制

**实现示例**：
```vue
<!-- CustomerContactPanel.vue 数据加载优化 -->
<script setup lang="ts">
import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
import { ApiStatus } from '@/enums/apiEnum'

// 加载联系人列表
const loadContacts = async () => {
  if (!props.businessId) return

  loading.value = true
  try {
    const res = await CrmCustomerDetailApi.getContactList(props.businessId, {
      page: currentPage.value,
      limit: pageSize.value
    })

    if (res.code === ApiStatus.success) {
      contacts.value = res.list || []
      total.value = res.total || 0
    } else {
      ElMessage.error(res.message || '加载联系人失败')
    }
  } catch (error) {
    console.error('加载联系人失败:', error)
    ElMessage.error('加载联系人失败')
  } finally {
    loading.value = false
  }
}

// 删除联系人
const handleDeleteContact = async (contact: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除联系人"${contact.name}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await CrmCustomerDetailApi.deleteContact(contact.id)

    if (res.code === ApiStatus.success) {
      ElMessage.success('删除成功')
      loadContacts() // 刷新列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除联系人失败:', error)
      ElMessage.error('删除失败')
    }
  }
}
</script>
```

#### 任务4.5：事件处理机制优化 (0.5天)
**负责人**：前端开发
**基于现有代码分析**：
- 各面板通过emit('action')触发操作
- 主组件通过handleAction统一处理
- 需要扩展支持权限验证和API调用

**任务内容**：
- [ ] 扩展handleAction方法支持新增的24个操作
- [ ] 实现操作前的权限验证
- [ ] 添加操作成功后的数据刷新机制

**实现方案**：
```typescript
// CustomerDetailDrawer/index.vue
const handleAction = async (action: string, data?: any) => {
  // 权限验证
  if (!hasPermissionAndAccess(getPermissionByAction(action), customerId, getOperationByAction(action))) {
    ElMessage.error('无权限执行此操作')
    return
  }

  switch (action) {
    case 'add-contact':
      await handleAddContact(data)
      break
    case 'edit-contact':
      await handleEditContact(data)
      break
    case 'delete-contact':
      await handleDeleteContact(data)
      break
    // ... 其他操作
    default:
      console.log('未知操作:', action, data)
  }
}

// 权限映射函数
const getPermissionByAction = (action: string): string => {
  const permissionMap: Record<string, string> = {
    'add-contact': 'crm:crm_customer_my:add_contact',
    'edit-contact': 'crm:crm_customer_my:edit_contact',
    'delete-contact': 'crm:crm_customer_my:delete_contact',
    // ... 其他映射
  }
  return permissionMap[action] || ''
}
```

### 🧪 阶段五：测试与优化

#### 任务5.1：功能测试 (1天)
**负责人**：后端开发 + 前端开发
**任务内容**：
- [ ] 联系人操作功能测试
- [ ] 合同操作功能测试
- [ ] 回款操作功能测试
- [ ] 跟进记录功能测试
- [ ] 客户操作功能测试

#### 任务5.2：权限测试 (0.5天)
**负责人**：后端开发
**任务内容**：
- [ ] 不同角色权限测试
- [ ] 数据权限边界测试
- [ ] 权限拦截功能测试
- [ ] 错误处理测试

#### 任务5.3：性能优化 (0.5天)
**负责人**：后端开发
**任务内容**：
- [ ] 权限验证性能优化
- [ ] 数据库查询优化
- [ ] 缓存机制实施
- [ ] 接口响应时间优化

## 📊 进度跟踪

### 里程碑节点
- [ ] **里程碑1** (第3天)：基础架构搭建完成
- [ ] **里程碑2** (第9天)：后端API开发完成
- [ ] **里程碑3** (第13天)：前端权限集成完成
- [ ] **里程碑4** (第16天)：前端代码深度对接完成
- [ ] **里程碑5** (第18天)：测试与优化完成

### 风险评估
| 风险项 | 风险等级 | 应对措施 |
|--------|----------|----------|
| 权限逻辑复杂度超预期 | 中 | 预留1天缓冲时间 |
| 前端组件权限集成复杂 | 中 | 分步骤实施，优先核心功能 |
| 前后端接口对接问题 | 低 | 提前进行接口设计评审 |
| 现有代码重构风险 | 中 | 保持向后兼容，渐进式改进 |
| 性能问题 | 低 | 分阶段性能测试 |

### 质量标准
- 代码覆盖率 ≥ 80%
- 接口响应时间 ≤ 500ms
- 权限验证准确率 = 100%
- 无安全漏洞
- 前端组件权限控制覆盖率 = 100%

### 关键技术难点
1. **权限指令扩展**：需要支持数据权限验证，不仅仅是功能权限
2. **组件事件处理**：统一24个操作的事件处理和权限验证
3. **API调用优化**：替换模拟数据，实现真实的数据交互
4. **错误处理统一**：建立统一的错误处理和用户反馈机制
5. **性能优化**：权限验证和数据加载的性能优化

## 🎨 前端对接详细清单

### 📁 文件修改清单

#### 新增文件
- [ ] `frontend/src/api/crm/crmCustomerDetail.ts` - 客户详情操作API
- [ ] `frontend/src/composables/useCustomerPermission.ts` - 客户权限验证组合式函数
- [ ] `frontend/src/directives/customerPermission.ts` - 客户权限指令扩展

#### 修改文件
- [ ] `frontend/src/components/custom/CustomerDetailDrawer/index.vue` - 主组件权限集成
- [ ] `frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerContactPanel.vue` - 联系人面板
- [ ] `frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerContractPanel.vue` - 合同面板
- [ ] `frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerFollowPanel.vue` - 跟进面板
- [ ] `frontend/src/directives/permission.ts` - 权限指令优化

### 🔧 组件权限控制详细清单

#### CustomerDetailDrawer/index.vue 主组件
**头部操作按钮权限控制**：
- [ ] 回收客户按钮 - `v-permission="'crm:crm_customer_my:recycle_customer'"`
- [ ] 转移客户按钮 - 🔄 预留（暂不实施）
- [ ] 共享客户按钮 - 🔄 预留（暂不实施）

**事件处理扩展**：
- [ ] 扩展handleAction方法支持22个新操作（21个核心功能 + 1个回收客户）
- [ ] 添加权限验证逻辑
- [ ] 实现操作成功后的数据刷新
- [ ] 为预留功能保留事件处理接口

#### CustomerContactPanel.vue 联系人面板
**按钮权限控制**：
- [ ] 新增联系人 - `v-permission="'crm:crm_customer_my:add_contact'"`
- [ ] 编辑联系人 - `v-permission="'crm:crm_customer_my:edit_contact'"`
- [ ] 删除联系人 - `v-permission="'crm:crm_customer_my:delete_contact'"`
- [ ] 联系人列表 - `v-permission="'crm:crm_customer_my:contact_list'"`

**数据加载优化**：
- [ ] 替换模拟数据为CrmCustomerDetailApi.getContactList()
- [ ] 实现分页加载和搜索功能
- [ ] 添加加载状态和错误处理

**操作功能实现**：
- [ ] 新增联系人弹窗和API调用
- [ ] 编辑联系人功能和API调用
- [ ] 删除联系人确认和API调用
- [ ] 操作成功后的列表刷新

#### CustomerContractPanel.vue 合同面板
**按钮权限控制**：
- [ ] 新增合同 - `v-permission="'crm:crm_customer_my:add_contract'"`
- [ ] 合同详情 - `v-permission="'crm:crm_customer_my:contract_detail'"`
- [ ] 编辑合同 - `v-permission="'crm:crm_customer_my:edit_contract'"`
- [ ] 删除合同 - `v-permission="'crm:crm_customer_my:delete_contract'"`
- [ ] 合同列表 - `v-permission="'crm:crm_customer_my:contract_list'"`
- [ ] 提交审批 - `v-permission="'crm:crm_customer_my:submit_approval'"`

**更多操作下拉菜单权限**：
- [ ] 新增回款 - `v-permission="'crm:crm_customer_my:add_receivable'"`
- [ ] 编辑回款 - `v-permission="'crm:crm_customer_my:edit_receivable'"`
- [ ] 删除回款 - `v-permission="'crm:crm_customer_my:delete_receivable'"`
- [ ] 回款详情 - `v-permission="'crm:crm_customer_my:receivable_detail'"`
- [ ] 回款列表 - `v-permission="'crm:crm_customer_my:receivable_list'"`
- [ ] 提交审批(回款) - `v-permission="'crm:crm_customer_my:submit_receivable_approval'"`

**数据加载和操作**：
- [ ] 替换模拟数据为真实API调用
- [ ] 实现合同CRUD操作
- [ ] 实现回款CRUD操作
- [ ] 实现审批流程对接

#### CustomerFollowPanel.vue 跟进面板
**按钮权限控制**：
- [ ] 新增跟进 - `v-permission="'crm:crm_customer_my:add_follow'"`
- [ ] 跟进详情 - `v-permission="'crm:crm_customer_my:follow_detail'"`
- [ ] 编辑跟进 - `v-permission="'crm:crm_customer_my:edit_follow'"`
- [ ] 删除跟进 - `v-permission="'crm:crm_customer_my:delete_follow'"`

**功能实现**：
- [ ] 跟进记录列表加载
- [ ] 跟进记录CRUD操作
- [ ] 附件上传功能
- [ ] 跟进提醒功能

### 🔌 API接口对接清单

#### CrmCustomerDetailApi接口实现
**联系人操作接口**：
- [ ] `addContact(customerId, data)` - 新增联系人
- [ ] `editContact(contactId, data)` - 编辑联系人
- [ ] `deleteContact(contactId)` - 删除联系人
- [ ] `getContactList(customerId, params)` - 获取联系人列表

**合同操作接口**：
- [ ] `addContract(customerId, data)` - 新增合同
- [ ] `editContract(contractId, data)` - 编辑合同
- [ ] `deleteContract(contractId)` - 删除合同
- [ ] `getContractDetail(contractId)` - 合同详情
- [ ] `getContractList(customerId, params)` - 合同列表
- [ ] `submitApproval(contractId, data)` - 提交审批

**回款操作接口**：
- [ ] `addReceivable(contractId, data)` - 新增回款
- [ ] `editReceivable(receivableId, data)` - 编辑回款
- [ ] `deleteReceivable(receivableId)` - 删除回款
- [ ] `getReceivableDetail(receivableId)` - 回款详情
- [ ] `getReceivableList(contractId, params)` - 回款列表
- [ ] `submitReceivableApproval(receivableId, data)` - 提交回款审批

**跟进记录接口**：
- [ ] `addFollow(customerId, data)` - 新增跟进
- [ ] `editFollow(followId, data)` - 编辑跟进
- [ ] `deleteFollow(followId)` - 删除跟进
- [ ] `getFollowDetail(followId)` - 跟进详情

**客户操作接口**：
- [ ] `recycleCustomer(customerId)` - 回收客户

**预留接口（暂不实施）**：
- [ ] `transferCustomer(customerId, data)` - 转移客户 🔄
- [ ] `shareCustomer(customerId, data)` - 共享客户 🔄

### 🎯 权限验证逻辑实现

#### useCustomerPermission组合式函数
- [ ] `hasCustomerAccess(customerId, operation)` - 客户数据权限验证
- [ ] `hasPermissionAndAccess(permission, customerId, operation)` - 组合权限验证
- [ ] `canEditRecord(record, userId)` - 记录编辑权限验证
- [ ] `canDeleteRecord(record, userId)` - 记录删除权限验证

#### 权限验证规则
- [ ] 客户负责人权限验证
- [ ] 记录创建人权限验证
- [ ] 共享权限验证（预留）
- [ ] 数据范围权限验证

### 📱 用户体验优化

#### 加载状态管理
- [ ] 统一的loading状态控制
- [ ] 骨架屏加载效果
- [ ] 数据为空时的空状态展示

#### 错误处理机制
- [ ] API调用错误统一处理
- [ ] 用户友好的错误提示
- [ ] 网络异常重试机制

#### 操作反馈
- [ ] 操作成功提示
- [ ] 操作失败提示
- [ ] 操作确认对话框
- [ ] 乐观更新机制

### 🧪 前端测试清单

#### 权限控制测试
- [ ] 不同角色用户的按钮显示测试
- [ ] 权限边界测试
- [ ] 数据权限验证测试

#### 功能测试
- [ ] 各操作按钮的功能测试
- [ ] API调用和数据展示测试
- [ ] 错误处理测试

#### 用户体验测试
- [ ] 加载状态测试
- [ ] 操作反馈测试
- [ ] 响应式布局测试

## 📝 交付清单

### 代码交付
- [ ] 后端控制器和服务类
- [ ] 前端权限控制组件
- [ ] API接口文档
- [ ] 数据库变更脚本

### 文档交付
- [ ] 权限配置说明文档
- [ ] API接口文档
- [ ] 部署说明文档
- [ ] 测试报告

### 配置交付
- [ ] 权限配置SQL脚本
- [ ] 路由配置文件
- [ ] 中间件配置

---

**文档版本**：v1.0  
**创建时间**：2025-01-14  
**最后更新**：2025-01-14  
**项目经理**：待定  
**技术负责人**：待定
