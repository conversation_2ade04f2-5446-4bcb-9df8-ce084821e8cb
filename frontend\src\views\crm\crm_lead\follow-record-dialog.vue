<script setup lang="ts">
  import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
  import { CrmLeadApi } from '@/api/crm/crmLead'
  import { ApiStatus } from '@/utils/http/status'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或view
  const loading = ref(false)
  const leadId = ref(0)
  const leadInfo = ref<any>({})

  // 表单引用
  const formRef = ref<FormInstance>()

  // 跟进记录列表
  const followRecords = ref<any[]>([])
  const recordsLoading = ref(false)

  // 表单数据
  const formData = reactive({
    follow_type: '',
    content: '',
    follow_date: '',
    next_plan: '',
    next_date: '',
    attachments: ''
  })

  // 重置表单数据的默认值
  const getDefaultFormData = () => ({
    follow_type: '',
    content: '',
    follow_date: '',
    next_plan: '',
    next_date: '',
    attachments: ''
  })

  // 表单验证规则
  const formRules = reactive({
    follow_type: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
    content: [
      { required: true, message: '请输入跟进内容', trigger: 'blur' },
      { min: 5, max: 500, message: '跟进内容长度在 5 到 500 个字符', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.trim().length < 5) {
            callback(new Error('跟进内容不能只包含空格'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    follow_date: [
      { required: true, message: '请选择跟进时间', trigger: 'change' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && new Date(value) > new Date()) {
            callback(new Error('跟进时间不能晚于当前时间'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    next_date: [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && new Date(value) <= new Date()) {
            callback(new Error('下次跟进时间必须晚于当前时间'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ]
  })

  // 跟进方式选项
  const followTypeOptions = [
    { label: '电话', value: 'phone' },
    { label: '拜访', value: 'visit' },
    { label: '邮件', value: 'email' },
    { label: '微信', value: 'wechat' },
    { label: '其他', value: 'other' }
  ]

  // 对话框标题
  const dialogTitle = computed(() => {
    if (dialogType.value === 'add') {
      return '添加跟进记录'
    }
    return '跟进记录'
  })

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, getDefaultFormData())
    formRef.value?.resetFields()
  }

  // 打开对话框
  const openDialog = async (type: string, id: number, leadData?: any) => {
    dialogType.value = type
    leadId.value = id
    leadInfo.value = leadData || {}
    dialogVisible.value = true

    if (type === 'add') {
      resetForm()
      // 设置默认跟进时间为当前时间
      formData.follow_date = new Date().toISOString().slice(0, 16)
    }

    // 加载跟进记录列表
    await loadFollowRecords()
  }

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false
    resetForm()
    followRecords.value = []
  }

  // 加载跟进记录列表
  const loadFollowRecords = async () => {
    if (!leadId.value) return

    recordsLoading.value = true
    try {
      const res = await CrmLeadApi.getFollowRecords(leadId.value, {
        page: 1,
        limit: 50
      })

      if (res.code === ApiStatus.success) {
        followRecords.value = res.data.list || []
      }
    } catch (error) {
      console.error('加载跟进记录失败:', error)
    } finally {
      recordsLoading.value = false
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      // 确认提交
      await ElMessageBox.confirm('确定要添加这条跟进记录吗？', '确认提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })

      loading.value = true

      const res = await CrmLeadApi.addFollowRecord(leadId.value, formData)

      if (res.code === ApiStatus.success) {
        ElMessage.success('跟进记录添加成功')
        emit('success')
        resetForm()
        await loadFollowRecords() // 重新加载记录列表
      } else {
        ElMessage.error(res.message || '添加失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 格式化跟进方式
  const formatFollowType = (type: string) => {
    const option = followTypeOptions.find((item) => item.value === type)
    return option ? option.label : type
  }

  // 格式化时间
  const formatDateTime = (dateTime: string) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 获取跟进类型标签类型
  const getFollowTypeTagType = (
    type: string
  ): 'primary' | 'success' | 'info' | 'warning' | 'danger' => {
    const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
      phone: 'primary',
      visit: 'success',
      email: 'info',
      wechat: 'warning',
      other: 'danger'
    }
    return typeMap[type] || 'info'
  }

  // 暴露方法给父组件
  defineExpose({
    openDialog,
    closeDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    top="5vh"
    destroy-on-close
    @close="closeDialog"
    class="follow-record-dialog-wrapper"
  >
    <div class="dialog-content">
      <div class="follow-record-dialog">
        <!-- 线索信息 -->
        <div class="lead-info">
          <h4>线索信息</h4>
          <div class="info-row">
            <span class="label">线索姓名：</span>
            <span class="value">{{ leadInfo.lead_name || '-' }}</span>
            <span class="label">公司名称：</span>
            <span class="value">{{ leadInfo.company || '-' }}</span>
            <span class="label">联系电话：</span>
            <span class="value">{{ leadInfo.mobile || leadInfo.phone || '-' }}</span>
          </div>
        </div>

        <!-- 主要内容区域：左右布局 -->
        <div class="main-content">
          <!-- 左侧：添加跟进记录表单 -->
          <div class="left-panel">
            <h4 style="margin-bottom: 15px">添加跟进记录</h4>
            <div v-if="dialogType === 'add'" class="add-form">
              <ElForm
                ref="formRef"
                :model="formData"
                :rules="formRules"
                label-width="100px"
                label-position="left"
              >
                <ElFormItem label="跟进方式" prop="follow_type">
                  <ElSelect
                    v-model="formData.follow_type"
                    placeholder="请选择跟进方式"
                    style="width: 100%"
                  >
                    <ElOption
                      v-for="option in followTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </ElSelect>
                </ElFormItem>

                <ElFormItem label="跟进时间" prop="follow_date">
                  <ElDatePicker
                    v-model="formData.follow_date"
                    type="datetime"
                    placeholder="请选择跟进时间"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm"
                    value-format="YYYY-MM-DD HH:mm:ss"
                  />
                </ElFormItem>

                <ElFormItem label="跟进内容" prop="content">
                  <ElInput
                    v-model="formData.content"
                    type="textarea"
                    :rows="5"
                    placeholder="请详细描述本次跟进的内容..."
                    maxlength="500"
                    show-word-limit
                  />
                </ElFormItem>

                <ElRow :gutter="20">
                  <ElCol :span="12">
                    <ElFormItem label="下次跟进时间">
                      <ElDatePicker
                        v-model="formData.next_date"
                        type="datetime"
                        placeholder="请选择下次跟进时间"
                        style="width: 100%"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                      />
                    </ElFormItem>
                  </ElCol>
                </ElRow>

                <ElFormItem label="下次跟进计划">
                  <ElInput
                    v-model="formData.next_plan"
                    type="textarea"
                    :rows="5"
                    placeholder="请输入下次跟进的计划..."
                    maxlength="300"
                    show-word-limit
                  />
                </ElFormItem>
              </ElForm>
            </div>
          </div>

          <!-- 右侧：跟进记录历史 -->
          <div class="right-panel">
            <div class="records-list">
              <h4>
                历史跟进记录
                <span v-if="followRecords.length > 0" class="record-count">
                  （共 {{ followRecords.length }} 条）
                </span>
              </h4>
              <div
                v-loading="recordsLoading"
                class="records-container"
                element-loading-text="加载跟进记录中..."
              >
                <div v-if="!recordsLoading && followRecords.length === 0" class="empty-records">
                  <ElEmpty description="暂无跟进记录" />
                </div>
                <div v-else class="record-timeline">
                  <ElTimeline>
                    <ElTimelineItem
                      v-for="record in followRecords"
                      :key="record.id"
                      :timestamp="formatDateTime(record.follow_date)"
                      placement="top"
                    >
                      <ElCard class="record-card">
                        <div class="record-header">
                          <div class="record-meta">
                            <ElTag :type="getFollowTypeTagType(record.follow_type)" size="small">
                              {{ formatFollowType(record.follow_type) }}
                            </ElTag>
                            <span class="record-creator">{{ record.creator_name || '未知' }}</span>
                          </div>
                        </div>
                        <div class="record-content">
                          {{ record.content }}
                        </div>
                        <div v-if="record.next_plan" class="record-next-plan">
                          <strong>下次计划：</strong>{{ record.next_plan }}
                          <span v-if="record.next_date" class="next-date">
                            （{{ formatDateTime(record.next_date) }}）
                          </span>
                        </div>
                      </ElCard>
                    </ElTimelineItem>
                  </ElTimeline>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="closeDialog" :disabled="loading">取消</ElButton>
        <ElButton
          v-if="dialogType === 'add'"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
          :disabled="loading || !formData.follow_type || !formData.content"
        >
          <span v-if="loading">保存中...</span>
          <span v-else>保存</span>
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
  // 对话框包装器样式
  :deep(.follow-record-dialog-wrapper) {
    .el-dialog__body {
      padding: 20px !important;
    }
  }

  .dialog-content {
    padding: 10px;
  }

  .follow-record-dialog {
    height: 100%;
    display: flex;
    flex-direction: column;

    .lead-info {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 6px;
      flex-shrink: 0;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .label {
          color: #606266;
          font-size: 13px;
        }

        .value {
          color: #303133;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    .main-content {
      flex: 1;
      display: flex;
      gap: 20px;
      min-height: 0;

      .left-panel {
        flex: 1;
        min-width: 0;

        .add-form {
          height: 500px;
          padding: 15px;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          background-color: #fafafa;

          h4 {
            margin: 0 0 15px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
          }
        }
      }

      .right-panel {
        flex: 1;
        min-width: 0;

        .records-list {
          height: 100%;
          display: flex;
          flex-direction: column;

          h4 {
            margin: 0 0 15px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;

            .record-count {
              color: #909399;
              font-size: 12px;
              font-weight: normal;
            }
          }

          .records-container {
            flex: 1;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            background-color: #fff;

            .empty-records {
              text-align: center;
              padding: 40px 0;
            }

            .record-timeline {
              height: 100%;

              :deep(.el-timeline) {
                padding-left: 0;

                .el-timeline-item {
                  padding-bottom: 20px;

                  .el-timeline-item__timestamp {
                    color: #909399;
                    font-size: 12px;
                    line-height: 1;
                  }

                  .el-timeline-item__wrapper {
                    padding-left: 28px;
                    top: -3px;
                  }

                  .el-timeline-item__node {
                    background-color: #409eff;
                    border-color: #409eff;
                  }
                }
              }

              .record-card {
                margin-bottom: 0;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

                :deep(.el-card__body) {
                  padding: 12px;
                }

                .record-header {
                  margin-bottom: 8px;

                  .record-meta {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .record-creator {
                      color: #606266;
                      font-size: 12px;
                      margin-left: auto;
                    }
                  }
                }

                .record-content {
                  color: #303133;
                  font-size: 13px;
                  line-height: 1.6;
                  margin-bottom: 8px;
                }

                .record-next-plan {
                  color: #606266;
                  font-size: 12px;
                  background-color: #f8f9fa;
                  padding: 8px;
                  border-radius: 4px;
                  margin-top: 8px;

                  .next-date {
                    color: #909399;
                  }
                }
              }
            }

            /* 滚动条样式优化 */
            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
