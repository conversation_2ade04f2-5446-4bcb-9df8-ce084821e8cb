<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 工作报告表路由
Route::group('api/crm/crm_work_report', function () {
	Route::get('index', 'app\crm\controller\CrmWorkReportController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmWorkReportController@detail');
	Route::post('add', 'app\crm\controller\CrmWorkReportController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmWorkReportController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmWorkReportController@delete');
	//	Route::post('copy/:id', 'app\crm\controller\CrmWorkReportController@copy');
	//    Route::post('batchDelete', 'app\crm\controller\CrmWorkReportController@batchDelete');
	//    Route::post('updateField', 'app\crm\controller\CrmWorkReportController@updateField');
	//    Route::post('status/:id', 'app\crm\controller\CrmWorkReportController@status');
	//    Route::post('import', 'app\crm\controller\CrmWorkReportController@import');
	//    Route::get('importTemplate', 'app\crm\controller\CrmWorkReportController@importTemplate');
	//    Route::get('downloadTemplate', 'app\crm\controller\CrmWorkReportController@downloadTemplate');
	//    Route::get('export', 'app\crm\controller\CrmWorkReportController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);