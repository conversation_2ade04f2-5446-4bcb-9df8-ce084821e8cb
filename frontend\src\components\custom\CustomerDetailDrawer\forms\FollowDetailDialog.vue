<template>
  <el-dialog
    v-model="visible"
    title="跟进记录详情"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-loading="loading" class="follow-detail">
      <div v-if="followDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="跟进标题">
            {{ followDetail.title || '跟进记录' }}
          </el-descriptions-item>
          <el-descriptions-item label="跟进类型">
            <el-tag :type="getTypeTagType(followDetail.follow_type)">
              {{ getFollowTypeText(followDetail.follow_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="重要程度">
            <el-tag :type="getImportanceType(followDetail.importance)">
              {{ getImportanceText(followDetail.importance) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="跟进人">
            {{ followDetail.user_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="跟进时间">
            {{ formatDateTime(followDetail.follow_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="下次跟进时间">
            {{ formatDateTime(followDetail.next_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(followDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(followDetail.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="跟进内容" :span="2">
            <div v-if="followDetail.content" class="content-display" v-html="followDetail.content"></div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="下次跟进计划" :span="2">
            {{ followDetail.next_plan || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件展示 -->
        <div v-if="hasAttachments" class="attachments-section">
          <h4>附件</h4>
          <div class="attachment-list">
            <div
              v-for="(attachment, index) in followDetail.attachments"
              :key="attachment.id || index"
              class="attachment-item"
            >
              <el-card shadow="hover" class="attachment-card">
                <div class="attachment-info">
                  <el-icon class="attachment-icon">
                    <component :is="getFileIcon(attachment.type)" />
                  </el-icon>
                  <div class="attachment-details">
                    <div class="attachment-name">{{ attachment.name || '未知文件' }}</div>
                    <div class="attachment-meta">
                      <span v-if="attachment.size" class="file-size">{{ formatFileSize(attachment.size) }}</span>
                      <span v-if="attachment.type" class="file-type">{{ attachment.type }}</span>
                    </div>
                  </div>
                  <div class="attachment-actions">
                    <el-button
                      v-if="attachment.url"
                      type="primary"
                      size="small"
                      @click="downloadFile(attachment)"
                    >
                      下载
                    </el-button>
                    <el-button
                      v-if="isPreviewable(attachment.type)"
                      type="success"
                      size="small"
                      @click="previewFile(attachment)"
                    >
                      预览
                    </el-button>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button 
          v-if="hasButtonPermission('crm:crm_customer_my:edit_follow')"
          type="primary" 
          @click="handleEdit">
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Document, Picture, VideoPlay, Download } from '@element-plus/icons-vue'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'
  import { useCustomerPermission } from '@/composables/useCustomerPermission'

  // 组件属性
  interface Props {
    modelValue: boolean
    followId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    followId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    edit: [followId: number]
  }>()

  // 权限验证
  const { hasButtonPermission } = useCustomerPermission()

  // 响应式数据
  const loading = ref(false)
  const followDetail = ref<any>(null)

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 计算属性
  const hasAttachments = computed(() => {
    return followDetail.value?.attachments && 
           Array.isArray(followDetail.value.attachments) && 
           followDetail.value.attachments.length > 0
  })

  // 监听对话框显示状态
  watch(visible, (newVal) => {
    if (newVal && props.followId) {
      loadFollowDetail()
    }
  })

  // 加载跟进记录详情
  const loadFollowDetail = async () => {
    if (!props.followId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getFollowDetail(props.followId)
      
      if (res.code === ApiStatus.success) {
        followDetail.value = res.data
        // 处理附件数据
        if (followDetail.value.attachments && typeof followDetail.value.attachments === 'string') {
          try {
            followDetail.value.attachments = JSON.parse(followDetail.value.attachments)
          } catch (e) {
            followDetail.value.attachments = []
          }
        } else if (!followDetail.value.attachments) {
          followDetail.value.attachments = []
        }
      } else {
        ElMessage.error(res.message || '加载跟进记录详情失败')
      }
    } catch (error) {
      console.error('加载跟进记录详情失败:', error)
      ElMessage.error('加载跟进记录详情失败')
    } finally {
      loading.value = false
    }
  }

  // 格式化方法
  const formatDateTime = (datetime: string) => {
    if (!datetime) return '-'
    return new Date(datetime).toLocaleString()
  }

  const getTypeTagType = (type: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<string, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      phone: 'primary',
      email: 'success',
      visit: 'warning',
      meeting: 'danger'
    }
    return typeMap[type] || 'primary'
  }

  const getFollowTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      phone: '电话沟通',
      email: '邮件联系',
      visit: '上门拜访',
      meeting: '会议讨论'
    }
    return typeMap[type] || '其他'
  }

  const getImportanceType = (importance: number): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
    const importanceMap: Record<number, 'success' | 'primary' | 'warning' | 'info' | 'danger'> = {
      1: 'info',
      2: 'warning',
      3: 'danger'
    }
    return importanceMap[importance] || 'info'
  }

  const getImportanceText = (importance: number) => {
    const importanceMap: Record<number, string> = {
      1: '一般',
      2: '重要',
      3: '紧急'
    }
    return importanceMap[importance] || '一般'
  }

  const getFileIcon = (type: string | undefined | null) => {
    if (!type) return Document
    if (type.includes('image')) return Picture
    if (type.includes('video')) return VideoPlay
    return Document
  }

  const formatFileSize = (size: number) => {
    if (size < 1024) return size + ' B'
    if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }

  const isPreviewable = (type: string | undefined | null) => {
    if (!type) return false
    return type.includes('image') || type.includes('pdf') || type.includes('text')
  }

  const downloadFile = (attachment: any) => {
    if (attachment.url) {
      window.open(attachment.url, '_blank')
    }
  }

  const previewFile = (attachment: any) => {
    if (attachment.url) {
      window.open(attachment.url, '_blank')
    }
  }

  // 编辑按钮处理
  const handleEdit = () => {
    emit('edit', props.followId!)
    visible.value = false
  }
</script>

<style scoped lang="scss">
  .follow-detail {
    .detail-content {
      margin-top: 16px;

      .content-display {
        line-height: 1.6;
        color: var(--el-text-color-primary);

        :deep(p) {
          margin: 0 0 8px 0;
        }
      }
    }

    .attachments-section {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid var(--el-border-color-light);

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        color: var(--el-text-color-primary);
      }

      .attachment-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 12px;

        .attachment-card {
          .attachment-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .attachment-icon {
              font-size: 24px;
              color: var(--el-color-primary);
            }

            .attachment-details {
              flex: 1;
              min-width: 0;

              .attachment-name {
                font-weight: 600;
                color: var(--el-text-color-primary);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .attachment-meta {
                font-size: 12px;
                color: var(--el-text-color-secondary);
                margin-top: 4px;

                .file-size, .file-type {
                  margin-right: 8px;
                }
              }
            }

            .attachment-actions {
              display: flex;
              gap: 8px;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
