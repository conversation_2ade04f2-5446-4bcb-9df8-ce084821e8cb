<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑跟进记录' : '新增跟进记录'"
    width="700px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <div v-loading="loading" class="form-container">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="right"
      >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="跟进方式" prop="follow_type">
            <el-select v-model="formData.follow_type" placeholder="请选择跟进方式">
              <el-option label="电话沟通" value="phone" />
              <el-option label="上门拜访" value="visit" />
              <el-option label="邮件联系" value="email" />
              <el-option label="微信沟通" value="wechat" />
              <el-option label="其他方式" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跟进时间" prop="follow_date">
            <el-date-picker
              v-model="formData.follow_date"
              type="datetime"
              placeholder="请选择跟进时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="跟进内容" prop="content">
        <el-input
          v-model="formData.content"
          type="textarea"
          :rows="4"
          placeholder="请详细描述本次跟进的内容..."
        />
      </el-form-item>

      <el-form-item label="下次跟进时间" prop="next_date">
        <el-date-picker
          v-model="formData.next_date"
          type="datetime"
          placeholder="请选择下次跟进时间"
          style="width: 100%"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="下次跟进计划" prop="next_plan">
        <el-input
          v-model="formData.next_plan"
          type="textarea"
          :rows="3"
          placeholder="请描述下次跟进的计划和目标..."
        />
      </el-form-item>

      <el-form-item label="附件" prop="attachments">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          multiple
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">支持上传多个文件，单个文件不超过10MB</div>
          </template>
        </el-upload>
      </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules, UploadFile } from 'element-plus'
  import { CrmCustomerDetailApi } from '@/api/crm/crmCustomerDetail'
  import { ApiStatus } from '@/utils/http/status'

  // 组件属性
  interface Props {
    modelValue: boolean
    customerId?: number
    followId?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: false,
    customerId: 0,
    followId: 0
  })

  // 事件定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const uploadRef = ref()
  const loading = ref(false)
  const fileList = ref<UploadFile[]>([])

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const isEdit = computed(() => !!props.followId)

  // 表单数据
  const formData = reactive({
    related_type: 'customer',
    related_id: 0,
    follow_type: '',
    content: '',
    follow_date: '',
    next_plan: '',
    next_date: '',
    attachments: ''
  })

  // 表单验证规则
  const formRules: FormRules = {
    follow_type: [{ required: true, message: '请选择跟进方式', trigger: 'change' }],
    content: [{ required: true, message: '请输入跟进内容', trigger: 'blur' }],
    follow_date: [{ required: true, message: '请选择跟进时间', trigger: 'change' }]
  }

  // 监听跟进ID变化，加载跟进详情
  watch(
    () => props.followId,
    (newId) => {
      if (newId && props.modelValue) {
        loadFollowDetail()
      }
    },
    { immediate: true }
  )

  // 监听对话框显示状态
  watch(
    () => props.modelValue,
    (newVal) => {
      if (newVal && props.followId) {
        loadFollowDetail()
      } else if (newVal && !props.followId) {
        // 新增模式，重置表单
        resetFormData()
      }
    }
  )

  // 加载跟进详情
  const loadFollowDetail = async () => {
    if (!props.followId) return

    loading.value = true
    try {
      const res = await CrmCustomerDetailApi.getFollowDetail(props.followId)

      if (res.code === ApiStatus.success) {
        const data = res.data
        Object.assign(formData, {
          related_type: 'customer',
          related_id: props.customerId,
          follow_type: data.follow_type || '',
          content: data.content || '',
          follow_date: data.follow_date || '',
          next_plan: data.next_plan || '',
          next_date: data.next_date || '',
          attachments: data.attachments || ''
        })

        // 处理附件列表
        if (data.attachments) {
          try {
            const attachments = JSON.parse(data.attachments)
            fileList.value = attachments.map((file: any, index: number) => ({
              uid: index,
              name: file.name,
              url: file.url
            }))
          } catch (e) {
            fileList.value = []
          }
        }
      } else {
        ElMessage.error(res.message || '加载跟进详情失败')
      }
    } catch (error) {
      console.error('加载跟进详情失败:', error)
      ElMessage.error('加载跟进详情失败')
    } finally {
      loading.value = false
    }
  }

  // 重置表单数据
  const resetFormData = () => {
    Object.assign(formData, {
      related_type: 'customer',
      related_id: props.customerId || 0,
      follow_type: '',
      content: '',
      follow_date: '',
      next_plan: '',
      next_date: '',
      attachments: ''
    })
    fileList.value = []
  }

  // 文件变化处理
  const handleFileChange = (file: UploadFile) => {
    // 这里可以添加文件上传逻辑
    console.log('文件变化:', file)
  }

  // 文件移除处理
  const handleFileRemove = (file: UploadFile) => {
    const index = fileList.value.findIndex(f => f.uid === file.uid)
    if (index > -1) {
      fileList.value.splice(index, 1)
    }
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      // 处理附件数据
      const attachments = fileList.value.map(file => ({
        name: file.name,
        url: file.url || '',
        size: file.size || 0
      }))
      
      const submitData = {
        ...formData,
        attachments: JSON.stringify(attachments)
      }

      let res
      if (isEdit.value) {
        res = await CrmCustomerDetailApi.editFollow(props.followId!, submitData)
      } else {
        res = await CrmCustomerDetailApi.addFollow(props.customerId!, submitData)
      }

      if (res.code === ApiStatus.success) {
        ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
        visible.value = false
        emit('success')
      } else {
        ElMessage.error(res.message || '操作失败')
      }
    } catch (error) {
      console.error('提交表单失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 对话框关闭处理
  const handleClosed = () => {
    formRef.value?.resetFields()
    resetFormData()
  }
</script>

<style scoped lang="scss">
  .form-container {
    min-height: 200px;
  }

  .dialog-footer {
    text-align: right;
  }
</style>
