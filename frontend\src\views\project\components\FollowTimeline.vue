<template>
  <div class="follow-timeline">
    <div v-loading="loading" class="timeline-container">
      <!-- 空状态 -->
      <div v-if="!loading && (!followList || followList.length === 0)" class="empty-state">
        <el-empty description="暂无跟进记录" :image-size="120">
          <template #description>
            <span class="empty-description">暂无跟进记录</span>
          </template>
        </el-empty>
      </div>

      <!-- 跟进时间线 -->
      <div v-else class="timeline-content">
        <el-timeline>
          <el-timeline-item
            v-for="follow in followList"
            :key="follow.id"
            :timestamp="formatDateTime(follow.follow_date)"
            placement="top"
            :type="getTimelineType(follow.follow_type)"
            :size="follow.next_date ? 'large' : 'normal'"
          >
            <el-card class="follow-card" shadow="hover">
              <div class="follow-header">
                <div class="follow-info">
                  <div class="follow-title">
                    <el-tag :type="getFollowTypeTag(follow.follow_type)" size="small">
                      {{ getFollowTypeText(follow.follow_type) }}
                    </el-tag>
                    <span class="follow-user">{{ follow.creator?.nickname || '未知用户' }}</span>
                  </div>

                  <div class="follow-actions" v-if="canEdit(follow) || canDelete(follow)">
                    <el-dropdown trigger="click" placement="bottom-end">
                      <el-button text size="small" class="action-btn">
                        <el-icon>
                          <MoreFilled />
                        </el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item v-if="canEdit(follow)" @click="handleEdit(follow)">
                            <el-icon>
                              <Edit />
                            </el-icon>
                            编辑
                          </el-dropdown-item>
                          <el-dropdown-item
                            v-if="canDelete(follow)"
                            @click="handleDelete(follow)"
                            class="danger-item"
                          >
                            <el-icon>
                              <Delete />
                            </el-icon>
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>

              <div class="follow-content">
                <p class="content-text">{{ follow.content }}</p>

                <!-- 下次跟进计划 -->
                <div v-if="follow.next_plan" class="next-plan">
                  <div class="plan-label">
                    <el-icon>
                      <Calendar />
                    </el-icon>
                    下次计划
                  </div>
                  <p class="plan-content">{{ follow.next_plan }}</p>
                  <div v-if="follow.next_date" class="next-date">
                    <el-icon>
                      <Clock />
                    </el-icon>
                    {{ formatDateTime(follow.next_date) }}
                  </div>
                </div>

                <!-- 附件列表 -->
                <div v-if="follow.attachments && follow.attachments.length > 0" class="attachments">
                  <div
                    v-for="(file, index) in parseAttachments(follow.attachments)"
                    :key="index"
                    class="attachment-item"
                  >
                    <el-icon>
                      <Paperclip />
                    </el-icon>
                    <span class="file-name">{{ file.name }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { MoreFilled, Edit, Delete, Paperclip, Calendar, Clock } from '@element-plus/icons-vue'
  import { TaskApi } from '@/api/project/projectApi'
  import { formatDateTime } from '@/utils/date'
  import { useAuth } from '@/composables/useAuth'

  // Props
  interface Props {
    taskId: number
  }

  const props = defineProps<Props>()

  // Emits
  const emit = defineEmits<{
    edit: [follow: any]
  }>()

  // 响应式数据
  const loading = ref(false)
  const followList = ref<any[]>([])
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 方法
  const loadFollows = async () => {
    try {
      loading.value = true
      console.log('FollowTimeline: 开始加载跟进记录', props.taskId)
      const response = await TaskApi.getFollows(props.taskId, {
        page: currentPage.value,
        limit: pageSize.value
      })

      console.log('FollowTimeline: 跟进记录响应', response)
      if (response.code === 200) {
        followList.value = response.data.list || []
        total.value = response.data.total || 0
        console.log('FollowTimeline: 跟进记录数据', followList.value, total.value)
      }
    } catch (error) {
      console.error('加载跟进记录失败:', error)
      // ElMessage.error('加载跟进记录失败')
    } finally {
      loading.value = false
    }
  }

  const getFollowTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      phone: '电话',
      meeting: '会议',
      email: '邮件',
      other: '其他'
    }
    return typeMap[type] || type
  }

  const getFollowTypeTag = (type: string) => {
    const tagMap: Record<string, string> = {
      phone: 'success',
      meeting: 'warning',
      email: 'info',
      other: 'default'
    }
    return tagMap[type] || 'default'
  }

  const getTimelineType = (type: string) => {
    const typeMap: Record<string, string> = {
      phone: 'success',
      meeting: 'warning',
      email: 'info',
      other: 'primary'
    }
    return typeMap[type] || 'primary'
  }

  const parseAttachments = (attachments: string | any[]) => {
    if (!attachments) return []

    try {
      if (typeof attachments === 'string') {
        return JSON.parse(attachments)
      }
      return attachments
    } catch (error) {
      console.error('解析附件失败:', error)
      return []
    }
  }

  // 权限验证
  const { hasAuth } = useAuth()

  const canEdit = (follow: any) => {
    // 检查编辑跟进权限
    return hasAuth('project:task:follow:edit')
  }

  const canDelete = (follow: any) => {
    // 检查删除跟进权限
    return hasAuth('project:task:follow:delete')
  }

  const handleEdit = (follow: any) => {
    emit('edit', follow)
  }

  const handleDelete = async (follow: any) => {
    try {
      await ElMessageBox.confirm('确定要删除这条跟进记录吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await TaskApi.deleteRecord(follow.id)
      ElMessage.success('跟进记录删除成功')

      // 重新加载跟进列表
      await loadFollows()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('删除跟进记录失败:', error)
        // ElMessage.error(error.message || '删除跟进记录失败')
      }
    }
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    loadFollows()
  }

  const handleCurrentChange = (page: number) => {
    currentPage.value = page
    loadFollows()
  }

  // 暴露方法给父组件
  const refresh = () => {
    currentPage.value = 1
    loadFollows()
  }

  defineExpose({
    refresh
  })

  // 生命周期
  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      loadFollows()
    })
  })
</script>

<style lang="scss" scoped>
  .follow-timeline {
    .timeline-container {
      min-height: 200px;
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;

      .empty-description {
        color: #86909c;
        font-size: 14px;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-600);
        }
      }
    }

    .timeline-content {
      :deep(.el-timeline) {
        padding-left: 0;
      }

      .follow-card {
        margin-bottom: 16px;
        border-radius: 8px;

        :deep(.el-card__body) {
          padding: 16px;
        }

        .follow-header {
          .follow-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;

            .follow-title {
              display: flex;
              align-items: center;
              gap: 12px;

              .follow-user {
                font-weight: 500;
                color: #303133;
              }
            }

            .follow-actions {
              .action-btn {
                color: #909399;

                &:hover {
                  color: #409eff;
                }
              }
            }
          }
        }

        .follow-content {
          .content-text {
            margin: 0 0 16px 0;
            line-height: 1.6;
            color: #606266;
            white-space: pre-wrap;
            word-break: break-word;
          }

          .next-plan {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 16px;

            .plan-label {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 13px;
              font-weight: 500;
              color: #409eff;
              margin-bottom: 8px;
            }

            .plan-content {
              margin: 0 0 8px 0;
              color: #606266;
              line-height: 1.5;
            }

            .next-date {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 12px;
              color: #909399;
            }
          }

          .attachments {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .attachment-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 12px;
              background-color: #f5f7fa;
              border-radius: 6px;
              font-size: 13px;
              color: #606266;

              .el-icon {
                color: #909399;
              }

              .file-name {
                flex: 1;
              }
            }
          }
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 24px;
    }
  }

  :deep(.el-dropdown-menu__item.danger-item) {
    color: #f56c6c;

    &:hover {
      background-color: #fef0f0;
      color: #f56c6c;
    }
  }
</style>
