# 回款列表操作按钮修复总结

## 🐛 **问题描述**
客户详情组件中的合同panel中的回款列表的操作列按钮，根据审批状态适配不正确：
- 已审批通过的回款记录仍然显示"编辑"按钮
- 已审批通过的回款记录仍然显示"删除"按钮  
- 审批中的回款记录仍然显示"编辑"和"删除"按钮
- 缺少"提交审批"按钮

## ✅ **修复内容**

### **1. 操作按钮条件显示逻辑**

#### **修复前**
```vue
<el-table-column label="操作" width="230" fixed="right">
  <template #default="{ row }">
    <ArtButtonTable text="详情" @click="handleViewReceivable(row)" />
    <ArtButtonTable text="编辑" type="edit" @click="handleEditReceivable(row)" />
    <el-dropdown>
      <ArtButtonTable text="更多" type="more" />
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-if="row.approval_status === 1"
            @click="handleWithdrawReceivable(row)"
          >
            撤回
          </el-dropdown-item>
          <el-dropdown-item @click="handleDeleteReceivable(row)" divided>
            删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </template>
</el-table-column>
```

#### **修复后**
```vue
<el-table-column label="操作" width="230" fixed="right">
  <template #default="{ row }">
    <ArtButtonTable text="详情" @click="handleViewReceivable(row)" />
    <!-- 只有草稿状态(0)或已拒绝状态(3)的回款可以编辑 -->
    <ArtButtonTable 
      v-if="canEditReceivable(row)"
      text="编辑" 
      type="edit" 
      @click="handleEditReceivable(row)" 
    />
    <el-dropdown v-if="hasDropdownActions(row)">
      <ArtButtonTable text="更多" type="more" />
      <template #dropdown>
        <el-dropdown-menu>
          <!-- 提交审批：草稿状态(0)或已拒绝状态(3)可以提交 -->
          <el-dropdown-item
            v-if="canSubmitReceivable(row)"
            @click="handleSubmitReceivable(row)"
          >
            提交审批
          </el-dropdown-item>
          <!-- 撤回审批：审批中状态(1)可以撤回 -->
          <el-dropdown-item
            v-if="canWithdrawReceivable(row)"
            @click="handleWithdrawReceivable(row)"
          >
            撤回审批
          </el-dropdown-item>
          <!-- 删除：只有草稿状态(0)或已拒绝状态(3)可以删除 -->
          <el-dropdown-item 
            v-if="canDeleteReceivable(row)"
            @click="handleDeleteReceivable(row)" 
            divided
          >
            删除
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </template>
</el-table-column>
```

### **2. 新增权限判断方法**

```typescript
// 判断收款是否可以编辑
const canEditReceivable = (receivable: any) => {
  // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以编辑
  const status = receivable.approval_status ?? 0
  return status === 0 || status === 3
}

// 判断收款是否可以删除
const canDeleteReceivable = (receivable: any) => {
  // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以删除
  const status = receivable.approval_status ?? 0
  return status === 0 || status === 3
}

// 判断收款是否可以提交审批
const canSubmitReceivable = (receivable: any) => {
  // 使用approval_status字段，只有草稿状态(0)或已拒绝状态(3)的收款可以提交
  const status = receivable.approval_status ?? 0
  return status === 0 || status === 3
}

// 判断收款是否可以撤回
const canWithdrawReceivable = (receivable: any) => {
  // 使用approval_status字段，只有审批中状态(1)的收款可以撤回
  const status = receivable.approval_status ?? 0
  return status === 1
}

// 判断是否有下拉菜单操作
const hasDropdownActions = (receivable: any) => {
  return canSubmitReceivable(receivable) || canWithdrawReceivable(receivable) || canDeleteReceivable(receivable)
}
```

### **3. 恢复提交审批功能**

取消注释了 `handleSubmitReceivable` 方法，恢复提交审批功能。

## 🎯 **修复效果**

### **按审批状态显示的操作按钮**

| 审批状态 | 状态码 | 详情 | 编辑 | 提交审批 | 撤回审批 | 删除 | 更多菜单 |
|----------|--------|------|------|----------|----------|------|----------|
| **草稿** | 0 | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ |
| **审批中** | 1 | ✅ | ❌ | ❌ | ✅ | ❌ | ✅ |
| **已通过** | 2 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **已拒绝** | 3 | ✅ | ✅ | ✅ | ❌ | ✅ | ✅ |
| **已终止** | 4 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **已撤回** | 5 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **已作废** | 6 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

### **业务逻辑说明**

#### **可编辑状态**
- ✅ **草稿状态(0)**：新创建的回款记录，可以编辑
- ✅ **已拒绝状态(3)**：审批被拒绝的回款记录，可以修改后重新提交
- ❌ **其他状态**：审批中、已通过、已终止、已撤回、已作废的回款记录不可编辑

#### **可删除状态**
- ✅ **草稿状态(0)**：新创建的回款记录，可以删除
- ✅ **已拒绝状态(3)**：审批被拒绝的回款记录，可以删除
- ❌ **其他状态**：审批中、已通过、已终止、已撤回、已作废的回款记录不可删除

#### **可提交审批状态**
- ✅ **草稿状态(0)**：新创建的回款记录，可以提交审批
- ✅ **已拒绝状态(3)**：审批被拒绝的回款记录，可以重新提交审批
- ❌ **其他状态**：已在审批流程中或已结束的回款记录不可重复提交

#### **可撤回审批状态**
- ✅ **审批中状态(1)**：正在审批中的回款记录，可以撤回
- ❌ **其他状态**：非审批中状态的回款记录不可撤回

## 💼 **用户体验提升**

### **操作安全性**
- ✅ **防止误操作**：已审批通过的回款记录不再显示编辑和删除按钮
- ✅ **状态一致性**：按钮显示与审批状态完全匹配
- ✅ **操作合理性**：只在合适的状态下显示对应的操作按钮

### **界面清晰性**
- ✅ **按钮精简**：不显示不可用的操作按钮，界面更清爽
- ✅ **逻辑清晰**：用户一眼就能看出当前可以执行的操作
- ✅ **状态明确**：通过按钮显示就能了解回款记录的当前状态

### **功能完整性**
- ✅ **提交审批**：恢复了提交审批功能
- ✅ **撤回审批**：保留了撤回审批功能
- ✅ **编辑删除**：在合适的状态下保留编辑删除功能
- ✅ **查看详情**：所有状态下都可以查看详情

## 🧪 **测试建议**

### **功能测试**
1. **草稿状态回款**：验证可以编辑、删除、提交审批
2. **审批中回款**：验证只能查看详情和撤回审批
3. **已通过回款**：验证只能查看详情
4. **已拒绝回款**：验证可以编辑、删除、重新提交审批

### **界面测试**
1. **按钮显示**：验证不同状态下按钮显示是否正确
2. **更多菜单**：验证更多菜单是否在有操作时才显示
3. **操作反馈**：验证操作成功后状态和按钮的更新

## 🎉 **修复完成**

回款列表操作按钮现在已经根据审批状态正确适配，用户界面更加安全、清晰和易用！
