<!--最简单的ApiSelect测试组件-->
<template>
  <div>
    <h3>SimpleApiSelect 测试组件</h3>
    
    <!-- 测试1: 完全空的select -->
    <div style="margin-bottom: 20px;">
      <label>测试1 - 完全空的select:</label>
      <el-select v-model="value1" placeholder="请选择" style="width: 300px;">
        <template #empty>
          <div style="padding: 12px; text-align: center; color: #ff0000; background: #ffeeee;">
            🔴 测试1: 完全空的select
          </div>
        </template>
      </el-select>
      <span style="margin-left: 10px;">选中值: {{ value1 }}</span>
    </div>

    <!-- 测试2: 有空数组的select -->
    <div style="margin-bottom: 20px;">
      <label>测试2 - 空数组options:</label>
      <el-select v-model="value2" placeholder="请选择" style="width: 300px;">
        <el-option
          v-for="item in emptyOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
        <template #empty>
          <div style="padding: 12px; text-align: center; color: #ff0000; background: #ffeeee;">
            🔴 测试2: 空数组options ({{ emptyOptions.length }} 个选项)
          </div>
        </template>
      </el-select>
      <span style="margin-left: 10px;">选中值: {{ value2 }}</span>
    </div>

    <!-- 测试3: 模拟API加载后为空 -->
    <div style="margin-bottom: 20px;">
      <label>测试3 - 模拟API加载:</label>
      <el-select 
        v-model="value3" 
        placeholder="请选择" 
        style="width: 300px;"
        :loading="loading"
      >
        <el-option
          v-for="item in apiOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
        <template #empty>
          <div style="padding: 12px; text-align: center; color: #ff0000; background: #ffeeee;">
            🔴 测试3: API加载完成 ({{ apiOptions.length }} 个选项, 加载中: {{ loading }})
          </div>
        </template>
      </el-select>
      <button @click="loadApiData" style="margin-left: 10px;">加载API数据</button>
      <span style="margin-left: 10px;">选中值: {{ value3 }}</span>
    </div>

    <!-- 测试4: 使用filterable -->
    <div style="margin-bottom: 20px;">
      <label>测试4 - filterable空数据:</label>
      <el-select 
        v-model="value4" 
        placeholder="请选择" 
        style="width: 300px;"
        filterable
      >
        <el-option
          v-for="item in emptyOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
        <template #empty>
          <div style="padding: 12px; text-align: center; color: #ff0000; background: #ffeeee;">
            🔴 测试4: filterable空数据 ({{ emptyOptions.length }} 个选项)
          </div>
        </template>
      </el-select>
      <span style="margin-left: 10px;">选中值: {{ value4 }}</span>
    </div>

    <!-- 调试信息 -->
    <div style="margin-top: 30px; padding: 10px; background: #f5f5f5; border-radius: 4px;">
      <h4>调试信息:</h4>
      <p>emptyOptions.length: {{ emptyOptions.length }}</p>
      <p>apiOptions.length: {{ apiOptions.length }}</p>
      <p>loading: {{ loading }}</p>
      <p>Element Plus版本: 检查package.json</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const value1 = ref('')
const value2 = ref('')
const value3 = ref('')
const value4 = ref('')

const loading = ref(false)
const emptyOptions = ref([]) // 空数组
const apiOptions = ref([])   // API数据

// 模拟API加载
const loadApiData = async () => {
  loading.value = true
  
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模拟返回空数据（就像我们的项目成员API）
  apiOptions.value = []
  loading.value = false
  
  console.log('SimpleApiSelect: 模拟API加载完成，返回空数组')
}

// 组件挂载时自动加载一次
loadApiData()
</script>
