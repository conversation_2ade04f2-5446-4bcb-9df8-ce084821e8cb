<template>
  <div class="project-statistics" v-loading="loading" element-loading-text="加载统计数据...">
    <div class="stats-overview">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon>
            <Tickets />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total_tasks || 0 }}</div>
          <div class="stat-label">总任务数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon completed">
          <el-icon>
            <CircleCheck />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completed_tasks || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon progress">
          <el-icon>
            <Loading />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.in_progress_tasks || 0 }}</div>
          <div class="stat-label">进行中</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon pending">
          <el-icon>
            <Clock />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending_tasks || 0 }}</div>
          <div class="stat-label">待办</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon members">
          <el-icon>
            <User />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.member_count || 0 }}</div>
          <div class="stat-label">成员数量</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon progress-percent">
          <el-icon>
            <TrendCharts />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.progress || 0 }}%</div>
          <div class="stat-label">项目进度</div>
        </div>
      </div>
    </div>

    <div class="charts-section">
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h4>任务状态分布</h4>
          </div>
          <div class="chart-content">
            <div ref="taskStatusChartRef" style="height: 200px"></div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h4>任务优先级分布</h4>
          </div>
          <div class="chart-content">
            <div ref="taskPriorityChartRef" style="height: 200px"></div>
          </div>
        </div>
      </div>

      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h4>项目进度趋势</h4>
          </div>
          <div class="chart-content">
            <div ref="progressTrendChartRef" class="progress-trend-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="details-section">
      <div class="detail-card">
        <div class="detail-header">
          <h4>成员工作量统计</h4>
        </div>
        <div class="detail-content">
          <div class="member-stats">
            <div v-for="member in memberStats" :key="member.id" class="member-stat-item">
              <div class="member-info">
                <el-avatar :size="32" :src="member.avatar">
                  {{ member.name?.charAt(0) }}
                </el-avatar>
                <span class="member-name">{{ member.name }}</span>
              </div>
              <div class="member-tasks">
                <span class="task-count">{{ member.task_count || 0 }}个任务</span>
                <div class="task-progress">
                  <el-progress
                    :percentage="member.completion_rate || 0"
                    :stroke-width="6"
                    :show-text="false"
                  />
                  <span class="progress-text">{{ member.completion_rate || 0 }}%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="detail-card">
        <div class="detail-header">
          <h4>最近活动</h4>
        </div>
        <div class="detail-content">
          <div class="activity-list">
            <div v-for="(activity, index) in recentActivities" :key="index" class="activity-item">
              <div class="activity-icon">
                <el-icon>
                  <Bell />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-text"
                  >{{ activity.user }} {{ activity.action }} {{ activity.target }}
                </div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
            <!-- 无数据提示 -->
            <div v-if="recentActivities.length === 0 && !loading" class="no-data">
              <el-empty description="暂无最近活动" :image-size="60" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
  import {
    Tickets,
    CircleCheck,
    Loading,
    Clock,
    User,
    Bell,
    TrendCharts
  } from '@element-plus/icons-vue'
  import { ProjectApi } from '@/api/project/projectApi'
  import { ElMessage } from 'element-plus'

  // Props
  interface Props {
    projectId: number
    stats: any
  }

  const props = defineProps<Props>()

  // 图表引用
  const taskStatusChartRef = ref<HTMLElement>()
  const taskPriorityChartRef = ref<HTMLElement>()
  const progressTrendChartRef = ref<HTMLElement>()

  // 图表实例 - 使用echarts直接创建
  import * as echarts from 'echarts'

  let taskStatusChart: echarts.ECharts | null = null
  let taskPriorityChart: echarts.ECharts | null = null
  let progressTrendChart: echarts.ECharts | null = null

  // 响应式数据
  const memberStats = ref<any[]>([])
  const recentActivities = ref<any[]>([])
  const chartData = ref<{
    taskStatus: any[]
    taskPriority: any[]
    progressTrend: any[]
  }>({
    taskStatus: [],
    taskPriority: [],
    progressTrend: []
  })

  // 统一的加载状态
  const loading = ref(false)

  // 图表数据加载方法
  const loadChartData = async () => {
    try {
      console.log('ProjectStatistics: 开始加载图表数据')

      // 加载任务状态分布数据
      const statusResponse = await ProjectApi.taskStatusStats(props.projectId)
      chartData.value.taskStatus = statusResponse.data || []

      // 加载任务优先级分布数据
      const priorityResponse = await ProjectApi.taskPriorityStats(props.projectId)
      chartData.value.taskPriority = priorityResponse.data || []

      // 加载项目进度趋势数据
      const trendResponse = await ProjectApi.progressTrend(props.projectId)
      chartData.value.progressTrend = trendResponse.data || []

      console.log('ProjectStatistics: 图表数据加载完成', chartData.value)

      // 等待DOM更新后渲染图表
      await nextTick()
      setTimeout(() => {
        renderCharts()
      }, 100) // 延迟100ms确保DOM完全渲染
    } catch (error) {
      console.error('加载图表数据失败:', error)
      ElMessage.error('加载图表数据失败')
      chartData.value = {
        taskStatus: [],
        taskPriority: [],
        progressTrend: []
      }
    }
  }

  // 方法
  const loadMemberStats = async () => {
    try {
      console.log('ProjectStatistics: 开始加载成员统计')
      const response = await ProjectApi.memberStats(props.projectId)
      console.log('成员统计API响应:', response)
      // 处理后端返回的数据格式：可能是数组或者包含list的对象
      if (Array.isArray(response.data)) {
        memberStats.value = response.data
      } else {
        memberStats.value = response.data.list || []
      }
      console.log('ProjectStatistics: 成员统计加载完成', memberStats.value)
    } catch (error) {
      console.error('加载成员统计失败:', error)
      ElMessage.error('加载成员统计失败')
      memberStats.value = []
    }
  }

  // 图表渲染方法
  const renderCharts = () => {
    renderTaskStatusChart()
    renderTaskPriorityChart()
    renderProgressTrendChart()
  }

  // 任务状态分布饼图
  const renderTaskStatusChart = () => {
    if (!taskStatusChartRef.value) return

    if (!taskStatusChart) {
      taskStatusChart = echarts.init(taskStatusChartRef.value)
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          fontSize: 12
        }
      },
      series: [
        {
          name: '任务状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: chartData.value.taskStatus.map((item) => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color
            }
          }))
        }
      ]
    }

    taskStatusChart.setOption(option)
  }

  // 任务优先级分布柱状图
  const renderTaskPriorityChart = () => {
    if (!taskPriorityChartRef.value) return

    if (!taskPriorityChart) {
      taskPriorityChart = echarts.init(taskPriorityChartRef.value)
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: chartData.value.taskPriority.map((item) => item.name),
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#606266'
        },
        splitLine: {
          lineStyle: {
            color: '#F5F7FA'
          }
        }
      },
      series: [
        {
          name: '任务数量',
          type: 'bar',
          data: chartData.value.taskPriority.map((item) => item.value),
          itemStyle: {
            color: '#409EFF',
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: '60%'
        }
      ]
    }

    taskPriorityChart.setOption(option)
  }

  // 项目进度趋势折线图
  const renderProgressTrendChart = () => {
    if (!progressTrendChartRef.value) return

    // 确保有数据
    if (!chartData.value.progressTrend || chartData.value.progressTrend.length === 0) {
      console.log('进度趋势数据为空，跳过渲染')
      return
    }

    if (!progressTrendChart) {
      progressTrendChart = echarts.init(progressTrendChartRef.value)
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true
      },
      grid: {
        left: '15%',
        right: '5%',
        bottom: '15%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: (chartData.value.progressTrend || []).map((item) => item.date || ''),
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: '#F5F7FA'
          }
        }
      },
      series: [
        {
          name: '项目进度',
          type: 'line',
          data: (chartData.value.progressTrend || []).map((item) => item.progress || 0),
          smooth: true,
          lineStyle: {
            color: '#67C23A',
            width: 3
          },
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ]
            }
          }
        }
      ]
    }

    progressTrendChart.setOption(option)

    // 确保图表响应式调整
    nextTick(() => {
      progressTrendChart?.resize()
    })
  }

  const loadRecentActivities = async () => {
    try {
      console.log('ProjectStatistics: 开始加载最近活动')
      const response = await ProjectApi.recentActivities(props.projectId)
      console.log('最近活动API响应:', response)
      console.log('响应数据类型:', typeof response.data)
      console.log('是否为数组:', Array.isArray(response.data))

      // 处理后端返回的数据格式：可能是数组或者包含list的对象
      if (Array.isArray(response.data)) {
        recentActivities.value = response.data
        console.log('设置最近活动数据(数组):', recentActivities.value)
      } else if (response.data && response.data.list) {
        recentActivities.value = response.data.list
        console.log('设置最近活动数据(对象.list):', recentActivities.value)
      } else {
        recentActivities.value = []
        console.log('未找到有效的最近活动数据，设置为空数组')
      }
      console.log('ProjectStatistics: 最近活动加载完成', recentActivities.value)
    } catch (error) {
      console.error('加载最近活动失败:', error)
      ElMessage.error('加载最近活动失败')
      recentActivities.value = []
    }
  }

  // 统一的数据加载方法
  const loadAllData = async () => {
    if (!props.projectId) return

    loading.value = true
    try {
      console.log('ProjectStatistics: 开始加载所有数据')

      // 并行加载所有数据
      await Promise.all([loadChartData(), loadMemberStats(), loadRecentActivities()])

      console.log('ProjectStatistics: 所有数据加载完成')
    } catch (error) {
      console.error('加载统计数据失败:', error)
      ElMessage.error('加载统计数据失败')
    } finally {
      loading.value = false
    }
  }

  // 监听props变化
  watch(
    () => props.projectId,
    () => {
      if (props.projectId) {
        loadAllData()
      }
    },
    { immediate: true }
  )

  // 窗口大小变化时调整图表
  const handleResize = () => {
    if (taskStatusChart) taskStatusChart.resize()
    if (taskPriorityChart) taskPriorityChart.resize()
    if (progressTrendChart) progressTrendChart.resize()
  }

  // 初始化
  onMounted(() => {
    // 只监听窗口大小变化，数据加载由watch处理
    window.addEventListener('resize', handleResize)
  })

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (taskStatusChart) {
      taskStatusChart.dispose()
      taskStatusChart = null
    }
    if (taskPriorityChart) {
      taskPriorityChart.dispose()
      taskPriorityChart = null
    }
    if (progressTrendChart) {
      progressTrendChart.dispose()
      progressTrendChart = null
    }
  })

  // 销毁图表实例
  onUnmounted(() => {
    taskStatusChart?.dispose()
    taskPriorityChart?.dispose()
    progressTrendChart?.dispose()
  })
</script>

<style scoped lang="scss">
  .project-statistics {
    .stats-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 32px;

      .stat-card {
        background: white;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 20px;
        display: flex;
        align-items: center;

        // 黑暗模式适配
        html.dark & {
          background: var(--art-main-bg-color);
          border-color: var(--art-border-color);
        }

        gap: 16px;

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f0f9ff;
          color: #1664ff;

          &.completed {
            background: #f6ffed;
            color: #52c41a;
          }

          &.progress {
            background: #fff7e6;
            color: #fa8c16;
          }

          &.pending {
            background: #f0f0f0;
            color: #8c8c8c;
          }

          &.members {
            background: #f9f0ff;
            color: #722ed1;
          }

          &.progress-percent {
            background: #e6f7ff;
            color: #1890ff;
          }

          // 黑暗模式适配
          html.dark & {
            background: rgba(var(--art-primary), 0.1);
            color: rgb(var(--art-primary));

            &.completed {
              background: rgba(var(--art-success), 0.1);
              color: rgb(var(--art-success));
            }

            &.progress {
              background: rgba(var(--art-warning), 0.1);
              color: rgb(var(--art-warning));
            }

            &.pending {
              background: var(--art-color);
              color: var(--art-text-gray-600);
            }

            &.members {
              background: rgba(114, 46, 209, 0.1);
              color: #722ed1;
            }

            &.progress-percent {
              background: rgba(var(--art-info), 0.1);
              color: rgb(var(--art-info));
            }
          }

          .el-icon {
            font-size: 24px;
          }
        }

        .stat-content {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin-bottom: 4px;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }

          .stat-label {
            font-size: 14px;
            color: #86909c;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }
        }
      }
    }

    .charts-section {
      margin-bottom: 32px;

      .chart-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;

        .chart-card {
          flex: 1;
          background: white;
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          padding: 20px;
          overflow: hidden;

          // 黑暗模式适配
          html.dark & {
            background: var(--art-main-bg-color);
            border-color: var(--art-border-color);
          }

          &.full-width {
            flex: none;
            width: 100%;
            max-width: 100%;
          }

          .chart-header {
            margin-bottom: 16px;

            h4 {
              margin: 0;
              color: #1f2329;
              font-size: 16px;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-900);
              }
            }
          }

          .chart-content {
            overflow: hidden;

            .chart-placeholder {
              height: 200px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              color: #86909c;
              background: #f7f8fa;
              border-radius: 6px;

              // 黑暗模式适配
              html.dark & {
                color: var(--art-text-gray-600);
                background: var(--art-color);
              }
            }

            .progress-trend-chart {
              height: 200px;
              width: 100%;
              max-width: 100%;
              overflow: hidden;
              box-sizing: border-box;
              position: relative;

              // 强制限制ECharts容器
              > div {
                max-width: 100% !important;
                overflow: hidden !important;
              }

              .el-icon {
                font-size: 48px;
                margin-bottom: 8px;
              }

              span {
                font-size: 14px;
              }
            }
          }
        }
      }
    }

    .details-section {
      display: flex;
      gap: 16px;

      .detail-card {
        flex: 1;
        background: white;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 20px;

        // 黑暗模式适配
        html.dark & {
          background: var(--art-main-bg-color);
          border-color: var(--art-border-color);
        }

        .detail-header {
          margin-bottom: 16px;

          h4 {
            margin: 0;
            color: #1f2329;
            font-size: 16px;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }
        }

        .detail-content {
          .member-stats {
            .member-stat-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 0;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .member-info {
                display: flex;
                align-items: center;
                gap: 8px;

                .member-name {
                  font-weight: 500;
                  color: #1f2329;

                  // 黑暗模式适配
                  html.dark & {
                    color: var(--art-text-gray-900);
                  }
                }
              }

              .member-tasks {
                display: flex;
                align-items: center;
                gap: 12px;

                .task-count {
                  font-size: 12px;
                  color: #86909c;

                  // 黑暗模式适配
                  html.dark & {
                    color: var(--art-text-gray-600);
                  }
                }

                .task-progress {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  width: 120px;

                  .progress-text {
                    font-size: 12px;
                    color: #86909c;
                    white-space: nowrap;

                    // 黑暗模式适配
                    html.dark & {
                      color: var(--art-text-gray-600);
                    }
                  }
                }
              }
            }
          }

          .activity-list {
            .activity-item {
              display: flex;
              gap: 12px;
              padding: 12px 0;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              .activity-icon {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #f0f9ff;
                color: #1664ff;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;

                // 黑暗模式适配
                html.dark & {
                  background: rgba(var(--art-primary), 0.1);
                  color: rgb(var(--art-primary));
                }
              }

              .el-icon {
                font-size: 16px;
              }
            }

            .activity-content {
              flex: 1;

              .activity-text {
                color: #1f2329;
                margin-bottom: 4px;
                line-height: 1.4;

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-900);
                }
              }

              .activity-time {
                font-size: 12px;
                color: #86909c;

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-600);
                }
              }
            }
          }

          .no-data {
            text-align: center;
            padding: 20px;
            color: #86909c;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }
        }
      }
    }
  }
</style>
