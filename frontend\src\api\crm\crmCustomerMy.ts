import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 客户表相关接口
 */
export class CrmCustomerMyApi {
  /**
   * 获取客户表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_customer_my/index',
      params
    })
  }

  /**
   * 获取客户表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/crm/crm_customer_my/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加客户表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_customer_my/add',
      data
    })
  }

  /**
   * 更新客户表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/crm/crm_customer_my/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除客户表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_customer_my/delete/${id}`
    })
  }

  /**
   * 批量删除客户表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/crm/crm_customer_my/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 获取下拉选项
   * @param customer_name 客户名称
   */
  static options(customer_name?: string) {
    return request.get<BaseResult>({
      url: `/crm/crm_customer_my/options`,
      params: {
        customer_name
      }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_customer_my/updateField',
      data
    })
  }

  /**
   * 修改客户表状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/crm/crm_customer_my/status/${data.id}`,
      data
    })
  }

  /**
   * 导出客户表数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/crm/crm_customer_my/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导入客户表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/crm/crm_customer_my/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/crm/crm_customer_my/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/crm/crm_customer_my/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}
