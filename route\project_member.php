<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 项目成员表路由
Route::group('api/project/project_member', function () {
	Route::get('index', 'app\project\controller\ProjectMemberController@index');
	Route::get('detail/:id', 'app\project\controller\ProjectMemberController@detail');
	Route::post('add', 'app\project\controller\ProjectMemberController@add');
	Route::post('edit/:id', 'app\project\controller\ProjectMemberController@edit');
	Route::post('delete/:id', 'app\project\controller\ProjectMemberController@delete');
	Route::post('batchDelete', 'app\project\controller\ProjectMemberController@batchDelete');
	Route::post('updateField', 'app\project\controller\ProjectMemberController@updateField');
	Route::post('status/:id', 'app\project\controller\ProjectMemberController@status');
	
	// 获取可添加的用户列表（权限控制）
	Route::get('availableUsers/:projectId', 'app\project\controller\ProjectMemberController@availableUsers');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);