<template>
  <div class="leave-form-view">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="请假类型">
        {{ getLeaveTypeName(formData.leave_type) }}
      </el-descriptions-item>

      <el-descriptions-item label="时间段">
        {{ formData.start_time }} 至 {{ formData.end_time }}
      </el-descriptions-item>

      <el-descriptions-item label="请假时长"> {{ formData.duration }} 小时</el-descriptions-item>

      <!--      <el-descriptions-item label="申请状态">
              <el-tag :type="getStatusType(formData.status)">
                {{ getStatusName(formData.status) }}
              </el-tag>
            </el-descriptions-item>-->

      <el-descriptions-item label="请假事由" :span="2">
        {{ formData.reason || '无' }}
      </el-descriptions-item>

      <el-descriptions-item v-if="formData.emergency_contact" label="紧急联系人">
        {{ formData.emergency_contact }}
      </el-descriptions-item>

      <el-descriptions-item v-if="formData.emergency_phone" label="紧急联系电话">
        {{ formData.emergency_phone }}
      </el-descriptions-item>

      <el-descriptions-item
        v-if="formData.attachment && formData.attachment.length > 0"
        label="附件"
        :span="2"
      >
        <div class="attachment-list">
          <div v-for="(file, index) in formData.attachment" :key="index" class="attachment-item">
            <div class="image-attachment">
              <el-image
                :src="file"
                :preview-src-list="formData.attachment"
                :initial-index="index"
                fit="cover"
                class="attachment-image"
                preview-teleported
                :hide-on-click-modal="true"
              />
            </div>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup lang="ts">
  import { ElDescriptions, ElDescriptionsItem, ElImage } from 'element-plus'

  // 组件属性定义
  const props = defineProps({
    // 表单数据
    formData: {
      type: Object,
      required: true
    },
    // 业务代码
    businessCode: {
      type: String,
      default: 'leave'
    }
  })

  // 获取请假类型名称
  const getLeaveTypeName = (type: string | number) => {
    const leaveTypes: Record<string, string> = {
      '1': '事假',
      '2': '病假',
      '3': '年假',
      '4': '婚假',
      '5': '产假',
      '6': '丧假',
      '7': '调休',
      '8': '其他'
    }

    return leaveTypes[type] || '未知类型'
  }

  // 获取状态名称
  /*const getStatusName = (status: string | number) => {
    const statusMap: Record<string, string> = {
      '0': '草稿',
      '1': '已提交',
      '2': '审批中',
      '3': '已通过',
      '4': '已拒绝',
      '5': '已终止',
      '6': '已撤回'
    }

    return statusMap[status] || '未知状态'
  }*/

  // 获取状态对应的标签类型
  /*const getStatusType = (status: string | number) => {
    const statusTypeMap: Record<string, string> = {
      '0': 'info',
      '1': 'warning',
      '2': 'warning',
      '3': 'success',
      '4': 'danger',
      '5': 'info',
      '6': 'info'
    }

    return statusTypeMap[status] || 'info'
  }*/
</script>

<style scoped lang="scss">
  .leave-form-view {
    width: 100%;
    margin-bottom: 20px;
  }

  .attachment-item {
    margin: 5px 0;
  }

  .leave-form-view :deep(.el-descriptions__cell.is-left-bordered) {
    width: 100%;
  }

  .attachment-list {
    .attachment-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      // 图片附件样式
      .image-attachment {
        display: inline-block;
        margin-right: 12px;
        margin-bottom: 12px;

        .attachment-image {
          width: 80px;
          height: 80px;
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          cursor: pointer;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }
        }
      }

      // 文件附件样式
      .file-attachment {
        display: flex;
        align-items: center;
        gap: 8px;

        .file-size {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
</style>
