<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 任务评论表路由
Route::group('api/project/project_task_comment', function () {
	Route::get('index', 'app\project\controller\ProjectTaskCommentController@index');
	Route::get('detail/:id', 'app\project\controller\ProjectTaskCommentController@detail');
	Route::post('add', 'app\project\controller\ProjectTaskCommentController@add');
	Route::post('edit/:id', 'app\project\controller\ProjectTaskCommentController@edit');
	Route::post('delete/:id', 'app\project\controller\ProjectTaskCommentController@delete');
	Route::post('batchDelete', 'app\project\controller\ProjectTaskCommentController@batchDelete');
	Route::post('updateField', 'app\project\controller\ProjectTaskCommentController@updateField');
	Route::post('status/:id', 'app\project\controller\ProjectTaskCommentController@status');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);