/**
 * API采集器 - 通过公开API获取设计资源
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

class APIDesignCollector {
  constructor(options = {}) {
    this.outputDir = options.outputDir || './api-design-collection';
    this.rateLimitDelay = options.rateLimitDelay || 1000;
    this.maxRetries = options.maxRetries || 3;
  }

  /**
   * GitHub API - 获取优秀的Vue/React项目
   */
  async collectFromGitHub(query = 'vue admin dashboard', options = {}) {
    const searchUrl = 'https://api.github.com/search/repositories';
    const params = {
      q: `${query} language:vue stars:>100`,
      sort: 'stars',
      order: 'desc',
      per_page: options.limit || 30
    };

    try {
      const response = await axios.get(searchUrl, { params });
      const repositories = response.data.items;

      const collectionData = [];
      
      for (const repo of repositories) {
        // 获取仓库详细信息
        const repoData = await this.getRepositoryDetails(repo);
        if (repoData) {
          collectionData.push(repoData);
        }
        
        // 避免API限制
        await this.delay(this.rateLimitDelay);
      }

      await this.saveCollectionData('github-repos', collectionData);
      return collectionData;

    } catch (error) {
      console.error('GitHub API采集失败:', error.message);
      return [];
    }
  }

  /**
   * 获取仓库详细信息
   */
  async getRepositoryDetails(repo) {
    try {
      // 获取README内容
      const readmeUrl = `https://api.github.com/repos/${repo.full_name}/readme`;
      const readmeResponse = await axios.get(readmeUrl, {
        headers: { 'Accept': 'application/vnd.github.v3.raw' }
      });

      // 获取package.json信息
      const packageUrl = `https://api.github.com/repos/${repo.full_name}/contents/package.json`;
      let packageInfo = null;
      try {
        const packageResponse = await axios.get(packageUrl);
        const packageContent = Buffer.from(packageResponse.data.content, 'base64').toString();
        packageInfo = JSON.parse(packageContent);
      } catch (e) {
        // package.json可能不存在
      }

      // 获取目录结构
      const contentsUrl = `https://api.github.com/repos/${repo.full_name}/contents`;
      const contentsResponse = await axios.get(contentsUrl);

      return {
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description,
        stars: repo.stargazers_count,
        forks: repo.forks_count,
        language: repo.language,
        topics: repo.topics,
        homepage: repo.homepage,
        cloneUrl: repo.clone_url,
        readme: readmeResponse.data,
        packageInfo,
        directoryStructure: contentsResponse.data.map(item => ({
          name: item.name,
          type: item.type,
          size: item.size
        })),
        lastUpdated: repo.updated_at,
        createdAt: repo.created_at
      };

    } catch (error) {
      console.error(`获取仓库详情失败: ${repo.full_name}`, error.message);
      return null;
    }
  }

  /**
   * Figma Community API - 获取设计文件
   */
  async collectFromFigmaCommunity(searchTerm = 'dashboard', options = {}) {
    // 注意：Figma Community API需要认证
    const figmaToken = process.env.FIGMA_TOKEN;
    if (!figmaToken) {
      console.warn('需要设置FIGMA_TOKEN环境变量');
      return [];
    }

    try {
      // Figma Community搜索API（示例）
      const searchUrl = 'https://api.figma.com/v1/files/search';
      const response = await axios.get(searchUrl, {
        headers: {
          'X-Figma-Token': figmaToken
        },
        params: {
          term: searchTerm,
          ...options
        }
      });

      const designs = response.data.files || [];
      const collectionData = [];

      for (const design of designs) {
        const designData = await this.getFigmaFileDetails(design.key, figmaToken);
        if (designData) {
          collectionData.push(designData);
        }
        
        await this.delay(this.rateLimitDelay);
      }

      await this.saveCollectionData('figma-designs', collectionData);
      return collectionData;

    } catch (error) {
      console.error('Figma API采集失败:', error.message);
      return [];
    }
  }

  /**
   * 获取Figma文件详情
   */
  async getFigmaFileDetails(fileKey, token) {
    try {
      const fileUrl = `https://api.figma.com/v1/files/${fileKey}`;
      const response = await axios.get(fileUrl, {
        headers: { 'X-Figma-Token': token }
      });

      const file = response.data;
      
      // 提取设计token
      const designTokens = this.extractFigmaDesignTokens(file);
      
      return {
        name: file.name,
        key: fileKey,
        lastModified: file.lastModified,
        version: file.version,
        designTokens,
        components: this.extractFigmaComponents(file),
        styles: this.extractFigmaStyles(file),
        pages: file.document.children.map(page => ({
          name: page.name,
          type: page.type,
          childrenCount: page.children?.length || 0
        }))
      };

    } catch (error) {
      console.error(`获取Figma文件失败: ${fileKey}`, error.message);
      return null;
    }
  }

  /**
   * 提取Figma设计token
   */
  extractFigmaDesignTokens(file) {
    const tokens = {
      colors: [],
      typography: [],
      effects: []
    };

    // 从样式中提取
    if (file.styles) {
      Object.values(file.styles).forEach(style => {
        switch (style.styleType) {
          case 'FILL':
            tokens.colors.push({
              name: style.name,
              description: style.description,
              type: 'color'
            });
            break;
          case 'TEXT':
            tokens.typography.push({
              name: style.name,
              description: style.description,
              type: 'typography'
            });
            break;
          case 'EFFECT':
            tokens.effects.push({
              name: style.name,
              description: style.description,
              type: 'effect'
            });
            break;
        }
      });
    }

    return tokens;
  }

  /**
   * 提取Figma组件
   */
  extractFigmaComponents(file) {
    const components = [];
    
    if (file.components) {
      Object.values(file.components).forEach(component => {
        components.push({
          name: component.name,
          description: component.description,
          key: component.key,
          componentSetId: component.componentSetId
        });
      });
    }

    return components;
  }

  /**
   * 提取Figma样式
   */
  extractFigmaStyles(file) {
    const styles = [];
    
    if (file.styles) {
      Object.values(file.styles).forEach(style => {
        styles.push({
          name: style.name,
          styleType: style.styleType,
          description: style.description
        });
      });
    }

    return styles;
  }

  /**
   * Dribbble API采集
   */
  async collectFromDribbble(tag = 'dashboard', options = {}) {
    const dribbbleToken = process.env.DRIBBBLE_TOKEN;
    if (!dribbbleToken) {
      console.warn('需要设置DRIBBBLE_TOKEN环境变量');
      return [];
    }

    try {
      const shotsUrl = 'https://api.dribbble.com/v2/shots';
      const response = await axios.get(shotsUrl, {
        headers: {
          'Authorization': `Bearer ${dribbbleToken}`
        },
        params: {
          tags: tag,
          per_page: options.limit || 20,
          sort: 'popular'
        }
      });

      const shots = response.data;
      const collectionData = shots.map(shot => ({
        id: shot.id,
        title: shot.title,
        description: shot.description,
        images: shot.images,
        tags: shot.tags,
        user: {
          name: shot.user.name,
          username: shot.user.username,
          avatar: shot.user.avatar_url
        },
        stats: {
          views: shot.views_count,
          likes: shot.likes_count,
          comments: shot.comments_count
        },
        createdAt: shot.created_at,
        updatedAt: shot.updated_at,
        htmlUrl: shot.html_url
      }));

      await this.saveCollectionData('dribbble-shots', collectionData);
      return collectionData;

    } catch (error) {
      console.error('Dribbble API采集失败:', error.message);
      return [];
    }
  }

  /**
   * 组件库文档API采集
   */
  async collectComponentLibraryDocs() {
    const libraries = [
      {
        name: 'element-plus',
        docsUrl: 'https://element-plus.org/zh-CN/component/',
        apiUrl: 'https://api.github.com/repos/element-plus/element-plus'
      },
      {
        name: 'ant-design-vue',
        docsUrl: 'https://antdv.com/components/',
        apiUrl: 'https://api.github.com/repos/vueComponent/ant-design-vue'
      },
      {
        name: 'naive-ui',
        docsUrl: 'https://www.naiveui.com/zh-CN/os-theme/components/',
        apiUrl: 'https://api.github.com/repos/TuSimple/naive-ui'
      }
    ];

    const collectionData = [];

    for (const library of libraries) {
      try {
        const repoResponse = await axios.get(library.apiUrl);
        const repoData = repoResponse.data;

        // 获取组件列表（从README或特定API）
        const components = await this.getComponentList(library);

        collectionData.push({
          name: library.name,
          docsUrl: library.docsUrl,
          repository: {
            stars: repoData.stargazers_count,
            forks: repoData.forks_count,
            issues: repoData.open_issues_count,
            lastUpdate: repoData.updated_at
          },
          components,
          metadata: {
            language: repoData.language,
            license: repoData.license?.name,
            topics: repoData.topics
          }
        });

        await this.delay(this.rateLimitDelay);

      } catch (error) {
        console.error(`采集组件库失败: ${library.name}`, error.message);
      }
    }

    await this.saveCollectionData('component-libraries', collectionData);
    return collectionData;
  }

  /**
   * 获取组件列表
   */
  async getComponentList(library) {
    // 这里可以根据不同的组件库实现不同的获取策略
    // 例如解析文档页面、读取配置文件等
    const commonComponents = [
      'Button', 'Input', 'Select', 'Table', 'Form', 
      'Dialog', 'Card', 'Menu', 'Pagination', 'Upload'
    ];

    return commonComponents.map(name => ({
      name,
      url: `${library.docsUrl}${name.toLowerCase()}.html`,
      category: this.categorizeComponent(name)
    }));
  }

  /**
   * 组件分类
   */
  categorizeComponent(componentName) {
    const categories = {
      'Basic': ['Button', 'Icon', 'Typography'],
      'Form': ['Input', 'Select', 'Checkbox', 'Radio', 'Form'],
      'Data Display': ['Table', 'List', 'Card', 'Collapse'],
      'Navigation': ['Menu', 'Breadcrumb', 'Pagination', 'Steps'],
      'Feedback': ['Alert', 'Message', 'Notification', 'Dialog'],
      'Layout': ['Grid', 'Layout', 'Space', 'Divider'],
      'Others': ['Upload', 'Transfer', 'Tree', 'Calendar']
    };

    for (const [category, components] of Object.entries(categories)) {
      if (components.includes(componentName)) {
        return category;
      }
    }
    return 'Others';
  }

  /**
   * 延迟函数
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 保存采集数据
   */
  async saveCollectionData(filename, data) {
    await this.ensureDir(this.outputDir);
    const filePath = path.join(this.outputDir, `${filename}-${Date.now()}.json`);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`数据已保存到: ${filePath}`);
  }

  /**
   * 确保目录存在
   */
  async ensureDir(dirPath) {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * 批量采集所有来源
   */
  async collectAll(options = {}) {
    console.log('开始批量采集设计资源...');
    
    const results = {
      github: await this.collectFromGitHub('vue admin dashboard', options.github),
      componentLibraries: await this.collectComponentLibraryDocs(),
      // figma: await this.collectFromFigmaCommunity('dashboard', options.figma),
      // dribbble: await this.collectFromDribbble('dashboard', options.dribbble)
    };

    // 生成汇总报告
    const summary = {
      timestamp: new Date().toISOString(),
      totalItems: Object.values(results).reduce((sum, items) => sum + items.length, 0),
      sources: Object.keys(results),
      results
    };

    await this.saveCollectionData('collection-summary', summary);
    
    console.log('批量采集完成！');
    console.log(`总共采集了 ${summary.totalItems} 个项目/资源`);
    
    return summary;
  }
}

module.exports = APIDesignCollector;

// 使用示例
if (require.main === module) {
  const collector = new APIDesignCollector();
  
  collector.collectAll({
    github: { limit: 20 },
    figma: { limit: 10 },
    dribbble: { limit: 15 }
  }).catch(console.error);
}
