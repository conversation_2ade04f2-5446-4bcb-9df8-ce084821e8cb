<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 每日报价单表路由
Route::group('api/daily/daily_price_order', function () {
	Route::get('index', 'app\daily\controller\DailyPriceOrderController@index');
	Route::get('detail/:id', 'app\daily\controller\DailyPriceOrderController@detail');
	Route::post('add', 'app\daily\controller\DailyPriceOrderController@add');
	Route::post('edit/:id', 'app\daily\controller\DailyPriceOrderController@edit');
	Route::post('delete/:id', 'app\daily\controller\DailyPriceOrderController@delete');
	//	Route::post('batchDelete', 'app\daily\controller\DailyPriceOrderController@batchDelete');
	//	Route::post('updateField', 'app\daily\controller\DailyPriceOrderController@updateField');
	//	Route::post('status/:id', 'app\daily\controller\DailyPriceOrderController@status');
	//	Route::post('import', 'app\daily\controller\DailyPriceOrderController@import');
	//	Route::get('importTemplate', 'app\daily\controller\DailyPriceOrderController@importTemplate');
	//	Route::get('downloadTemplate', 'app\daily\controller\DailyPriceOrderController@downloadTemplate');
	//	Route::get('export', 'app\daily\controller\DailyPriceOrderController@export');
	
	// 审批相关路由
	Route::post('submit_approval', 'app\daily\controller\DailyPriceOrderController@submitApproval');
	Route::post('recall_approval', 'app\daily\controller\DailyPriceOrderController@recallApproval');
	Route::post('void_order', 'app\daily\controller\DailyPriceOrderController@voidOrder');
	
	// 业务操作路由
	//	Route::post('handle_approval_passed', 'app\daily\controller\DailyPriceOrderController@handleApprovalPassed');
	
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);

