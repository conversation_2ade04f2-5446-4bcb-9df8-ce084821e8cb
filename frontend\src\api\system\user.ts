import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 用户相关接口
 */
export class UserApi {
  /**
   * 获取用户列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/system/user/index',
      params
    })
  }

  /**
   * 获取用户详情
   * @param id 用户ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/system/user/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加用户
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/system/user/add',
      data
    })
  }

  /**
   * 更新用户
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/system/user/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除用户
   * @param id 用户ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/system/user/delete/${id}`
    })
  }

  /**
   * 批量删除用户
   * @param ids 用户ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/system/user/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/system/user/updateField',
      data
    })
  }

  /**
   * 获取用户下拉选项
   * @param params 查询参数
   */
  static options(params?: any) {
    return request.get<BaseResult>({
      url: '/system/user/options',
      params
    })
  }

  /**
   * 重置密码
   * @param id 用户ID
   * @param password 新密码
   */
  static resetPassword(id: number | string, password: string) {
    return request.post<BaseResult>({
      url: `/system/user/resetPassword/${id}`,
      data: { password }
    })
  }

  /**
   * 修改用户状态
   * @param id 用户ID
   * @param status 状态
   */
  static changeStatus(id: number | string, status: number) {
    return request.post<BaseResult>({
      url: `/system/user/status/${id}`,
      data: { status }
    })
  }

  /**
   * 获取当前用户信息
   */
  static getCurrentUser() {
    return request.get<BaseResult>({
      url: '/system/user/current'
    })
  }

  /**
   * 更新当前用户信息
   * @param data 用户数据
   */
  static updateCurrentUser(data: any) {
    return request.post<BaseResult>({
      url: '/system/user/updateCurrent',
      data
    })
  }

  /**
   * 修改当前用户密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   */
  static changePassword(oldPassword: string, newPassword: string) {
    return request.post<BaseResult>({
      url: '/system/user/changePassword',
      data: {
        old_password: oldPassword,
        new_password: newPassword
      }
    })
  }

  /**
   * 获取销售人员列表（用于CRM分配）
   * @param params 查询参数
   */
  static getSalesUsers(params?: any) {
    return request.get<BaseResult>({
      url: '/system/user/salesUsers',
      params: {
        role_type: 'sales', // 销售角色
        status: 1, // 启用状态
        ...params
      }
    })
  }

  /**
   * 根据部门获取用户列表
   * @param departmentId 部门ID
   * @param params 其他查询参数
   */
  static getUsersByDepartment(departmentId: number | string, params?: any) {
    return request.get<BaseResult>({
      url: '/system/user/byDepartment',
      params: {
        department_id: departmentId,
        ...params
      }
    })
  }
}
