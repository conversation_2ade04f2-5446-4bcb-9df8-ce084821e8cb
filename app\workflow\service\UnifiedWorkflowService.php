<?php
declare(strict_types=1);

namespace app\workflow\service;

use app\common\exception\BusinessException;
use app\workflow\constants\WorkflowStatusConstant;
use app\workflow\factory\DynamicWorkflowFactory;
use app\workflow\interfaces\FormServiceInterface;
use app\workflow\model\WorkflowDefinition;
use app\workflow\model\WorkflowType;
use think\facade\Db;
use think\facade\Log;

/**
 * 统一工作流操作服务
 *
 * 提供标准化的工作流操作接口，统一封装审批、撤回、作废等操作
 * 消除WorkflowableService的重复功能，简化业务Service的工作流集成
 */
class UnifiedWorkflowService
{
	/**
	 * 执行工作流操作
	 *
	 * @param string $operation 操作类型 (submit|withdraw|void|terminate)
	 * @param array  $params    操作参数
	 * @return int|string 操作结果
	 * @throws BusinessException
	 */
	public function executeWorkflowOperation(string $operation, array $params)
	{
		// 验证必需参数
		$this->validateParams($operation, $params);
		
		// 记录操作日志
		Log::info("开始执行工作流操作", [
			'operation'     => $operation,
			'business_code' => $params['business_code'] ?? '',
			'business_id'   => $params['business_id'] ?? '',
			'operator_id'   => $params['operator_id'] ?? get_user_id()
		]);
		
		try {
			switch ($operation) {
				case 'submit':
					return $this->submitWorkflow($params);
				
				case 'withdraw':
					return $this->withdrawWorkflow($params);
				
				case 'void':
					return $this->voidWorkflow($params);
				
				case 'terminate':
					return $this->terminateWorkflow($params);
				
				default:
					throw new BusinessException("不支持的操作类型: {$operation}");
			}
		}
		catch (\Exception $e) {
			Log::error("工作流操作执行失败", [
				'operation' => $operation,
				'params'    => $params,
				'error'     => $e->getMessage()
			]);
			throw $e;
		}
	}
	
	/**
	 * 提交工作流
	 *
	 * @param array $params 参数
	 * @return int|string instance_id
	 */
	private function submitWorkflow(array $params): int|string
	{
		Db::startTrans();
		try {
			// 1. 验证业务数据
			$formService  = $this->getFormService($params['business_code']);
			$businessData = $formService->getFormData($params['business_id']);
			
			if (empty($businessData)) {
				throw new BusinessException('业务记录不存在');
			}
			
			// 2. 验证业务状态
			$this->validateBusinessStatus($businessData, 'submit');
			
			// 3. 创建工作流实例
			$businessWorkflowService = new BusinessWorkflowService();
			$workflowParams          = [
				'business_code'  => $params['business_code'],
				'business_id'    => $params['business_id'],
				'title'          => $params['title'] ?? $formService->getInstanceTitle($businessData),
				'submitter_id'   => $params['operator_id'] ?? get_user_id(),
				'submitter_name' => get_user_name($params['operator_id'] ?? get_user_id()),
				'form_data'      => $businessData,
				'definition_id'  => $params['definition_id'] ?? $this->getDefaultDefinitionId($params['business_code'])
			];
			
			$result = $businessWorkflowService->createWorkflowForBusiness($workflowParams);
			
			Db::commit();
			
			Log::info("工作流提交成功", [
				'business_code' => $params['business_code'],
				'business_id'   => $params['business_id'],
				'instance_id'   => $result['instance_id']
			]);
			
			return $result['instance_id'];
			
		}
		catch (\Exception $e) {
			Db::rollback();
			throw new BusinessException("提交失败: " . $e->getMessage());
		}
	}
	
	/**
	 * 撤回工作流
	 *
	 * @param array $params 参数
	 * @return int|string instance_id
	 */
	private function withdrawWorkflow(array $params)
	{
		try {
			// 1. 验证业务数据和状态
			$formService  = $this->getFormService($params['business_code']);
			$businessData = $formService->getFormData($params['business_id']);
			
			if (empty($businessData)) {
				throw new BusinessException('业务记录不存在');
			}
			
			$this->validateBusinessStatus($businessData, 'withdraw');
			
			// 2. 执行撤回操作
			$syncService = new WorkflowStatusSyncService();
			$result      = $syncService->syncAllWorkflowStatus($businessData['workflow_instance_id'], WorkflowStatusConstant::STATUS_RECALLED, $params['reason'] ?? '用户撤回申请', $params['operator_id'] ?? get_user_id());
			
			if (!$result) {
				throw new BusinessException('撤回操作失败');
			}
			
			Log::info("工作流撤回成功", [
				'business_code' => $params['business_code'],
				'business_id'   => $params['business_id'],
				'instance_id'   => $businessData['workflow_instance_id']
			]);
			
			return $businessData['workflow_instance_id'];
			
		}
		catch (\Exception $e) {
			throw new BusinessException("撤回失败: " . $e->getMessage());
		}
	}
	
	/**
	 * 作废工作流
	 *
	 * @param array $params 参数
	 * @return int|string instance_id
	 */
	private function voidWorkflow(array $params): int|string
	{
		try {
			// 1. 验证业务数据和状态
			$formService  = $this->getFormService($params['business_code']);
			$businessData = $formService->getFormData($params['business_id']);
			
			if (empty($businessData)) {
				throw new BusinessException('业务记录不存在');
			}
			
			$this->validateBusinessStatus($businessData, 'void');
			
			// 2. 执行作废操作
			$syncService = new WorkflowStatusSyncService();
			$result      = $syncService->syncAllWorkflowStatus($businessData['workflow_instance_id'], WorkflowStatusConstant::STATUS_VOID, $params['reason'] ?? '业务作废', $params['operator_id'] ?? get_user_id());
			
			if (!$result) {
				throw new BusinessException('作废操作失败');
			}
			
			Log::info("工作流作废成功", [
				'business_code' => $params['business_code'],
				'business_id'   => $params['business_id'],
				'instance_id'   => $businessData['workflow_instance_id']
			]);
			
			return $businessData['workflow_instance_id'];
			
		}
		catch (\Exception $e) {
			throw new BusinessException("作废失败: " . $e->getMessage());
		}
	}
	
	/**
	 * 终止工作流
	 *
	 * @param array $params 参数
	 * @return int|string 结果
	 */
	private function terminateWorkflow(array $params): int|string
	{
		try {
			// 1. 验证业务数据和状态
			$formService  = $this->getFormService($params['business_code']);
			$businessData = $formService->getFormData($params['business_id']);
			
			if (empty($businessData)) {
				throw new BusinessException('业务记录不存在');
			}
			
			$this->validateBusinessStatus($businessData, 'terminate');
			
			// 2. 执行终止操作
			$syncService = new WorkflowStatusSyncService();
			$result      = $syncService->syncAllWorkflowStatus($businessData['workflow_instance_id'], WorkflowStatusConstant::STATUS_TERMINATED, $params['reason'] ?? '流程终止', $params['operator_id'] ?? get_user_id());
			
			if (!$result) {
				throw new BusinessException('终止操作失败');
			}
			
			Log::info("工作流终止成功", [
				'business_code' => $params['business_code'],
				'business_id'   => $params['business_id'],
				'instance_id'   => $businessData['workflow_instance_id']
			]);
			
			return $businessData['workflow_instance_id'];
			
		}
		catch (\Exception $e) {
			throw new BusinessException("终止失败: " . $e->getMessage());
		}
	}
	
	/**
	 * 获取FormService实例
	 *
	 * @param string $businessCode 业务代码
	 * @return FormServiceInterface
	 * @throws BusinessException
	 */
	public function getFormService(string $businessCode): FormServiceInterface
	{
		// 临时解决方案：直接映射已知的业务类型
		/*$serviceMap = [
			'daily_price_order'       => \app\daily\service\DailyPriceOrderService::class,
			'hr_leave'                => \app\hr\service\HrLeaveService::class,
			'crm_contract'            => \app\crm\service\CrmContractService::class,
			'crm_contract_receivable' => \app\crm\service\CrmContractReceivableService::class,
		];
		
		if (isset($serviceMap[$businessCode])) {
			$serviceClass = $serviceMap[$businessCode];
			if (class_exists($serviceClass)) {
				$service = new $serviceClass();
				if ($service instanceof FormServiceInterface) {
					return $service;
				}
			}
		}*/
		
		// 回退到动态工厂
		$formService = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
		
		if (!$formService) {
			throw new BusinessException("不支持的业务类型: {$businessCode}");
		}
		
		return $formService;
	}
	
	/**
	 * 验证参数
	 *
	 * @param string $operation 操作类型
	 * @param array  $params    参数
	 * @throws BusinessException
	 */
	private function validateParams(string $operation, array $params): void
	{
		// 通用必需参数
		$requiredParams = [
			'business_code',
			'business_id'
		];
		
		// 特定操作的额外必需参数
		if ($operation === 'submit') {
			// 提交操作可能需要额外参数，但这里保持灵活
		}
		
		foreach ($requiredParams as $param) {
			if (!isset($params[$param]) || empty($params[$param])) {
				throw new BusinessException("缺少必需参数: {$param}");
			}
		}
	}
	
	/**
	 * 验证业务状态
	 *
	 * @param array  $businessData 业务数据
	 * @param string $operation    操作类型
	 * @throws BusinessException
	 */
	private function validateBusinessStatus(array $businessData, string $operation): void
	{
		$approvalStatus = $businessData['approval_status'] ?? 0;
		
		switch ($operation) {
			case 'submit':
				if ($approvalStatus !== 0) { // 草稿状态
					throw new BusinessException('只有草稿状态的记录才能提交审批');
				}
				break;
			
			case 'withdraw':
				if ($approvalStatus !== 1) { // 审批中状态
					throw new BusinessException('只有审批中的记录才能撤回');
				}
				if (empty($businessData['workflow_instance_id'])) {
					throw new BusinessException('未找到关联的工作流实例');
				}
				break;
			
			case 'void':
			case 'terminate':
				if (!in_array($approvalStatus, [
					1,
					2
				])) { // 审批中或已通过状态
					throw new BusinessException('当前状态不允许此操作');
				}
				if (empty($businessData['workflow_instance_id'])) {
					throw new BusinessException('未找到关联的工作流实例');
				}
				break;
		}
	}
	
	/**
	 * 获取默认的工作流定义ID
	 *
	 * @param string $businessCode 业务代码
	 * @return int
	 */
	private function getDefaultDefinitionId(string $businessCode): int
	{
		// 查找对应业务类型的默认工作流定义
		$workflowType = WorkflowType::where('business_code', $businessCode)
		                            ->where('status', 1)
		                            ->findOrEmpty();
		
		if ($workflowType->isEmpty()) {
			throw new BusinessException("未找到对应的业务类型: {$businessCode}");
		}
		
		
		$definition = WorkflowDefinition::where('type_id', $workflowType->id)
		                                ->where('status', 1)
		                                ->order('id', 'asc')
		                                ->findOrEmpty();
		
		if ($definition->isEmpty()) {
			throw new BusinessException("未找到可用的工作流定义");
		}
		
		return $definition->id;
	}
}
