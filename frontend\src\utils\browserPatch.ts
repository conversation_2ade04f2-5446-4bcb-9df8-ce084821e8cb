/**
 * 浏览器兼容性补丁
 * 解决 "Unable to preventDefault inside passive event listener invocation" 错误
 * 
 * 这个错误通常出现在使用 Element Plus 的 DatePicker 等组件时，
 * 与 default-passive-events 包产生冲突导致的。
 */

;(function () {
  if (typeof EventTarget !== 'undefined') {
    const func = EventTarget.prototype.addEventListener
    EventTarget.prototype.addEventListener = function (type, fn, capture) {
      ;(this as any).func = func
      if (typeof capture !== 'boolean') {
        capture = capture || {}
        capture.passive = false
      }
      ;(this as any).func(type, fn, capture)
    }
  }
})()
