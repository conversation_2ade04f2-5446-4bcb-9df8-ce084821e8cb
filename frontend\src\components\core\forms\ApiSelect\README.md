# ApiSelect 通用API选择器组件

## 📋 组件概述

`ApiSelect` 是一个基于 Element Plus 的通用API选择器组件，支持：
- ✅ 单选/多选模式
- ✅ 远程搜索
- ✅ 智能数据缓存
- ✅ 自定义字段映射
- ✅ 完整的 Element Plus 配置支持
- ✅ 预加载策略优化
- ✅ 防重复请求机制

## 🎯 核心特性

### 1. 灵活的API配置
```typescript
interface ApiConfig {
  url: string                                    // API 地址 *必填*
  method?: 'get' | 'post'                       // 请求方法，默认 'get'
  params?: Record<string, any>                  // 固定参数
  searchParam?: string                          // 搜索参数名，默认 'keyword'
  dataPath?: string                             // 数据路径，默认 'data'
  listPath?: string                             // 列表路径，默认 null
  transform?: (data: any) => OptionItem[]       // 数据转换函数
  headers?: Record<string, string>              // 请求头
}
```

### 2. 字段映射配置
```typescript
// 支持自定义字段映射
labelField?: string      // label 字段名，默认 'name'
valueField?: string      // value 字段名，默认 'id'
disabledField?: string   // disabled 字段名，默认 'disabled'
extraField?: string      // 额外信息字段名
```

### 3. 智能加载策略 🆕
- **预加载模式**：组件挂载时自动加载数据，提供最佳用户体验
- **防重复请求**：内置防重复机制，避免不必要的API调用
- **智能缓存**：自动缓存结果，避免重复请求
- **精确监听**：只监听API URL变化，避免误触发重新加载
- **状态管理**：完善的初始化状态管理，确保数据加载的准确性
- **性能优化**：选择值时无额外请求，关闭下拉框时无额外请求

## 🚀 使用示例

### 基础用法（推荐配置）
```vue
<template>
  <ApiSelect
    v-model="selectedUser"
    :api="{ url: '/api/users' }"
    placeholder="请选择用户"
    :auto-load="true"
    :load-on-focus="false"
  />
</template>

<script setup>
import { ref } from 'vue'
import ApiSelect from '@/components/core/forms/ApiSelect'

const selectedUser = ref(null)
</script>
```

### 表单中的最佳实践
```vue
<template>
  <!-- 产品分类选择器 -->
  <ApiSelect
    v-model="formData.category_id"
    :api="{ url: '/crm/crm_product_category/options' }"
    placeholder="请选择产品分类"
    clearable
    :auto-load="true"
    :load-on-focus="false"
  />

  <!-- 计量单位选择器 -->
  <ApiSelect
    v-model="formData.unit_id"
    :api="{ url: '/crm/crm_product_unit/options' }"
    placeholder="请选择计量单位"
    clearable
    :auto-load="true"
    :load-on-focus="false"
  />
</template>
```

### 多选模式
```vue
<template>
  <ApiSelect
    v-model="selectedUsers"
    :api="{ url: '/api/users' }"
    :multiple="true"
    placeholder="请选择用户"
    :collapse-tags="true"
  />
</template>

<script setup>
const selectedUsers = ref([])
</script>
```

### 自定义字段映射
```vue
<template>
  <ApiSelect
    v-model="selectedDept"
    :api="{ url: '/api/departments' }"
    label-field="dept_name"
    value-field="dept_id"
    extra-field="dept_code"
    :show-option-extra="true"
  />
</template>
```

### 复杂API配置
```vue
<template>
  <ApiSelect
    v-model="selectedProduct"
    :api="apiConfig"
    :multiple="true"
    @load-success="handleLoadSuccess"
    @load-error="handleLoadError"
  />
</template>

<script setup>
const apiConfig = {
  url: '/api/products/search',
  method: 'post',
  params: { status: 1, category: 'electronics' },
  searchParam: 'q',
  dataPath: 'result.items',
  headers: { 'X-Custom-Header': 'value' },
  transform: (data) => data.map(item => ({
    id: item.product_id,
    name: item.product_name,
    disabled: !item.available
  }))
}

const handleLoadSuccess = (data) => {
  console.log('数据加载成功:', data)
}

const handleLoadError = (error) => {
  console.error('数据加载失败:', error)
}
</script>
```

### 在搜索表单中使用
```vue
<template>
  <ArtSearchBar
    :items="searchItems"
    v-model:filter="searchFilter"
    @search="handleSearch"
  />
</template>

<script setup>
const searchItems = [
  {
    label: '负责人',
    prop: 'owner_id',
    type: 'api-select',
    config: {
      api: {
        url: '/api/users',
        params: { role: 'manager' }
      },
      placeholder: '请选择负责人',
      clearable: true
    }
  },
  {
    label: '部门',
    prop: 'dept_ids',
    type: 'api-select',
    config: {
      api: { url: '/api/departments' },
      multiple: true,
      placeholder: '请选择部门',
      collapseTags: true
    }
  }
]
</script>
```

## 📊 Props 配置

### 必填配置
| 参数 | 类型 | 说明 |
|------|------|------|
| api | ApiConfig | API 配置对象，必须包含 url |

### 基础配置
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | SelectValue | - | v-model 绑定值 |
| multiple | boolean | false | 是否多选 |
| placeholder | string | '请选择' | 占位符 |
| clearable | boolean | true | 是否可清除 |
| readonly | boolean | false | 是否只读 |
| disabled | boolean | false | 是否禁用 |
| size | string | 'default' | 尺寸 |

### 字段映射
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| labelField | string | 'name' | label 字段名 |
| valueField | string | 'id' | value 字段名 |
| disabledField | string | 'disabled' | disabled 字段名 |
| extraField | string | - | 额外信息字段名 |

### 搜索配置
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| filterable | boolean | true | 是否可搜索 |
| remote | boolean | true | 是否远程搜索 |
| minSearchLength | number | 0 | 最小搜索长度 |
| searchDelay | number | 300 | 搜索延迟(ms) |

### 行为配置
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| autoLoad | boolean | true | 是否自动加载（推荐：true） |
| cacheResults | boolean | true | 是否缓存结果 |
| loadOnFocus | boolean | true | 是否聚焦时加载（推荐：false） |

### 🎯 加载策略说明

#### 推荐配置（预加载模式）
```vue
<ApiSelect
  :auto-load="true"
  :load-on-focus="false"
  :api="{ url: '/api/options' }"
/>
```
- ✅ **用户体验最佳**：表单打开时预加载，用户点击下拉框立即看到选项
- ✅ **避免重复请求**：内置防重复机制和缓存
- ✅ **性能优化**：选择值时无额外请求，关闭下拉框时无额外请求
- ✅ **智能监听**：只监听API URL变化，避免误触发重新加载
- ✅ **适用场景**：表单中的选择器（如产品分类、计量单位等）

#### 按需加载模式
```vue
<ApiSelect
  :auto-load="false"
  :load-on-focus="true"
  :api="{ url: '/api/options' }"
/>
```
- ⚠️ **用户体验较差**：用户点击时需要等待加载
- ✅ **节省资源**：只在需要时才加载
- ✅ **适用场景**：搜索表单中的可选筛选项

## 🎭 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value | v-model 更新 |
| change | value, option | 值变化 |
| clear | - | 清空 |
| focus | event | 聚焦 |
| blur | event | 失焦 |
| visible-change | visible | 下拉显示状态变化 |
| load-success | data | 数据加载成功 |
| load-error | error | 数据加载失败 |

## 🔧 Methods 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| refresh | - | 刷新数据 |
| clearCache | - | 清空缓存 |
| loadData | searchKeyword? | 加载数据 |
| getOptionList | - | 获取选项列表 |

## 📝 API 响应格式

### 标准响应格式
```json
{
  "code": 1,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "disabled": false
    }
  ],
  "message": "success"
}
```

### 嵌套数据格式
```json
{
  "code": 1,
  "data": {
    "result": {
      "items": [
        {
          "user_id": 1,
          "user_name": "张三",
          "status": 1
        }
      ]
    }
  }
}
```

配置：
```javascript
{
  dataPath: 'data.result.items',
  labelField: 'user_name',
  valueField: 'user_id',
  transform: (data) => data.map(item => ({
    id: item.user_id,
    name: item.user_name,
    disabled: item.status !== 1
  }))
}
```

## ⚠️ 注意事项

### 1. API 要求
- API 必须返回 `code: 1` 表示成功
- 数据必须是数组格式
- 支持 GET/POST 请求方法

### 2. 加载策略选择 🆕
- **表单场景**：推荐使用 `autoLoad: true, loadOnFocus: false`
- **搜索场景**：可使用 `autoLoad: false, loadOnFocus: true`
- **避免重复请求**：组件内置防重复机制，无需担心重复调用
- **智能监听**：只监听API URL变化，避免响应式数据变化导致的误触发
- **状态管理**：完善的初始化状态，确保数据只在需要时加载

### 3. 性能优化 🆕
- 启用缓存可减少重复请求
- 设置合理的搜索延迟
- 使用数据转换函数优化数据结构
- 合理配置加载策略
- **零额外请求**：选择值和关闭下拉框时无额外API请求
- **精确监听**：避免不必要的重新加载

### 4. 错误处理
- 组件会自动处理 API 错误
- 可通过 `load-error` 事件监听错误
- 错误时会显示空状态

### 5. 最佳实践
```vue
<!-- ✅ 推荐：表单中的选择器 -->
<ApiSelect
  v-model="formData.category_id"
  :api="{ url: '/api/categories/options' }"
  placeholder="请选择分类"
  clearable
  :auto-load="true"
  :load-on-focus="false"
/>

<!-- ❌ 不推荐：会导致重复请求 -->
<ApiSelect
  v-model="formData.category_id"
  :api="{ url: '/api/categories/options' }"
  :auto-load="true"
  :load-on-focus="true"
/>
```

## 🎉 总结

`ApiSelect` 组件提供了完整的API选择器解决方案：

- ✅ **智能加载策略**：支持预加载和按需加载两种模式
- ✅ **防重复请求**：内置防重复机制，避免不必要的API调用
- ✅ **精确监听**：只监听API URL变化，避免误触发重新加载
- ✅ **零额外请求**：选择值和关闭下拉框时无额外API请求
- ✅ **灵活配置**：支持各种API格式和字段映射
- ✅ **性能优化**：智能缓存和防抖搜索
- ✅ **用户体验**：预加载模式提供最佳用户体验
- ✅ **易于集成**：可用于表单、搜索栏等各种场景

### 🎯 推荐使用场景

| 场景 | autoLoad | loadOnFocus | 说明 |
|------|----------|-------------|------|
| 表单选择器 | true | false | 预加载，用户体验最佳 |
| 搜索筛选器 | false | true | 按需加载，节省资源 |
| 弹窗表单 | true | false | 预加载，避免用户等待 |

适用于所有需要从API获取选项数据的选择器场景！
