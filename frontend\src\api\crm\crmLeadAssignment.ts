import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 线索分配记录相关接口
 */
export class CrmLeadAssignmentApi {
  /**
   * 获取线索分配记录列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_lead_assignment/index',
      params
    })
  }

  /**
   * 获取线索分配记录详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/crm/crm_lead_assignment/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加线索分配记录
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_lead_assignment/add',
      data
    })
  }

  /**
   * 更新线索分配记录
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_assignment/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除线索分配记录
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_assignment/delete/${id}`
    })
  }

  /**
   * 批量删除线索分配记录
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/crm/crm_lead_assignment/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/crm/crm_lead_assignment/updateField',
      data
    })
  }

  /**
   * 获取下拉选项
   */
  static options() {
    return request.get<BaseResult>({
      url: `/crm/crm_lead_assignment/options`
    })
  }

  /**
   * 导出线索分配记录数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/crm/crm_lead_assignment/export',
      params,
      responseType: 'blob'
    })
  }

  /**
   * 导入线索分配记录数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/crm/crm_lead_assignment/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/crm/crm_lead_assignment/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/crm/crm_lead_assignment/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }

  /**
   * 根据线索ID获取分配记录
   * @param leadId 线索ID
   * @param params 查询参数
   */
  static getByLeadId(leadId: number | string, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_lead_assignment/index',
      params: {
        lead_id: leadId,
        ...params
      }
    })
  }

  /**
   * 根据用户ID获取分配记录
   * @param userId 用户ID
   * @param params 查询参数
   */
  static getByUserId(userId: number | string, params?: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/crm/crm_lead_assignment/index',
      params: {
        to_user_id: userId,
        ...params
      }
    })
  }
}
