<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 线索分配记录表路由
Route::group('api/crm/crm_lead_assignment', function () {
	Route::get('index', 'app\crm\controller\CrmLeadAssignmentController@index');
	Route::get('detail/:id', 'app\crm\controller\CrmLeadAssignmentController@detail');
	Route::post('add', 'app\crm\controller\CrmLeadAssignmentController@add');
	Route::post('edit/:id', 'app\crm\controller\CrmLeadAssignmentController@edit');
	Route::post('delete/:id', 'app\crm\controller\CrmLeadAssignmentController@delete');
	Route::post('batchDelete', 'app\crm\controller\CrmLeadAssignmentController@batchDelete');
	Route::post('updateField', 'app\crm\controller\CrmLeadAssignmentController@updateField');
	Route::get('options', 'app\crm\controller\CrmLeadAssignmentController@options');
	Route::post('import', 'app\crm\controller\CrmLeadAssignmentController@import');
	Route::get('importTemplate', 'app\crm\controller\CrmLeadAssignmentController@importTemplate');
	Route::get('downloadTemplate', 'app\crm\controller\CrmLeadAssignmentController@downloadTemplate');
	Route::get('export', 'app\crm\controller\CrmLeadAssignmentController@export');
})
     ->middleware([
	     TokenAuthMiddleware::class,
	     PermissionMiddleware::class
     ]);