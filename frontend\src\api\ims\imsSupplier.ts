import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 供应商表相关接口
 */
export class ImsSupplierApi {
  /**
   * 获取供应商表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/ims/ims_supplier/index',
      params
    })
  }

  /**
   * 获取供应商表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/ims/ims_supplier/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加供应商表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/ims/ims_supplier/add',
      data
    })
  }

  /**
   * 更新供应商表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/ims/ims_supplier/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除供应商表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/ims/ims_supplier/delete/${id}`
    })
  }

  /**
   * 批量删除供应商表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/ims/ims_supplier/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/ims/ims_supplier/updateField',
      data
    })
  }

  
  /**
   * 修改供应商表状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/ims/ims_supplier/status/${data.id}`,
      data
    })
  }

  /**
   * 获取供应商选项（用于下拉选择）
   */
  static options() {
    return request.get<BaseResult>({
      url: '/ims/ims_supplier/options'
    })
  }
  

  
  /**
   * 导出供应商表数据
   * @param params 导出参数
   */
  static export(params: any) {
    return request.get({
      url: '/ims/ims_supplier/export',
      params,
      responseType: 'blob'
    })
  }
  


  /**
   * 导入供应商表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/ims/ims_supplier/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/ims/ims_supplier/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/ims/ims_supplier/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}