<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑任务' : '新建任务'"
    width="600px"
    :before-close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :loading="true" animated>
        <template #template>
          <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 16px" />
          <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 16px" />
          <el-skeleton-item variant="text" style="width: 60%; margin-bottom: 16px" />
          <el-skeleton-item variant="text" style="width: 90%; margin-bottom: 16px" />
          <el-skeleton-item variant="text" style="width: 70%; margin-bottom: 16px" />
        </template>
      </el-skeleton>
    </div>

    <!-- 表单内容 -->
    <el-form
      v-else
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="任务标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入任务标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入任务描述"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="任务状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择任务状态" style="width: 100%">
              <el-option label="待办" :value="1" />
              <el-option label="进行中" :value="2" />
              <el-option label="已完成" :value="3" />
              <el-option label="已关闭" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
              <el-option label="紧急" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 只有在没有指定项目ID时才显示项目选择 -->
      <el-form-item v-if="!props.projectId" label="所属项目" prop="project_id">
        <el-select
          v-model="formData.project_id"
          placeholder="请选择所属项目"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="project in projectList"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
      </el-form-item>

      <!-- 当指定了项目ID时，显示项目名称（只读） -->
      <!--      <el-form-item v-else label="所属项目">
              <el-input :value="currentProjectName" readonly placeholder="当前项目" style="width: 100%" />
            </el-form-item>-->

      <el-form-item label="执行人" prop="assignee_id">
        <ApiSelect
          v-if="assigneeApiConfig"
          v-model="formData.assignee_id"
          :api="assigneeApiConfig"
          :placeholder="isProjectOwner ? '请选择执行人' : '当前任务执行人为您自己'"
          :emptyText="isProjectOwner ? '暂无项目成员' : '您不是项目负责人，只能创建自己的任务'"
          loadingText="加载成员中..."
          clearable
          filterable
          :auto-load="true"
          :load-on-focus="false"
          :cache-results="false"
          style="width: 100%"
          @load-success="handleAssigneeLoadSuccess"
          @load-error="handleAssigneeLoadError"
        />
        <el-input v-else placeholder="请先选择项目" disabled style="width: 100%" />
        <div v-if="!isProjectOwner && assigneeApiConfig" class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          <span>您不是项目负责人，只能创建分配给自己的任务</span>
        </div>
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="start_date">
            <el-date-picker
              v-model="formData.start_date"
              type="date"
              placeholder="选择开始时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止时间" prop="end_date">
            <el-date-picker
              v-model="formData.end_date"
              type="date"
              placeholder="选择截止时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- TODO: 预估工时字段暂时注释，后续需要完善工时估算准确性和管理机制 -->
      <!--
      <el-form-item label="预估工时" prop="estimated_hours">
        <el-input-number
          v-model="formData.estimated_hours"
          placeholder="请输入预估工时"
          style="width: 100%"
          :min="0"
          :precision="1"
          controls-position="right"
        />
      </el-form-item>
      -->
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage, FormInstance, FormRules } from 'element-plus'
  import { InfoFilled } from '@element-plus/icons-vue'
  import { TaskApi, ProjectApi } from '@/api/project/projectApi'
  import ApiSelect from '@/components/core/forms/ApiSelect/index.vue'
  import SimpleApiSelect from '@/components/test/SimpleApiSelect.vue'

  // Props
  interface Props {
    visible: boolean
    taskId?: number | null
    projectId?: number
    initialStatus?: number
  }

  const props = withDefaults(defineProps<Props>(), {
    taskId: null,
    projectId: undefined,
    initialStatus: undefined
  })

  // Emits
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    success: []
  }>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const loading = ref(false)
  const projectList = ref([])
  const isProjectOwner = ref(false)

  // 表单数据
  const formData = reactive({
    title: '',
    description: '',
    status: props.initialStatus || 1,
    priority: 2,
    project_id: props.projectId || null,
    assignee_id: null,
    start_date: '',
    end_date: ''
    // TODO: estimated_hours字段暂时注释，后续需要完善工时估算准确性和管理机制
    // estimated_hours: null
  })

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  const isEdit = computed(() => !!taskId.value)

  // 当前项目名称
  const currentProjectName = computed(() => {
    if (!props.projectId) return ''
    const project = projectList.value.find((p) => p.id === props.projectId)
    return project?.name || '当前项目'
  })

  // 执行人API配置
  const assigneeApiConfig = ref(null)

  // 更新执行人API配置
  const updateAssigneeApiConfig = () => {
    try {
      if (props.projectId) {
        // 项目详情页面：直接带上当前项目ID参数发请求
        assigneeApiConfig.value = {
          url: `/project/project/memberOptions/${props.projectId}`
        }
        // 检查项目负责人权限
        checkProjectOwnerPermission(props.projectId)
      } else if (formData?.project_id) {
        // 任务管理页面：根据选择的项目ID请求成员列表
        assigneeApiConfig.value = {
          url: `/project/project/memberOptions/${formData.project_id}`
        }
        // 检查项目负责人权限
        checkProjectOwnerPermission(formData.project_id)
      } else {
        // 没有项目ID时，清空配置
        assigneeApiConfig.value = null
        isProjectOwner.value = false
      }
    } catch (error) {
      assigneeApiConfig.value = null
      isProjectOwner.value = false
    }
  }

  // 检查项目负责人权限
  const checkProjectOwnerPermission = async (projectId: number) => {
    try {
      const response = await ProjectApi.checkProjectOwner(projectId)
      isProjectOwner.value = response.data?.is_owner || false
    } catch (error) {
      console.error('检查项目负责人权限失败:', error)
      isProjectOwner.value = false
    }
  }

  // 监听项目选择变化
  watch(
    () => formData?.project_id,
    (newProjectId, oldProjectId) => {
      if (newProjectId !== oldProjectId) {
        // 项目变化时清空执行人选择
        if (formData) {
          formData.assignee_id = null
        }
        // 更新API配置
        updateAssigneeApiConfig()
      }
    },
    { immediate: false }
  )

  // 在组件挂载后初始化API配置
  onMounted(() => {
    updateAssigneeApiConfig()
  })

  // 处理执行人加载成功
  const handleAssigneeLoadSuccess = (data: any[]) => {
    console.log('TaskForm: 项目成员加载成功，数据:', data)
    console.log('TaskForm: 成员数量:', data.length)
    // 不再显示message提示，让用户在下拉框中看到"暂无项目成员"
    if (data.length > 0) {
      console.log(
        'TaskForm: 项目成员列表:',
        data.map((item) => `${item.name}(${item.value})`).join(', ')
      )
    }
  }

  // 处理执行人加载错误
  const handleAssigneeLoadError = (error: any) => {
    console.error('加载项目成员失败:', error)
    // ElMessage.warning('加载项目成员失败，请检查项目是否存在或有权限访问')
  }

  // 单独存储任务ID用于API调用
  const taskId = ref<number | null>(null)

  // 表单验证规则
  const formRules = computed((): FormRules => {
    const rules: FormRules = {
      title: [
        { required: true, message: '请输入任务标题', trigger: 'blur' },
        { min: 2, max: 100, message: '任务标题长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      status: [{ required: true, message: '请选择任务状态', trigger: 'change' }]
    }

    // 只有在没有指定项目ID时才需要验证项目选择
    if (!props.projectId) {
      rules.project_id = [{ required: true, message: '请选择所属项目', trigger: 'change' }]
    }

    return rules
  })

  // 方法
  const resetForm = () => {
    Object.assign(formData, {
      title: '',
      description: '',
      status: props.initialStatus || 1,
      priority: 2,
      project_id: props.projectId || null,
      assignee_id: null,
      start_date: '',
      end_date: ''
      // TODO: estimated_hours字段暂时注释，后续需要完善工时估算准确性和管理机制
      // estimated_hours: null
    })
    taskId.value = null

    // 更新执行人API配置
    updateAssigneeApiConfig()

    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 监听弹窗显示状态
  watch(
    () => props.visible,
    async (visible) => {
      if (visible) {
        if (props.taskId) {
          // 编辑模式：先显示loading，然后请求详情数据
          loading.value = true
          await loadTaskDetail(props.taskId)
        } else {
          // 新增模式：重置表单，不需要loading
          loading.value = false
          resetForm()
        }
      }
    }
  )

  // 监听初始状态
  watch(
    () => props.initialStatus,
    (newStatus) => {
      if (newStatus && !props.taskId) {
        formData.status = newStatus
      }
    },
    { immediate: true }
  )

  // 加载任务详情
  const loadTaskDetail = async (id: number) => {
    try {
      console.log('TaskForm: 加载任务详情', id)
      // loading状态由调用方控制
      const response = await TaskApi.detail(id)
      console.log('TaskForm: 任务详情响应', response.data)

      // 设置任务ID
      taskId.value = id

      // 填充表单数据
      const detailData = response.data
      Object.assign(formData, {
        title: detailData.title || detailData.name || '',
        description: detailData.description || '',
        status: detailData.status || 1,
        priority: detailData.priority || 2,
        project_id: detailData.project_id || props.projectId || null,
        assignee_id: detailData.assignee_id || detailData.owner_id || null,
        start_date: detailData.start_date || '',
        end_date: detailData.end_date || '',
        estimated_hours: detailData.estimated_hours || null
      })

      // 更新执行人API配置
      updateAssigneeApiConfig()

      console.log('TaskForm: 表单数据已填充', formData)
    } catch (error) {
      console.error('TaskForm: 加载任务详情失败', error)
      ElMessage.error('加载任务详情失败')
      // 加载失败时重置表单
      resetForm()
    } finally {
      loading.value = false
    }
  }

  const handleClose = () => {
    dialogVisible.value = false
    resetForm()
  }

  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()
      loading.value = true

      const submitData = { ...formData }

      if (isEdit.value && taskId.value) {
        // 编辑时需要传递ID
        submitData.id = taskId.value
        await TaskApi.update(submitData)
        ElMessage.success('任务更新成功')
      } else {
        // 新增时不需要ID
        await TaskApi.add(submitData)
        ElMessage.success('任务创建成功')
      }

      emit('success')
      handleClose()
    } catch (error) {
      console.error('TaskForm: 提交失败', error)
      ElMessage.error(isEdit.value ? '任务更新失败' : '任务创建失败')
    } finally {
      loading.value = false
    }
  }

  // 加载项目列表
  const loadProjectList = async () => {
    try {
      // 如果已经有项目ID，则不需要加载项目列表
      if (props.projectId) {
        console.log('TaskForm: 已有项目ID，跳过加载项目列表')
        return
      }

      console.log('TaskForm: 加载项目列表')
      const response = await ProjectApi.list({ page: 1, size: 100 })
      projectList.value = response.data.list || []
    } catch (error) {
      console.error('加载项目列表失败:', error)
    }
  }

  // 初始化
  loadProjectList()
</script>

<style scoped lang="scss">
  .loading-container {
    min-height: 300px;
    padding: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .form-tip {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-top: 4px;
    font-size: 12px;
    color: #86909c;

    .el-icon {
      font-size: 14px;
    }

    // 黑暗模式适配
    html.dark & {
      color: var(--art-text-gray-600);
    }
  }
</style>
