<template>
  <div class="project-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 项目内容 -->
    <div v-else>
      <!-- 项目头部信息 -->
      <div class="project-header">
        <div class="header-left">
          <el-button text @click="goBack">
            <el-icon>
              <ArrowLeft />
            </el-icon>
            返回
          </el-button>
          <div class="project-title-section">
            <div class="title-row">
              <h1 class="project-title">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                {{ projectInfo.name || '加载中...' }}
              </h1>
              <div class="project-badges">
                <el-tag :type="getStatusType(projectInfo.status)" size="large" effect="dark">
                  {{ getStatusText(projectInfo.status) }}
                </el-tag>
                <el-tag :type="getPriorityType(projectInfo.priority)" size="large" effect="plain">
                  {{ getPriorityText(projectInfo.priority) }}
                </el-tag>
              </div>
            </div>

            <div class="project-description" v-if="projectInfo.description">
              {{ projectInfo.description }}
            </div>

            <div class="project-stats-bar">
              <div class="stat-item">
                <el-icon>
                  <TrendCharts />
                </el-icon>
                <span>进度: {{ Math.round(projectStats.progress || 0) }}%</span>
                <el-progress
                  :percentage="Math.round(projectStats.progress || 0)"
                  :stroke-width="4"
                  :show-text="false"
                  class="mini-progress"
                />
              </div>
              <div class="stat-item">
                <el-icon>
                  <User />
                </el-icon>
                <span>成员: {{ projectStats.member_count || 0 }}人</span>
              </div>
              <div class="stat-item">
                <el-icon>
                  <Tickets />
                </el-icon>
                <span>任务: {{ projectStats.total_tasks || 0 }}个</span>
              </div>
              <div class="stat-item">
                <el-icon>
                  <Calendar />
                </el-icon>
                <span :class="{ overdue: isOverdue(projectInfo.end_date) }">
                  截止: {{ formatDate(projectInfo.end_date) }}
                </span>
              </div>
              <div class="stat-item" v-if="projectInfo.budget">
                <el-icon>
                  <Money />
                </el-icon>
                <span>预算: ¥{{ projectInfo.budget.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="header-right">
          <el-button @click="handleEditProject">
            <el-icon>
              <Edit />
            </el-icon>
            编辑项目
          </el-button>
          <!--          <el-button type="primary" @click="handleAddTask">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      新建任务
                    </el-button>-->
        </div>
      </div>

      <!-- 视图切换标签 -->
      <div class="view-tabs">
        <div class="tabs-container">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="看板视图" name="kanban">
              <template #label>
                <span class="tab-label">
                  <el-icon><Grid /></el-icon>
                  看板视图
                </span>
              </template>
            </el-tab-pane>
            <el-tab-pane label="列表视图" name="list">
              <template #label>
                <span class="tab-label">
                  <el-icon><List /></el-icon>
                  列表视图
                </span>
              </template>
            </el-tab-pane>
            <el-tab-pane label="成员管理" name="members">
              <template #label>
                <span class="tab-label">
                  <el-icon><UserFilled /></el-icon>
                  成员管理
                </span>
              </template>
            </el-tab-pane>
            <el-tab-pane label="统计报表" name="statistics">
              <template #label>
                <span class="tab-label">
                  <el-icon><DataAnalysis /></el-icon>
                  统计报表
                </span>
              </template>
            </el-tab-pane>
          </el-tabs>

          <!-- 刷新按钮 -->
          <div class="tab-actions">
            <el-button
              :loading="refreshLoading"
              @click="handleRefreshCurrentTab"
              size="small"
              :title="getRefreshButtonTitle()"
            >
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area" v-loading="tabLoading" element-loading-text="加载中...">
        <!-- 看板视图 -->
        <div v-if="activeTab === 'kanban'" class="kanban-view">
          <TaskKanban
            :project-id="projectId"
            :kanban-data="kanbanData"
            @task-moved="handleTaskMoved"
            @task-click="handleTaskClick"
            @add-task="handleAddTaskToColumn"
            @edit-task="handleEditTask"
            @delete-task="handleDeleteTask"
          />
        </div>

        <!-- 列表视图 -->
        <div v-if="activeTab === 'list'" class="list-view">
          <TaskList
            ref="taskListRef"
            :project-id="projectId"
            @task-click="handleTaskClick"
            @edit-task="handleEditTask"
            @delete-task="handleDeleteTask"
            @add-task="handleAddTask"
          />
        </div>

        <!-- 成员管理 -->
        <div v-if="activeTab === 'members'" class="members-view">
          <ProjectMembers
            :project-id="projectId"
            :members="projectMembers"
            @add-member="handleAddMember"
            @remove-member="handleRemoveMember"
            @update-role="handleUpdateMemberRole"
          />
        </div>

        <!-- 统计报表 -->
        <div v-if="activeTab === 'statistics'" class="statistics-view">
          <ProjectStatistics :project-id="projectId" :stats="projectStats" />
        </div>
      </div>

      <!-- 任务详情弹窗 -->
      <TaskDetail
        v-model:visible="taskDetailVisible"
        :task-id="currentTaskId"
        @task-updated="handleTaskUpdated"
        @edit-task="handleEditTaskFromDetail"
      />

      <!-- 新建/编辑任务弹窗 -->
      <TaskForm
        v-model:visible="taskFormVisible"
        :project-id="projectId"
        :task-data="currentTask"
        :initial-status="initialTaskStatus"
        @success="handleTaskFormSuccess"
      />

      <!-- 编辑项目弹窗 -->
      <ProjectForm
        v-model:visible="projectFormVisible"
        :project-data="projectInfo"
        @success="handleProjectFormSuccess"
      />
    </div>
    <!-- 关闭项目内容div -->
  </div>
</template>

<script setup lang="ts">
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
  import {
    ArrowLeft,
    FolderOpened,
    TrendCharts,
    User,
    Tickets,
    Calendar,
    Edit,
    Grid,
    List,
    UserFilled,
    DataAnalysis,
    Money,
    Refresh
  } from '@element-plus/icons-vue'
  import { ProjectApi, TaskApi } from '@/api/project/projectApi'
  import { formatDate, isOverdue } from '@/utils/date'
  import TaskKanban from './components/TaskKanban.vue'
  import TaskList from './components/TaskList.vue'
  import ProjectMembers from './components/ProjectMembers.vue'
  import ProjectStatistics from './components/ProjectStatistics.vue'
  import TaskDetail from './components/TaskDetail.vue'
  import TaskForm from './components/TaskForm.vue'
  import ProjectForm from './components/ProjectForm.vue'

  // 路由
  const route = useRoute()
  const router = useRouter()
  const projectId = computed(() => Number(route.params.id))

  // 响应式数据
  const activeTab = ref('kanban')
  const projectInfo = ref({})
  const projectStats = ref({})
  const projectMembers = ref<any[]>([])
  const kanbanData = ref<any>({ tasks: [] }) // 初始化为正确的结构
  const taskDetailVisible = ref(false)
  const taskFormVisible = ref(false)
  const projectFormVisible = ref(false)
  const currentTaskId = ref(0)
  const currentTask = ref(null)
  const initialTaskStatus = ref<number | null>(null)

  // 组件引用
  const taskListRef = ref()

  // 加载状态
  const loading = ref(true)
  const refreshLoading = ref(false)

  // 统一的tab loading状态
  const tabLoading = ref(false)

  // 各个tab的数据加载状态
  const tabDataLoaded = reactive({
    kanban: false,
    list: false,
    members: false,
    statistics: false
  })

  // 方法
  const goBack = () => {
    // 优先返回到项目列表页面，如果没有历史记录则直接跳转
    if (window.history.length > 1) {
      router.go(-1)
    } else {
      router.push('/project/list')
    }
  }

  const handleTabChange = async (tabName: string) => {
    console.log('Tab切换到:', tabName)
    activeTab.value = tabName

    // 统一的loading状态管理
    tabLoading.value = true

    try {
      switch (tabName) {
        case 'kanban':
          if (!tabDataLoaded.kanban) {
            await loadKanbanData()
            tabDataLoaded.kanban = true
          }
          break

        case 'list':
          // 任务列表数据由TaskList组件自行加载，这里不需要处理
          tabDataLoaded.list = true
          break

        case 'members':
          if (!tabDataLoaded.members) {
            await loadProjectMembers()
            tabDataLoaded.members = true
          }
          break

        case 'statistics':
          // 统计数据由ProjectStatistics组件自行加载，这里不需要处理
          tabDataLoaded.statistics = true
          break
      }
    } finally {
      // 无论是否有数据加载，都显示短暂的loading效果
      setTimeout(() => {
        tabLoading.value = false
      }, 300)
    }
  }

  const handleEditProject = () => {
    projectFormVisible.value = true
  }

  const handleAddTask = () => {
    currentTask.value = null
    initialTaskStatus.value = null
    taskFormVisible.value = true
  }

  const handleAddTaskToColumn = (status: number) => {
    currentTask.value = null
    initialTaskStatus.value = status
    taskFormVisible.value = true
  }

  const handleTaskMoved = async (taskId: number, newStatus: number) => {
    try {
      // 更新任务状态
      await TaskApi.updateStatus({ id: taskId, status: newStatus })
      ElMessage.success('任务状态更新成功')
      await loadKanbanData()
    } catch (error) {
      ElMessage.error('任务状态更新失败')
    }
  }

  const handleTaskClick = (taskId: number) => {
    console.log('ProjectDetail: 点击任务', taskId)
    currentTaskId.value = taskId
    taskDetailVisible.value = true
  }

  const handleEditTask = (task: any) => {
    currentTask.value = task
    taskFormVisible.value = true
  }

  const handleEditTaskFromDetail = (task: any) => {
    // 从任务详情弹窗编辑任务
    currentTask.value = task
    taskDetailVisible.value = false
    taskFormVisible.value = true
  }

  const handleDeleteTask = async (taskId: number) => {
    try {
      // 确认删除
      await ElMessageBox.confirm('确定要删除这个任务吗？删除后无法恢复。', '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      })

      // 显示loading
      const loading = ElLoading.service({
        lock: true,
        text: '删除中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        await TaskApi.delete(taskId)
        ElMessage.success('任务删除成功')

        // 重新加载数据
        await Promise.all([loadKanbanData(), loadProjectDetail()])

        // 如果当前在列表视图，刷新列表数据
        if (activeTab.value === 'list' && taskListRef.value) {
          taskListRef.value.refreshTasks()
        }
      } finally {
        loading.close()
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除任务失败:', error)
        ElMessage.error('任务删除失败')
      }
    }
  }

  const handleTaskUpdated = () => {
    loadKanbanData()
    loadProjectDetail()

    // 如果当前在列表视图，刷新列表数据
    if (activeTab.value === 'list' && taskListRef.value) {
      taskListRef.value.refreshTasks()
    }
  }

  const handleTaskFormSuccess = () => {
    taskFormVisible.value = false
    loadKanbanData()
    loadProjectDetail()

    // 如果当前在列表视图，刷新列表数据
    if (activeTab.value === 'list' && taskListRef.value) {
      taskListRef.value.refreshTasks()
    }
  }

  const handleProjectFormSuccess = () => {
    projectFormVisible.value = false
    loadProjectDetail()
  }

  const handleAddMember = async (memberData: any) => {
    try {
      await ProjectApi.addMember({
        project_id: projectId.value,
        ...memberData
      })
      ElMessage.success('成员添加成功')
      await loadProjectMembers()
    } catch (error) {
      ElMessage.error('成员添加失败')
    }
  }

  const handleRemoveMember = async (userId: number) => {
    try {
      await ProjectApi.removeMember({
        project_id: projectId.value,
        user_id: userId
      })
      ElMessage.success('成员移除成功')
      await loadProjectMembers()
    } catch (error) {
      ElMessage.error('成员移除失败')
    }
  }

  const handleUpdateMemberRole = async (userId: number, role: string) => {
    try {
      await ProjectApi.updateMemberRole({
        project_id: projectId.value,
        user_id: userId,
        role: role
      })
      ElMessage.success('成员角色更新成功')
      await loadProjectMembers()
    } catch (error) {
      ElMessage.error('成员角色更新失败')
    }
  }

  // 状态和优先级处理函数
  const getStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'primary', // 进行中
      2: 'success', // 已完成
      3: 'warning', // 已暂停
      4: 'danger' // 已取消
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: number) => {
    const textMap: Record<number, string> = {
      1: '进行中',
      2: '已完成',
      3: '已暂停',
      4: '已取消'
    }
    return textMap[status] || '未知'
  }

  const getPriorityType = (
    priority: number
  ): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'info', // 低
      2: 'primary', // 中
      3: 'warning', // 高
      4: 'danger' // 紧急
    }
    return typeMap[priority] || 'info'
  }

  const getPriorityText = (priority: number) => {
    const textMap: Record<number, string> = {
      1: '低',
      2: '中',
      3: '高',
      4: '紧急'
    }
    return textMap[priority] || '未知'
  }

  // 数据加载
  const loadProjectDetail = async () => {
    loading.value = true
    try {
      const response = await ProjectApi.projectDetail(projectId.value)
      projectInfo.value = response.data.project
      projectStats.value = response.data.stats
    } catch (error) {
      console.error('加载项目详情失败:', error)
      ElMessage.error('加载项目详情失败')
      projectInfo.value = {}
      projectStats.value = {}
    } finally {
      loading.value = false
    }
  }

  const loadKanbanData = async () => {
    try {
      const response = await ProjectApi.kanban(projectId.value)
      console.log('后端返回的看板数据:', response.data)

      // 处理后端返回的数据结构：[{status: {...}, tasks: [...]}, ...]
      if (response.data && Array.isArray(response.data)) {
        // 将后端的分组数据转换为前端期望的扁平任务数组
        const allTasks: any[] = []
        response.data.forEach((group: any) => {
          if (group.tasks && Array.isArray(group.tasks)) {
            allTasks.push(...group.tasks)
          }
        })
        kanbanData.value = {
          tasks: allTasks,
          statusGroups: response.data // 保存原始分组数据，以备后用
        }
        console.log('转换后的看板数据:', kanbanData.value)
      } else {
        kanbanData.value = { tasks: [] }
      }
    } catch (error) {
      console.error('加载看板数据失败:', error)
      ElMessage.error('加载看板数据失败')
      kanbanData.value = { tasks: [] }
    }
  }

  const loadProjectMembers = async () => {
    try {
      const response = await ProjectApi.members(projectId.value)
      projectMembers.value = response.data.list || []
    } catch (error) {
      console.error('加载项目成员失败:', error)
      ElMessage.error('加载项目成员失败')
      projectMembers.value = []
    }
  }

  // 加载任务列表数据
  const loadTaskList = async () => {
    try {
      const response = await TaskApi.list({ project_id: projectId.value })
      console.log('任务列表数据:', response.data)
    } catch (error) {
      console.error('加载任务列表失败:', error)
      ElMessage.error('加载任务列表失败')
    }
  }

  // 加载项目统计数据 - 统计组件会自己加载数据，这里不需要重复加载
  const loadProjectStatistics = async () => {
    try {
      console.log('统计报表tab已激活，数据由ProjectStatistics组件自行加载')
    } catch (error) {
      console.error('加载项目统计失败:', error)
      ElMessage.error('加载项目统计失败')
    }
  }

  // 刷新当前标签页数据
  const handleRefreshCurrentTab = async () => {
    refreshLoading.value = true
    try {
      switch (activeTab.value) {
        case 'kanban':
          await loadKanbanData()
          ElMessage.success('看板数据刷新成功')
          break
        case 'list':
          await loadTaskList()
          ElMessage.success('任务列表刷新成功')
          break
        case 'members':
          await loadProjectMembers()
          ElMessage.success('成员数据刷新成功')
          break
        case 'statistics':
          // 统计组件会自己处理刷新
          ElMessage.success('统计数据刷新成功')
          break
        default:
          ElMessage.warning('未知的标签页')
      }
    } catch (error) {
      console.error('刷新失败:', error)
      ElMessage.error('刷新失败')
    } finally {
      refreshLoading.value = false
    }
  }

  // 获取刷新按钮的提示文本
  const getRefreshButtonTitle = () => {
    const tabNames: Record<string, string> = {
      kanban: '刷新看板数据',
      list: '刷新任务列表',
      members: '刷新成员数据',
      statistics: '刷新统计数据'
    }
    return tabNames[activeTab.value] || '刷新数据'
  }

  // 初始化
  onMounted(() => {
    console.log('项目详情页面初始化，项目ID:', projectId.value)
    console.log('当前路由:', route.path)

    // 监控API请求
    const originalFetch = window.fetch
    window.fetch = function (...args) {
      console.log('详情页面API请求:', args[0])
      return originalFetch.apply(this, args)
    }

    loadProjectDetail()
    // 默认加载看板数据
    loadKanbanData()
    tabDataLoaded.kanban = true
  })
</script>

<style scoped lang="scss">
  .project-detail-container {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .header-left {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .project-title-section {
        .title-row {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 8px;

          .project-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 24px;
            font-weight: 600;
            color: #1f2329;
            margin: 0;
          }

          .project-badges {
            display: flex;
            gap: 8px;
          }
        }

        .project-description {
          color: #86909c;
          font-size: 14px;
          line-height: 1.5;
          margin-bottom: 12px;
          max-width: 600px;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .project-stats-bar {
          display: flex;
          gap: 24px;
          flex-wrap: wrap;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #86909c;

            .el-icon {
              font-size: 16px;
            }

            .mini-progress {
              width: 60px;
              margin-left: 8px;
            }

            &.overdue {
              color: #ff4d4f;
            }

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);

              &.overdue {
                color: rgb(var(--art-danger));
              }
            }
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .view-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;

    .tabs-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      :deep(.el-tabs) {
        flex: 1;
      }
    }

    .tab-actions {
      padding: 0 20px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 20px;
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    .tab-label {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }
  }

  .content-area {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 24px;
    min-height: 600px;

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }
  }
</style>
