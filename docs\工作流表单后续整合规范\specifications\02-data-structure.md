# 数据结构规范

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 规范目标

### 核心目标
1. **数据一致性**：确保前后端数据结构的一致性
2. **类型安全**：通过TypeScript类型定义保证类型安全
3. **可维护性**：清晰的数据结构便于维护和扩展
4. **标准化**：统一的数据格式和命名规范

## 📊 数据库表结构规范

### 基础字段规范

所有业务表都必须包含以下标准字段：

```sql
-- 标准基础字段
CREATE TABLE business_table (
  id int PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  
  -- 工作流相关字段
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回',
  approval_node varchar(100) DEFAULT '' COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime DEFAULT NULL COMMENT '提交时间',
  approved_at datetime DEFAULT NULL COMMENT '审批完成时间',
  
  -- 审计字段
  created_id int NOT NULL DEFAULT 0 COMMENT '创建人ID',
  updated_id int NOT NULL DEFAULT 0 COMMENT '更新人ID',
  created_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted_at datetime DEFAULT NULL COMMENT '删除时间（软删除）',
  
  -- 多租户字段
  tenant_id int NOT NULL DEFAULT 0 COMMENT '租户ID',
  
  -- 索引
  INDEX idx_instance_id (instance_id),
  INDEX idx_approval_status (approval_status),
  INDEX idx_submitter_id (submitter_id),
  INDEX idx_created_id (created_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_deleted_at (deleted_at)
);
```

### 工作流核心表结构

#### 工作流实例表

```sql
CREATE TABLE workflow_instance (
  id int PRIMARY KEY AUTO_INCREMENT,
  definition_id int NOT NULL COMMENT '工作流定义ID',
  business_code varchar(50) NOT NULL COMMENT '业务代码',
  business_id int DEFAULT 0 COMMENT '业务数据ID',
  title varchar(200) NOT NULL COMMENT '实例标题',
  form_data JSON NOT NULL COMMENT '表单数据',
  status tinyint DEFAULT 1 COMMENT '状态:0=草稿,1=审批中,2=已通过,3=已驳回,4=已终止,5=已撤回',
  submitter_id int NOT NULL COMMENT '提交人ID',
  submitter_name varchar(50) COMMENT '提交人姓名',
  current_node varchar(100) COMMENT '当前节点',
  cc_users JSON COMMENT '抄送用户',
  process_data JSON COMMENT '流程数据',
  started_at datetime COMMENT '开始时间',
  finished_at datetime COMMENT '完成时间',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  INDEX idx_business_code (business_code),
  INDEX idx_business_id (business_id),
  INDEX idx_submitter_id (submitter_id),
  INDEX idx_status (status),
  INDEX idx_tenant_id (tenant_id)
);
```

#### Form-Create表单定义表

```sql
CREATE TABLE form_create_definition (
  id int PRIMARY KEY AUTO_INCREMENT,
  name varchar(100) NOT NULL COMMENT '表单名称',
  business_code varchar(50) NOT NULL COMMENT '业务代码 fc_001',
  form_rule JSON NOT NULL COMMENT 'Form-Create规则配置',
  form_option JSON COMMENT 'Form-Create选项配置',
  designer_config JSON COMMENT '设计器配置',
  template_id int DEFAULT 0 COMMENT '模板ID',
  category varchar(50) DEFAULT '' COMMENT '表单分类',
  description text COMMENT '表单描述',
  preview_image varchar(255) COMMENT '预览图片',
  status tinyint DEFAULT 1 COMMENT '状态:1=启用,0=禁用',
  version varchar(20) DEFAULT '1.0' COMMENT '版本号',
  is_template tinyint DEFAULT 0 COMMENT '是否为模板',
  sort_order int DEFAULT 0 COMMENT '排序',
  created_id int DEFAULT 0,
  updated_id int DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int DEFAULT 0,
  
  UNIQUE KEY uk_business_code (business_code, tenant_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_template (is_template),
  INDEX idx_tenant_id (tenant_id)
);
```

### 业务表示例

#### 合同表

```sql
CREATE TABLE crm_contract (
  id int PRIMARY KEY AUTO_INCREMENT,
  contract_number varchar(50) NOT NULL COMMENT '合同编号',
  contract_name varchar(200) NOT NULL COMMENT '合同名称',
  customer_id int NOT NULL COMMENT '客户ID',
  customer_name varchar(100) COMMENT '客户名称',
  contract_amount decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '合同金额',
  contract_type tinyint DEFAULT 1 COMMENT '合同类型:1=销售合同,2=采购合同,3=服务合同',
  sign_date date COMMENT '签署日期',
  start_date date COMMENT '开始日期',
  end_date date COMMENT '结束日期',
  payment_terms varchar(500) COMMENT '付款条款',
  contract_content text COMMENT '合同内容',
  attachments JSON COMMENT '附件信息',
  remark text COMMENT '备注',
  
  -- 工作流字段
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态',
  approval_node varchar(100) DEFAULT '' COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime DEFAULT NULL COMMENT '提交时间',
  approved_at datetime DEFAULT NULL COMMENT '审批完成时间',
  
  -- 标准字段
  created_id int NOT NULL DEFAULT 0,
  updated_id int NOT NULL DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int NOT NULL DEFAULT 0,
  
  UNIQUE KEY uk_contract_number (contract_number, tenant_id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_contract_type (contract_type),
  INDEX idx_approval_status (approval_status),
  INDEX idx_tenant_id (tenant_id)
);
```

#### 回款表

```sql
CREATE TABLE crm_contract_receivable (
  id int PRIMARY KEY AUTO_INCREMENT,
  receivable_number varchar(50) NOT NULL COMMENT '回款编号',
  contract_id int NOT NULL COMMENT '合同ID',
  contract_number varchar(50) COMMENT '合同编号',
  customer_id int NOT NULL COMMENT '客户ID',
  customer_name varchar(100) COMMENT '客户名称',
  receivable_amount decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '应收金额',
  received_amount decimal(15,2) DEFAULT 0.00 COMMENT '实收金额',
  receivable_date date NOT NULL COMMENT '应收日期',
  received_date date COMMENT '实收日期',
  payment_method varchar(50) COMMENT '付款方式',
  bank_info varchar(200) COMMENT '银行信息',
  voucher_number varchar(50) COMMENT '凭证号',
  remark text COMMENT '备注',
  
  -- 工作流字段
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态',
  approval_node varchar(100) DEFAULT '' COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime DEFAULT NULL COMMENT '提交时间',
  approved_at datetime DEFAULT NULL COMMENT '审批完成时间',
  
  -- 标准字段
  created_id int NOT NULL DEFAULT 0,
  updated_id int NOT NULL DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int NOT NULL DEFAULT 0,
  
  UNIQUE KEY uk_receivable_number (receivable_number, tenant_id),
  INDEX idx_contract_id (contract_id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_approval_status (approval_status),
  INDEX idx_tenant_id (tenant_id)
);
```

## 🔧 TypeScript类型定义规范

### 基础类型定义

```typescript
// 基础实体接口
interface BaseEntity {
  id?: number
  created_id: number
  updated_id: number
  created_at?: string
  updated_at?: string
  deleted_at?: string | null
  tenant_id: number
}

// 工作流实体接口
interface WorkflowEntity extends BaseEntity {
  instance_id?: number
  approval_status: ApprovalStatus
  approval_node?: string
  submitter_id?: number
  submitted_at?: string | null
  approved_at?: string | null
}

// 审批状态枚举
enum ApprovalStatus {
  DRAFT = 0,      // 草稿
  PENDING = 1,    // 审批中
  APPROVED = 2,   // 已通过
  REJECTED = 3,   // 已驳回
  TERMINATED = 4, // 已终止
  WITHDRAWN = 5   // 已撤回
}

// 表单模式枚举
enum FormMode {
  CREATE = 'create',
  EDIT = 'edit',
  VIEW = 'view'
}
```

### 工作流相关类型

```typescript
// 工作流实例接口
interface WorkflowInstance extends BaseEntity {
  definition_id: number
  business_code: string
  business_id?: number
  title: string
  form_data: Record<string, any>
  status: ApprovalStatus
  submitter_id: number
  submitter_name?: string
  current_node?: string
  cc_users?: number[]
  process_data?: Record<string, any>
  started_at?: string
  finished_at?: string
}

// 工作流任务接口
interface WorkflowTask extends BaseEntity {
  instance_id: number
  task_id: string
  node_id: string
  node_name: string
  assignee_id: number
  assignee_name?: string
  status: TaskStatus
  comment?: string
  handled_at?: string
  instance?: WorkflowInstance
}

// 任务状态枚举
enum TaskStatus {
  PENDING = 1,    // 待处理
  APPROVED = 2,   // 已通过
  REJECTED = 3,   // 已驳回
  TRANSFERRED = 4 // 已转办
}

// 抄送记录接口
interface WorkflowCc extends BaseEntity {
  instance_id: number
  cc_user_id: number
  cc_user_name?: string
  is_read: boolean
  read_at?: string
  instance?: WorkflowInstance
}
```

### 业务实体类型

```typescript
// 合同接口
interface Contract extends WorkflowEntity {
  contract_number: string
  contract_name: string
  customer_id: number
  customer_name?: string
  contract_amount: number
  contract_type: ContractType
  sign_date?: string
  start_date?: string
  end_date?: string
  payment_terms?: string
  contract_content?: string
  attachments?: FileAttachment[]
  remark?: string
}

// 合同类型枚举
enum ContractType {
  SALES = 1,    // 销售合同
  PURCHASE = 2, // 采购合同
  SERVICE = 3   // 服务合同
}

// 回款接口
interface Receivable extends WorkflowEntity {
  receivable_number: string
  contract_id: number
  contract_number?: string
  customer_id: number
  customer_name?: string
  receivable_amount: number
  received_amount?: number
  receivable_date: string
  received_date?: string
  payment_method?: string
  bank_info?: string
  voucher_number?: string
  remark?: string
}

// 文件附件接口
interface FileAttachment {
  id?: number
  name: string
  url: string
  size: number
  type: string
  upload_time?: string
}
```

### Form-Create相关类型

```typescript
// Form-Create配置接口
interface FormCreateConfig {
  id?: number
  name: string
  business_code: string
  form_rule: FormCreateRule[]
  form_option?: FormCreateOption
  designer_config?: FormCreateDesignerConfig
  template_id?: number
  category?: string
  description?: string
  preview_image?: string
  status: number
  version: string
  is_template: boolean
  sort_order?: number
}

// Form-Create规则接口
interface FormCreateRule {
  type: string
  field: string
  title: string
  value?: any
  props?: Record<string, any>
  validate?: FormCreateValidate[]
  children?: FormCreateRule[]
  control?: FormCreateControl[]
}

// Form-Create选项接口
interface FormCreateOption {
  form?: {
    labelWidth?: string
    size?: string
    disabled?: boolean
    inline?: boolean
  }
  row?: {
    gutter?: number
    type?: string
  }
  submitBtn?: boolean | {
    text?: string
    type?: string
    size?: string
    loading?: boolean
  }
  resetBtn?: boolean | {
    text?: string
    type?: string
    size?: string
  }
}

// Form-Create验证规则接口
interface FormCreateValidate {
  type: string
  message: string
  trigger?: string
  required?: boolean
  pattern?: string
  min?: number
  max?: number
  validator?: string
}

// Form-Create控制规则接口
interface FormCreateControl {
  handle: string
  condition: string
  rule: FormCreateRule[]
}

// Form-Create设计器配置接口
interface FormCreateDesignerConfig {
  width?: string
  height?: string
  components?: string[]
  plugins?: string[]
}
```

## 📡 API响应格式规范

### 统一响应格式

```typescript
// API响应基础接口
interface ApiResponse<T = any> {
  code: number          // 状态码：1=成功，0=失败
  message: string       // 响应消息
  data?: T             // 响应数据
  timestamp?: number   // 时间戳
  request_id?: string  // 请求ID
}

// 分页响应接口
interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    list: T[]          // 数据列表
    total: number      // 总数量
    page: number       // 当前页码
    limit: number      // 每页数量
    pages: number      // 总页数
  }
  timestamp?: number
  request_id?: string
}

// 错误响应接口
interface ErrorResponse {
  code: number
  message: string
  errors?: {
    field: string
    message: string
  }[]
  timestamp?: number
  request_id?: string
}
```

### 业务API响应示例

```typescript
// 合同详情响应
type ContractDetailResponse = ApiResponse<Contract>

// 合同列表响应
type ContractListResponse = PaginatedResponse<Contract>

// 工作流实例详情响应
type WorkflowInstanceDetailResponse = ApiResponse<WorkflowInstance>

// 表单提交响应
interface FormSubmitResponse extends ApiResponse {
  data: {
    id: number
    instance_id?: number
    status: ApprovalStatus
  }
}
```

## 🔄 数据转换规范

### 前后端数据转换

```typescript
// 数据转换工具类
class DataTransformer {
  // 日期格式转换
  static formatDate(date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
    return dayjs(date).format(format)
  }
  
  // 金额格式转换
  static formatAmount(amount: number): string {
    return amount.toLocaleString('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    })
  }
  
  // 状态文本转换
  static getStatusText(status: ApprovalStatus): string {
    const statusMap = {
      [ApprovalStatus.DRAFT]: '草稿',
      [ApprovalStatus.PENDING]: '审批中',
      [ApprovalStatus.APPROVED]: '已通过',
      [ApprovalStatus.REJECTED]: '已驳回',
      [ApprovalStatus.TERMINATED]: '已终止',
      [ApprovalStatus.WITHDRAWN]: '已撤回'
    }
    return statusMap[status] || '未知状态'
  }
  
  // 表单数据清理
  static cleanFormData(data: Record<string, any>): Record<string, any> {
    const cleaned = { ...data }
    
    // 移除空值
    Object.keys(cleaned).forEach(key => {
      if (cleaned[key] === '' || cleaned[key] === null || cleaned[key] === undefined) {
        delete cleaned[key]
      }
    })
    
    // 移除系统字段
    delete cleaned.id
    delete cleaned.created_at
    delete cleaned.updated_at
    delete cleaned.deleted_at
    
    return cleaned
  }
  
  // 业务数据转换为表单数据
  static businessToForm<T extends Record<string, any>>(business: T): Record<string, any> {
    const form = { ...business }
    
    // 移除工作流字段
    delete form.instance_id
    delete form.approval_status
    delete form.approval_node
    delete form.submitter_id
    delete form.submitted_at
    delete form.approved_at
    
    // 移除审计字段
    delete form.created_id
    delete form.updated_id
    delete form.created_at
    delete form.updated_at
    delete form.deleted_at
    delete form.tenant_id
    
    return form
  }
}
```

### 表单验证数据格式

```typescript
// 验证规则接口
interface ValidationRule {
  field: string
  rule: string
  message: string
  params?: any[]
}

// 验证错误接口
interface ValidationError {
  field: string
  message: string
  value?: any
}

// 验证结果接口
interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
}

// 表单验证器
class FormValidator {
  private rules: ValidationRule[] = []
  
  addRule(rule: ValidationRule): void {
    this.rules.push(rule)
  }
  
  validate(data: Record<string, any>): ValidationResult {
    const errors: ValidationError[] = []
    
    this.rules.forEach(rule => {
      const value = data[rule.field]
      const isValid = this.validateField(value, rule)
      
      if (!isValid) {
        errors.push({
          field: rule.field,
          message: rule.message,
          value
        })
      }
    })
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  private validateField(value: any, rule: ValidationRule): boolean {
    switch (rule.rule) {
      case 'required':
        return value !== null && value !== undefined && value !== ''
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
      case 'phone':
        return /^1[3-9]\d{9}$/.test(value)
      case 'min':
        return value >= rule.params?.[0]
      case 'max':
        return value <= rule.params?.[0]
      default:
        return true
    }
  }
}
```

## 📊 数据缓存规范

### 缓存键命名规范

```typescript
// 缓存键生成器
class CacheKeyGenerator {
  // 表单配置缓存键
  static formConfig(businessCode: string): string {
    return `form_config:${businessCode}`
  }
  
  // 业务详情缓存键
  static businessDetail(businessCode: string, id: number): string {
    return `business_detail:${businessCode}:${id}`
  }
  
  // 工作流实例缓存键
  static workflowInstance(instanceId: number): string {
    return `workflow_instance:${instanceId}`
  }
  
  // 用户权限缓存键
  static userPermission(userId: number, businessCode: string): string {
    return `user_permission:${userId}:${businessCode}`
  }
  
  // 组件缓存键
  static component(businessCode: string, type: 'form' | 'detail'): string {
    return `component:${type}:${businessCode}`
  }
}

// 缓存数据接口
interface CacheData<T = any> {
  data: T
  timestamp: number
  ttl: number
}

// 缓存管理器
class CacheManager {
  private cache = new Map<string, CacheData>()
  
  set<T>(key: string, data: T, ttl = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get<T>(key: string): T | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data as T
  }
  
  delete(key: string): void {
    this.cache.delete(key)
  }
  
  clear(): void {
    this.cache.clear()
  }
  
  // 清理过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, cached] of this.cache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        this.cache.delete(key)
      }
    }
  }
}
```

## 🔍 数据验证规范

### 字段验证规则

```typescript
// 字段验证规则定义
const fieldValidationRules = {
  // 基础字段验证
  id: {
    type: 'number',
    min: 1,
    message: 'ID必须是正整数'
  },
  
  name: {
    type: 'string',
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '名称长度必须在2-100个字符之间'
  },
  
  email: {
    type: 'string',
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入正确的邮箱地址'
  },
  
  phone: {
    type: 'string',
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },
  
  amount: {
    type: 'number',
    min: 0,
    precision: 2,
    message: '金额必须是非负数，最多保留2位小数'
  },
  
  date: {
    type: 'string',
    pattern: /^\d{4}-\d{2}-\d{2}$/,
    message: '日期格式必须是YYYY-MM-DD'
  },
  
  datetime: {
    type: 'string',
    pattern: /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
    message: '日期时间格式必须是YYYY-MM-DD HH:mm:ss'
  },
  
  // 业务字段验证
  contractNumber: {
    type: 'string',
    required: true,
    pattern: /^[A-Z]{2}\d{8}$/,
    message: '合同编号格式：2位大写字母+8位数字'
  },
  
  businessCode: {
    type: 'string',
    required: true,
    pattern: /^[a-z]+(_[a-z]+)*$/,
    message: '业务代码只能包含小写字母和下划线'
  }
}
```

---

**注意：** 所有数据结构都必须严格遵循本规范，确保系统的数据一致性和类型安全。