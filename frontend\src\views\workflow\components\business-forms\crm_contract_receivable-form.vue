<template>
  <el-dialog
    v-model="dialogVisible"
    title="回款申请"
    width="50%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
      class="receivable-form"
      v-loading="formLoading"
      element-loading-text="数据加载中..."
    >
      <el-form-item label="合同" prop="contract_id">
        <el-select v-model="formData.contract_id" placeholder="请选择合同" style="width: 100%" filterable>
          <el-option
            v-for="item in contractOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="回款金额" prop="receivable_amount">
        <el-input-number
          v-model="formData.receivable_amount"
          :min="0"
          :step="100"
          :precision="2"
          style="width: 100%"
          placeholder="请输入回款金额"
        />
      </el-form-item>

      <el-form-item label="回款日期" prop="receivable_date">
        <el-date-picker
          v-model="formData.receivable_date"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择回款日期"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="付款方式" prop="payment_method">
        <el-select v-model="formData.payment_method" placeholder="请选择付款方式" style="width: 100%">
          <el-option
            v-for="item in paymentMethods"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="success" plain @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交申请</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import type { FormInstance, FormRules } from 'element-plus'
  import { ApplicationApi } from '@/api/workflow/ApplicationApi'

  // 定义props
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    formId: {
      type: [Number, String],
      default: 0
    }
  })

  // 定义事件
  const emit = defineEmits(['update:modelValue', 'success', 'cancel', 'submit', 'save'])

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单加载状态
  const formLoading = ref(false)

  // 对话框可见状态
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  // 合同选项
  const contractOptions = ref([
    { label: '合同001 - 测试合同', value: 1 },
    { label: '合同002 - 示例合同', value: 2 }
  ])

  // 付款方式选项
  const paymentMethods = [
    { label: '银行转账', value: '1' },
    { label: '现金', value: '2' },
    { label: '支票', value: '3' },
    { label: '支付宝', value: '4' },
    { label: '微信支付', value: '5' },
    { label: '其他', value: '6' }
  ]

  // 表单数据
  const formData = reactive({
    id: 0,
    contract_id: '',
    receivable_amount: 0,
    receivable_date: '',
    payment_method: '',
    remark: '',
    form_type: 'receivable'
  })

  // 表单校验规则
  const rules = reactive<FormRules>({
    contract_id: [{ required: true, message: '请选择合同', trigger: 'change' }],
    receivable_amount: [
      { required: true, message: '请输入回款金额', trigger: 'blur' },
      { type: 'number', min: 0.01, message: '回款金额必须大于0', trigger: 'blur' }
    ],
    receivable_date: [{ required: true, message: '请选择回款日期', trigger: 'change' }],
    payment_method: [{ required: true, message: '请选择付款方式', trigger: 'change' }]
  })

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid, fields) => {
      if (valid) {
        emit('submit', formData)
      } else {
        console.log('表单校验失败', fields)
      }
    })
  }

  // 保存草稿
  const handleSaveDraft = async () => {
    emit('save', formData)
  }

  // 数据是否已通过setFormData设置的标记
  const dataPreloaded = ref(false)

  // 重置表单数据
  const resetFormData = () => {
    formData.id = 0
    formData.contract_id = ''
    formData.receivable_amount = 0
    formData.receivable_date = ''
    formData.payment_method = ''
    formData.remark = ''
    formLoading.value = false
    dataPreloaded.value = false
  }

  // 对话框关闭时重置数据预加载状态
  const handleClose = () => {
    dialogVisible.value = false
    resetFormData()
    emit('cancel')
  }

  // 设置表单数据（由父组件直接调用）
  const setFormData = (data: any) => {
    console.log('ReceivableForm.setFormData 接收到数据:', data)

    if (!data) return

    // 首先重置表单
    resetFormData()

    // 标记数据已预加载
    dataPreloaded.value = true

    // 直接设置表单值
    formLoading.value = false

    // 设置表单ID
    if (data.id) {
      formData.id = Number(data.id)
    }

    // 设置基本字段
    formData.contract_id = data.contract_id || ''
    formData.receivable_amount = parseFloat(data.receivable_amount) || 0
    formData.receivable_date = data.receivable_date || ''
    formData.payment_method = data.payment_method || ''
    formData.remark = data.remark || ''

    console.log('回款表单数据设置完成:', formData)
  }

  // 显示表单方法
  const showForm = async (id?: number | string) => {
    console.log('ReceivableForm.showForm 接收到ID:', id)

    // 如果数据已通过setFormData预加载，则不再重复加载
    if (dataPreloaded.value) {
      console.log('表单数据已通过setFormData预加载，不再重复加载')
      return
    }

    // 重置表单数据
    resetFormData()

    // 设置加载状态
    formLoading.value = true

    if (!id) {
      formLoading.value = false
      return
    }

    try {
      formData.id = Number(id)
      const res = await ApplicationApi.detail(Number(id))

      if (res.code === 1 && res.data && res.data.form_data) {
        const fd = res.data.form_data
        console.log('获取到的回款表单数据:', fd)

        await nextTick(() => {
          formData.contract_id = fd.contract_id || ''
          formData.receivable_amount = parseFloat(fd.receivable_amount) || 0
          formData.receivable_date = fd.receivable_date || ''
          formData.payment_method = fd.payment_method || ''
          formData.remark = fd.remark || ''

          console.log('回款表单数据设置完成:', formData)
        })
      }

      formLoading.value = false
    } catch (error) {
      console.error('加载回款表单数据失败', error)
      formLoading.value = false
    }
  }

  // 加载合同选项（实际项目中应该从API获取）
  const loadContractOptions = async () => {
    // TODO: 从API获取合同列表
    // const res = await ContractApi.getList()
    // contractOptions.value = res.data.map(item => ({
    //   label: `${item.contract_no} - ${item.contract_name}`,
    //   value: item.id
    // }))
  }

  // 组件挂载时加载合同选项
  onMounted(() => {
    loadContractOptions()
  })

  // 暴露方法
  defineExpose({
    showForm,
    setFormData
  })
</script>

<style scoped>
  .receivable-form {
    max-width: 600px;
    margin: 0 auto;
  }
</style>
