import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 任务表相关接口
 */
export class ProjectTaskApi {
  /**
   * 获取任务表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_task/index',
      params
    })
  }

  /**
   * 获取任务表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/project/project_task/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加任务表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task/add',
      data
    })
  }

  /**
   * 更新任务表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/project_task/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除任务表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_task/delete/${id}`
    })
  }

  /**
   * 批量删除任务表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/project/project_task/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task/updateField',
      data
    })
  }

  
  /**
   * 修改任务表状态
   * @param data 状态数据
   */
  static changeStatus(data: { id: number | string; status: number }) {
    return request.post<BaseResult>({
      url: `/project/project_task/status/${data.id}`,
      data
    })
  }
  

  


  /**
   * 导入任务表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/project/project_task/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/project/project_task/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/project/project_task/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}