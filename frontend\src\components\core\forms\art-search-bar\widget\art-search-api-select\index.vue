<!--通用API选择器组件 - 支持单选多选、远程搜索-->
<template>
  <el-select
    v-model="value"
    v-bind="mergedConfig"
    :loading="loading"
    :remote-method="handleRemoteSearch"
    @change="changeValue"
    @clear="handleClear"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <el-option
      v-for="option in optionList"
      :key="getOptionValue(option)"
      :label="getOptionLabel(option)"
      :value="getOptionValue(option)"
      :disabled="getOptionDisabled(option)"
    >
      <slot name="option" :option="option" :label="getOptionLabel(option)" :value="getOptionValue(option)">
        {{ getOptionLabel(option) }}
      </slot>
    </el-option>
    
    <template v-if="optionList.length === 0 && !loading" #empty>
      <div class="empty-text">
        {{ emptyText }}
      </div>
    </template>
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch, onMounted } from 'vue'
import { debounce } from '@pureadmin/utils'
import { SearchFormItem } from '@/types/search-form'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'

const { t } = useI18n()

// 组件值类型
export type ValueVO = string | number | (string | number)[] | null | undefined

// API 响应数据类型
interface ApiResponse {
  code: number
  data: any
  message?: string
}

// 选项数据类型
interface OptionItem {
  [key: string]: any
}

// API 配置类型
interface ApiConfig {
  url: string                           // API 地址
  method?: 'get' | 'post'              // 请求方法，默认 get
  params?: Record<string, any>         // 固定参数
  searchParam?: string                 // 搜索参数名，默认 'keyword'
  dataPath?: string                    // 数据路径，默认 'data'
  transform?: (data: any) => OptionItem[] // 数据转换函数
}

// 组件配置扩展
interface ExtendedConfig {
  // 基础配置
  placeholder?: string
  multiple?: boolean
  clearable?: boolean
  filterable?: boolean
  remote?: boolean
  readonly?: boolean
  disabled?: boolean
  size?: 'large' | 'default' | 'small'
  
  // 数据配置
  labelField?: string                  // label 字段名，默认 'name'
  valueField?: string                  // value 字段名，默认 'id'
  disabledField?: string               // disabled 字段名，默认 'disabled'
  
  // API 配置
  api?: ApiConfig                      // API 配置
  
  // 行为配置
  autoLoad?: boolean                   // 是否自动加载，默认 true
  minSearchLength?: number             // 最小搜索长度，默认 0
  searchDelay?: number                 // 搜索延迟，默认 300ms
  cacheResults?: boolean               // 是否缓存结果，默认 true
  
  // 文本配置
  emptyText?: string                   // 空数据文本
  loadingText?: string                 // 加载文本
  
  // Element Plus 原生配置
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  maxCollapseTags?: number
  reserveKeyword?: boolean
  defaultFirstOption?: boolean
  popperClass?: string
  teleported?: boolean
  persistent?: boolean
  automaticDropdown?: boolean
  fitInputWidth?: boolean
  suffixIcon?: string | Component
  tagType?: 'success' | 'info' | 'warning' | 'danger'
  validateEvent?: boolean
  placement?: string
}

// Props 定义
const props = defineProps<{
  value: ValueVO
  item: SearchFormItem
}>()

// Emits 定义
const emit = defineEmits<{
  (e: 'update:value', value: ValueVO): void
}>()

// 响应式数据
const loading = ref(false)
const optionList = ref<OptionItem[]>([])
const searchCache = ref<Map<string, OptionItem[]>>(new Map())

// 计算属性
const value = computed({
  get: () => props.value,
  set: (val: ValueVO) => emit('update:value', val)
})

// 合并配置
const mergedConfig = computed(() => {
  const defaultConfig: ExtendedConfig = {
    placeholder: `${t('table.searchBar.searchSelectPlaceholder')}${props.item.label}`,
    multiple: false,
    clearable: true,
    filterable: true,
    remote: true,
    readonly: false,
    disabled: false,
    size: 'default',
    labelField: 'name',
    valueField: 'id',
    disabledField: 'disabled',
    autoLoad: true,
    minSearchLength: 0,
    searchDelay: 300,
    cacheResults: true,
    emptyText: '暂无数据',
    loadingText: '加载中...',
    collapseTags: true,
    collapseTagsTooltip: true,
    reserveKeyword: false,
    defaultFirstOption: false,
    teleported: true,
    persistent: true,
    automaticDropdown: false,
    fitInputWidth: false,
    validateEvent: true
  }
  
  return reactive({
    ...defaultConfig,
    ...(props.item.config || {})
  })
})

// API 配置
const apiConfig = computed(() => {
  const config = mergedConfig.value.api
  if (!config?.url) {
    console.warn('ArtSearchApiSelect: API URL is required')
    return null
  }
  
  return {
    method: 'get',
    searchParam: 'keyword',
    dataPath: 'data',
    ...config
  } as Required<ApiConfig>
})

// 文本配置
const emptyText = computed(() => {
  return loading.value ? mergedConfig.value.loadingText : mergedConfig.value.emptyText
})

// 获取选项值
const getOptionValue = (option: OptionItem): string | number => {
  return option[mergedConfig.value.valueField!] ?? option.value ?? option.id
}

// 获取选项标签
const getOptionLabel = (option: OptionItem): string => {
  return option[mergedConfig.value.labelField!] ?? option.label ?? option.name ?? String(getOptionValue(option))
}

// 获取选项禁用状态
const getOptionDisabled = (option: OptionItem): boolean => {
  return option[mergedConfig.value.disabledField!] ?? option.disabled ?? false
}

// API 请求函数
const fetchData = async (searchKeyword = ''): Promise<OptionItem[]> => {
  if (!apiConfig.value) return []
  
  const cacheKey = searchKeyword || '__default__'
  
  // 检查缓存
  if (mergedConfig.value.cacheResults && searchCache.value.has(cacheKey)) {
    return searchCache.value.get(cacheKey)!
  }
  
  try {
    loading.value = true
    
    // 构建请求参数
    const params = {
      ...apiConfig.value.params,
      ...(searchKeyword && { [apiConfig.value.searchParam]: searchKeyword })
    }
    
    // 动态导入 HTTP 客户端
    const { http } = await import('@/utils/http')
    
    // 发起请求
    const response: ApiResponse = await http[apiConfig.value.method]({
      url: apiConfig.value.url,
      params: apiConfig.value.method === 'get' ? params : undefined,
      data: apiConfig.value.method === 'post' ? params : undefined
    })
    
    // 检查响应状态
    if (response.code !== 1) {
      throw new Error(response.message || '请求失败')
    }
    
    // 提取数据
    let data = response.data
    if (apiConfig.value.dataPath && apiConfig.value.dataPath !== 'data') {
      const paths = apiConfig.value.dataPath.split('.')
      for (const path of paths) {
        data = data?.[path]
      }
    }
    
    // 确保数据是数组
    if (!Array.isArray(data)) {
      console.warn('ArtSearchApiSelect: API response data is not an array', data)
      data = []
    }
    
    // 数据转换
    if (apiConfig.value.transform) {
      data = apiConfig.value.transform(data)
    }
    
    // 缓存结果
    if (mergedConfig.value.cacheResults) {
      searchCache.value.set(cacheKey, data)
    }
    
    return data
    
  } catch (error) {
    console.error('ArtSearchApiSelect: API request failed', error)
    ElMessage.error(`加载${props.item.label}失败`)
    return []
  } finally {
    loading.value = false
  }
}

// 远程搜索处理
const handleRemoteSearch = debounce(async (query: string) => {
  if (query.length < mergedConfig.value.minSearchLength!) {
    return
  }
  
  const data = await fetchData(query)
  optionList.value = data
}, mergedConfig.value.searchDelay!)

// 事件处理
const changeValue = (val: ValueVO): void => {
  if (props.item.onChange) {
    props.item.onChange({
      prop: props.item.prop,
      val
    })
  }
}

const handleClear = (): void => {
  // 清空时重新加载默认数据
  if (mergedConfig.value.autoLoad) {
    loadDefaultData()
  }
}

const handleFocus = (): void => {
  // 聚焦时加载数据
  if (optionList.value.length === 0 && mergedConfig.value.autoLoad) {
    loadDefaultData()
  }
}

const handleBlur = (): void => {
  // 失焦处理（如果需要）
}

// 加载默认数据
const loadDefaultData = async (): Promise<void> => {
  const data = await fetchData()
  optionList.value = data
}

// 清空缓存
const clearCache = (): void => {
  searchCache.value.clear()
}

// 刷新数据
const refresh = async (): Promise<void> => {
  clearCache()
  await loadDefaultData()
}

// 生命周期
onMounted(() => {
  if (mergedConfig.value.autoLoad) {
    loadDefaultData()
  }
})

// 监听配置变化
watch(
  () => props.item.config,
  () => {
    // 配置变化时清空缓存并重新加载
    clearCache()
    if (mergedConfig.value.autoLoad) {
      loadDefaultData()
    }
  },
  { deep: true }
)

// 暴露方法
defineExpose({
  refresh,
  clearCache,
  loadData: fetchData
})
</script>

<style lang="scss" scoped>
.empty-text {
  padding: 10px 0;
  margin: 0;
  text-align: center;
  color: var(--el-text-color-placeholder);
  font-size: 14px;
}
</style>
