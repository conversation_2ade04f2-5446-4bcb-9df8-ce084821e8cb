<script setup lang="ts">
  import { ref, reactive, defineExpose, defineEmits } from 'vue'
  import { ElMessage, ElMessageBox, FormInstance, FormItemRule } from 'element-plus'
  import { User, House, Phone } from '@element-plus/icons-vue'
  import { CrmLeadApi } from '@/api/crm/crmLead'
  import { ApiStatus } from '@/utils/http/status'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const loading = ref(false)
  const leadId = ref(0)
  const leadInfo = ref<any>({})

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive({
    customer_name: '',
    company: '',
    industry: '',
    address: '',
    website: '',
    description: '',
    contact_name: '',
    contact_position: '',
    contact_mobile: '',
    contact_phone: '',
    contact_email: '',
    contact_wechat: '',
    contact_qq: ''
  })

  // 重置表单数据的默认值
  const getDefaultFormData = () => ({
    customer_name: '',
    company: '',
    industry: '',
    address: '',
    website: '',
    description: '',
    contact_name: '',
    contact_position: '',
    contact_mobile: '',
    contact_phone: '',
    contact_email: '',
    contact_wechat: '',
    contact_qq: ''
  })

  // 表单验证规则
  const formRules = reactive({
    customer_name: [
      { required: true, message: '请输入客户名称', trigger: 'blur' },
      { min: 2, max: 50, message: '客户名称长度在 2 到 50 个字符', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.trim().length < 2) {
            callback(new Error('客户名称不能只包含空格'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    company: [
      { required: true, message: '请输入公司名称', trigger: 'blur' },
      { min: 2, max: 100, message: '公司名称长度在 2 到 100 个字符', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.trim().length < 2) {
            callback(new Error('公司名称不能只包含空格'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    contact_name: [
      { required: true, message: '请输入联系人姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '联系人姓名长度在 2 到 20 个字符', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && value.trim().length < 2) {
            callback(new Error('联系人姓名不能只包含空格'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      { required: true, message: '请输入联系人手机', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    contact_email: [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            callback(new Error('请输入正确的邮箱地址'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    website: [
      {
        validator: (rule: any, value: string, callback: any) => {
          if (value && !/^https?:\/\/.+/.test(value)) {
            callback(new Error('请输入正确的网站地址（以http://或https://开头）'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  })

  // 行业选项
  const industryOptions = [
    '互联网/电子商务',
    '计算机软件',
    'IT服务/系统集成',
    '电子技术/半导体/集成电路',
    '通信/电信/网络设备',
    '金融/投资/证券',
    '房地产',
    '建筑/建材/工程',
    '机械/设备/重工',
    '汽车及零配件',
    '制药/生物工程',
    '医疗/护理/卫生',
    '广告',
    '媒体/出版/影视',
    '教育/培训/院校',
    '咨询',
    '中介服务',
    '检测/认证',
    '法律',
    '翻译',
    '其他'
  ]

  // 重置表单
  const resetForm = () => {
    Object.assign(formData, getDefaultFormData())
    formRef.value?.resetFields()
  }

  // 打开对话框
  const openDialog = async (id: number, leadData?: any) => {
    leadId.value = id
    leadInfo.value = leadData || {}
    dialogVisible.value = true
    resetForm()
    
    // 预填充线索数据
    if (leadData) {
      formData.customer_name = leadData.lead_name || ''
      formData.company = leadData.company || ''
      formData.industry = leadData.industry || ''
      formData.address = leadData.address || ''
      formData.contact_name = leadData.lead_name || ''
      formData.contact_position = leadData.position || ''
      formData.contact_mobile = leadData.mobile || ''
      formData.contact_phone = leadData.phone || ''
      formData.contact_email = leadData.email || ''
    }
  }

  // 关闭对话框
  const closeDialog = () => {
    dialogVisible.value = false
    resetForm()
  }

  // 提交表单
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      // 确认转化
      await ElMessageBox.confirm(
        `确定要将线索"${leadInfo.value.lead_name}"转化为客户吗？转化后将无法撤销。`,
        '确认转化',
        {
          confirmButtonText: '确定转化',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      loading.value = true

      const res = await CrmLeadApi.convertToCustomer(leadId.value, formData)

      if (res.code === ApiStatus.success) {
        ElMessage.success('线索转化成功')
        emit('success')
        closeDialog()
      } else {
        ElMessage.error(res.message || '转化失败')
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('转化失败:', error)
        ElMessage.error('转化失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 暴露方法给父组件
  defineExpose({
    openDialog,
    closeDialog
  })
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="线索转化为客户"
    width="900px"
    top="5vh"
    destroy-on-close
    @close="closeDialog"
    class="convert-dialog-wrapper"
  >
    <div class="convert-dialog-content">
        <!-- 线索信息 -->
        <div class="lead-info">
          <h4>原线索信息</h4>
          <div class="info-row">
            <span class="label">线索姓名：</span>
            <span class="value">{{ leadInfo.lead_name || '-' }}</span>
            <span class="label">公司名称：</span>
            <span class="value">{{ leadInfo.company || '-' }}</span>
            <span class="label">联系电话：</span>
            <span class="value">{{ leadInfo.mobile || leadInfo.phone || '-' }}</span>
          </div>
        </div>

      <!-- 客户信息表单 -->
      <div class="customer-form">
        <h4>客户信息</h4>
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="left"
        >
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="客户名称" prop="customer_name">
                <ElInput
                  v-model="formData.customer_name"
                  placeholder="请输入客户名称"
                  :prefix-icon="User"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="公司名称" prop="company">
                <ElInput
                  v-model="formData.company"
                  placeholder="请输入公司名称"
                  :prefix-icon="House"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="所属行业">
                <ElSelect
                  v-model="formData.industry"
                  placeholder="请选择所属行业"
                  style="width: 100%"
                  filterable
                >
                  <ElOption
                    v-for="industry in industryOptions"
                    :key="industry"
                    :label="industry"
                    :value="industry"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="公司地址">
                <ElInput
                  v-model="formData.address"
                  placeholder="请输入公司地址"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="公司网站">
                <ElInput
                  v-model="formData.website"
                  placeholder="请输入公司网站"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElFormItem label="公司描述">
            <ElInput
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入公司描述信息..."
              maxlength="500"
              show-word-limit
            />
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 联系人信息表单 -->
      <div class="contact-form">
        <h4>主要联系人信息</h4>
        <ElForm
          :model="formData"
          :rules="formRules"
          label-width="100px"
          label-position="left"
        >
          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="联系人姓名" prop="contact_name">
                <ElInput
                  v-model="formData.contact_name"
                  placeholder="请输入联系人姓名"
                  :prefix-icon="User"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="职位">
                <ElInput
                  v-model="formData.contact_position"
                  placeholder="请输入联系人职位"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="手机号码" prop="contact_mobile">
                <ElInput
                  v-model="formData.contact_mobile"
                  placeholder="请输入手机号码"
                  :prefix-icon="Phone"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="固定电话">
                <ElInput
                  v-model="formData.contact_phone"
                  placeholder="请输入固定电话"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="邮箱">
                <ElInput
                  v-model="formData.contact_email"
                  placeholder="请输入邮箱地址"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="微信号">
                <ElInput
                  v-model="formData.contact_wechat"
                  placeholder="请输入微信号"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>

          <ElRow :gutter="20">
            <ElCol :span="12">
              <ElFormItem label="QQ号">
                <ElInput
                  v-model="formData.contact_qq"
                  placeholder="请输入QQ号"
                />
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="closeDialog" :disabled="loading">取消</ElButton>
        <ElButton
          type="primary"
          :loading="loading"
          @click="handleSubmit"
          :disabled="loading"
        >
          <span v-if="loading">转化中...</span>
          <span v-else>确认转化</span>
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped lang="scss">
  // 转化对话框包装器样式
  :deep(.convert-dialog-wrapper) {
    .el-dialog {
      margin-top: 5vh !important;
      margin-bottom: 5vh !important;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
    }

    .el-dialog__body {
      overflow: auto;
      padding: 20px !important;
      max-height: 65vh;
    }
  }

  .convert-dialog-content {
    .lead-info {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f0f9ff;
      border: 1px solid #bfdbfe;
      border-radius: 6px;

      h4 {
        margin: 0 0 10px 0;
        color: #1e40af;
        font-size: 14px;
        font-weight: 600;
      }

      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .label {
          color: #374151;
          font-size: 13px;
        }

        .value {
          color: #1f2937;
          font-size: 13px;
          font-weight: 500;
        }
      }
    }

    .customer-form,
    .contact-form {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;

      h4 {
        margin: 0 0 15px 0;
        color: #374151;
        font-size: 14px;
        font-weight: 600;
      }
    }

    .customer-form {
      background-color: #f9fafb;
    }

    .contact-form {
      background-color: #fefefe;
    }
  }

  .dialog-footer {
    text-align: right;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .convert-dialog-content {
      .lead-info,
      .customer-form,
      .contact-form {
        padding: 10px;
      }

      :deep(.el-row) {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
