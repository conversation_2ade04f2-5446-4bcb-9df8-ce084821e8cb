<?php

use app\common\middleware\OperationLogMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;


Route::group('api/system', function () {
	
	$nameSpace = '\app\system\controller';
	
	Route::post('upload', $nameSpace . '\UploadController@index');
	Route::get('getUploadToken', $nameSpace . '\UploadController@getUploadToken');
	Route::get('getUploadConfig', $nameSpace . '\UploadController@getUploadConfig');
	
	Route::post('logout', $nameSpace . '\AuthController@logout');
	
	Route::get('admin/info', $nameSpace . '\permission\AdminController@info');
	Route::get('admin/options', $nameSpace . '\permission\AdminController@options');
	
	
	Route::get('admin/permissions', $nameSpace . '\permission\AdminController@permissions');
	
	Route::post('admin/change_password', $nameSpace . '\permission\AdminController@changePassword');
	
	Route::get('menu/options', $nameSpace . '\permission\MenuController@options');
	Route::get('role/options', $nameSpace . '\permission\RoleController@options');
	Route::get('department/options', $nameSpace . '\permission\DepartmentController@options');
	Route::get('post/options', $nameSpace . '\permission\PostController@options');
	Route::get('tenant/options', $nameSpace . '\tenant\TenantList@options');
	
	
	Route::get('api/workflow/form_field/:id', '\app\workflow\controller\TypeController@getFormField');
	
	Route::get('type/businessOptions', '\app\workflow\controller\TypeController@businessOptions');
	
	Route::get('type/options', '\app\workflow\controller\TypeController@options');
	
	Route::get('article_category/options', $nameSpace . '\ArticleCategoryController@options');

	// 每日报价相关路由
	Route::get('daily/daily_price_order/check_duplicate_date', '\app\daily\controller\DailyPriceOrderController@checkDuplicateDate');
	Route::get('daily/daily_price_order/get_yesterday_prices', '\app\daily\controller\DailyPriceOrderController@getYesterdayPrices');

})
     ->middleware([
	     TokenAuthMiddleware::class,
	     OperationLogMiddleware::class
     ]);

