/**
 * ApiSelect 组件类型定义
 */

// 值类型定义
export type SelectValue = string | number | (string | number)[] | null | undefined

// API 响应类型
export interface ApiResponse {
  code: number
  data: any
  message?: string
  [key: string]: any
}

// 选项数据类型
export interface OptionItem {
  [key: string]: any
}

// API 配置类型
export interface ApiConfig {
  url: string                                    // API 地址 *必填*
  method?: 'get' | 'post'                       // 请求方法，默认 'get'
  params?: Record<string, any>                  // 固定参数
  searchParam?: string                          // 搜索参数名，默认 'keyword'
  dataPath?: string                             // 数据路径，默认 'data'
  listPath?: string                             // 列表路径，默认 null（直接使用 dataPath）
  transform?: (data: any) => OptionItem[]       // 数据转换函数
  headers?: Record<string, string>              // 请求头
}

// 组件 Props 类型
export interface ApiSelectProps {
  modelValue?: SelectValue                      // v-model 绑定值
  
  // API 配置
  api: ApiConfig                               // API 配置 *必填*
  
  // 字段映射
  labelField?: string                          // label 字段名，默认 'name'
  valueField?: string                          // value 字段名，默认 'id'
  disabledField?: string                       // disabled 字段名，默认 'disabled'
  extraField?: string                          // 额外信息字段名
  
  // 基础配置
  multiple?: boolean                           // 是否多选，默认 false
  placeholder?: string                         // 占位符
  clearable?: boolean                          // 是否可清除，默认 true
  readonly?: boolean                           // 是否只读，默认 false
  disabled?: boolean                           // 是否禁用，默认 false
  size?: 'large' | 'default' | 'small'        // 尺寸，默认 'default'
  
  // 搜索配置
  filterable?: boolean                         // 是否可搜索，默认 true
  remote?: boolean                             // 是否远程搜索，默认 true
  minSearchLength?: number                     // 最小搜索长度，默认 0
  searchDelay?: number                         // 搜索延迟，默认 300ms
  reserveKeyword?: boolean                     // 是否保留搜索关键字，默认 false
  
  // 行为配置
  autoLoad?: boolean                           // 是否自动加载，默认 true
  cacheResults?: boolean                       // 是否缓存结果，默认 true
  loadOnFocus?: boolean                        // 是否聚焦时加载，默认 true
  
  // 显示配置
  showOptionExtra?: boolean                    // 是否显示选项额外信息，默认 false
  showFooter?: boolean                         // 是否显示底部，默认 false
  emptyText?: string                           // 空数据文本，默认 '暂无数据'
  loadingText?: string                         // 加载文本，默认 '加载中...'
  
  // Element Plus 原生配置
  collapseTags?: boolean                       // 是否折叠标签，默认 true
  collapseTagsTooltip?: boolean               // 是否显示折叠标签提示，默认 true
  maxCollapseTags?: number                     // 最大显示标签数
  multipleLimit?: number                       // 多选时最多选择数量
  tagType?: 'success' | 'info' | 'warning' | 'danger' // 标签类型
  effect?: 'dark' | 'light' | 'plain'         // 主题
  
  // 下拉配置
  popperClass?: string                         // 下拉框类名
  teleported?: boolean                         // 是否传送到 body，默认 true
  persistent?: boolean                         // 是否持久化，默认 true
  automaticDropdown?: boolean                  // 是否自动下拉，默认 false
  fitInputWidth?: boolean                      // 是否适应输入框宽度，默认 false
  
  // 验证配置
  validateEvent?: boolean                      // 是否触发表单验证，默认 true
}

// 组件 Emits 类型
export interface ApiSelectEmits {
  (e: 'update:modelValue', value: SelectValue): void
  (e: 'change', value: SelectValue, option?: OptionItem | OptionItem[]): void
  (e: 'clear'): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
  (e: 'visible-change', visible: boolean): void
  (e: 'load-success', data: OptionItem[]): void
  (e: 'load-error', error: any): void
}

// 组件实例方法类型
export interface ApiSelectInstance {
  refresh: () => Promise<void>
  clearCache: () => void
  loadData: (searchKeyword?: string) => Promise<OptionItem[]>
  getOptionList: () => OptionItem[]
}

// 搜索表单配置类型（用于 ArtSearchBar）
export interface SearchApiSelectConfig extends Omit<ApiSelectProps, 'modelValue'> {
  // 继承所有 ApiSelectProps 除了 modelValue
}

// 常用 API 配置预设
export const API_PRESETS = {
  // 用户选择
  USERS: {
    url: '/api/users',
    labelField: 'name',
    valueField: 'id'
  },
  
  // 部门选择
  DEPARTMENTS: {
    url: '/api/departments',
    labelField: 'name',
    valueField: 'id'
  },
  
  // 角色选择
  ROLES: {
    url: '/api/roles',
    labelField: 'name',
    valueField: 'id'
  }
} as const

// 工具函数类型
export type TransformFunction<T = any> = (data: T[]) => OptionItem[]
export type ApiMethod = 'get' | 'post'
export type ComponentSize = 'large' | 'default' | 'small'
export type TagType = 'success' | 'info' | 'warning' | 'danger'
export type Effect = 'dark' | 'light' | 'plain'
