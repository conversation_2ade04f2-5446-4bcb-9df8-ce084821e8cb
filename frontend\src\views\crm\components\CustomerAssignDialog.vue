<!--客户分配对话框 - 使用通用部门人员选择器-->
<template>
  <el-dialog title="客户分配" v-model="dialogVisible" width="400px" append-to-body>
    <el-form :model="form" label-width="80px">
      <el-form-item label="客户名称">
        <el-input v-model="form.customerName" readonly />
      </el-form-item>

      <el-form-item label="负责人" required>
        <div class="assign-section">
          <el-button type="primary" @click="openPersonSelector" :disabled="!form.customerId">
            选择负责人
          </el-button>

          <div v-if="selectedPerson" class="selected-person">
            <el-tag closable @close="clearSelection">
              {{ selectedPerson.name }}
              <span v-if="selectedPerson.department" class="person-dept">
                ({{ selectedPerson.department }})
              </span>
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="分配说明">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入分配说明..." />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedPerson"
          :loading="submitting"
        >
          确认分配
        </el-button>
      </div>
    </template>

    <!-- 人员选择器 -->
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :selected-data="selectedPersonData"
      :multiple="false"
      title="选择客户负责人"
      :user-api="getSalesPersons"
      @confirm="handlePersonSelected"
    />
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, computed, reactive, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import DepartmentPersonSelector from '@/components/custom/DepartmentPersonSelector.vue'
  import { UserApi } from '@/api/system/user'

  // Props 定义
  interface Props {
    modelValue: boolean
    customerData?: {
      id: number
      name: string
      current_owner?: {
        id: number
        name: string
        department?: string
      }
    }
  }

  const props = defineProps<Props>()

  // Emits 定义
  interface Emits {
    'update:modelValue': [value: boolean]
    confirm: [data: { customerId: number; ownerId: number; remark: string }]
  }

  const emit = defineEmits<Emits>()

  // 响应式数据
  const selectorVisible = ref(false)
  const submitting = ref(false)
  const selectedPerson = ref<any>(null)

  const form = reactive({
    customerId: 0,
    customerName: '',
    ownerId: 0,
    remark: ''
  })

  // 计算属性
  const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const selectedPersonData = computed(() => {
    return selectedPerson.value ? [selectedPerson.value] : []
  })

  // 监听客户数据变化
  watch(
    () => props.customerData,
    (newData) => {
      if (newData) {
        form.customerId = newData.id
        form.customerName = newData.name

        // 如果有当前负责人，设置为已选择
        if (newData.current_owner) {
          selectedPerson.value = {
            id: newData.current_owner.id,
            name: newData.current_owner.name,
            department: newData.current_owner.department
          }
          form.ownerId = newData.current_owner.id
        }
      }
    },
    { immediate: true }
  )

  // 方法
  const openPersonSelector = () => {
    selectorVisible.value = true
  }

  const clearSelection = () => {
    selectedPerson.value = null
    form.ownerId = 0
  }

  // 自定义 API - 只获取销售人员
  const getSalesPersons = async (params?: any) => {
    try {
      const res = await UserApi.getSalesUsers({
        ...params,
        status: 1 // 只获取启用的销售人员
      })

      if (res.code === 1) {
        return res.data || []
      }
      throw new Error(res.message || '获取销售人员失败')
    } catch (error) {
      console.error('获取销售人员失败:', error)
      throw error
    }
  }

  const handlePersonSelected = (persons: any[]) => {
    if (persons.length > 0) {
      selectedPerson.value = persons[0]
      form.ownerId = persons[0].id
    }
  }

  const handleCancel = () => {
    dialogVisible.value = false
    resetForm()
  }

  const handleConfirm = async () => {
    if (!selectedPerson.value) {
      ElMessage.warning('请选择负责人')
      return
    }

    submitting.value = true

    try {
      emit('confirm', {
        customerId: form.customerId,
        ownerId: form.ownerId,
        remark: form.remark
      })

      ElMessage.success('分配成功')
      dialogVisible.value = false
      resetForm()
    } catch (error) {
      console.error('分配失败:', error)
      ElMessage.error('分配失败')
    } finally {
      submitting.value = false
    }
  }

  const resetForm = () => {
    form.customerId = 0
    form.customerName = ''
    form.ownerId = 0
    form.remark = ''
    selectedPerson.value = null
  }
</script>

<style lang="scss" scoped>
  .assign-section {
    .selected-person {
      margin-top: 8px;

      .person-dept {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
