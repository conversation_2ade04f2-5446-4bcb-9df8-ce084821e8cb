# 权限系统快速参考卡片

**版本**: v2.0
**更新日期**: 2025-01-31
**更新内容**: 控制器命名规范统一、路由组织优化、中间件配置标准化

## 🚀 快速开始

### 1. 后端路由权限（v2.0 规范）
```php
// route/modules/your_module.php
use app\common\middleware\TokenAuthMiddleware;
use app\common\middleware\PermissionMiddleware;

$nameSpace = 'app\your_module\controller';

Route::group('your_module', function () use ($nameSpace) {
    Route::get('list', $nameSpace . '\YourModuleController@index');
    Route::post('add', $nameSpace . '\YourModuleController@add');
    Route::put('edit', $nameSpace . '\YourModuleController@edit');
    Route::delete('delete', $nameSpace . '\YourModuleController@delete');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);
```

### 2. 前端权限控制
```vue
<template>
  <!-- 使用 v-auth 指令 -->
  <ElButton v-auth="'crm:crm_product:add'" @click="handleAdd">新增</ElButton>
  
  <!-- 使用 hasAuth 函数 -->
  <ElButton v-if="hasAuth('crm:crm_product:edit')" @click="handleEdit">编辑</ElButton>
</template>

<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'
const { hasAuth } = useAuth()
</script>
```

### 3. 数据库权限配置
```sql
INSERT INTO `system_menu` (`parent_id`, `title`, `name`, `type`, `status`) VALUES
(0, '新增产品', 'crm:crm_product:add', 2, 1),
(0, '编辑产品', 'crm:crm_product:edit', 2, 1),
(0, '删除产品', 'crm:crm_product:delete', 2, 1);
```

---

## 🏗️ 控制器命名规范（v2.0）

### 文件命名
```php
// ✅ 正确格式（必须包含Controller后缀）
app/{模块}/controller/{功能}Controller.php

// 示例
app/crm/controller/CrmCustomerMyController.php
app/system/controller/AuthController.php
app/system/controller/permission/AdminController.php
```

### 类名规范
```php
// ✅ 正确类名
class CrmCustomerMyController extends BaseController
class AuthController extends BaseController
class AdminController extends BaseController
```

### 路由引用
```php
// ✅ 正确引用格式
Route::get('index', 'app\crm\controller\CrmCustomerMyController@index');
Route::post('login', 'app\system\controller\AuthController@login');
```

---

## 🛡️ 中间件配置规范（v2.0）

### 标准配置组合
```php
// 1. 公开接口（无中间件）
Route::group('api/public', function () {
    Route::get('captcha', 'getCaptcha');
});

// 2. 认证接口（登录限制）
Route::group('api/auth', function () {
    Route::post('login', 'login');
})->middleware([CheckLoginAttempts::class]);

// 3. 公共接口（仅Token认证）
Route::group('api/common', function () {
    Route::get('options', 'getOptions');
})->middleware([TokenAuthMiddleware::class]);

// 4. 业务接口（Token + 权限验证）
Route::group('api/business', function () {
    Route::get('list', 'list');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);

// 5. 管理接口（Token + 权限 + 日志）
Route::group('api/admin', function () {
    Route::get('admin/list', 'list');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class,
    OperationLogMiddleware::class
]);
```

---

## 📝 权限标识规范

### 命名格式
```
模块:子模块:操作
例如: crm:crm_product:add
```

### 常用操作
| 操作 | 标识 | 说明 |
|------|------|------|
| 列表 | `list` | 查看列表 |
| 详情 | `detail` | 查看详情 |
| 新增 | `add` | 新增数据 |
| 编辑 | `edit` | 编辑数据 |
| 删除 | `delete` | 删除数据 |
| 导入 | `import` | 导入数据 |
| 导出 | `export` | 导出数据 |
| 审批 | `approve` | 审批操作 |
| 分配 | `assign` | 分配操作 |
| 认领 | `claim` | 认领操作 |

---

## 🔧 常用代码片段

### 前端权限检查
```typescript
// 基础权限检查
const canAdd = computed(() => hasAuth('crm:crm_product:add'))
const canEdit = computed(() => hasAuth('crm:crm_product:edit'))
const canDelete = computed(() => hasAuth('crm:crm_product:delete'))

// 业务权限检查
const canEditProduct = (product: any) => {
  return hasAuth('crm:crm_product:edit') && product.status === 0
}
```

### 表格操作列
```vue
<ElTableColumn prop="operation" label="操作" width="200">
  <template #default="scope">
    <ArtButtonTable
      v-auth="'crm:crm_product:detail'"
      text="详情"
      @click="showDetail(scope.row.id)"
    />
    <ArtButtonTable
      v-auth="'crm:crm_product:edit'"
      text="编辑"
      @click="showEdit(scope.row.id)"
    />
    <ArtButtonTable
      v-auth="'crm:crm_product:delete'"
      text="删除"
      @click="handleDelete(scope.row.id)"
    />
  </template>
</ElTableColumn>
```

### 下拉菜单权限
```vue
<ElDropdown @command="handleCommand">
  <ArtButtonTable text="更多" type="more" />
  <template #dropdown>
    <ElDropdownMenu>
      <ElDropdownItem v-if="hasAuth('crm:crm_product:copy')" command="copy">
        复制
      </ElDropdownItem>
      <ElDropdownItem v-if="hasAuth('crm:crm_product:move')" command="move">
        移动
      </ElDropdownItem>
    </ElDropdownMenu>
  </template>
</ElDropdown>
```

### 动态权限控制
```typescript
// 在 formatter 中使用
formatter: (row: any) => {
  const buttons = []
  
  if (hasAuth('crm:crm_product:edit')) {
    buttons.push(h(ArtButtonTable, {
      text: '编辑',
      onClick: () => handleEdit(row.id)
    }))
  }
  
  if (hasAuth('crm:crm_product:delete') && row.status === 0) {
    buttons.push(h(ArtButtonTable, {
      text: '删除',
      onClick: () => handleDelete(row.id)
    }))
  }
  
  return h('div', buttons)
}
```

---

## 🎯 最佳实践

### ✅ 推荐做法
```vue
<!-- 1. 优先使用 v-auth 指令 -->
<ElButton v-auth="'crm:crm_product:add'" @click="handleAdd">新增</ElButton>

<!-- 2. 复杂逻辑使用 hasAuth 函数 -->
<ElButton v-if="hasAuth('crm:crm_product:edit') && canEdit(row)" @click="handleEdit">
  编辑
</ElButton>

<!-- 3. 权限标识使用常量 -->
<script setup lang="ts">
const PERMISSIONS = {
  ADD: 'crm:crm_product:add',
  EDIT: 'crm:crm_product:edit',
  DELETE: 'crm:crm_product:delete'
}
</script>
```

### ❌ 避免做法
```vue
<!-- 1. 硬编码角色判断 -->
<ElButton v-if="user.role === 'admin'" @click="handleAdd">新增</ElButton>

<!-- 2. 权限标识拼写错误 -->
<ElButton v-auth="'crm:product:add'" @click="handleAdd">新增</ElButton>

<!-- 3. 缺少权限控制 -->
<ElButton @click="handleDelete">删除</ElButton>
```

---

## 🔍 调试技巧

### 权限调试
```typescript
// 开发环境权限调试
if (process.env.NODE_ENV === 'development') {
  console.log('当前用户权限:', userStore.permissions)
  console.log('检查权限:', permission, hasAuth(permission))
}
```

### 权限测试
```typescript
// 模拟不同权限进行测试
const testPermissions = [
  'crm:crm_product:add',
  'crm:crm_product:edit'
]

userStore.permissions = testPermissions
```

---

## 🚨 常见问题

### Q: 权限不生效？
**A**: 检查以下几点：
1. 权限中间件是否启用
2. 权限标识是否正确
3. 用户是否有对应权限
4. 前端权限缓存是否过期

### Q: 按钮不显示？
**A**: 检查：
1. v-auth 指令是否正确
2. 权限标识是否存在于数据库
3. 用户角色是否分配了该权限

### Q: 如何添加新权限？
**A**: 步骤：
1. 在数据库添加权限记录
2. 在路由中启用权限中间件
3. 在前端添加权限控制
4. 为角色分配新权限

---

## 📞 快速联系

- **技术支持**: 开发团队
- **文档问题**: 系统架构师
- **测试环境**: http://localhost:3006
- **最后更新**: 2025-01-25

---

**💡 提示**: 将此卡片保存为书签，随时查阅权限系统开发规范！
