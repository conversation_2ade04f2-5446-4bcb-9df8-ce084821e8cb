# AUGMENT开发提示词 - CRM工作报告模块

## 📋 项目背景

### 当前状态
- ✅ 数据库字段注释已优化完成
- ✅ CRUD生成器已执行完成，基础代码已生成
- ✅ 基础前后端文件结构已创建
- 🔄 需要进行定制开发和UI优化

### 项目目标
基于已生成的CRUD基础代码，完成CRM工作报告模块的定制开发，实现飞书风格的现代化UI设计和增强功能。

### 技术栈
- **后端**: ThinkPHP 8 + MySQL + Redis
- **前端**: Vue 3 + TypeScript + Element Plus + Vite
- **工具**: MCP工具集成、现有CRM组件体系

## 🎯 开发任务分解

### 阶段一：后端功能定制（0.5天）

#### 任务1.1：检查和优化生成的后端代码
**目标**: 验证CRUD生成器生成的后端代码，并进行必要的定制

**提示词**:
```
请帮我检查和优化CRM工作报告模块的后端代码。

背景信息：
- 已使用CRUD生成器生成了基础代码
- 表名：crm_work_report
- 模块：crm
- 需要添加复制汇报、附件管理等功能

请执行以下任务：

1. 检查生成的文件：
   - app/crm/controller/CrmWorkReportController.php
   - app/crm/model/CrmWorkReport.php  
   - app/crm/service/CrmWorkReportService.php

2. 验证基础CRUD功能是否正常

3. 在CrmWorkReportService中添加复制功能：
   - 方法名：copy($id)
   - 功能：复制现有汇报，修改标题为"复制-原标题"，日期改为当天

4. 在CrmWorkReportController中添加复制接口：
   - 路由：POST /crm/work-report/{id}/copy
   - 返回JSON格式响应

5. 在CrmWorkReport模型中添加：
   - 附件处理的访问器和修改器
   - 关联关系（创建人信息）
   - 业务相关的作用域方法

6. 测试所有API接口功能

请使用view工具查看现有代码，使用str-replace-editor工具进行修改，确保代码符合ThinkPHP 8规范和现有CRM系统架构。
```

#### 任务1.2：API接口测试
**目标**: 测试后端API接口功能

**提示词**:
```
请帮我测试CRM工作报告模块的后端API接口。

请执行以下测试：

1. 使用launch-process工具启动后端服务（如果需要）

2. 测试基础CRUD接口：
   - GET /crm/work-report - 列表查询
   - POST /crm/work-report - 创建汇报
   - GET /crm/work-report/{id} - 详情查询
   - PUT /crm/work-report/{id} - 更新汇报
   - DELETE /crm/work-report/{id} - 删除汇报

3. 测试扩展功能接口：
   - POST /crm/work-report/{id}/copy - 复制汇报
   - GET /crm/work-report/export - 导出功能

4. 测试搜索筛选功能：
   - 按标题搜索
   - 按类型筛选
   - 按日期范围筛选

5. 验证数据验证规则：
   - 必填字段验证
   - 字段长度验证
   - 数据格式验证

请使用适当的测试数据，记录测试结果，如有问题请修复。
```

### 阶段二：前端UI定制开发（1天）

#### 任务2.1：列表页面优化（飞书风格）
**目标**: 将生成的表格列表改为飞书风格的卡片布局

**提示词**:
```
请帮我优化CRM工作报告模块的列表页面，实现飞书风格的卡片布局。

背景信息：
- 文件路径：frontend/src/views/crm/crm_work_report/list.vue
- 已有CRUD生成器生成的基础代码
- 需要改为卡片式布局，参考飞书设计风格

请执行以下任务：

1. 查看现有的list.vue文件结构

2. 保留以下生成的功能：
   - 搜索筛选栏（ArtSearchBar）
   - 操作栏（ArtTableHeader）
   - 分页组件
   - 表单对话框
   - 基础的数据获取逻辑

3. 替换表格为卡片布局：
   - 使用CSS Grid布局，响应式设计
   - 每个卡片显示：汇报日期、类型标签、标题、内容预览、创建人、创建时间、附件数量
   - 添加悬停效果和微交互动画

4. 实现飞书风格设计：
   - 色彩系统：日报蓝色、周报绿色、月报橙色
   - 圆角卡片、阴影效果
   - 现代化的字体和间距

5. 添加操作按钮：
   - 查看详情、编辑、复制、删除
   - 按钮样式符合飞书设计

6. 添加复制功能的前端逻辑

请确保代码质量和用户体验，保持与现有CRM系统的一致性。
```

#### 任务2.2：表单页面优化
**目标**: 优化表单页面，集成富文本编辑器和改进布局

**提示词**:
```
请帮我优化CRM工作报告模块的表单页面，集成富文本编辑器并改进布局设计。

背景信息：
- 文件路径：frontend/src/views/crm/crm_work_report/form-dialog.vue
- 需要集成富文本编辑器替换textarea
- 需要实现分区布局设计

请执行以下任务：

1. 检查现有的form-dialog.vue文件

2. 安装富文本编辑器依赖：
   - 推荐使用@vueup/vue-quill
   - 或者其他适合的富文本编辑器

3. 优化表单布局：
   - 分为三个区域：基本信息、汇报内容、附件上传
   - 使用卡片式分区设计
   - 合理的间距和视觉层次

4. 集成富文本编辑器：
   - 替换content、summary、plan字段的textarea
   - 配置基础工具栏（粗体、斜体、列表、图片等）
   - 确保数据正确保存和显示

5. 优化附件上传组件：
   - 支持拖拽上传
   - 多文件上传
   - 文件预览和删除

6. 添加表单功能：
   - 草稿保存功能
   - 智能默认值（日期默认今天）
   - 表单验证优化

7. 应用飞书风格样式：
   - 统一的色彩和字体
   - 现代化的表单控件样式
   - 合理的留白和布局

请确保富文本编辑器功能完整，表单体验流畅。
```

#### 任务2.3：详情页面开发
**目标**: 创建汇报详情页面

**提示词**:
```
请帮我创建CRM工作报告模块的详情页面。

背景信息：
- CRUD生成器没有生成详情页面，需要新建
- 需要展示完整的汇报信息
- 支持富文本内容展示

请执行以下任务：

1. 创建详情页面组件：
   - 文件路径：frontend/src/views/crm/crm_work_report/detail.vue
   - 或者在list.vue中添加详情抽屉组件

2. 实现详情页面布局：
   - 头部信息区：标题、类型、日期、创建人、操作按钮
   - 内容区：工作内容、工作总结、下期计划
   - 附件区：附件列表和下载功能

3. 富文本内容展示：
   - 正确渲染HTML格式的富文本内容
   - 保持原有格式和样式

4. 操作功能：
   - 编辑按钮（打开表单对话框）
   - 复制按钮（调用复制API）
   - 删除按钮（确认后删除）

5. 附件管理：
   - 显示附件列表
   - 支持附件下载
   - 显示附件类型图标

6. 应用飞书风格设计：
   - 清晰的信息层次
   - 舒适的阅读体验
   - 统一的视觉风格

请确保详情页面信息完整，操作便捷。
```

### 阶段三：功能完善和测试（0.5天）

#### 任务3.1：前端API集成测试
**目标**: 测试前端与后端API的集成

**提示词**:
```
请帮我测试CRM工作报告模块的前端功能。

请执行以下测试：

1. 启动前端开发服务器：
   - 使用launch-process工具启动npm run dev

2. 访问页面测试基础功能：
   - 访问列表页面：/crm/work-report
   - 测试搜索筛选功能
   - 测试分页功能

3. 测试CRUD操作：
   - 新增汇报（测试富文本编辑器）
   - 编辑汇报（测试数据回显）
   - 查看详情（测试富文本显示）
   - 删除汇报（测试确认对话框）

4. 测试扩展功能：
   - 复制汇报功能
   - 附件上传下载
   - 导入导出功能

5. 测试UI体验：
   - 卡片布局响应式效果
   - 悬停动画效果
   - 表单验证提示
   - 加载状态显示

6. 浏览器兼容性测试：
   - Chrome、Firefox、Safari、Edge

请记录测试结果，发现问题及时修复。
```

#### 任务3.2：性能优化和Bug修复
**目标**: 优化性能并修复发现的问题

**提示词**:
```
请帮我优化CRM工作报告模块的性能并修复Bug。

请执行以下优化：

1. 性能优化：
   - 检查页面加载时间
   - 优化富文本编辑器性能
   - 优化图片和附件加载
   - 检查内存泄漏

2. 代码优化：
   - 检查TypeScript类型定义
   - 优化API调用逻辑
   - 添加错误处理
   - 优化组件渲染

3. 用户体验优化：
   - 添加加载状态
   - 优化错误提示
   - 改进操作反馈
   - 优化表单验证

4. Bug修复：
   - 修复发现的功能问题
   - 修复样式问题
   - 修复兼容性问题

5. 代码规范检查：
   - 检查代码格式
   - 检查命名规范
   - 检查注释完整性

请确保最终代码质量高，用户体验好。
```

## 🔧 MCP工具使用指南

### ✅ 已验证可用的MCP工具
1. **codebase-retrieval**: 代码库检索和分析 ✅
2. **view**: 查看文件和目录结构 ✅
3. **str-replace-editor**: 精确编辑代码文件 ✅
4. **save-file**: 创建新文件 ✅
5. **launch-process**: 启动开发服务器 ✅
6. **read-process**: 读取进程输出 ✅
7. **open-browser**: 打开浏览器测试 ✅
8. **diagnostics**: 检查代码问题 ✅

### 🗂️ 项目环境信息
- **项目路径**: `e:\项目\self_admin\base_admin`
- **前端路径**: `frontend/src/`
- **后端路径**: `app/crm/`
- **开发服务器**: 前端 localhost:3006, 后端 www.bs.com
- **CRUD生成器**: 已执行完成，基础文件已生成

### 开发流程建议
1. 每个任务开始前先用view工具查看相关文件
2. 使用str-replace-editor进行代码修改
3. 使用launch-process启动服务进行测试
4. 使用open-browser验证功能（URL: localhost:3006）
5. 使用diagnostics检查代码质量

## ✅ 验收标准

### 功能验收
- [ ] 所有CRUD操作正常
- [ ] 复制汇报功能正常
- [ ] 富文本编辑器功能完整
- [ ] 附件上传下载正常
- [ ] 搜索筛选功能正确
- [ ] 导入导出功能正常

### UI验收
- [ ] 列表页面采用卡片式布局
- [ ] 表单页面采用分区设计
- [ ] 详情页面信息完整
- [ ] 飞书风格设计实现
- [ ] 响应式设计适配

### 性能验收
- [ ] 页面加载时间 < 2秒
- [ ] 富文本编辑器响应流畅
- [ ] 文件上传成功率 > 98%
- [ ] 无明显内存泄漏

## 📝 注意事项

1. **保持现有架构**: 基于生成的代码进行定制，不要重写
2. **遵循规范**: 保持与现有CRM系统的代码规范一致
3. **测试驱动**: 每个功能完成后立即测试
4. **用户体验**: 重点关注UI设计和交互体验
5. **错误处理**: 添加完善的错误处理和用户提示

## 🎯 成功指标

- **开发效率**: 2个工作日内完成所有功能
- **代码质量**: 无严重Bug，符合规范
- **用户体验**: UI美观现代，操作流畅
- **功能完整**: 满足所有设计要求

## 📚 技术参考

### 现有CRM组件参考
- **列表组件**: `frontend/src/views/crm/crm_customer_my/list.vue`
- **表单组件**: `frontend/src/views/crm/crm_customer_my/form-dialog.vue`
- **详情组件**: `frontend/src/components/custom/CustomerDetailDrawer/`
- **API封装**: `frontend/src/api/crm/crmCustomerApi.ts`

### 样式参考
- **CRM样式**: `frontend/src/assets/styles/crm-ux-optimization.scss`
- **色彩变量**: 使用现有的CSS变量系统
- **组件样式**: 参考现有CRM模块的样式实现

### 后端参考
- **控制器**: `app/crm/controller/CrmCustomerController.php`
- **服务类**: `app/crm/service/CrmCustomerService.php`
- **模型**: `app/crm/model/CrmCustomer.php`

## 🔄 开发检查清单

### 阶段一检查点
- [ ] 后端API接口全部正常响应
- [ ] 复制功能API测试通过
- [ ] 数据验证规则正确
- [ ] 权限控制正常

### 阶段二检查点
- [ ] 列表页面卡片布局实现
- [ ] 富文本编辑器集成成功
- [ ] 表单分区布局完成
- [ ] 详情页面功能完整

### 阶段三检查点
- [ ] 前后端集成测试通过
- [ ] UI设计符合飞书风格
- [ ] 性能指标达标
- [ ] 浏览器兼容性良好

## 🚨 常见问题解决

### 富文本编辑器问题
- 如果@vueup/vue-quill有问题，可以尝试quill或tinymce
- 注意富文本内容的XSS防护
- 确保HTML内容正确保存和显示

### 样式冲突问题
- 使用scoped样式避免全局污染
- 参考现有CRM模块的样式实现
- 保持与Element Plus主题的一致性

### API调用问题
- 检查租户ID是否正确传递
- 确保权限中间件正常工作
- 注意错误处理和用户提示

### 性能问题
- 富文本编辑器按需加载
- 图片和附件懒加载
- 合理使用Vue的响应式特性
