# 请假申请工作流集成清理指南

## 📋 清理概述

本文档详细说明了请假申请对接新工作流业务集成后需要清理的文件和代码，包括备份操作、删除操作和重构操作的具体步骤。

## 🎯 清理目标

1. **移除过时的工作流集成方式**
2. **清理重复的代码实现**
3. **删除不再使用的测试文件**
4. **统一工作流集成架构**
5. **保持代码库的整洁性**

## 📂 需要清理的文件列表

### 1. 过时的工作流模块文件

#### 1.1 删除过时的SQL文件
```bash
# 需要删除的文件
app/workflow/hr_module.sql

# 删除原因
这是第一个版本的HR模块SQL文件，已被新的hr_leave表结构替代
```

#### 1.2 删除过时的服务文件
```bash
# 需要删除的文件
app/hr/service/HrTravelService.php

# 删除原因
使用了旧的ApplicationService集成方式，与新的工作流架构不兼容
```

### 2. 测试文件清理

#### 2.1 删除旧的测试文件
```bash
# 需要删除的文件
app/workflow/test/test_new_workflow_instance.php
app/workflow/test/test_complete_workflow_with_notification.php

# 删除原因
这些是早期的工作流测试文件，功能已被新的测试脚本覆盖
```

### 3. 重复实现清理

#### 3.1 ApplicationController中的请假相关方法
```bash
# 需要修改的文件
app/workflow/controller/ApplicationController.php

# 需要删除的方法
- submitLeave()
- getLeaveDetail()
- updateLeaveStatus()

# 删除原因
这些方法的功能已迁移到HrLeaveController中，避免重复实现
```

#### 3.2 旧的工作流配置文件
```bash
# 需要检查并可能删除的文件
config/workflow_old.php
config/hr_workflow.php

# 删除条件
如果这些配置文件只用于旧的请假申请流程，且新流程不再依赖
```

### 4. 前端文件清理

#### 4.1 旧的前端组件
```bash
# 需要备份后删除的文件
frontend/src/views/workflow/components/business-forms/hr_leave-form-old.vue
frontend/src/views/hr/leave/old-leave-form.vue

# 删除原因
已被新的hr_leave-form.vue替代
```

#### 4.2 旧的API接口文件
```bash
# 需要备份后删除的文件
frontend/src/api/hr/leave-old.ts
frontend/src/api/workflow/hr-leave.ts

# 删除原因
已被新的hrLeave.ts统一替代
```

## 🔧 清理操作步骤

### 步骤1：备份重要文件

在删除任何文件之前，先进行备份：

```bash
# 创建备份目录
mkdir -p backup/hr_leave_integration_$(date +%Y%m%d_%H%M%S)

# 备份即将删除的文件
cp app/workflow/hr_module.sql backup/hr_leave_integration_$(date +%Y%m%d_%H%M%S)/
cp app/hr/service/HrTravelService.php backup/hr_leave_integration_$(date +%Y%m%d_%H%M%S)/
cp -r app/workflow/test/ backup/hr_leave_integration_$(date +%Y%m%d_%H%M%S)/test/
cp frontend/src/views/workflow/components/business-forms/hr_leave-form.vue backup/hr_leave_integration_$(date +%Y%m%d_%H%M%S)/hr_leave-form-old.vue
```

### 步骤2：删除过时文件

按照优先级顺序删除文件：

#### 2.1 删除后端过时文件
```bash
# 删除过时的SQL文件
rm -f app/workflow/hr_module.sql

# 删除过时的服务文件
rm -f app/hr/service/HrTravelService.php

# 删除测试文件
rm -f app/workflow/test/test_new_workflow_instance.php
rm -f app/workflow/test/test_complete_workflow_with_notification.php
```

#### 2.2 删除前端过时文件
```bash
# 删除旧的API文件（如果存在）
rm -f frontend/src/api/hr/leave-old.ts
rm -f frontend/src/api/workflow/hr-leave.ts

# 删除旧的组件文件（如果存在）
rm -f frontend/src/views/hr/leave/old-leave-form.vue
```

### 步骤3：修改现有文件

#### 3.1 修改ApplicationController
```php
// 在 app/workflow/controller/ApplicationController.php 中
// 删除以下方法（如果存在）：

// 删除这些方法
public function submitLeave() { ... }
public function getLeaveDetail() { ... }
public function updateLeaveStatus() { ... }

// 添加重定向或提示
public function submitLeave() {
    return $this->error('请假申请功能已迁移到HR模块，请使用新的接口');
}
```

#### 3.2 更新路由配置
```php
// 在相关路由文件中，移除旧的请假申请路由
// 确保新的HR请假申请路由正确配置
```

### 步骤4：清理数据库

#### 4.1 检查旧表数据
```sql
-- 检查是否还有旧的请假申请表
SHOW TABLES LIKE '%leave%';

-- 如果存在旧表且数据已迁移，可以删除
-- DROP TABLE IF EXISTS old_hr_leave;
```

#### 4.2 清理无用的工作流定义
```sql
-- 检查工作流定义表中是否有旧的请假申请流程
SELECT * FROM workflow_definition WHERE business_code = 'hr_leave_old';

-- 如果确认不再使用，可以删除
-- DELETE FROM workflow_definition WHERE business_code = 'hr_leave_old';
```

### 步骤5：验证清理结果

#### 5.1 运行测试脚本
```bash
# 运行新的测试脚本，确保功能正常
php think run test_hr_leave_workflow_integration.php
```

#### 5.2 检查代码引用
```bash
# 搜索代码中是否还有对已删除文件的引用
grep -r "HrTravelService" app/
grep -r "hr_module.sql" app/
grep -r "test_new_workflow_instance" app/
```

#### 5.3 前端功能验证
```bash
# 检查前端是否还有对旧API的调用
grep -r "leave-old" frontend/src/
grep -r "hr-leave" frontend/src/api/workflow/
```

## ⚠️ 注意事项

### 1. 数据安全
- **必须先备份**：在删除任何文件前，务必进行完整备份
- **分步执行**：不要一次性删除所有文件，分步骤验证
- **保留日志**：记录每个删除操作，便于回滚

### 2. 依赖检查
- **检查引用**：确保没有其他模块依赖即将删除的文件
- **测试验证**：每删除一个文件后都要运行相关测试
- **功能确认**：确保新功能完全覆盖旧功能

### 3. 团队协作
- **通知团队**：提前通知团队成员清理计划
- **文档更新**：更新相关的开发文档和API文档
- **代码审查**：清理操作需要经过代码审查

## 📋 清理检查清单

### 文件删除检查
- [ ] app/workflow/hr_module.sql 已删除
- [ ] app/hr/service/HrTravelService.php 已删除
- [ ] app/workflow/test/test_new_workflow_instance.php 已删除
- [ ] app/workflow/test/test_complete_workflow_with_notification.php 已删除
- [ ] 旧的前端API文件已删除
- [ ] 旧的前端组件文件已删除

### 代码修改检查
- [ ] ApplicationController中的请假相关方法已删除或重定向
- [ ] 路由配置已更新
- [ ] 没有代码引用已删除的文件
- [ ] 新的工作流集成功能正常

### 测试验证检查
- [ ] 新的测试脚本运行通过
- [ ] 前端功能正常
- [ ] 后端API正常
- [ ] 工作流集成正常
- [ ] 数据库结构正确

### 文档更新检查
- [ ] API文档已更新
- [ ] 开发文档已更新
- [ ] 部署文档已更新
- [ ] 用户手册已更新

## 🔄 回滚计划

如果清理过程中出现问题，可以按以下步骤回滚：

### 1. 立即回滚
```bash
# 从备份恢复文件
cp backup/hr_leave_integration_*/filename.php app/path/to/filename.php
```

### 2. 数据库回滚
```sql
-- 如果修改了数据库，从备份恢复
-- 具体操作根据实际修改内容确定
```

### 3. 重新部署
```bash
# 重新部署应用
# 确保所有服务正常运行
```

## 📞 联系支持

如果在清理过程中遇到问题，请：

1. **检查备份**：确认备份文件完整
2. **查看日志**：检查应用和数据库日志
3. **运行测试**：执行相关测试脚本
4. **寻求帮助**：联系技术团队获取支持

---

**清理完成后，请确认所有功能正常运行，并更新相关文档。**
