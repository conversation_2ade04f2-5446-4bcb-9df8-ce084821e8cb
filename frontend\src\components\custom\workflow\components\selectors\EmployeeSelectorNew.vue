<!--员工选择器组件 - 使用通用组件适配-->
<template>
  <DepartmentPersonSelector
    v-model="dialogVisible"
    :selected-data="adaptedSelectedData"
    :title="title"
    :department-api="loadDepartmentTree"
    :user-api="loadEmployeeList"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { WorkflowApi } from '../../api/workflowApi'
import { TargetType, type TargetObject } from '../../types'
import DepartmentPersonSelector from '@/components/custom/DepartmentPersonSelector.vue'

// 定义组件属性
const props = defineProps<{
  modelValue: boolean
  selectedData?: TargetObject[]
  title?: string
}>()

// 定义组件事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [selectedItems: TargetObject[]]
}>()

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 适配选中数据格式
const adaptedSelectedData = computed(() => {
  if (!props.selectedData) return []
  
  return props.selectedData.map(item => ({
    id: item.id,
    name: item.name,
    type: item.type,
    avatar: item.avatar,
    position: item.position,
    department: item.department
  }))
})

// 适配部门树 API
const loadDepartmentTree = async () => {
  const res = await WorkflowApi.getDepartmentList()
  if (res.code === 1 && res.data) {
    return res.data
  }
  throw new Error(res.message || '获取部门列表失败')
}

// 适配员工列表 API
const loadEmployeeList = async (params?: any) => {
  const res = await WorkflowApi.getUserList(params)
  if (res.code === 1 && res.data) {
    return res.data.map((user: any) => ({
      id: user.id,
      name: user.name,
      type: TargetType.USER,
      avatar: user.avatar,
      position: user.position,
      department: user.department
    }))
  }
  throw new Error(res.message || '获取员工列表失败')
}

// 处理确认选择
const handleConfirm = (selectedItems: any[]) => {
  const adaptedItems: TargetObject[] = selectedItems.map(item => ({
    id: item.id,
    name: item.name,
    type: item.type || TargetType.USER
  }))
  
  emit('confirm', adaptedItems)
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}
</script>
