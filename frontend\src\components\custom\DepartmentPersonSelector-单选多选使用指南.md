# DepartmentPersonSelector 简化版使用指南

## 📋 组件概述

现在提供了三个不同场景的部门人员选择组件：

1. **DepartmentPersonSelector** - 弹窗式选择器（工作流、复杂选择场景）
2. **DepartmentPersonForm** - 表单内联选择器（表单字段）
3. **DepartmentPersonSearch** - 搜索器（表格搜索、快速筛选）

## 🎯 单选多选数据结构

### 简化的数据类型定义
```typescript
// 简化的人员数据结构（只包含ID）
interface PersonItem {
  id: string | number
}

// 单选数据结构
type SingleValue = PersonItem | null

// 多选数据结构
type MultipleValue = PersonItem[]

// 统一数据结构
type Value = SingleValue | MultipleValue
```

### 数据格式对比
```typescript
// 单选模式（简化版）
const singleValue: PersonItem | null = {
  id: 1  // 只需要传入ID
}

// 多选模式（简化版）
const multipleValue: PersonItem[] = [
  { id: 1 },  // 只需要传入ID
  { id: 2 }   // 组件内部会自动获取人员详细信息
]
```

### 组件内部数据处理
```typescript
// 组件内部会自动处理人员信息的获取和缓存
// 用户只需要关心ID，组件会：
// 1. 根据ID获取人员详细信息（姓名、头像、部门等）
// 2. 缓存人员信息，避免重复请求
// 3. 在界面上正确显示人员信息
```

## 🚀 使用示例

### 1. 弹窗式选择器 (DepartmentPersonSelector)

#### 单选模式
```vue
<template>
  <div>
    <el-button @click="openSelector">选择负责人</el-button>
    
    <!-- 显示选中结果 -->
    <div v-if="selectedPerson">
      已选择: {{ selectedPerson.name }}
    </div>
    
    <!-- 单选选择器 -->
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :selected-data="selectedPerson"
      :multiple="false"
      title="选择负责人"
      @confirm="handleSingleConfirm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { DepartmentPersonSelector } from '@/components/custom'

const selectorVisible = ref(false)
const selectedPerson = ref(null) // 单选：PersonItem | null

const openSelector = () => {
  selectorVisible.value = true
}

const handleSingleConfirm = (person) => {
  selectedPerson.value = person // person 是 { id: number } | null
  console.log('选择的人员ID:', person?.id)
}
</script>
```

#### 多选模式
```vue
<template>
  <div>
    <el-button @click="openSelector">选择团队成员</el-button>
    
    <!-- 显示选中结果 -->
    <div v-if="selectedPersons.length > 0">
      已选择 {{ selectedPersons.length }} 人:
      <el-tag v-for="person in selectedPersons" :key="person.id">
        {{ person.name }}
      </el-tag>
    </div>
    
    <!-- 多选选择器 -->
    <DepartmentPersonSelector
      v-model="selectorVisible"
      :selected-data="selectedPersons"
      :multiple="true"
      title="选择团队成员"
      @confirm="handleMultipleConfirm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectorVisible = ref(false)
const selectedPersons = ref([]) // 多选：PersonItem[]

const handleMultipleConfirm = (persons) => {
  selectedPersons.value = persons // persons 是 PersonItem[]
  console.log('选择的人员:', persons)
}
</script>
```

### 2. 表单内联选择器 (DepartmentPersonForm)

#### 表单中的单选
```vue
<template>
  <el-form :model="form" label-width="100px">
    <el-form-item label="项目负责人" required>
      <DepartmentPersonForm
        v-model="form.owner"
        :multiple="false"
        placeholder="请选择项目负责人"
        @change="handleOwnerChange"
      />
    </el-form-item>

    <el-form-item label="团队成员">
      <DepartmentPersonForm
        v-model="form.members"
        :multiple="true"
        placeholder="请选择团队成员"
        :collapse-tags="true"
        @change="handleMembersChange"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'

const form = reactive({
  owner: null,        // 单选：{ id: number } | null
  members: []         // 多选：{ id: number }[]
})

const handleOwnerChange = (owner) => {
  console.log('负责人ID:', owner?.id)
}

const handleMembersChange = (members) => {
  console.log('成员IDs:', members.map(m => m.id))
}
</script>
```

### 3. 表格搜索器 (DepartmentPersonSearch)

#### 表格搜索中的使用
```vue
<template>
  <div>
    <!-- 搜索栏 -->
    <el-form :model="searchForm" inline>
      <el-form-item label="负责人">
        <DepartmentPersonSearch
          v-model="searchForm.owner"
          :multiple="false"
          placeholder="搜索负责人"
          size="small"
          @change="handleSearch"
        />
      </el-form-item>

      <el-form-item label="参与人员">
        <DepartmentPersonSearch
          v-model="searchForm.participants"
          :multiple="true"
          placeholder="搜索参与人员"
          size="small"
          :collapse-tags="true"
          @change="handleSearch"
        />
      </el-form-item>
    </el-form>
    
    <!-- 表格 -->
    <el-table :data="tableData">
      <!-- 表格列 -->
    </el-table>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'

const searchForm = reactive({
  owner: null,        // 单选筛选
  participants: []    // 多选筛选
})

const tableData = ref([])

const handleSearch = () => {
  // 执行搜索
  console.log('搜索条件:', searchForm)
  loadTableData()
}

const loadTableData = async () => {
  // 根据搜索条件加载数据
  const params = {
    owner_id: searchForm.owner?.id,
    participant_ids: searchForm.participants.map(p => p.id)
  }
  // 调用 API...
}
</script>
```

## 🔧 配置参数对比

| 参数 | DepartmentPersonSelector | DepartmentPersonSelect | DepartmentPersonFilter |
|------|-------------------------|----------------------|----------------------|
| **使用场景** | 复杂选择、工作流 | 表单字段 | 表格搜索、筛选 |
| **显示方式** | 弹窗 | 内联下拉 | 内联下拉+搜索 |
| **multiple** | ✅ 支持 | ✅ 支持 | ✅ 支持 |
| **数据结构** | 单选/多选自适应 | 单选/多选自适应 | 单选/多选自适应 |
| **搜索功能** | 内置搜索 | 通过弹窗搜索 | 实时远程搜索 |
| **自定义API** | ✅ 支持 | ✅ 支持 | ✅ 支持 |

## 📊 事件对比

### 事件返回值
```typescript
// 单选模式事件
@confirm="(person: PersonItem | null) => void"
@change="(person: PersonItem | null) => void"

// 多选模式事件  
@confirm="(persons: PersonItem[]) => void"
@change="(persons: PersonItem[]) => void"
```

### 事件处理示例
```typescript
// 统一处理函数
const handlePersonChange = (value: PersonItem | PersonItem[] | null) => {
  if (multiple) {
    // 多选模式
    const persons = value as PersonItem[]
    console.log('选择的人员列表:', persons)
  } else {
    // 单选模式
    const person = value as PersonItem | null
    console.log('选择的人员:', person)
  }
}
```

## ⚠️ 注意事项

### 1. 数据格式一致性
```typescript
// ✅ 正确：单选传入单个对象或null
<DepartmentPersonSelect 
  v-model="singlePerson" 
  :multiple="false" 
/>

// ❌ 错误：单选传入数组
<DepartmentPersonSelect 
  v-model="[singlePerson]" 
  :multiple="false" 
/>
```

### 2. 表单验证
```vue
<el-form-item 
  label="负责人" 
  :rules="[{ required: true, message: '请选择负责人' }]"
>
  <DepartmentPersonSelect
    v-model="form.owner"
    :multiple="false"
  />
</el-form-item>
```

### 3. 性能优化
```vue
<!-- 大数据量时启用虚拟滚动 -->
<DepartmentPersonSelector
  v-model="visible"
  :selected-data="selected"
  :virtual-scroll="true"
  :item-height="40"
/>
```

## 🎯 最佳实践

### 1. 根据场景选择组件
- **复杂选择** → DepartmentPersonSelector
- **表单字段** → DepartmentPersonSelect  
- **表格筛选** → DepartmentPersonFilter

### 2. 数据结构设计
```typescript
// 推荐的表单数据结构
interface FormData {
  // 单选字段
  owner: PersonItem | null
  reviewer: PersonItem | null
  
  // 多选字段
  members: PersonItem[]
  participants: PersonItem[]
}
```

### 3. 事件处理
```typescript
// 统一的事件处理器
const createPersonHandler = (field: string, multiple: boolean) => {
  return (value: PersonItem | PersonItem[] | null) => {
    form[field] = value
    
    // 触发表单验证
    formRef.value?.validateField(field)
    
    // 其他业务逻辑
    handleFormChange()
  }
}
```

## 🎉 总结

通过这三个组件的组合，可以满足不同场景下的部门人员选择需求：

- ✅ **数据结构统一**：单选返回对象，多选返回数组
- ✅ **使用场景全覆盖**：弹窗、表单、搜索
- ✅ **API 一致性**：相同的配置和事件接口
- ✅ **类型安全**：完整的 TypeScript 支持

选择合适的组件，传入正确的数据格式，就能获得最佳的用户体验！
