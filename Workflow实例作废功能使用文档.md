# Workflow实例作废功能使用文档

## 📋 功能概述

本功能提供了作废已通过工作流实例的能力，专门供其他模块调用。只有审批通过后的实例才可以作废成功。

## 🎯 功能特点

- ✅ **状态限制**: 只能作废已通过状态(status=2)的实例
- ✅ **服务层调用**: 仅提供服务层接口，不提供HTTP API
- ✅ **自动通知**: 作废成功后自动发送通知给提交人
- ✅ **历史记录**: 自动记录作废操作历史
- ✅ **事务安全**: 使用数据库事务确保数据一致性
- ✅ **业务解耦**: 不处理额外业务逻辑，由调用方自行处理

## 🚀 使用方法

### 基本调用

```php
use app\workflow\service\WorkflowEngineService;

// 作废已通过的工作流实例
$result = WorkflowEngineService::getInstance()->voidApprovedInstance(
    $instanceId,    // 实例ID（必填）
    '业务需要作废', // 作废原因（可选）
    $operatorId     // 操作人ID（可选，默认为当前用户）
);

if ($result) {
    // 作废成功，可以处理后续业务逻辑
    echo "实例作废成功";
} else {
    // 作废失败，检查日志获取失败原因
    echo "实例作废失败";
}
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `$instanceId` | int | 是 | 工作流实例ID |
| `$reason` | string | 否 | 作废原因，默认为空 |
| `$operatorId` | int | 否 | 操作人ID，默认为当前用户或系统 |

### 返回值

- **true**: 作废成功
- **false**: 作废失败（详细原因查看日志）

## 📊 状态流转

```
已通过(status=2) → 已作废(status=6)
```

**前置条件**: 实例状态必须为已通过(APPROVED = 2)
**结果状态**: 实例状态变为已作废(VOID = 6)

## 🔍 功能验证

### 1. 状态检查
```php
// 检查实例当前状态
$instance = \think\facade\Db::table('workflow_instance')
    ->where('id', $instanceId)
    ->find();

if ($instance['status'] == \app\workflow\constants\WorkflowStatusConstant::APPROVED) {
    // 可以执行作废操作
}
```

### 2. 历史记录验证
```php
// 检查作废操作历史
$history = \think\facade\Db::table('workflow_history')
    ->where('instance_id', $instanceId)
    ->where('node_name', '流程作废')
    ->order('operation_time', 'desc')
    ->find();
```

### 3. 通知消息验证
```php
// 检查作废通知消息
$message = \think\facade\Db::table('notice_message')
    ->where('code', 'workflow_task_void')
    ->where('business_id', $instanceId)
    ->order('created_at', 'desc')
    ->find();
```

## ⚠️ 注意事项

### 1. 状态限制
- 只有已通过状态的实例才能作废
- 其他状态（审批中、已驳回、已终止等）无法作废

### 2. 权限控制
- 建议调用方自行实现权限验证
- 确保操作人有作废该实例的权限

### 3. 业务逻辑
- 本功能只处理工作流实例的状态变更
- 相关业务数据的处理需要调用方自行实现

### 4. 错误处理
- 作废失败时请检查日志获取详细错误信息
- 常见失败原因：实例不存在、状态不符合要求

## 🛠️ 测试验证

使用提供的测试脚本验证功能：

```bash
php test_void_approved_instance.php
```

测试脚本会验证：
- ✅ 实例状态检查
- ✅ 作废操作执行
- ✅ 状态更新验证
- ✅ 历史记录创建
- ✅ 通知消息发送

## 📝 使用示例

### 示例1：CRM模块作废合同
```php
// 在CRM模块中作废合同相关的工作流实例
use app\workflow\service\WorkflowEngineService;

class ContractService 
{
    public function voidContract($contractId, $reason)
    {
        // 1. 获取合同关联的工作流实例ID
        $instanceId = $this->getWorkflowInstanceId($contractId);
        
        // 2. 作废工作流实例
        $result = WorkflowEngineService::getInstance()->voidApprovedInstance(
            $instanceId,
            $reason,
            request()->adminId
        );
        
        if ($result) {
            // 3. 处理合同业务逻辑
            $this->updateContractStatus($contractId, 'void');
            return true;
        }
        
        return false;
    }
}
```

### 示例2：批量作废
```php
// 批量作废多个实例
$instanceIds = [1, 2, 3, 4, 5];
$successCount = 0;

foreach ($instanceIds as $instanceId) {
    $result = WorkflowEngineService::getInstance()->voidApprovedInstance(
        $instanceId,
        '批量作废操作',
        request()->adminId
    );
    
    if ($result) {
        $successCount++;
    }
}

echo "成功作废 {$successCount} 个实例";
```

## 🔧 故障排查

### 常见问题

1. **作废失败 - 实例不存在**
   - 检查实例ID是否正确
   - 确认实例未被删除

2. **作废失败 - 状态不符合**
   - 检查实例当前状态
   - 确认实例为已通过状态(status=2)

3. **通知发送失败**
   - 检查消息中心配置
   - 确认模板存在且启用

### 日志查看
```bash
# 查看工作流相关日志
tail -f runtime/log/$(date +%Y%m)/$(date +%d).log | grep -i workflow
```

## 📞 技术支持

如遇技术问题，请：
1. 查看系统日志获取详细错误信息
2. 使用测试脚本验证功能状态
3. 参考本文档的故障排查部分

---

**版本**: v1.0  
**更新时间**: 2025-07-16  
**适用范围**: 所有需要作废工作流实例的业务模块
