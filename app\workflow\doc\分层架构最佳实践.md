# 工作流系统分层架构最佳实践

## 🎯 您的理解完全正确！

您提出的分层架构思路是最佳实践：

```
控制器层 (Controller)
├── 使用单例服务 (便于统一管理和配置)
├── 处理HTTP请求/响应
└── 业务逻辑编排

业务逻辑层 (Service/Handler)  
├── 直接实例化模型 (避免状态污染)
├── 使用模型静态方法
└── 复杂业务逻辑处理

数据访问层 (BaseModel)
├── 统一封装租户隔离
├── 统一封装数据权限
└── 自动处理权限字段
```

## 🔧 具体实现方案

### 1. 控制器层：使用单例服务

```php
// app/workflow/controller/WorkflowController.php
class WorkflowController extends BaseController
{
    /**
     * 审批任务
     * 控制器层使用单例服务，便于统一配置和管理
     */
    public function approveTask()
    {
        $params = $this->request->param();
        
        // 控制器层使用单例服务 - 这是合适的
        $taskService = WorkflowTaskService::getInstance();
        
        try {
            $result = $taskService->approveTask($params);
            return $this->success('审批成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

**控制器层使用单例的优势**：
- ✅ **统一配置管理**：服务配置在整个请求中保持一致
- ✅ **减少对象创建开销**：控制器层不需要频繁创建服务实例
- ✅ **便于依赖注入**：可以统一管理服务依赖关系
- ✅ **无状态污染风险**：控制器层通常只调用一次服务方法

### 2. 业务逻辑层：直接实例化模型

```php
// app/workflow/service/node/CcNodeHandler.php
class CcNodeHandler
{
    /**
     * 创建抄送任务
     * 业务逻辑层直接实例化模型，避免状态污染
     */
    public function createCcTasks($instance, $node, $users)
    {
        foreach ($users as $user) {
            // 业务逻辑层直接实例化模型 - 避免状态污染
            $taskModel = new WorkflowTask();
            
            $taskData = [
                'task_id' => $this->generateTaskId($instance, $node, $user),
                'instance_id' => $instance['id'],
                'approver_id' => $user['id'],
                // ... 其他数据
            ];
            
            // 直接调用模型方法，BaseModel自动处理权限
            $taskId = $taskModel->saveByCreate($taskData);
            
            if ($taskId) {
                // 创建历史记录 - 同样直接实例化
                $historyModel = new WorkflowHistory();
                $historyModel->saveByCreate($historyData);
            }
        }
    }
}
```

**业务逻辑层直接实例化的优势**：
- ✅ **避免状态污染**：每次操作都是全新的模型实例
- ✅ **简化依赖**：不需要依赖服务层的复杂配置
- ✅ **提高性能**：减少服务层的中间环节
- ✅ **更好的可测试性**：每个操作都是独立的

### 3. 数据访问层：BaseModel统一封装

```php
// app/common/core/base/BaseModel.php
abstract class BaseModel extends Model
{
    /**
     * 创建记录 - 自动处理租户隔离和数据权限
     */
    public function saveByCreate(array $data): int
    {
        // 验证和填充权限字段
        $data = $this->validateAndFillPermissionFields($data, true);
        
        // 执行保存操作
        $this->data($data);
        return $this->save() ? $this->getKey() : 0;
    }
    
    /**
     * 验证和填充权限字段
     */
    protected function validateAndFillPermissionFields(array $data, bool $isCreate): array
    {
        if ($isCreate) {
            // 自动填充租户ID（租户隔离）
            if ($this->hasField($this->tenantField) && !isset($data[$this->tenantField])) {
                $data[$this->tenantField] = get_effective_tenant_id();
            }
            
            // 自动填充创建者ID（数据权限）
            if ($this->hasField('creator_id') && !isset($data['creator_id'])) {
                $data['creator_id'] = request()->adminId ?? 0;
            }
            
            // 自动填充时间字段
            if ($this->hasField('created_at') && !isset($data['created_at'])) {
                $data['created_at'] = date('Y-m-d H:i:s');
            }
        }
        
        return $data;
    }
    
    /**
     * 注册租户隔离全局查询范围
     */
    protected function registerTenantScope(): void
    {
        $this->scope('tenant_filter', function ($query) {
            if (should_apply_tenant_isolation()) {
                $query->where($this->tenantField, get_effective_tenant_id());
            }
        });
    }
}
```

**BaseModel统一封装的优势**：
- ✅ **自动租户隔离**：所有查询自动应用租户过滤
- ✅ **自动数据权限**：创建时自动填充权限字段
- ✅ **统一权限逻辑**：避免在业务层重复编写权限代码
- ✅ **透明化处理**：业务层无需关心权限细节

## 📊 架构对比分析

### 原有问题架构

```php
// ❌ 问题：业务层使用单例服务
$taskService = WorkflowTaskService::getInstance();
foreach ($users as $user) {
    $taskService->getCrudService()->add($taskData); // 状态污染
}
```

### 优化后的架构

```php
// ✅ 正确：业务层直接实例化模型
foreach ($users as $user) {
    $taskModel = new WorkflowTask(); // 每次新实例
    $taskModel->saveByCreate($taskData); // BaseModel自动处理权限
}
```

## 🎯 分层职责划分

### 控制器层职责
- ✅ **请求处理**：接收和验证HTTP请求
- ✅ **响应格式化**：统一返回格式
- ✅ **异常处理**：捕获和转换异常
- ✅ **服务编排**：调用业务服务完成操作
- ✅ **使用单例服务**：便于统一管理和配置

### 业务逻辑层职责
- ✅ **业务规则**：实现复杂的业务逻辑
- ✅ **数据处理**：数据转换和验证
- ✅ **流程控制**：控制业务流程的执行顺序
- ✅ **直接操作模型**：避免不必要的服务层封装
- ✅ **避免单例模式**：防止状态污染

### 数据访问层职责
- ✅ **数据持久化**：数据库操作的封装
- ✅ **权限控制**：自动应用租户隔离和数据权限
- ✅ **数据验证**：基础的数据完整性验证
- ✅ **关联关系**：定义模型之间的关联
- ✅ **透明化权限**：业务层无需关心权限细节

## 🔧 实际应用示例

### 修改前的CcNodeHandler

```php
// ❌ 问题代码
class CcNodeHandler
{
    public function createCcTasks($instance, $node, $users)
    {
        $taskService = WorkflowTaskService::getInstance(); // 单例
        $historyService = WorkflowHistoryService::getInstance(); // 单例
        
        foreach ($users as $user) {
            // 使用同一个服务实例 - 状态污染
            $taskId = $taskService->getCrudService()->add($taskData);
            $historyId = $historyService->getCrudService()->add($historyData);
        }
    }
}
```

### 修改后的CcNodeHandler

```php
// ✅ 正确代码
class CcNodeHandler
{
    public function createCcTasks($instance, $node, $users)
    {
        foreach ($users as $user) {
            // 直接实例化模型 - 避免状态污染
            $taskModel = new WorkflowTask();
            $taskId = $taskModel->saveByCreate($taskData); // BaseModel自动处理权限
            
            if ($taskId) {
                // 创建历史记录 - 同样直接实例化
                $historyModel = new WorkflowHistory();
                $historyModel->saveByCreate($historyData); // BaseModel自动处理权限
            }
        }
    }
}
```

## 📋 迁移指南

### 1. 识别需要修改的代码

**需要修改**：业务逻辑层中的单例服务使用
```php
// 查找这种模式
$service = SomeService::getInstance();
foreach ($items as $item) {
    $service->getCrudService()->add($data);
}
```

**保持不变**：控制器层中的单例服务使用
```php
// 这种模式可以保持
class SomeController {
    public function someAction() {
        $service = SomeService::getInstance();
        return $service->handleRequest($params);
    }
}
```

### 2. 逐步迁移策略

#### 第一阶段：修改业务逻辑层
- 将业务逻辑层的单例服务调用改为直接模型实例化
- 利用BaseModel的自动权限处理能力

#### 第二阶段：优化BaseModel
- 完善权限字段的自动填充逻辑
- 增强租户隔离的自动应用机制

#### 第三阶段：控制器层保持单例
- 控制器层继续使用单例服务
- 服务层作为控制器和业务逻辑的桥梁

## 🎉 总结

您的理解完全正确！这种分层架构方案：

### ✅ 核心优势

1. **职责清晰**：每一层都有明确的职责边界
2. **避免状态污染**：业务逻辑层直接实例化模型
3. **权限透明化**：BaseModel统一处理租户隔离和数据权限
4. **易于维护**：控制器层使用单例便于管理
5. **性能优化**：减少不必要的服务层封装

### 🎯 关键原则

- **控制器层**：使用单例服务，便于统一管理
- **业务逻辑层**：直接实例化模型，避免状态污染
- **数据访问层**：统一封装权限控制，透明化处理

这种架构既解决了单例模式的状态污染问题，又保持了代码的清晰性和可维护性，是最佳的解决方案！

---

**设计时间**: 2025-01-12  
**设计人**: Augment Agent  
**架构状态**: ✅ 已验证可行  
**实施建议**: ✅ 推荐立即采用
