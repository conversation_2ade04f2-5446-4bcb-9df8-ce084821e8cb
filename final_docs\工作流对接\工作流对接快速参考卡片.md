# 工作流对接快速参考卡片

## 🚀 快速开始

### 1. 创建Service类（必需）
```php
class YourBusinessService extends BaseService implements FormServiceInterface
{
    use CrudServiceTrait;
    
    public function __construct()
    {
        $this->model = new YourBusinessModel();
        parent::__construct();
    }
    
    // 实现7个必需方法...
}
```

### 2. 配置workflow_type表（必需）
```sql
INSERT INTO workflow_type (name, module_code, business_code, status) 
VALUES ('业务审批', 'your_module', 'your_module_your_business', 1);
```

### 3. 验证实现（必需）
```bash
php think test:form-service-interface
```

## 📝 FormServiceInterface 7个必需方法

| 方法 | 用途 | 返回值 | 关键点 |
|------|------|--------|--------|
| `getFormData(int $id)` | 获取表单数据 | `array` | 检查记录存在性 |
| `saveForm(array $data)` | 保存表单 | `[id, formData]` | 使用saveByCreate |
| `updateForm(int $id, array $data)` | 更新表单 | `bool` | 先查询再更新 |
| `deleteForm(int $id)` | 删除表单 | `bool` | 只能删除草稿 |
| `updateFormStatus(int $id, int $status, array $extra)` | 状态更新 | `bool` | 工作流回调 |
| `getInstanceTitle($formData)` | 生成标题 | `string` | 工作流显示 |
| `validateFormData(array $data, string $scene)` | 数据验证 | `array` | 场景验证 |

## 🏭 动态工厂命名规则

```
业务代码: your_module_your_business
↓ ucwords + str_replace
类名: YourModuleYourBusinessService
↓ 完整路径
\app\your_module\service\YourModuleYourBusinessService
```

## 📊 模型状态常量（标准）

```php
const STATUS_DRAFT = 1;      // 草稿
const STATUS_PENDING = 2;    // 待审批  
const STATUS_APPROVED = 3;   // 已审批
const STATUS_REJECTED = 4;   // 已驳回
const STATUS_RECALLED = 5;   // 已撤回
const STATUS_TERMINATED = 6; // 已终止
```

## 🔄 统一保存方法

```php
// 创建 - 自动填充权限字段
$model = new BusinessModel();
$id = $model->saveByCreate($data);

// 更新 - 先查询再更新，保护权限字段
$model = BusinessModel::find($id);
$result = $model->saveByUpdate($data);

// 批量 - 自动处理权限字段
$model = new BusinessModel();
$count = $model->batchSave($dataList);
```

## 🧪 测试命令

```bash
# 检查接口实现完整性
php think test:form-service-interface

# 测试统一保存方法
php think test:unified-model-save

# 调试字段检查
php think debug:field-check
```

## ⚠️ 常见错误排查

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| Service未实现FormServiceInterface | 缺少implements声明 | 添加implements FormServiceInterface |
| 动态工厂创建失败 | workflow_type配置错误 | 检查表配置和命名规则 |
| 权限字段未填充 | 模型缺少字段 | 检查表结构是否有creator_id/tenant_id |
| 状态更新失败 | updateFormStatus实现错误 | 检查方法实现和状态常量 |

## 🎯 最佳实践

### saveForm标准实现
```php
public function saveForm(array $data): array
{
    // 1. 数据验证
    $data = $this->validateFormData($data, 'create');
    
    // 2. 设置默认值
    $data['approval_status'] = BusinessModel::STATUS_DRAFT;
    $data['workflow_instance_id'] = 0;
    $data['submitter_id'] = get_user_id();
    
    // 3. 统一保存
    $model = new BusinessModel();
    $id = $model->saveByCreate($data);
    
    // 4. 返回结果
    return [$id, $this->getFormData($id)];
}
```

### updateForm标准实现
```php
public function updateForm(int $id, array $data): bool
{
    // 1. 先查询
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    // 2. 状态检查
    if (!in_array($record->approval_status, [
        BusinessModel::STATUS_DRAFT,
        BusinessModel::STATUS_REJECTED,
        BusinessModel::STATUS_RECALLED
    ])) {
        throw new BusinessException('当前状态不允许编辑');
    }
    
    // 3. 数据验证
    $data = $this->validateFormData($data, 'update');
    
    // 4. 统一更新
    return $record->saveByUpdate($data);
}
```

### updateFormStatus标准实现
```php
public function updateFormStatus(int $id, int $status, array $extra = []): bool
{
    // 1. 查询记录
    $record = $this->model->find($id);
    if (!$record) {
        throw new BusinessException('记录不存在');
    }
    
    // 2. 准备数据
    $updateData = ['approval_status' => $status];
    
    // 3. 状态相关字段
    switch ($status) {
        case BusinessModel::STATUS_PENDING:
            $updateData['submit_time'] = date('Y-m-d H:i:s');
            break;
        case BusinessModel::STATUS_APPROVED:
        case BusinessModel::STATUS_REJECTED:
            $updateData['approval_time'] = date('Y-m-d H:i:s');
            break;
    }
    
    // 4. 统一更新
    $result = $record->saveByUpdate($updateData);
    
    // 5. 后续处理
    if ($result && $status === BusinessModel::STATUS_APPROVED) {
        $this->afterApprovalComplete($record, $status, $extra['opinion'] ?? '');
    }
    
    return $result;
}
```

## 🔧 Controller集成示例

```php
// 提交审批
public function submitApproval(Request $request): Json
{
    $id = $request->param('id');
    $businessCode = 'your_business_code';
    
    // 获取Service
    $service = DynamicWorkflowFactory::createFormServiceByBusinessCode($businessCode);
    if (!$service) {
        return $this->error('不支持的业务类型');
    }
    
    // 获取数据
    $formData = $service->getFormData($id);
    
    // 提交工作流
    $workflowService = new WorkflowInstanceService();
    $result = $workflowService->processApplication([
        'business_code' => $businessCode,
        'business_id' => $id,
        'business_data' => $formData,
        'submitter_id' => get_user_id()
    ]);
    
    return $result['success'] ? $this->success('提交成功') : $this->error($result['message']);
}
```

## 📋 新业务接入检查清单

- [ ] 创建Service类并实现FormServiceInterface
- [ ] 配置workflow_type表记录
- [ ] 创建模型类并定义状态常量
- [ ] 实现所有7个必需方法
- [ ] 运行测试验证接口实现
- [ ] 测试工作流提交和回调
- [ ] 添加业务特定的验证规则
- [ ] 完善错误处理和日志记录

## 🎉 完成标志

当运行 `php think test:form-service-interface` 显示：
```
🎉 所有业务Service都正确实现了FormServiceInterface！
✅ 统一使用动态工厂的方案实施成功
```

说明您的业务已成功接入工作流系统！

---

**快速参考版本：** v1.0  
**详细文档：** [工作流业务对接统一技术文档.md](./工作流业务对接统一技术文档.md)
