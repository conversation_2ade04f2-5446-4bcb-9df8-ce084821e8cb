{
    // =====================================================
    // VSCode MCP 统一配置文件
    // 最后更新：2025-01-31
    // 说明：包含所有MCP服务器的统一配置
    // =====================================================

    // VSCode 基础设置
    "solidity.telemetry": true,
    "editor.fontSize": 18,
    "editor.fontWeight": "normal",
    "editor.wordWrap": "on",
    "remote.extensionKind": {
        "GitHub.copilot": [
            "ui"
        ],
        "github.copilot-chat": [
            "ui"
        ]
    },
    "github.copilot.enable": {
        "*": false
    },
    "workbench.startupEditor": "none",
    "chat.mcp.serverSampling": {},

    // MCP 服务器配置
    "mcpServers": {
        // 文件系统服务器 - ThinkPHP项目文件访问
        "thinkphp-filesystem": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-filesystem",
                "E:\\项目\\self_admin\\base_admin"
            ],
            "env": {
                "ALLOWED_EXTENSIONS": ".php,.vue,.js,.json,.md,.sql,.env,.yaml,.xml,.css,.scss,.ts,.jsx,.tsx,.html,.txt,.log,.ini,.conf,.config",
                "EXCLUDE_PATTERNS": "vendor/*,node_modules/*,runtime/cache/*,runtime/log/*,.git/*,*.tmp,*.temp"
            }
        },

        // MySQL数据库服务器 - 已修复，使用Python 3.12环境
        "mysql-database": {
            "command": "C:\\ProgramData\\anaconda3\\envs\\python312\\python.exe",
            "args": [
                "-m",
                "mcp_server_mysql"
            ],
            "env": {
                "MYSQL_HOST": "*************",
                "MYSQL_PORT": "3306",
                "MYSQL_USER": "www_bs_com",
                "MYSQL_PASSWORD": "PdadjMXmNy8Pn9tj",
                "MYSQL_DATABASE": "www_bs_com"
            }
        },

        // Playwright浏览器自动化服务器
        "playwright": {
            "command": "npx",
            "args": [
                "-y",
                "@playwright/mcp@latest",
                "--config", "playwright-mcp-config.json",
                "--port", "8931"
            ],
            "env": {
                "NODE_ENV": "production"
            }
        },

        // 浏览器工具服务器
        "browser-tools": {
            "command": "npx",
            "args": [
                "-y",
                "@agentdeskai/browser-tools-mcp"
            ],
            "env": {
                "NODE_ENV": "production"
            }
        },

        // HTTP请求服务器
        "fetch": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-fetch"
            ],
            "env": {}
        },

        // Puppeteer浏览器服务器
        "puppeteer": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-puppeteer"
            ],
            "env": {
                "PUPPETEER_HEADLESS": "false",
                "PUPPETEER_TIMEOUT": "30000"
            }
        },

        // 序列思考服务器
        "sequential-thinking": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-sequential-thinking"
            ],
            "env": {}
        },

        // Brave搜索服务器（需要API密钥）
        "brave-search": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-brave-search"
            ],
            "env": {
                "BRAVE_API_KEY": "your_brave_api_key_here"
            }
        }
    }
}

