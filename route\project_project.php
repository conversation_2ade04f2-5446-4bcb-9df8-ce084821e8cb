<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 项目表路由
Route::group('api/project/project', function () {
    // 基础CRUD路由
    Route::get('index', 'app\project\controller\ProjectController@index');
    Route::get('detail/:id', 'app\project\controller\ProjectController@detail');
    Route::post('add', 'app\project\controller\ProjectController@add');
    Route::post('edit/:id', 'app\project\controller\ProjectController@edit');
    Route::post('delete/:id', 'app\project\controller\ProjectController@delete');
    Route::post('batchDelete', 'app\project\controller\ProjectController@batchDelete');
    Route::post('updateField', 'app\project\controller\ProjectController@updateField');
    Route::post('status/:id', 'app\project\controller\ProjectController@status');
	
	

    // 自定义API路由
    Route::get('my', 'app\project\controller\ProjectController@myProjects');
    Route::get('project-detail/:id', 'app\project\controller\ProjectController@projectDetail');
    Route::get('kanban', 'app\project\controller\ProjectController@kanban');
    Route::post('add-member', 'app\project\controller\ProjectController@addMember');
    Route::post('remove-member', 'app\project\controller\ProjectController@removeMember');

    // 统计相关路由
    Route::get('task-status-stats', 'app\project\controller\ProjectController@taskStatusStats');
    Route::get('task-priority-stats', 'app\project\controller\ProjectController@taskPriorityStats');
    Route::get('progress-trend', 'app\project\controller\ProjectController@progressTrend');
    Route::get('member-stats', 'app\project\controller\ProjectController@memberStats');
    Route::get('recent-activities', 'app\project\controller\ProjectController@recentActivities');

    // 项目成员选项接口（用于任务执行人选择）
	Route::get('memberOptions/:id', 'app\project\controller\ProjectController@memberOptions');

	// 检查项目负责人权限
	Route::get('checkOwner/:id', 'app\project\controller\ProjectController@checkProjectOwner');

	// 获取项目负责人选项列表
	Route::get('ownerOptions', 'app\project\controller\ProjectController@ownerOptions');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);