<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 供应商表路由
Route::group('api/ims/ims_supplier', function () {
    Route::get('index', 'app\ims\controller\ImsSupplierController@index');
    Route::get('detail/:id', 'app\ims\controller\ImsSupplierController@detail');
    Route::post('add', 'app\ims\controller\ImsSupplierController@add');
    Route::post('edit/:id', 'app\ims\controller\ImsSupplierController@edit');
    Route::post('delete/:id', 'app\ims\controller\ImsSupplierController@delete');
//    Route::post('batchDelete', 'app\ims\controller\ImsSupplierController@batchDelete');
//    Route::post('updateField', 'app\ims\controller\ImsSupplierController@updateField');
//	Route::post('status/:id', 'app\ims\controller\ImsSupplierController@status');
//    Route::post('import', 'app\ims\controller\ImsSupplierController@import');
//    Route::get('importTemplate', 'app\ims\controller\ImsSupplierController@importTemplate');
//    Route::get('downloadTemplate', 'app\ims\controller\ImsSupplierController@downloadTemplate');
//    Route::get('export', 'app\ims\controller\ImsSupplierController@export');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);