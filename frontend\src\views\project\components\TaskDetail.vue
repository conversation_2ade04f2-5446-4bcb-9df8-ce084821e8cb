<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="1200px"
    :before-close="handleClose"
    top="5vh"
    destroy-on-close
  >
    <div class="task-detail-content">
      <el-skeleton :loading="loading" animated>
        <template #template>
          <el-skeleton-item variant="h1" style="width: 50%" />
          <el-skeleton-item variant="text" style="width: 100%" />
          <el-skeleton-item variant="text" style="width: 80%" />
        </template>
        <template #default>
          <div v-if="taskData" class="task-detail-wrapper">
            <!-- 左右布局容器 -->
            <div class="task-layout-container">
              <!-- 左侧：任务详情 -->
              <div class="task-detail-left">
                <!-- 任务标题和状态 -->
                <div class="task-header">
                  <div class="task-title-section">
                    <h2 class="task-title">{{ taskData.title }}</h2>
                    <div class="task-badges">
                      <el-tag
                        :type="getStatusType(taskData.status)"
                        size="large"
                        class="status-tag"
                      >
                        {{ getStatusText(taskData.status) }}
                      </el-tag>
                      <el-tag
                        :type="getPriorityType(taskData.priority)"
                        size="large"
                        class="priority-tag"
                      >
                        {{ getPriorityText(taskData.priority) }}
                      </el-tag>
                    </div>
                  </div>
                </div>

                <!-- 任务描述 -->
                <div class="task-description" v-if="taskData.description">
                  <h4 class="section-title">任务描述</h4>
                  <div class="description-content" v-html="taskData.description"></div>
                </div>

                <!-- 任务详细信息 -->
                <div class="task-details">
                  <h4 class="section-title">详细信息</h4>
                  <el-descriptions :column="1" border size="default">
                    <el-descriptions-item label="任务状态" label-class-name="detail-label">
                      <el-tag :type="getStatusType(taskData.status)" size="default">
                        {{ getStatusText(taskData.status) }}
                      </el-tag>
                    </el-descriptions-item>

                    <el-descriptions-item label="优先级" label-class-name="detail-label">
                      <el-tag :type="getPriorityType(taskData.priority)" size="default">
                        {{ getPriorityText(taskData.priority) }}
                      </el-tag>
                    </el-descriptions-item>

                    <el-descriptions-item label="负责人" label-class-name="detail-label">
                      <div class="assignee-info" v-if="taskData.assignee_name">
                        <el-avatar :size="24" class="assignee-avatar">
                          {{ taskData.assignee_name?.charAt(0) }}
                        </el-avatar>
                        <span class="assignee-name">{{ taskData.assignee_name }}</span>
                      </div>
                      <span v-else class="no-assignee">未分配</span>
                    </el-descriptions-item>

                    <el-descriptions-item label="项目" label-class-name="detail-label">
                      <span class="project-name">{{ taskData.project_name || '未知项目' }}</span>
                    </el-descriptions-item>

                    <el-descriptions-item label="开始日期" label-class-name="detail-label">
                      <div class="date-info" v-if="taskData.start_date">
                        <el-icon class="date-icon">
                          <Calendar />
                        </el-icon>
                        <span class="date-text">{{ formatDate(taskData.start_date) }}</span>
                      </div>
                      <span v-else class="no-date">未设置</span>
                    </el-descriptions-item>

                    <el-descriptions-item label="截止日期" label-class-name="detail-label">
                      <div class="date-info" v-if="taskData.due_date">
                        <el-icon
                          class="date-icon"
                          :class="{ 'overdue-icon': isOverdue(taskData.due_date) }"
                        >
                          <Calendar />
                        </el-icon>
                        <span
                          class="date-text"
                          :class="{ 'overdue-text': isOverdue(taskData.due_date) }"
                        >
                          {{ formatDate(taskData.due_date) }}
                        </span>
                        <el-tag
                          v-if="isOverdue(taskData.due_date)"
                          type="danger"
                          size="small"
                          class="overdue-tag"
                        >
                          已逾期
                        </el-tag>
                      </div>
                      <span v-else class="no-date">未设置</span>
                    </el-descriptions-item>

                    <el-descriptions-item
                      label="完成时间"
                      label-class-name="detail-label"
                      v-if="taskData.status === 3"
                    >
                      <div class="date-info" v-if="taskData.completed_at">
                        <el-icon class="date-icon completed-icon">
                          <CircleCheck />
                        </el-icon>
                        <span class="date-text completed-text">{{
                          formatDateTime(taskData.completed_at)
                        }}</span>
                      </div>
                      <span v-else class="no-date">未记录</span>
                    </el-descriptions-item>

                    <el-descriptions-item label="创建时间" label-class-name="detail-label">
                      <span class="date-text">{{ formatDateTime(taskData.created_at) }}</span>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>

              <!-- 右侧：评论和跟进 -->
              <div class="task-detail-right">
                <div class="task-records">
                  <!-- Tabs和刷新按钮的容器 -->
                  <div class="tabs-header">
                    <el-tabs v-model="activeRecordTab" class="record-tabs">
                      <el-tab-pane label="评论" name="comment" />
                      <el-tab-pane label="跟进" name="follow" />
                    </el-tabs>
                    <div class="tab-actions">
                      <el-button
                        v-if="activeRecordTab === 'comment'"
                        type="primary"
                        @click="showCommentForm"
                        :icon="ChatDotRound"
                        size="small"
                      >
                        添加评论
                      </el-button>
                      <el-button
                        v-if="activeRecordTab === 'follow'"
                        type="success"
                        @click="showFollowForm"
                        :icon="TrendCharts"
                        size="small"
                      >
                        添加跟进
                      </el-button>
                      <el-button
                        type="default"
                        @click="activeRecordTab === 'comment' ? refreshComments() : refreshFollows()"
                        :icon="Refresh"
                        size="small"
                      >
                        刷新
                      </el-button>
                    </div>
                  </div>

                  <!-- 标签页内容 -->
                  <div class="tab-content">
                    <!-- 评论标签页 -->
                    <div v-show="activeRecordTab === 'comment'" class="comment-section">
                      <!-- 评论列表 -->
                      <div class="records-scroll-area">
                        <CommentList
                          ref="commentListRef"
                          :task-id="taskData.id"
                          @edit="handleEditComment"
                        />
                      </div>
                    </div>

                    <!-- 跟进标签页 -->
                    <div v-show="activeRecordTab === 'follow'" class="follow-section">
                      <!-- 跟进时间线 -->
                      <div class="records-scroll-area">
                        <FollowTimeline
                          ref="followTimelineRef"
                          :task-id="taskData.id"
                          @edit="handleEditFollow"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">关闭</el-button>
        <el-button v-auth="'project:task:edit'" type="primary" @click="handleEdit" size="large">
          <el-icon>
            <Edit />
          </el-icon>
          编辑任务
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 评论弹窗 -->
  <CommentDialog
    v-model="commentDialogVisible"
    :task-id="taskData?.id"
    @success="handleCommentDialogSuccess"
  />

  <!-- 跟进弹窗 -->
  <FollowDialog
    v-model="followDialogVisible"
    :task-id="taskData?.id"
    @success="handleFollowDialogSuccess"
  />
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import {
    Calendar,
    CircleCheck,
    Edit,
    ChatDotRound,
    TrendCharts,
    Refresh
  } from '@element-plus/icons-vue'
  import { TaskApi } from '@/api/project/projectApi'
  import { formatDate, formatDateTime } from '@/utils/date'

  import CommentDialog from './CommentDialog.vue'
  import CommentList from './CommentList.vue'
  import FollowDialog from './FollowDialog.vue'
  import FollowTimeline from './FollowTimeline.vue'

  // Props
  interface Props {
    visible: boolean
    taskId?: number | null
  }

  const props = withDefaults(defineProps<Props>(), {
    taskId: null
  })

  // Emits
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    'task-updated': []
    'edit-task': [task: any]
  }>()

  // 响应式数据
  const loading = ref(false)
  const taskData = ref<any>(null)
  const activeRecordTab = ref('comment')

  const commentDialogVisible = ref(false)
  const followDialogVisible = ref(false)

  // 组件引用
  const commentListRef = ref()
  const followTimelineRef = ref()

  // 计算属性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })

  // 监听弹窗显示状态，只在弹窗打开时加载数据
  watch(
    () => props.visible,
    (visible) => {
      console.log('TaskDetail: visible变化', visible, props.taskId)
      if (visible && props.taskId) {
        // 重置标签页到默认状态
        activeRecordTab.value = 'comment'
        loadTaskDetail(props.taskId)
      } else if (!visible) {
        // 弹窗关闭时清空数据
        taskData.value = null
      }
    }
  )

  // 方法
  const loadTaskDetail = async (taskId: number, retryCount = 0) => {
    try {
      console.log('TaskDetail: 加载任务详情', taskId, retryCount > 0 ? `(重试 ${retryCount})` : '')
      loading.value = true
      const response = await TaskApi.detail(taskId)
      console.log('TaskDetail: 任务详情响应', response)

      // 检查响应数据格式
      if (response && response.data) {
        taskData.value = response.data
      } else {
        throw new Error('响应数据格式错误')
      }
    } catch (error) {
      console.error('TaskDetail: 加载任务详情失败:', error)
      taskData.value = null
    } finally {
      loading.value = false
    }
  }

  const handleClose = () => {
    console.log('TaskDetail: 关闭弹窗')
    dialogVisible.value = false
    // 延迟清空数据，避免关闭动画时显示空白
    setTimeout(() => {
      taskData.value = null
    }, 300)
  }

  const handleEdit = () => {
    if (taskData.value) {
      emit('edit-task', taskData.value)
      handleClose()
    }
  }

  // 评论相关方法
  const showCommentForm = () => {
    commentDialogVisible.value = true
  }



  const handleCommentDialogSuccess = () => {
    // 刷新评论列表
    if (commentListRef.value) {
      commentListRef.value.refresh()
    }
  }

  const handleEditComment = (comment: any) => {
    // TODO: 实现编辑评论功能
    console.log('编辑评论:', comment)
  }

  // 跟进相关方法
  const showFollowForm = () => {
    followDialogVisible.value = true
  }



  const handleFollowDialogSuccess = () => {
    // 刷新跟进列表
    if (followTimelineRef.value) {
      followTimelineRef.value.refresh()
    }
  }

  const handleEditFollow = (follow: any) => {
    // TODO: 实现编辑跟进功能
    console.log('编辑跟进:', follow)
  }

  // 刷新功能
  const refreshComments = () => {
    if (commentListRef.value) {
      commentListRef.value.refresh()
    }
  }

  const refreshFollows = () => {
    if (followTimelineRef.value) {
      followTimelineRef.value.refresh()
    }
  }

  // 状态相关方法
  const getStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'info', // 待办
      2: 'primary', // 进行中
      3: 'success', // 已完成
      4: 'danger' // 已关闭
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: number): string => {
    const textMap: Record<number, string> = {
      1: '待办',
      2: '进行中',
      3: '已完成',
      4: '已关闭'
    }
    return textMap[status] || '未知状态'
  }

  // 优先级相关方法
  const getPriorityType = (
    priority: number
  ): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'info', // 低
      2: 'primary', // 中
      3: 'warning' // 高
    }
    return typeMap[priority] || 'primary'
  }

  const getPriorityText = (priority: number): string => {
    const textMap: Record<number, string> = {
      1: '低优先级',
      2: '中优先级',
      3: '高优先级'
    }
    return textMap[priority] || '中优先级'
  }

  // 日期相关方法
  const isOverdue = (dueDate: string): boolean => {
    if (!dueDate) return false
    return new Date(dueDate) < new Date() && taskData.value?.status !== 3
  }
</script>

<style scoped lang="scss">
  .task-detail-content {
    min-height: 400px;
    max-height: 75vh;
    overflow: hidden;

    .task-detail-wrapper {
      height: 100%;

      .task-layout-container {
        display: flex;
        gap: 24px;
        height: 100%;
        align-items: stretch; // 确保左右两侧高度一致

        .task-detail-left {
          flex: 1;
          min-width: 0;
          overflow-y: auto;
          padding-right: 12px;

          .task-header {
            margin-bottom: 10px;
            //padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;

            // 黑暗模式适配
            html.dark & {
              border-bottom-color: var(--art-border-color);
            }

            .task-title-section {
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 16px;
              height: 40px;

              .task-title {
                flex: 1;
                font-size: 18px;
                font-weight: 600;
                color: #1f2329;
                margin: 0;
                line-height: 1.4;

                // 黑暗模式适配
                html.dark & {
                  color: var(--art-text-gray-900);
                }
              }

              .task-badges {
                display: flex;
                gap: 8px;
                flex-shrink: 0;

                .status-tag,
                .priority-tag {
                  font-weight: 500;
                }
              }
            }
          }
        }

        .task-detail-right {
          flex: 1;
          min-width: 0;
          border-left: 1px solid #f0f0f0;
          padding-left: 24px;
          display: flex;
          flex-direction: column;
          height: 100%; // 确保右侧占满高度

          // 黑暗模式适配
          html.dark & {
            border-left-color: var(--art-border-color);
          }

          // 评论和跟进区域样式
          .task-records {
            height: 100%;
            display: flex;
            flex-direction: column;

            .tabs-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-bottom: 1px solid #e4e7ed;
              margin-bottom: 16px;
              flex-shrink: 0;

              // 黑暗模式适配
              html.dark & {
                border-bottom-color: var(--art-border-color);
              }

              .record-tabs {
                flex: 1;

                :deep(.el-tabs__header) {
                  margin: 0;
                }

                :deep(.el-tabs__nav-wrap) {
                  &::after {
                    display: none; // 隐藏默认的底部边框
                  }
                }
              }

              .tab-actions {
                display: flex;
                gap: 8px;
                align-items: center;

                // 确保所有按钮高度一致
                .el-button {
                  height: 32px !important;
                  min-height: 32px !important;
                  max-height: 32px !important;
                  line-height: 1 !important;
                  padding: 0 12px !important;

                  // 确保按钮内容垂直居中
                  display: inline-flex !important;
                  align-items: center !important;
                  justify-content: center !important;
                  box-sizing: border-box !important;
                }
              }
            }

            .tab-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;
            }
          }
        }

      .task-description {
        margin-bottom: 24px;

        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #1f2329;
          margin: 0 0 12px 0;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }

        .description-content {
          background: #f7f8fa;
          padding: 14px;
          border-radius: 6px;
          border: 1px solid #e5e6eb;
          color: #4e5969;
          line-height: 1.6;
          font-size: 13px;

          // 黑暗模式适配
          html.dark & {
            background: var(--art-color);
            border-color: var(--art-border-color);
            color: var(--art-text-gray-700);
          }
        }
      }

      .task-details {
        .section-title {
          font-size: 15px;
          font-weight: 600;
          color: #1f2329;
          margin: 0 0 16px 0;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }

        :deep(.el-descriptions) {
          .el-descriptions__label {
            font-weight: 600;
            color: #86909c;
            font-size: 13px;
            width: 100px;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }

          .el-descriptions__content {
            font-size: 13px;
            color: #1f2329;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }
        }

        .assignee-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .assignee-avatar {
            background: #1664ff;
            color: white;
            font-weight: 500;
          }

          .assignee-name {
            font-weight: 500;
            color: #1f2329;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }
        }

        .no-assignee,
        .no-date {
          color: #86909c;
          font-style: italic;

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);
          }
        }

        .project-name {
          font-weight: 500;
          color: #1664ff;

          // 黑暗模式适配
          html.dark & {
            color: rgb(var(--art-primary));
          }
        }

        .date-info {
          display: flex;
          align-items: center;
          gap: 6px;

          .date-icon {
            font-size: 16px;
            color: #86909c;

            &.overdue-icon {
              color: #f56c6c;
            }

            &.completed-icon {
              color: #67c23a;
            }

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);

              &.overdue-icon {
                color: rgb(var(--art-danger));
              }

              &.completed-icon {
                color: rgb(var(--art-success));
              }
            }
          }

          .date-text {
            font-weight: 500;

            &.overdue-text {
              color: #f56c6c;

              // 黑暗模式适配
              html.dark & {
                color: rgb(var(--art-danger));
              }
            }

            &.completed-text {
              color: #67c23a;

              // 黑暗模式适配
              html.dark & {
                color: rgb(var(--art-success));
              }
            }
          }

          .overdue-tag {
            margin-left: 8px;
          }
        }

          .comment-section,
          .follow-section {
            height: 100%;
            display: flex;
            flex-direction: column;

            .form-container {
              margin-bottom: 16px;
              padding: 16px;
              background-color: #f8f9fa;
              border-radius: 6px;
              border: 1px solid #e5e6eb;
              flex-shrink: 0;

              // 黑暗模式适配
              html.dark & {
                background-color: var(--art-color);
                border-color: var(--art-border-color);
              }
            }

            .records-scroll-area {
              flex: 1;
              overflow-y: auto;
              min-height: 0;
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }


</style>
