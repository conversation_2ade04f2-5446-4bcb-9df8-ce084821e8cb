-- 任务评论和跟进功能权限菜单
-- 执行前请确保已有项目任务管理菜单

-- 查找项目任务管理菜单ID
SET @task_menu_id = (SELECT id FROM system_menu WHERE name = 'project:task:index' LIMIT 1);

-- 如果找不到任务管理菜单，请先创建或检查菜单名称
-- SELECT id, title, name FROM system_menu WHERE title LIKE '%任务%' OR name LIKE '%task%';

-- 插入任务评论和跟进功能权限
INSERT INTO `system_menu` (`parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) VALUES

-- 评论功能权限
(@task_menu_id, '添加评论', 'project:task:comment:add', '', '', 2, '', 50, 0, 0, 1, 1, '在任务中添加评论', NOW(), NOW()),
(@task_menu_id, '编辑评论', 'project:task:comment:edit', '', '', 2, '', 51, 0, 0, 1, 1, '编辑任务评论（自己的或管理员权限）', NOW(), NOW()),
(@task_menu_id, '删除评论', 'project:task:comment:delete', '', '', 2, '', 52, 0, 0, 1, 1, '删除任务评论（自己的或管理员权限）', NOW(), NOW()),

-- 跟进功能权限
(@task_menu_id, '添加跟进', 'project:task:follow:add', '', '', 2, '', 53, 0, 0, 1, 1, '在任务中添加跟进记录', NOW(), NOW()),
(@task_menu_id, '编辑跟进', 'project:task:follow:edit', '', '', 2, '', 54, 0, 0, 1, 1, '编辑任务跟进（自己的或管理员权限）', NOW(), NOW()),
(@task_menu_id, '删除跟进', 'project:task:follow:delete', '', '', 2, '', 55, 0, 0, 1, 1, '删除任务跟进（自己的或管理员权限）', NOW(), NOW());

-- 验证插入结果
SELECT 
    id,
    parent_id,
    title,
    name,
    type,
    sort,
    status,
    remark
FROM system_menu 
WHERE parent_id = @task_menu_id 
  AND name LIKE '%comment%' OR name LIKE '%follow%'
ORDER BY sort;

-- 如果需要删除这些权限，可以使用以下SQL：
/*
DELETE FROM system_menu 
WHERE parent_id = @task_menu_id 
  AND (name LIKE '%comment%' OR name LIKE '%follow%');
*/
