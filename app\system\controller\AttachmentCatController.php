<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\system\service\AttachmentCatService;
use think\response\Json;

class AttachmentCatController extends BaseAdminController
{
	/**
	 * @var AttachmentCatService
	 */
	private AttachmentCatService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = AttachmentCatService::getInstance();
		$this->service->getCrudService()
		              ->setEnableDataPermission(false);
	}
	
	/**
	 * 获取文件列表
	 */
	public function index(): Json
	{
		return $this->success('获取成功', $this->service->getList(input()));
	}
	
	/**
	 * 创建
	 */
	public function add(): Json
	{
		return $this->service->create(input(), $this->adminId, $this->tenantId)
			? $this->success('创建成功')
			: $this->error('创建失败');
	}
	
	/**
	 * 更新
	 */
	public function edit($id): J<PERSON>
	{
		return $this->service->update((int)$id, input())
			? $this->success('操作成功')
			: $this->error('操作失败');
	}
	
	/**
	 * 删除
	 */
	public function delete(): Json
	{
		return $this->service->delete(input('id/d'))
			? $this->success('删除成功')
			: $this->error('删除失败');
	}
	
	/**
	 * 用于下拉选择
	 */
	public function options(): Json
	{
		return $this->success('获取成功', $this->service->getCrudService()
		                                                ->getSelectOptions());
	}
	
	
}