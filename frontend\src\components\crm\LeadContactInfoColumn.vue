<template>
  <div class="lead-contact-info">
    <!-- 第一行：姓名 + 职位 -->
    <div class="primary-line">
      <ElIcon class="name-icon"><User /></ElIcon>
      <span class="name">{{ data.lead_name || '-' }}</span>
      <span class="position" v-if="data.position">
        <ElIcon class="position-icon"><Briefcase /></ElIcon>
        {{ data.position }}
      </span>
    </div>

    <!-- 第二行：手机号 (主要联系方式) -->
    <div class="contact-line primary" v-if="data.mobile">
      <ElIcon class="contact-icon">
        <Phone />
      </ElIcon>
      <span class="contact-value">{{ data.mobile }}</span>
      <!--      <ElButton type="primary" link size="small" @click="callPhone(data.mobile)" class="action-btn">
              拨打
            </ElButton>-->
    </div>

    <!-- 第三行：邮箱 -->
    <div class="contact-line secondary" v-if="data.email">
      <ElIcon class="contact-icon">
        <Message />
      </ElIcon>
      <ElLink :href="`mailto:${data.email}`" type="primary" class="email-link" :title="data.email">
        {{ formatEmail(data.email) }}
      </ElLink>
    </div>

    <!-- 第四行：电话 (次要) -->
    <div class="contact-line tertiary" v-if="data.phone && data.phone !== data.mobile">
      <ElIcon class="contact-icon">
        <Phone />
      </ElIcon>
      <span class="contact-value secondary">{{ data.phone }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElIcon, ElLink, ElButton, ElMessage } from 'element-plus'
  import { Phone, Message, User, Briefcase } from '@element-plus/icons-vue'

  interface ContactData {
    lead_name?: string
    position?: string
    mobile?: string
    phone?: string
    email?: string
  }

  interface Props {
    data: ContactData
  }

  const props = defineProps<Props>()

  // 格式化邮箱显示
  const formatEmail = (email: string): string => {
    if (!email) return '-'
    return email.length > 20 ? email.substring(0, 20) + '...' : email
  }

  // 拨打电话
  const callPhone = (phone: string) => {
    if (!phone) return

    // 这里可以集成电话系统或复制到剪贴板
    navigator.clipboard
      ?.writeText(phone)
      .then(() => {
        ElMessage.success(`电话号码已复制: ${phone}`)
      })
      .catch(() => {
        ElMessage.info(`电话号码: ${phone}`)
      })
  }
</script>

<style scoped lang="scss">
  .lead-contact-info {
    padding: 8px 0;
    font-size: 15px;

    .primary-line {
      font-weight: 600;
      color: #303133;
      margin-bottom: 6px;
      line-height: 1.4;
      display: flex;
      align-items: center;

      .name-icon {
        margin-right: 4px;
        font-size: 14px;
        color: #409eff;
        flex-shrink: 0;
      }

      .name {
        font-size: 15px;
        margin-right: 8px;
      }

      .position {
        font-size: 13px;
        color: #909399;
        font-weight: 400;
        display: flex;
        align-items: center;

        .position-icon {
          margin-right: 2px;
          font-size: 11px;
          color: #c0c4cc;
        }
      }
    }

    .contact-line {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 15px;
      line-height: 1.3;

      .contact-icon {
        margin-right: 6px;
        font-size: 12px;
        flex-shrink: 0;
      }

      .contact-value {
        flex: 1;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 15px;
      }

      .action-btn {
        margin-left: 8px;
        padding: 0 6px;
        height: 20px;
        font-size: 11px;
      }

      &.primary {
        color: #409eff;
        font-weight: 500;

        .contact-value {
          color: #303133;
        }
      }

      &.secondary {
        color: #606266;
      }

      &.tertiary {
        color: #909399;
      }
    }

    .email-link {
      font-size: 12px;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .lead-contact-info {
      .primary-line {
        .name {
          font-size: 13px;
        }

        .position {
          font-size: 11px;
        }
      }

      .contact-line {
        font-size: 11px;

        .action-btn {
          padding: 0 4px;
          height: 18px;
          font-size: 10px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .lead-contact-info {
      padding: 6px 0;

      .primary-line {
        margin-bottom: 4px;

        .name {
          font-size: 12px;
        }

        .position {
          font-size: 10px;
        }
      }

      .contact-line {
        margin-bottom: 3px;
        font-size: 10px;

        .contact-icon {
          font-size: 10px;
        }

        .action-btn {
          display: none;
        }
      }

      .email-link {
        font-size: 10px;
      }
    }
  }
</style>
