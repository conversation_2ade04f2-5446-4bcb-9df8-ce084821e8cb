# CRM工作报告模块快速开始指南

## 🚀 快速开始

### 第一步：数据库字段注释优化

1. **更新字段注释**，为CRUD生成器添加标记：

```sql
-- 更新crm_work_report表字段注释
ALTER TABLE `crm_work_report` 
MODIFY COLUMN `title` varchar(200) NOT NULL DEFAULT '' COMMENT '报告标题 @required @max:200 @search:like @exp @imp',
MODIFY COLUMN `type` varchar(20) NOT NULL DEFAULT '' COMMENT '报告类型:daily=日报,weekly=周报,monthly=月报 @required @search:eq @exp @imp @component:tag',
MODIFY COLUMN `report_date` date DEFAULT NULL COMMENT '报告日期 @required @search:date @exp @imp',
MODIFY COLUMN `content` text COMMENT '报告内容 @required @form:textarea @exp @imp',
MODIFY COLUMN `summary` text COMMENT '工作总结 @form:textarea @exp @imp',
MODIFY COLUMN `plan` text COMMENT '下期计划 @form:textarea @exp @imp',
MODIFY COLUMN `attachments` text COMMENT '附件(JSON格式) @form:upload @component:file',
MODIFY COLUMN `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp';

-- 更新表注释
ALTER TABLE `crm_work_report` COMMENT='工作报告表 @module:crm @exp:true @imp:true';
```

### 第二步：使用CRUD生成器

2. **执行生成器命令**：

```bash
# 进入项目根目录
cd /path/to/your/project

# 执行CRUD生成器命令
php think generator:crud crm_work_report --module=crm --frontend --overwrite
```

3. **验证生成结果**：

生成器将创建以下文件：

```
# 后端文件
app/crm/
├── controller/CrmWorkReportController.php    # 控制器
├── model/CrmWorkReport.php                   # 模型
├── service/CrmWorkReportService.php          # 服务类
└── route/crm.php                            # 路由（更新）

# 前端文件
frontend/src/
├── api/crm/crmWorkReportApi.ts              # API接口
└── views/crm/crm_work_report/
    ├── list.vue                             # 列表页面
    ├── form-dialog.vue                      # 表单对话框
    └── import-export-dialog.vue             # 导入导出
```

### 第三步：测试基础功能

4. **启动开发服务器**：

```bash
# 后端（如果需要）
php think run

# 前端
cd frontend
npm run dev
```

5. **访问页面测试**：
- 访问：`http://localhost:3000/crm/work-report`
- 测试基础CRUD功能
- 验证搜索、分页、导入导出功能

## 🎨 UI定制开发

### 第四步：列表页面优化（飞书风格）

6. **修改列表页面** (`frontend/src/views/crm/crm_work_report/list.vue`)：

```vue
<template>
  <div class="crm-work-report-page">
    <!-- 保留生成的搜索栏 -->
    <ArtSearchBar v-model:filter="searchForm" :items="searchItems" @search="handleSearch" />
    
    <!-- 保留生成的操作栏 -->
    <ArtTableHeader>
      <template #left>
        <el-button type="primary" @click="handleAdd">写汇报</el-button>
        <el-button @click="handleExport">导出</el-button>
      </template>
    </ArtTableHeader>
    
    <!-- 替换表格为卡片式布局 -->
    <div class="report-card-list">
      <div v-for="item in tableData" :key="item.id" class="report-card">
        <div class="card-header">
          <span class="report-date">📅 {{ item.report_date }}</span>
          <el-tag :type="getTypeColor(item.type)">{{ getTypeText(item.type) }}</el-tag>
          <span class="creator">👤 {{ item.creator_name }}</span>
        </div>
        <div class="card-title">📝 {{ item.title }}</div>
        <div class="card-content">💬 {{ item.content.substring(0, 50) }}...</div>
        <div class="card-footer">
          <span class="attachment-count">📎 {{ getAttachmentCount(item.attachments) }}个附件</span>
          <span class="create-time">⏰ {{ formatTime(item.created_at) }}</span>
          <div class="actions">
            <el-button type="primary" link @click="handleDetail(item)">👁️ 查看详情</el-button>
            <el-button type="primary" link @click="handleEdit(item)">✏️ 编辑</el-button>
            <el-button type="primary" link @click="handleCopy(item.id)">📋 复制</el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 保留生成的分页和对话框 -->
    <el-pagination />
    <FormDialog ref="formDialogRef" @success="handleSuccess" />
  </div>
</template>

<script setup>
// 保留生成的基础逻辑，添加定制方法
const getTypeColor = (type) => {
  const colors = { daily: 'primary', weekly: 'success', monthly: 'warning' }
  return colors[type] || 'info'
}

const getTypeText = (type) => {
  const texts = { daily: '日报', weekly: '周报', monthly: '月报' }
  return texts[type] || type
}

const handleCopy = async (id) => {
  try {
    await CrmWorkReportApi.copy(id)
    ElMessage.success('复制成功')
    await fetchData()
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.report-card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.report-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.report-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2329;
}

.card-content {
  color: #646a73;
  margin-bottom: 12px;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #8f959e;
}

.actions {
  display: flex;
  gap: 8px;
}
</style>
```

### 第五步：表单页面优化

7. **集成富文本编辑器** (`frontend/src/views/crm/crm_work_report/form-dialog.vue`)：

```bash
# 安装富文本编辑器
npm install @vueup/vue-quill
```

```vue
<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="1200px">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <div class="section-title">📋 基本信息</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="汇报标题" prop="title">
              <el-input v-model="formData.title" placeholder="请输入汇报标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="汇报类型" prop="type">
              <el-select v-model="formData.type" placeholder="请选择汇报类型">
                <el-option label="日报" value="daily" />
                <el-option label="周报" value="weekly" />
                <el-option label="月报" value="monthly" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="汇报日期" prop="report_date">
              <el-date-picker v-model="formData.report_date" type="date" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- 内容区域 -->
      <div class="form-section">
        <div class="section-title">📝 汇报内容</div>
        <el-form-item label="工作内容" prop="content">
          <QuillEditor v-model:content="formData.content" content-type="html" />
        </el-form-item>
        <el-form-item label="工作总结">
          <QuillEditor v-model:content="formData.summary" content-type="html" />
        </el-form-item>
        <el-form-item label="下期计划">
          <QuillEditor v-model:content="formData.plan" content-type="html" />
        </el-form-item>
      </div>
      
      <!-- 附件区域 -->
      <div class="form-section">
        <div class="section-title">📎 附件上传</div>
        <el-form-item label="附件">
          <el-upload drag multiple :auto-upload="false">
            <el-icon><upload-filled /></el-icon>
            <div>点击上传或拖拽文件到此处</div>
          </el-upload>
        </el-form-item>
      </div>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button @click="handleSaveDraft">💾 保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit">📤 提交汇报</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

// 保留生成的基础逻辑，添加富文本编辑器
const handleSaveDraft = () => {
  // 草稿保存逻辑
}
</script>

<style scoped>
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1f2329;
}
</style>
```

## ✅ 验收检查

### 第六步：功能验证

8. **检查清单**：

- [ ] 基础CRUD功能正常（生成器保证）
- [ ] 搜索筛选功能正常（生成器保证）
- [ ] 导入导出功能正常（生成器保证）
- [ ] 列表页面采用卡片式布局
- [ ] 表单页面集成富文本编辑器
- [ ] 附件上传下载功能正常
- [ ] 复制汇报功能正常
- [ ] UI风格符合飞书设计

### 第七步：后端功能扩展

9. **添加复制功能** (`app/crm/service/CrmWorkReportService.php`)：

```php
<?php
namespace app\crm\service;

class CrmWorkReportService extends CrudService
{
    /**
     * 复制汇报
     */
    public function copy($id)
    {
        $original = $this->detail($id);
        $data = $original->toArray();
        
        // 移除主键和时间戳
        unset($data['id'], $data['created_at'], $data['updated_at']);
        
        // 修改标题和日期
        $data['title'] = '复制-' . $data['title'];
        $data['report_date'] = date('Y-m-d');
        
        return $this->create($data);
    }
}
```

10. **添加复制接口** (`app/crm/controller/CrmWorkReportController.php`)：

```php
/**
 * 复制汇报
 */
public function copy($id)
{
    try {
        $result = $this->service->copy($id);
        return $this->success('复制成功', $result);
    } catch (\Exception $e) {
        return $this->error('复制失败：' . $e->getMessage());
    }
}
```

## 🎉 完成

恭喜！您已经成功使用CRUD生成器快速创建了CRM工作报告模块，并完成了飞书风格的UI定制。

### 开发时间对比
- **传统开发**: 5个工作日
- **使用生成器**: 2.5个工作日
- **效率提升**: 50%

### 下一步
- 根据用户反馈继续优化UI和功能
- 添加更多定制功能（模板、统计等）
- 与其他CRM模块进行集成
