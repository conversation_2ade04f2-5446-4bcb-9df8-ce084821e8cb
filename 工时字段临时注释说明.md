# 工时字段临时注释说明

## 📋 修改概述

由于当前工时字段（预估工时、实际工时）存在估算不准确的问题，暂时注释掉前端相关的工时显示功能，后续需要完善工时估算准确性和管理机制。

## 🔧 已修改的文件

### 1. 项目卡片组件 - ProjectCard.vue
**文件路径：** `frontend/src/views/project/components/ProjectCard.vue`

**修改内容：**
- 注释掉项目统计区域中的工时显示
- 保留任务数和成员数统计

```vue
<!-- TODO: 工时显示暂时注释，后续需要完善工时估算准确性和管理机制 -->
<!-- 
<div class="stat-item">
  <el-icon>
    <Clock />
  </el-icon>
  <span class="stat-label">工时</span>
  <span class="stat-value">{{ project.total_hours || 0 }}h</span>
</div>
-->
```

### 2. 任务看板组件 - TaskKanban.vue
**文件路径：** `frontend/src/views/project/components/TaskKanban.vue`

**修改内容：**
- 注释掉任务卡片中的工时进度显示
- 保留任务的其他信息显示

```vue
<!-- TODO: 任务工时进度暂时注释，后续需要完善工时估算准确性和管理机制 -->
<!-- 
<div class="task-progress" v-if="task.estimated_hours">
  <div class="progress-info">
    <span class="progress-label">工时</span>
    <span class="progress-value">
      {{ task.actual_hours || 0 }}h / {{ task.estimated_hours }}h
    </span>
  </div>
  <el-progress
    :percentage="getProgressPercentage(task)"
    :stroke-width="4"
    :show-text="false"
    :color="getProgressColor(task)"
  />
</div>
-->
```

### 3. 任务表单组件 - TaskForm.vue
**文件路径：** `frontend/src/views/project/components/TaskForm.vue`

**修改内容：**
- 注释掉预估工时输入字段
- 从表单数据和重置方法中移除estimated_hours字段

```vue
<!-- TODO: 预估工时字段暂时注释，后续需要完善工时估算准确性和管理机制 -->
<!-- 
<el-form-item label="预估工时" prop="estimated_hours">
  <el-input-number
    v-model="formData.estimated_hours"
    placeholder="请输入预估工时"
    style="width: 100%"
    :min="0"
    :precision="1"
    controls-position="right"
  />
</el-form-item>
-->
```

### 4. 任务列表页面 - project_task/list.vue
**文件路径：** `frontend/src/views/project/project_task/list.vue`

**修改内容：**
- 注释掉详情面板中的工时字段显示

```vue
<!-- TODO: 工时字段暂时注释，后续需要完善工时估算准确性和管理机制 -->
<!-- 
<ElDescriptionsItem label="预估工时(小时)">
  {{ detailData.estimated_hours || '-' }}
</ElDescriptionsItem>

<ElDescriptionsItem label="实际工时(小时)">
  {{ detailData.actual_hours || '-' }}
</ElDescriptionsItem>
-->
```

### 5. 任务表单对话框 - project_task/form-dialog.vue
**文件路径：** `frontend/src/views/project/project_task/form-dialog.vue`

**修改内容：**
- 注释掉工时输入字段

```vue
<!-- TODO: 工时字段暂时注释，后续需要完善工时估算准确性和管理机制 -->
<!-- 
<ElFormItem label="预估工时(小时)" prop="estimated_hours">
  <ElInputNumber v-model="formData.estimated_hours" placeholder="请输入预估工时(小时)" style="width: 100%" controls-position="right" />
</ElFormItem>
<ElFormItem label="实际工时(小时)" prop="actual_hours">
  <ElInputNumber v-model="formData.actual_hours" placeholder="请输入实际工时(小时)" style="width: 100%" controls-position="right" />
</ElFormItem>
-->
```

## ⚠️ 重要说明

### 不受影响的功能
1. **项目进度计算** - 仍然基于任务完成状态，不依赖工时
2. **任务状态管理** - 任务的创建、编辑、状态变更正常
3. **项目统计** - 任务数、成员数等其他统计正常

### 暂时隐藏的功能
1. **工时统计显示** - 项目卡片中的工时统计
2. **任务工时进度** - 任务看板中的工时完成度
3. **工时输入** - 新建/编辑任务时的工时字段

### 数据库字段状态
- **保留** - 所有工时相关的数据库字段仍然保留
- **后端API** - 工时相关的后端接口仍然可用
- **数据完整性** - 现有的工时数据不会丢失

## 🔮 后续处理计划

### 短期目标
1. 建立工时估算规范和指导
2. 添加工时偏差预警机制
3. 完善工时审核流程

### 中期目标
1. 引入工时管理培训
2. 建立历史数据分析
3. 优化工时估算准确性

### 长期目标
1. 考虑引入故事点或复杂度评估
2. 建立基于历史数据的自动估算
3. 完善项目成本核算体系

## 📝 恢复工时功能的步骤

当工时管理机制完善后，可以通过以下步骤恢复功能：

1. **取消注释** - 移除所有TODO注释，恢复工时相关代码
2. **数据验证** - 验证现有工时数据的准确性
3. **用户培训** - 对团队进行工时估算培训
4. **逐步启用** - 先在小范围项目中测试，再全面推广

## 🏷️ 标记说明

所有注释都使用统一的TODO标记：
```
<!-- TODO: 工时字段暂时注释，后续需要完善工时估算准确性和管理机制 -->
```

这样便于后续搜索和批量处理。
