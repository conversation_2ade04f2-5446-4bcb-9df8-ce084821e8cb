import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 项目成员表相关接口
 */
export class ProjectMemberApi {
  /**
   * 获取项目成员表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_member/index',
      params
    })
  }

  /**
   * 获取项目成员表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/project/project_member/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加项目成员表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_member/add',
      data
    })
  }

  /**
   * 更新项目成员表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/project_member/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除项目成员表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_member/delete/${id}`
    })
  }

  /**
   * 批量删除项目成员表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/project/project_member/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_member/updateField',
      data
    })
  }

  

  


  /**
   * 导入项目成员表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/project/project_member/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/project/project_member/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/project/project_member/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }

  /**
   * 获取可添加的用户列表
   * 权限控制：只有项目负责人或租户管理员才可以添加成员
   * @param projectId 项目ID
   */
  static availableUsers(projectId: number | string) {
    return request.get<BaseResult>({
      url: `/project/project_member/availableUsers/${projectId}`
    })
  }
}