# 组件开发规范

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 规范目标

### 核心目标
1. **统一标准**：确保所有表单组件遵循统一的开发标准
2. **提升质量**：通过规范化开发提升代码质量和可维护性
3. **降低成本**：减少学习成本和维护成本
4. **保证兼容**：确保组件间的良好兼容性和互操作性

## 📁 目录结构规范

### 组件目录结构

```
src/components/
├── business-forms/                    # 申请表单组件
│   ├── crm_contract.vue              # 合同申请表单
│   ├── crm_contract_receivable.vue   # 回款申请表单
│   ├── daily_price_order.vue         # 每日报价申请表单
│   ├── hr_leave.vue                  # 请假申请表单
│   ├── hr_travel.vue                 # 出差申请表单
│   ├── form-create-renderer.vue      # Form-Create渲染器
│   ├── dynamic-form.vue              # 动态表单渲染器
│   └── generic-form.vue              # 通用表单（兜底）
├── business-detail/                   # 详情展示组件
│   ├── crm_contract.vue              # 合同详情展示
│   ├── crm_contract_receivable.vue   # 回款详情展示
│   ├── daily_price_order.vue         # 每日报价详情展示
│   ├── hr_leave.vue                  # 请假详情展示
│   ├── hr_travel.vue                 # 出差详情展示
│   ├── form-create-viewer.vue        # Form-Create详情查看器
│   ├── dynamic-detail.vue            # 动态详情查看器
│   └── generic-detail.vue            # 通用详情（兜底）
├── form-engine/                       # 表单引擎
│   ├── component-mapper.ts            # 组件映射器
│   ├── config-loader.ts               # 配置加载器
│   ├── cache-manager.ts               # 缓存管理器
│   └── validation-engine.ts           # 验证引擎
└── shared/                            # 共享组件
    ├── form-skeleton.vue              # 表单骨架屏
    ├── form-error.vue                 # 表单错误组件
    └── form-loading.vue               # 表单加载组件
```

### 文件命名规范

| 组件类型 | 命名规则 | 示例 |
|---------|----------|------|
| 申请表单 | `${businessCode}.vue` | `crm_contract.vue` |
| 详情表单 | `${businessCode}.vue` | `crm_contract.vue` |
| 渲染器组件 | `${type}-renderer.vue` | `form-create-renderer.vue` |
| 查看器组件 | `${type}-viewer.vue` | `form-create-viewer.vue` |
| 通用组件 | `generic-${type}.vue` | `generic-form.vue` |
| 工具类 | `${function}-${type}.ts` | `component-mapper.ts` |

## 🔧 组件接口规范

### 申请表单组件接口

```typescript
// 申请表单统一接口
interface BusinessFormComponent {
  // Props
  data?: any                    // 表单数据（编辑时传入）
  mode?: 'create' | 'edit'     // 表单模式
  definitionId?: number        // 工作流定义ID
  formId?: number              // 表单ID（编辑时）
  
  // Events
  onSubmit: (data: any) => void    // 提交申请
  onSave: (data: any) => void      // 保存草稿
  onCancel: () => void             // 取消操作
  onChange: (data: any) => void    // 数据变化
  
  // Methods (通过defineExpose暴露)
  showForm: (id?: number) => void     // 显示表单
  resetForm: () => void               // 重置表单
  validate: () => Promise<boolean>    // 表单验证
  getFormData: () => any              // 获取表单数据
  setFormData: (data: any) => void    // 设置表单数据
}
```

### 详情组件接口

```typescript
// 详情组件统一接口
interface DetailComponent {
  // Props
  data: any                    // 详情数据
  businessCode?: string        // 业务代码
  loading?: boolean           // 加载状态
  
  // Events
  onRefresh?: () => void      // 刷新数据
  onExport?: () => void       // 导出数据
  
  // Methods (通过defineExpose暴露)
  refresh: () => void         // 刷新数据
  export: () => void          // 导出数据
}
```

## 📝 组件开发模板

### 申请表单组件模板

```vue
<!-- business-forms/template.vue -->
<template>
  <div class="business-form-container">
    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="business-form"
    >
      <!-- 表单项 -->
      <el-form-item label="字段名称" prop="fieldName">
        <el-input v-model="formData.fieldName" placeholder="请输入..." />
      </el-form-item>
      
      <!-- 更多表单项... -->
    </el-form>
    
    <!-- 表单操作按钮 -->
    <div class="form-actions" v-if="showActions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSave" :loading="saving">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交申请
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件名称（必须）
defineOptions({
  name: 'BusinessFormTemplate'
})

// Props接口
interface Props {
  data?: any
  mode?: 'create' | 'edit'
  definitionId?: number
  formId?: number
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  showActions: true,
  data: () => ({})
})

// Events接口
const emit = defineEmits<{
  submit: [data: any]
  save: [data: any]
  cancel: []
  change: [data: any]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)

// 表单数据
const formData = reactive({
  fieldName: '',
  // 其他字段...
})

// 表单验证规则
const formRules: FormRules = {
  fieldName: [
    { required: true, message: '请输入字段名称', trigger: 'blur' }
  ]
  // 其他验证规则...
}

// 表单提交
const handleSubmit = async () => {
  if (submitting.value) return
  
  try {
    submitting.value = true
    
    const valid = await formRef.value?.validate()
    if (valid) {
      emit('submit', { ...formData })
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 保存草稿
const handleSave = async () => {
  if (saving.value) return
  
  try {
    saving.value = true
    emit('save', { ...formData })
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 显示表单
const showForm = async (id?: number) => {
  if (id && props.mode === 'edit') {
    try {
      loading.value = true
      // 加载表单数据的逻辑
      const res = await BusinessApi.detail(id)
      if (res.code === 1) {
        Object.assign(formData, res.data)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
    } finally {
      loading.value = false
    }
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, getDefaultFormData())
}

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    return await formRef.value?.validate() || false
  } catch (error) {
    return false
  }
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 设置表单数据
const setFormData = (data: any) => {
  Object.assign(formData, data)
}

// 获取默认表单数据
const getDefaultFormData = () => {
  return {
    fieldName: ''
    // 其他默认值...
  }
}

// 监听数据变化
watch(formData, (newData) => {
  emit('change', newData)
}, { deep: true })

// 初始化
onMounted(() => {
  if (props.data) {
    setFormData(props.data)
  }
})

// 暴露方法
defineExpose({
  showForm,
  resetForm,
  validate,
  getFormData,
  setFormData
})
</script>

<style scoped>
.business-form-container {
  padding: 20px;
}

.business-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 20px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
```

### 详情组件模板

```vue
<!-- business-detail/template.vue -->
<template>
  <div class="business-detail-container" v-loading="loading">
    <!-- 详情内容 -->
    <el-descriptions :column="2" border class="business-detail">
      <el-descriptions-item label="字段名称">
        {{ data.fieldName || '-' }}
      </el-descriptions-item>
      
      <!-- 更多详情项... -->
    </el-descriptions>
    
    <!-- 操作按钮 -->
    <div class="detail-actions" v-if="showActions">
      <el-button @click="handleRefresh" icon="Refresh">刷新</el-button>
      <el-button @click="handleExport" icon="Download">导出</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件名称（必须）
defineOptions({
  name: 'BusinessDetailTemplate'
})

// Props接口
interface Props {
  data: any
  businessCode?: string
  loading?: boolean
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: false,
  data: () => ({})
})

// Events接口
const emit = defineEmits<{
  refresh: []
  export: []
}>()

// 刷新数据
const handleRefresh = () => {
  emit('refresh')
}

// 导出数据
const handleExport = () => {
  emit('export')
}

// 格式化值
const formatValue = (key: string, value: any): string => {
  if (value === null || value === undefined) return '-'
  
  // 金额格式化
  if (key.includes('amount') && typeof value === 'number') {
    return `¥${value.toLocaleString()}`
  }
  
  // 日期格式化
  if (key.includes('date') || key.includes('time')) {
    return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
  }
  
  // 布尔值格式化
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }
  
  return String(value)
}

// 暴露方法
defineExpose({
  refresh: handleRefresh,
  export: handleExport
})
</script>

<style scoped>
.business-detail-container {
  padding: 20px;
}

.business-detail {
  margin-bottom: 20px;
}

.detail-actions {
  text-align: center;
  border-top: 1px solid #e4e7ed;
  padding-top: 20px;
}

.detail-actions .el-button {
  margin: 0 10px;
}
</style>
```

## 🎨 样式规范

### CSS类命名规范

```scss
// 组件样式命名规范
.business-form-container {          // 容器类：组件名-container
  .business-form {                  // 主体类：组件名
    .form-header {                  // 子元素类：功能名
      .header-title {               // 孙元素类：父元素-子功能
        &.is-required {             // 状态类：is-状态名
          color: #f56c6c;
        }
      }
    }
    
    .form-content {
      .form-section {               // 区域类：功能-section
        &--primary {                // 修饰符：--修饰名
          background-color: #f0f9ff;
        }
      }
    }
    
    .form-actions {
      .action-button {
        &.is-loading {
          pointer-events: none;
        }
      }
    }
  }
}
```

### 样式变量规范

```scss
// 表单组件样式变量
:root {
  // 间距变量
  --form-padding: 20px;
  --form-margin: 16px;
  --form-item-margin: 16px;
  
  // 尺寸变量
  --form-label-width: 120px;
  --form-max-width: 800px;
  --form-min-height: 400px;
  
  // 颜色变量
  --form-border-color: #e4e7ed;
  --form-bg-color: #ffffff;
  --form-header-bg: #f5f7fa;
  --form-required-color: #f56c6c;
  
  // 字体变量
  --form-font-size: 14px;
  --form-title-size: 16px;
  --form-label-size: 14px;
}
```

## 🔍 代码质量规范

### TypeScript规范

```typescript
// 类型定义规范
interface FormData {
  id?: number                    // 可选字段使用?
  name: string                   // 必填字段
  amount: number                 // 明确类型
  status: FormStatus             // 使用枚举类型
  createdAt?: Date              // 日期类型
  tags?: string[]               // 数组类型
  metadata?: Record<string, any> // 对象类型
}

// 枚举定义规范
enum FormStatus {
  DRAFT = 0,
  PENDING = 1,
  APPROVED = 2,
  REJECTED = 3
}

// 函数类型规范
type FormValidator = (data: FormData) => Promise<boolean>
type FormSubmitHandler = (data: FormData) => Promise<void>

// 组件Props类型规范
interface ComponentProps {
  // 基础属性
  id?: string | number
  className?: string
  style?: CSSProperties
  
  // 业务属性
  data?: FormData
  mode?: 'create' | 'edit' | 'view'
  loading?: boolean
  disabled?: boolean
  
  // 回调函数
  onSubmit?: FormSubmitHandler
  onChange?: (data: FormData) => void
  onValidate?: FormValidator
}
```

### 错误处理规范

```typescript
// 统一错误处理
class FormError extends Error {
  constructor(
    message: string,
    public code: string,
    public field?: string
  ) {
    super(message)
    this.name = 'FormError'
  }
}

// 错误处理函数
const handleFormError = (error: unknown, context: string) => {
  console.error(`${context} 错误:`, error)
  
  if (error instanceof FormError) {
    ElMessage.error(error.message)
    return error
  }
  
  if (error instanceof Error) {
    ElMessage.error(error.message || '操作失败')
    return new FormError(error.message, 'UNKNOWN_ERROR')
  }
  
  ElMessage.error('未知错误')
  return new FormError('未知错误', 'UNKNOWN_ERROR')
}

// 异步操作错误处理
const safeAsyncOperation = async <T>(
  operation: () => Promise<T>,
  context: string
): Promise<T | null> => {
  try {
    return await operation()
  } catch (error) {
    handleFormError(error, context)
    return null
  }
}
```

## 📋 验证规范

### 表单验证规范

```typescript
// 验证规则定义
interface ValidationRule {
  required?: boolean
  type?: 'string' | 'number' | 'email' | 'url' | 'date'
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
  trigger?: 'blur' | 'change'
}

// 常用验证规则
const commonRules = {
  required: (message: string): ValidationRule => ({
    required: true,
    message,
    trigger: 'blur'
  }),
  
  email: (): ValidationRule => ({
    type: 'email',
    message: '请输入正确的邮箱地址',
    trigger: 'blur'
  }),
  
  phone: (): ValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码',
    trigger: 'blur'
  }),
  
  amount: (min = 0): ValidationRule => ({
    type: 'number',
    min,
    message: `金额不能小于${min}`,
    trigger: 'blur'
  })
}

// 表单验证示例
const formRules: FormRules = {
  name: [commonRules.required('请输入名称')],
  email: [commonRules.email()],
  phone: [commonRules.phone()],
  amount: [commonRules.amount(0.01)]
}
```

## 🧪 测试规范

### 组件测试模板

```typescript
// 组件测试示例
import { mount } from '@vue/test-utils'
import { describe, it, expect, beforeEach } from 'vitest'
import BusinessFormTemplate from '../business-forms/template.vue'

describe('BusinessFormTemplate', () => {
  let wrapper: any
  
  beforeEach(() => {
    wrapper = mount(BusinessFormTemplate, {
      props: {
        mode: 'create'
      }
    })
  })
  
  it('应该正确渲染表单', () => {
    expect(wrapper.find('.business-form-container').exists()).toBe(true)
    expect(wrapper.find('.business-form').exists()).toBe(true)
  })
  
  it('应该正确处理表单提交', async () => {
    const submitSpy = vi.fn()
    wrapper = mount(BusinessFormTemplate, {
      props: {
        mode: 'create',
        onSubmit: submitSpy
      }
    })
    
    // 填写表单
    await wrapper.find('input[name="fieldName"]').setValue('测试值')
    
    // 提交表单
    await wrapper.find('.form-actions .el-button--primary').trigger('click')
    
    expect(submitSpy).toHaveBeenCalledWith({
      fieldName: '测试值'
    })
  })
  
  it('应该正确处理表单验证', async () => {
    // 不填写必填字段直接提交
    await wrapper.find('.form-actions .el-button--primary').trigger('click')
    
    // 应该显示验证错误
    expect(wrapper.find('.el-form-item__error').exists()).toBe(true)
  })
  
  it('应该正确暴露方法', () => {
    const exposed = wrapper.vm
    
    expect(typeof exposed.showForm).toBe('function')
    expect(typeof exposed.resetForm).toBe('function')
    expect(typeof exposed.validate).toBe('function')
    expect(typeof exposed.getFormData).toBe('function')
    expect(typeof exposed.setFormData).toBe('function')
  })
})
```

## 📚 文档规范

### 组件文档模板

```markdown
# 组件名称

## 概述
简要描述组件的功能和用途。

## 使用示例

### 基础用法
\`\`\`vue
<template>
  <BusinessFormTemplate
    :data="formData"
    mode="create"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>
\`\`\`

### 高级用法
\`\`\`vue
<template>
  <BusinessFormTemplate
    ref="formRef"
    :data="formData"
    mode="edit"
    :definition-id="workflowId"
    @submit="handleSubmit"
    @save="handleSave"
    @cancel="handleCancel"
  />
</template>
\`\`\`

## API

### Props
| 参数 | 说明 | 类型 | 默认值 | 必填 |
|------|------|------|--------|------|
| data | 表单数据 | object | {} | 否 |
| mode | 表单模式 | 'create' \| 'edit' | 'create' | 否 |
| definitionId | 工作流定义ID | number | - | 否 |

### Events
| 事件名 | 说明 | 参数 |
|--------|------|------|
| submit | 提交表单 | (data: FormData) |
| save | 保存草稿 | (data: FormData) |
| cancel | 取消操作 | - |

### Methods
| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| showForm | 显示表单 | (id?: number) | void |
| resetForm | 重置表单 | - | void |
| validate | 验证表单 | - | Promise<boolean> |

## 注意事项
- 列出使用时需要注意的事项
- 已知问题和限制
- 最佳实践建议
```

## 🔧 开发工具配置

### ESLint配置

```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    'plugin:vue/vue3-recommended'
  ],
  rules: {
    // Vue组件规则
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/prop-name-casing': ['error', 'camelCase'],
    'vue/event-name-casing': ['error', 'camelCase'],
    
    // TypeScript规则
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn'
  }
}
```

### Prettier配置

```javascript
// .prettierrc.js
module.exports = {
  semi: false,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'none',
  printWidth: 100,
  bracketSpacing: true,
  arrowParens: 'avoid',
  vueIndentScriptAndStyle: true
}
```

---

**注意：** 所有组件开发都必须严格遵循本规范，确保代码质量和项目的可维护性。