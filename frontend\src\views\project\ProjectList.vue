<template>
  <div class="project-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon>
            <FolderOpened />
          </el-icon>
          项目管理
        </h1>
        <span class="page-subtitle">管理和跟踪您的项目进度</span>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAddProject">
          <el-icon>
            <Plus />
          </el-icon>
          新建项目
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.name"
          placeholder="搜索项目名称..."
          clearable
          @change="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <div class="filter-section">
        <el-select
          v-model="searchForm.status"
          placeholder="项目状态"
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已暂停" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>

        <el-select
          v-model="searchForm.owner_id"
          placeholder="项目负责人"
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option v-for="user in userList" :key="user.id" :label="user.name" :value="user.id" />
        </el-select>

        <div class="view-toggle">
          <el-radio-group v-model="viewMode" @change="handleViewModeChange">
            <el-radio-button value="card">
              <el-icon>
                <Grid />
              </el-icon>
              卡片
            </el-radio-button>
            <el-radio-button value="list">
              <el-icon>
                <List />
              </el-icon>
              列表
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 项目分组显示 -->
    <div class="project-sections">
      <!-- 我的项目 -->
      <div class="project-section">
        <div class="section-header" @click="myProjectsCollapsed = !myProjectsCollapsed">
          <h2 class="section-title">
            <el-icon>
              <User />
            </el-icon>
            我的项目
            <span class="count">({{ myProjects.length }})</span>
            <!-- 权限指令位置标记 -->
            <span class="permission-note" v-auth="'project:my:view'">
              <el-tooltip content="需要'我的项目查看'权限" placement="top">
                <el-icon class="permission-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </span>
          </h2>
          <el-tooltip
            :content="myProjectsCollapsed ? '展开我的项目' : '收起我的项目'"
            placement="top"
          >
            <el-button
              circle
              class="collapse-btn"
              @click.stop="myProjectsCollapsed = !myProjectsCollapsed"
            >
              <el-icon>
                <component :is="myProjectsCollapsed ? 'ArrowDown' : 'ArrowUp'" />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>

        <el-collapse-transition>
          <div v-show="!myProjectsCollapsed">
            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="project-cards">
              <div v-if="myProjectsLoading" class="loading-container">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else-if="myProjects.length === 0" class="no-data">
                <el-empty description="暂无我的项目数据" />
              </div>
              <template v-else>
                <ProjectCard
                  v-for="project in myProjects"
                  :key="project.id"
                  :project="project"
                  @view-detail="handleViewDetail"
                  @edit="handleEdit"
                  @delete="handleDelete"
                />
              </template>
            </div>

            <!-- 列表视图 -->
            <div v-if="viewMode === 'list'" class="project-table">
              <div v-if="myProjects.length === 0 && !myProjectsLoading" class="no-data">
                <el-empty description="暂无我的项目数据" />
              </div>
              <ProjectTable
                v-else
                :projects="myProjects"
                :loading="myProjectsLoading"
                @view-detail="handleViewDetail"
                @edit="handleEdit"
                @delete="handleDelete"
              />
            </div>

            <!-- 我的项目分页 -->
            <div v-if="myProjects.length > 0" class="pagination-wrapper">
              <el-pagination
                v-model:current-page="myProjectsPagination.page"
                v-model:page-size="myProjectsPagination.limit"
                :total="myProjectsPagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleMyProjectsSizeChange"
                @current-change="handleMyProjectsCurrentChange"
              />
            </div>
          </div>
        </el-collapse-transition>
      </div>

      <!-- 全部项目 -->
      <div class="project-section">
        <div class="section-header" @click="allProjectsCollapsed = !allProjectsCollapsed">
          <h2 class="section-title">
            <el-icon>
              <FolderOpened />
            </el-icon>
            全部项目
            <span class="count">({{ allProjects.length }})</span>
            <!-- 权限指令位置标记 -->
            <span class="permission-note" v-auth="'project:all:view'">
              <el-tooltip
                content="需要'全部项目查看'权限 - 显示您有权限查看的所有项目"
                placement="top"
              >
                <el-icon class="permission-icon"><InfoFilled /></el-icon>
              </el-tooltip>
            </span>
          </h2>
          <el-tooltip
            :content="allProjectsCollapsed ? '展开全部项目' : '收起全部项目'"
            placement="top"
          >
            <el-button
              circle
              class="collapse-btn"
              @click.stop="allProjectsCollapsed = !allProjectsCollapsed"
            >
              <el-icon>
                <component :is="allProjectsCollapsed ? 'ArrowDown' : 'ArrowUp'" />
              </el-icon>
            </el-button>
          </el-tooltip>
        </div>

        <el-collapse-transition>
          <div v-show="!allProjectsCollapsed">
            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="project-cards">
              <div v-if="allProjectsLoading" class="loading-container">
                <el-skeleton :rows="3" animated />
              </div>
              <div v-else-if="allProjects.length === 0" class="no-data">
                <el-empty description="暂无项目数据" />
              </div>
              <template v-else>
                <ProjectCard
                  v-for="project in allProjects"
                  :key="project.id"
                  :project="project"
                  @view-detail="handleViewDetail"
                  @edit="handleEdit"
                  @delete="handleDelete"
                />
              </template>
            </div>

            <!-- 列表视图 -->
            <div v-if="viewMode === 'list'" class="project-table">
              <div v-if="allProjects.length === 0 && !allProjectsLoading" class="no-data">
                <el-empty description="暂无项目数据" />
              </div>
              <ProjectTable
                v-else
                :projects="allProjects"
                :loading="allProjectsLoading"
                @view-detail="handleViewDetail"
                @edit="handleEdit"
                @delete="handleDelete"
              />
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新建/编辑项目弹窗 -->
    <ProjectForm
      v-model:visible="formVisible"
      :project-data="currentProject"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { FolderOpened, Plus, Search, Grid, List, User, InfoFilled } from '@element-plus/icons-vue'
  import { ProjectApi } from '@/api/project/projectApi'
  import { isAllEmpty } from '@pureadmin/utils'
  import ProjectCard from './components/ProjectCard.vue'
  import ProjectTable from './components/ProjectTable.vue'
  import ProjectForm from './components/ProjectForm.vue'
  import { ApiStatus } from '@/utils/http/status'

  // 路由
  const router = useRouter()

  // 响应式数据
  const viewMode = ref('card') // 视图模式：card | list
  const myProjectsCollapsed = ref(false) // 我的项目折叠状态
  const allProjectsCollapsed = ref(false) // 全部项目折叠状态
  const myProjects = ref<any[]>([])
  const allProjects = ref<any[]>([])
  const userList = ref<Array<{ id: number; name: string }>>([])
  const formVisible = ref(false)
  const currentProject = ref(null)
  const myProjectsLoading = ref(false) // 我的项目加载状态
  const allProjectsLoading = ref(false) // 全部项目加载状态

  // 搜索表单
  const searchForm = reactive({
    name: '',
    status: '',
    owner_id: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 10,
    total: 0
  })

  // 我的项目分页数据
  const myProjectsPagination = reactive({
    page: 1,
    limit: 10,
    total: 0
  })

  // 方法
  const handleSearch = () => {
    loadMyProjects()
    loadAllProjects()
  }

  const handleViewModeChange = () => {
    // 视图模式切换逻辑
  }

  const handleAddProject = () => {
    currentProject.value = null
    formVisible.value = true
  }

  const handleViewDetail = (project: any) => {
    // 在当前页面内跳转到项目详情页，会自动在标签栏添加标签
    router.push(`/project/detail/${project.id}`)
  }

  const handleEdit = (project: any) => {
    currentProject.value = project
    formVisible.value = true
  }

  const handleDelete = async (project: any) => {
    try {
      await ElMessageBox.confirm(`确定要删除项目"${project.name}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ProjectApi.delete(project.id)
      ElMessage.success('删除成功')
      await loadMyProjects()
      await loadAllProjects()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleFormSuccess = () => {
    formVisible.value = false
    loadMyProjects()
    loadAllProjects()
  }

  const handleSizeChange = (size: number) => {
    pagination.limit = size
    loadAllProjects()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    loadAllProjects()
  }

  // 我的项目分页处理
  const handleMyProjectsSizeChange = (size: number) => {
    myProjectsPagination.limit = size
    myProjectsPagination.page = 1
    loadMyProjects()
  }

  const handleMyProjectsCurrentChange = (page: number) => {
    myProjectsPagination.page = page
    loadMyProjects()
  }

  // 加载我的项目
  const loadMyProjects = async () => {
    try {
      myProjectsLoading.value = true
      console.log('开始加载我的项目，loading状态:', myProjectsLoading.value)

      const params: any = {
        page: myProjectsPagination.page,
        limit: myProjectsPagination.limit
      }

      if (!isAllEmpty(searchForm.name)) params.name = searchForm.name
      if (!isAllEmpty(searchForm.status)) params.status = searchForm.status
      if (!isAllEmpty(searchForm.owner_id)) params.owner_id = searchForm.owner_id

      // 使用Promise.all确保最小loading时间和API请求都完成
      const [response] = await Promise.all([
        ProjectApi.myProjects(params),
        new Promise((resolve) => setTimeout(resolve, 300)) // 最小loading时间300ms
      ])

      console.log('我的项目API响应:', response)

      if (response.code === ApiStatus.success) {
        // 处理后端返回的数据格式：可能是数组或者包含list的对象
        if (Array.isArray(response.data)) {
          myProjects.value = response.data
          myProjectsPagination.total = response.data.length
        } else {
          myProjects.value = response.data.list || []
          myProjectsPagination.total = response.data.total || 0
        }
        console.log('我的项目数据:', myProjects.value)
      }
    } catch (error) {
      console.error('加载我的项目失败:', error)
      ElMessage.error('加载我的项目失败')
    } finally {
      myProjectsLoading.value = false
      console.log('我的项目加载完成，loading状态:', myProjectsLoading.value)
    }
  }

  // 加载全部项目
  const loadAllProjects = async () => {
    try {
      allProjectsLoading.value = true
      console.log('开始加载全部项目，loading状态:', allProjectsLoading.value)

      const params: any = {
        page: pagination.page,
        limit: pagination.limit
      }

      if (!isAllEmpty(searchForm.name)) params.name = searchForm.name
      if (!isAllEmpty(searchForm.status)) params.status = searchForm.status
      if (!isAllEmpty(searchForm.owner_id)) params.owner_id = searchForm.owner_id

      // 使用Promise.all确保最小loading时间和API请求都完成
      const [response] = await Promise.all([
        ProjectApi.list(params),
        new Promise((resolve) => setTimeout(resolve, 300)) // 最小loading时间300ms
      ])

      console.log('全部项目API响应:', response)

      if (response.code === ApiStatus.success) {
        allProjects.value = response.data.list || []
        pagination.total = response.data.total || 0
      }
    } catch (error) {
      console.error('加载项目列表失败:', error)
      ElMessage.error('加载项目列表失败')
    } finally {
      allProjectsLoading.value = false
      console.log('全部项目加载完成，loading状态:', allProjectsLoading.value)
    }
  }

  // 加载用户列表
  const loadUserList = async () => {
    try {
      console.log('ProjectList: 加载项目负责人选项')
      const response = await ProjectApi.getOwnerOptions()
      userList.value = response.data || []
      console.log('ProjectList: 项目负责人选项加载完成', userList.value.length)
    } catch (error) {
      console.error('加载项目负责人选项失败:', error)
      ElMessage.error('加载项目负责人选项失败')
      userList.value = []
    }
  }

  // 初始化
  onMounted(() => {
    loadMyProjects()
    loadAllProjects()
    loadUserList()
  })
</script>

<style scoped lang="scss">
  .project-list-container {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;

    // 黑暗模式适配
    html.dark & {
      background-color: var(--art-bg-color);
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2329;
        margin: 0 0 4px 0;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-900);
        }
      }

      .page-subtitle {
        color: #86909c;
        font-size: 14px;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-600);
        }
      }
    }
  }

  .search-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }

    .search-section {
      .search-input {
        width: 300px;
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .filter-select {
        width: 150px;
      }

      .view-toggle {
        margin-left: 16px;
      }
    }
  }

  .project-sections {
    .project-section {
      margin-bottom: 32px;
      background-color: #ffffff;
      border-radius: 12px;
      padding: 16px;

      // 黑暗模式适配
      html.dark & {
        background-color: var(--art-main-bg-color);
        border: 1px solid var(--art-root-card-border-color);
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s;
        border: 1px solid transparent;

        &:hover {
          background-color: #f0f2f5;
          border-color: #eaeaea;

          // 黑暗模式适配
          html.dark & {
            background-color: var(--art-hoverColor);
            border-color: var(--art-border-color);
          }
        }

        .section-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 18px;
          font-weight: 600;
          color: #1f2329;
          margin: 0;

          .count {
            color: #86909c;
            font-weight: normal;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }

          .permission-note {
            margin-left: 8px;

            .permission-icon {
              font-size: 14px;
              color: #f59e0b;
              cursor: help;

              // 黑暗模式适配
              html.dark & {
                color: rgb(var(--art-warning));
              }
            }
          }

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-900);
          }
        }

        .collapse-btn {
          color: #86909c;
          background-color: transparent;
          border: none;
          transition: all 0.2s;
          width: 28px;
          height: 28px !important;

          &:hover {
            color: #409eff;
            background-color: #f0f9ff;
            transform: scale(1.1);
          }

          &:focus {
            color: #409eff;
            background-color: #f0f9ff;
          }

          // 黑暗模式适配
          html.dark & {
            color: var(--art-text-gray-600);

            &:hover,
            &:focus {
              color: rgb(var(--art-primary));
              background-color: rgba(var(--art-primary), 0.1);
            }
          }
        }
      }

      .project-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 16px;

        .add-project-card {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 200px;
          background: white;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #1664ff;
            color: #1664ff;
          }

          // 黑暗模式适配
          html.dark & {
            background: var(--art-main-bg-color);
            border-color: var(--art-border-dashed-color);

            &:hover {
              border-color: rgb(var(--art-primary));
              color: rgb(var(--art-primary));
            }
          }
        }

        .el-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }

  .loading-container {
    padding: 20px;
    text-align: center;
  }

  .no-data {
    text-align: center;
    padding: 40px 20px;
    color: #86909c;

    // 黑暗模式适配
    html.dark & {
      color: var(--art-text-gray-600);
    }
  }
</style>
