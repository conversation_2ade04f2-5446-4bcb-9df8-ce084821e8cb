<?php

use app\common\middleware\PermissionMiddleware;
use app\common\middleware\TokenAuthMiddleware;
use think\facade\Route;

// 客户共享操作日志表路由
/*Route::group('api/crm/crm_customer_share_log', function () {
    Route::get('index', 'app\crm\controller\CrmCustomerShareLogController@index');
    Route::get('detail/:id', 'app\crm\controller\CrmCustomerShareLogController@detail');
    Route::post('add', 'app\crm\controller\CrmCustomerShareLogController@add');
    Route::post('edit/:id', 'app\crm\controller\CrmCustomerShareLogController@edit');
    Route::post('delete/:id', 'app\crm\controller\CrmCustomerShareLogController@delete');
    Route::post('batchDelete', 'app\crm\controller\CrmCustomerShareLogController@batchDelete');
    Route::post('updateField', 'app\crm\controller\CrmCustomerShareLogController@updateField');
    Route::post('status/:id', 'app\crm\controller\CrmCustomerShareLogController@status');
    Route::post('import', 'app\crm\controller\CrmCustomerShareLogController@import');
    Route::get('importTemplate', 'app\crm\controller\CrmCustomerShareLogController@importTemplate');
    Route::get('downloadTemplate', 'app\crm\controller\CrmCustomerShareLogController@downloadTemplate');
    Route::get('export', 'app\crm\controller\CrmCustomerShareLogController@export');
})->middleware([
    TokenAuthMiddleware::class,
    PermissionMiddleware::class
]);*/