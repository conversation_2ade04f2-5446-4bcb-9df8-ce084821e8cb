# Workflow消息中心快速参考指南

## 🚀 快速开始

### 1. 发送消息的标准代码
```php
use app\notice\service\NoticeDispatcherService;
use app\workflow\constants\WorkflowStatusConstant;

// 准备变量（使用英文键名）
$variables = [
    'title' => '张三的请假申请',
    'task_name' => '部门经理审批',
    'submitter_name' => '张三',
    'created_at' => date('Y-m-d H:i:s')
];

// 发送消息
$result = NoticeDispatcherService::getInstance()->send(
    'workflow',           // 模块名
    'task_approval',      // 消息类型
    $variables,           // 变量数据
    [1, 2, 3]            // 接收人ID数组
);
```

## 📋 消息类型速查表

| 消息类型 | 模板编码 | 常量 | 用途 |
|---------|---------|------|------|
| 审批通知 | workflow_task_approval | MESSAGE_TASK_APPROVAL | 发送给审批人 |
| 审批结果 | workflow_task_approved | MESSAGE_TASK_APPROVED | 发送给提交人 |
| 抄送通知 | workflow_task_cc | MESSAGE_TASK_CC | 发送给抄送人 |
| 催办通知 | workflow_task_urge | MESSAGE_TASK_URGE | 发送给审批人 |
| 转交通知 | workflow_task_transfer | MESSAGE_TASK_TRANSFER | 发送给接收人 |
| 终止通知 | workflow_task_terminated | MESSAGE_TASK_TERMINATED | 发送给提交人 |
| 作废通知 | workflow_task_void | MESSAGE_TASK_VOID | 发送给提交人 |

## 🔧 常用变量速查表

### 基础变量
```php
$variables = [
    'title'          => '流程标题',      // 必填
    'submitter_name' => '提交人姓名',    // 必填
    'created_at'     => '提交时间',      // 必填
    'detail_url'     => '详情链接'       // 可选
];
```

### 审批相关
```php
$variables = [
    'task_name'      => '任务名称',      // 必填
    'approver_name'  => '审批人姓名',    // 必填
    'result'         => '审批结果',      // 必填：通过/拒绝
    'completed_at'   => '审批时间',      // 必填
    'opinion'        => '审批意见'       // 可选
];
```

### 操作相关
```php
$variables = [
    'urger_name'     => '催办人姓名',    // 催办通知
    'from_user'      => '转交人姓名',    // 转交通知
    'to_user'        => '接收人姓名',    // 转交通知
    'transfer_time'  => '转交时间',      // 转交通知
    'reason'         => '操作原因'       // 可选
];
```

### 状态变更
```php
$variables = [
    'terminate_by'   => '终止人姓名',    // 终止通知
    'terminate_time' => '终止时间',      // 终止通知
    'void_by'        => '作废人姓名',    // 作废通知
    'void_time'      => '作废时间',      // 作废通知
    'submit_time'    => '提交时间',      // 状态通知
    'result'         => '操作结果'       // 已终止/已作废
];
```

## 📝 实际使用示例

### 1. 发送审批通知
```php
// 在 ApprovalNodeHandler.php 中
$variables = [
    'task_name'      => $node['nodeName'] ?? '审批任务',
    'title'          => $instance['title'] ?? '未命名流程',
    'submitter_name' => $instance['submitter_name'] ?? '系统',
    'created_at'     => $instance['created_at'] ?? date('Y-m-d H:i:s'),
    'detail_url'     => '/workflow/task/detail?instance_id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_APPROVAL,
    $variables,
    [$approverId]
);
```

### 2. 发送审批结果通知
```php
// 在 WorkflowTaskService.php 中
$variables = [
    'title'         => $instance['title'],
    'result'        => $isApproved ? '通过' : '拒绝',
    'opinion'       => $task['opinion'] ?? '',
    'approver_name' => $currentUserName,
    'completed_at'  => $task['handle_time'] ?? date('Y-m-d H:i:s')
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_APPROVED,
    $variables,
    [$submitterId]
);
```

### 3. 发送作废通知（新增）
```php
// 在 WorkflowEngine.php 中
$variables = [
    'title'       => $instance['title'],
    'result'      => '已作废',
    'submit_time' => $instance['created_at'],
    'void_time'   => date('Y-m-d H:i:s'),
    'void_by'     => $operatorName,
    'reason'      => $reason,
    'detail_url'  => '/workflow/detail?id=' . $instance['id']
];

NoticeDispatcherService::getInstance()->send(
    WorkflowStatusConstant::MODULE_NAME,
    WorkflowStatusConstant::MESSAGE_TASK_VOID,
    $variables,
    [$submitterId]
);
```

## 🚨 常见错误及解决方案

### 1. 消息发送失败
**错误**: `缺少必填变量 流程标题, 任务名称`
**原因**: 传入的变量键名与模板配置不匹配
**解决**: 检查变量键名是否为英文，如 `title` 而不是 `流程标题`

### 2. 变量未替换
**错误**: 消息内容显示 `${流程标题}` 而不是实际值
**原因**: 模板配置中的 `field` 字段与传入的键名不匹配
**解决**: 确保模板配置正确，`field` 字段与代码传入的键名一致

### 3. 模板不存在
**错误**: `Template not found: workflow_task_approval`
**原因**: 数据库中缺少对应的模板记录
**解决**: 执行模板创建SQL或检查模板状态是否为启用

## 🔍 调试命令

### 检查模板
```sql
-- 查看所有workflow模板
SELECT id, code, name, status FROM notice_template WHERE module_code = 'workflow';

-- 查看特定模板的变量配置
SELECT variables_config FROM notice_template WHERE code = 'workflow_task_approval';
```

### 检查消息记录
```sql
-- 查看最近的消息
SELECT id, code, title, receiver_id, status, created_at 
FROM notice_message 
WHERE code LIKE 'workflow_%' 
ORDER BY created_at DESC LIMIT 10;

-- 查看未替换的变量
SELECT id, title, content 
FROM notice_message 
WHERE (title LIKE '%${%' OR content LIKE '%${%') 
AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

### 测试发送
```php
// 简单测试
$result = NoticeDispatcherService::getInstance()->send(
    'workflow',
    'task_approval',
    ['title' => '测试流程', 'task_name' => '测试任务'],
    [1]
);

echo $result ? "发送成功: {$result}" : "发送失败";
```

## 📊 状态常量速查

```php
// 实例状态
WorkflowStatusConstant::STATUS_SAVED      // 0 - 已保存
WorkflowStatusConstant::STATUS_PENDING    // 1 - 审批中
WorkflowStatusConstant::STATUS_APPROVED   // 2 - 已通过
WorkflowStatusConstant::STATUS_REJECTED   // 3 - 已驳回
WorkflowStatusConstant::STATUS_TERMINATED // 4 - 已终止
WorkflowStatusConstant::STATUS_RECALLED   // 5 - 已撤回
WorkflowStatusConstant::STATUS_VOID       // 6 - 已作废

// 消息类型
WorkflowStatusConstant::MESSAGE_TASK_APPROVAL    // task_approval
WorkflowStatusConstant::MESSAGE_TASK_APPROVED    // task_approved
WorkflowStatusConstant::MESSAGE_TASK_CC          // task_cc
WorkflowStatusConstant::MESSAGE_TASK_URGE        // task_urge
WorkflowStatusConstant::MESSAGE_TASK_TRANSFER    // task_transfer
WorkflowStatusConstant::MESSAGE_TASK_TERMINATED  // task_terminated
WorkflowStatusConstant::MESSAGE_TASK_VOID        // task_void
```

## 🎯 最佳实践提醒

1. **变量键名**: 始终使用英文键名，如 `title` 而不是 `流程标题`
2. **异常处理**: 消息发送失败不应影响主业务流程
3. **日志记录**: 记录发送结果，便于问题排查
4. **常量使用**: 使用常量而不是硬编码字符串
5. **数据验证**: 确保必填变量有值，时间格式正确

## 📞 支持

如遇问题，请检查：
1. 模板是否存在且启用
2. 变量键名是否正确
3. 必填变量是否都有值
4. 日志中的错误信息

---

**版本**: 1.0  
**更新时间**: 2025-07-16
