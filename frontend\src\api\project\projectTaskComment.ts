import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 任务评论表相关接口
 */
export class ProjectTaskCommentApi {
  /**
   * 获取任务评论表列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/project/project_task_comment/index',
      params
    })
  }

  /**
   * 获取任务评论表详情
   * @param id 记录ID
   * @param options 可选参数
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/project/project_task_comment/detail/${id}`,
      params: options
    })
  }

  /**
   * 添加任务评论表
   * @param data 表单数据
   */
  static add(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task_comment/add',
      data
    })
  }

  /**
   * 更新任务评论表
   * @param data 表单数据
   */
  static update(data: any) {
    return request.post<BaseResult>({
      url: `/project/project_task_comment/edit/${data.id}`,
      data
    })
  }

  /**
   * 删除任务评论表
   * @param id 记录ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/project/project_task_comment/delete/${id}`
    })
  }

  /**
   * 批量删除任务评论表
   * @param ids 记录ID数组
   */
  static batchDelete(ids: (number | string)[]) {
    return request.post<BaseResult>({
      url: `/project/project_task_comment/batchDelete`,
      data: { ids }
    })
  }

  /**
   * 更新单个字段
   * @param data 字段数据
   */
  static updateField(data: any) {
    return request.post<BaseResult>({
      url: '/project/project_task_comment/updateField',
      data
    })
  }

  

  


  /**
   * 导入任务评论表数据
   * @param file 导入文件
   */
  static import(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post<BaseResult>({
      url: '/project/project_task_comment/import',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 获取导入模板
   */
  static importTemplate() {
    return request.get<BaseResult>({
      url: '/project/project_task_comment/importTemplate'
    })
  }

  /**
   * 下载导入模板
   */
  static downloadTemplate(fileName: string) {
    return request.get({
      url: '/project/project_task_comment/downloadTemplate',
      params: { file: fileName },
      responseType: 'blob'
    })
  }
}