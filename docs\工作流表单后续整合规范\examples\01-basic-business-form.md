# 基础业务表单示例

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 示例概述

本示例展示如何创建一个完整的业务表单组件，包括申请表单和详情展示组件。以合同管理为例，演示统一表单架构的实际应用。

## 📊 数据结构设计

### 数据库表结构

```sql
-- 合同表
CREATE TABLE crm_contract (
  id int PRIMARY KEY AUTO_INCREMENT,
  contract_number varchar(50) NOT NULL COMMENT '合同编号',
  contract_name varchar(200) NOT NULL COMMENT '合同名称',
  customer_id int NOT NULL COMMENT '客户ID',
  customer_name varchar(100) COMMENT '客户名称',
  contract_amount decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '合同金额',
  contract_type tinyint DEFAULT 1 COMMENT '合同类型:1=销售合同,2=采购合同,3=服务合同',
  sign_date date COMMENT '签署日期',
  start_date date COMMENT '开始日期',
  end_date date COMMENT '结束日期',
  payment_terms varchar(500) COMMENT '付款条款',
  contract_content text COMMENT '合同内容',
  attachments JSON COMMENT '附件信息',
  remark text COMMENT '备注',
  
  -- 工作流字段
  instance_id int DEFAULT 0 COMMENT '工作流实例ID',
  approval_status tinyint DEFAULT 0 COMMENT '审批状态',
  approval_node varchar(100) DEFAULT '' COMMENT '当前审批节点',
  submitter_id int DEFAULT 0 COMMENT '提交人ID',
  submitted_at datetime DEFAULT NULL COMMENT '提交时间',
  approved_at datetime DEFAULT NULL COMMENT '审批完成时间',
  
  -- 标准字段
  created_id int NOT NULL DEFAULT 0,
  updated_id int NOT NULL DEFAULT 0,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at datetime DEFAULT NULL,
  tenant_id int NOT NULL DEFAULT 0,
  
  UNIQUE KEY uk_contract_number (contract_number, tenant_id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_contract_type (contract_type),
  INDEX idx_approval_status (approval_status),
  INDEX idx_tenant_id (tenant_id)
);
```

### TypeScript类型定义

```typescript
// types/contract.ts
export interface Contract extends WorkflowEntity {
  id?: number
  contract_number: string
  contract_name: string
  customer_id: number
  customer_name?: string
  contract_amount: number
  contract_type: ContractType
  sign_date?: string
  start_date?: string
  end_date?: string
  payment_terms?: string
  contract_content?: string
  attachments?: FileAttachment[]
  remark?: string
}

export enum ContractType {
  SALES = 1,    // 销售合同
  PURCHASE = 2, // 采购合同
  SERVICE = 3   // 服务合同
}

export interface FileAttachment {
  id?: number
  name: string
  url: string
  size: number
  type: string
  upload_time?: string
}

export interface WorkflowEntity extends BaseEntity {
  instance_id?: number
  approval_status: ApprovalStatus
  approval_node?: string
  submitter_id?: number
  submitted_at?: string | null
  approved_at?: string | null
}

export interface BaseEntity {
  id?: number
  created_id: number
  updated_id: number
  created_at?: string
  updated_at?: string
  deleted_at?: string | null
  tenant_id: number
}

export enum ApprovalStatus {
  DRAFT = 0,      // 草稿
  PENDING = 1,    // 审批中
  APPROVED = 2,   // 已通过
  REJECTED = 3,   // 已驳回
  TERMINATED = 4, // 已终止
  WITHDRAWN = 5   // 已撤回
}
```

## 🔧 申请表单组件

### 合同申请表单

```vue
<!-- business-forms/crm_contract.vue -->
<template>
  <div class="contract-form-container" v-loading="loading">
    <!-- 表单内容 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="contract-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contract_number">
              <el-input
                v-model="formData.contract_number"
                placeholder="请输入合同编号"
                :disabled="mode === 'edit'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contract_type">
              <el-select
                v-model="formData.contract_type"
                placeholder="请选择合同类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in contractTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="合同名称" prop="contract_name">
          <el-input
            v-model="formData.contract_name"
            placeholder="请输入合同名称"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户" prop="customer_id">
              <CustomerSelect
                v-model="formData.customer_id"
                @change="handleCustomerChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contract_amount">
              <el-input-number
                v-model="formData.contract_amount"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入合同金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- 时间信息 -->
      <div class="form-section">
        <div class="section-title">时间信息</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="签署日期" prop="sign_date">
              <el-date-picker
                v-model="formData.sign_date"
                type="date"
                placeholder="请选择签署日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始日期" prop="start_date">
              <el-date-picker
                v-model="formData.start_date"
                type="date"
                placeholder="请选择开始日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                v-model="formData.end_date"
                type="date"
                placeholder="请选择结束日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- 详细信息 -->
      <div class="form-section">
        <div class="section-title">详细信息</div>
        
        <el-form-item label="付款条款" prop="payment_terms">
          <el-input
            v-model="formData.payment_terms"
            type="textarea"
            :rows="3"
            placeholder="请输入付款条款"
          />
        </el-form-item>
        
        <el-form-item label="合同内容" prop="contract_content">
          <el-input
            v-model="formData.contract_content"
            type="textarea"
            :rows="5"
            placeholder="请输入合同内容"
          />
        </el-form-item>
        
        <el-form-item label="附件上传">
          <FileUpload
            v-model="formData.attachments"
            :limit="10"
            :file-types="['pdf', 'doc', 'docx', 'jpg', 'png']"
          />
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </div>
    </el-form>
    
    <!-- 表单操作按钮 -->
    <div class="form-actions" v-if="showActions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleSave" :loading="saving">保存草稿</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        提交申请
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { ContractApi } from '@/api/crm/contract'
import { ApplicationApi } from '@/api/workflow/application'
import CustomerSelect from '@/components/business/CustomerSelect.vue'
import FileUpload from '@/components/common/FileUpload.vue'
import type { Contract, ContractType } from '@/types/contract'

// 组件名称
defineOptions({
  name: 'CrmContractForm'
})

// Props接口
interface Props {
  data?: Partial<Contract>
  mode?: 'create' | 'edit'
  definitionId?: number
  formId?: number
  showActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create',
  showActions: true,
  data: () => ({})
})

// Events接口
const emit = defineEmits<{
  submit: [data: Contract]
  save: [data: Contract]
  cancel: []
  change: [data: Contract]
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const saving = ref(false)
const submitting = ref(false)

// 表单数据
const formData = reactive<Partial<Contract>>({
  contract_number: '',
  contract_name: '',
  customer_id: 0,
  customer_name: '',
  contract_amount: 0,
  contract_type: 1,
  sign_date: '',
  start_date: '',
  end_date: '',
  payment_terms: '',
  contract_content: '',
  attachments: [],
  remark: ''
})

// 合同类型选项
const contractTypeOptions = [
  { label: '销售合同', value: 1 },
  { label: '采购合同', value: 2 },
  { label: '服务合同', value: 3 }
]

// 表单验证规则
const formRules: FormRules = {
  contract_number: [
    { required: true, message: '请输入合同编号', trigger: 'blur' },
    { min: 2, max: 50, message: '合同编号长度在2-50个字符', trigger: 'blur' }
  ],
  contract_name: [
    { required: true, message: '请输入合同名称', trigger: 'blur' },
    { min: 2, max: 200, message: '合同名称长度在2-200个字符', trigger: 'blur' }
  ],
  customer_id: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  contract_amount: [
    { required: true, message: '请输入合同金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '合同金额必须大于0', trigger: 'blur' }
  ],
  contract_type: [
    { required: true, message: '请选择合同类型', trigger: 'change' }
  ]
}

// 客户选择变化处理
const handleCustomerChange = (customerId: number, customerInfo: any) => {
  formData.customer_id = customerId
  formData.customer_name = customerInfo?.name || ''
}

// 表单提交
const handleSubmit = async () => {
  if (submitting.value) return
  
  try {
    submitting.value = true
    
    const valid = await formRef.value?.validate()
    if (valid) {
      emit('submit', { ...formData } as Contract)
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    submitting.value = false
  }
}

// 保存草稿
const handleSave = async () => {
  if (saving.value) return
  
  try {
    saving.value = true
    emit('save', { ...formData } as Contract)
  } finally {
    saving.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
}

// 显示表单
const showForm = async (id?: number) => {
  if (id && props.mode === 'edit') {
    try {
      loading.value = true
      const res = await ApplicationApi.detail(id)
      if (res.code === 1 && res.data.form_data) {
        Object.assign(formData, res.data.form_data)
      }
    } catch (error) {
      console.error('加载表单数据失败:', error)
      ElMessage.error('加载表单数据失败')
    } finally {
      loading.value = false
    }
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, getDefaultFormData())
}

// 表单验证
const validate = async (): Promise<boolean> => {
  try {
    return await formRef.value?.validate() || false
  } catch (error) {
    return false
  }
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 设置表单数据
const setFormData = (data: Partial<Contract>) => {
  Object.assign(formData, data)
}

// 设置编辑数据
const setEditData = (data: Partial<Contract>) => {
  Object.assign(formData, data)
}

// 获取默认表单数据
const getDefaultFormData = (): Partial<Contract> => {
  return {
    contract_number: '',
    contract_name: '',
    customer_id: 0,
    customer_name: '',
    contract_amount: 0,
    contract_type: 1,
    sign_date: '',
    start_date: '',
    end_date: '',
    payment_terms: '',
    contract_content: '',
    attachments: [],
    remark: ''
  }
}

// 监听数据变化
watch(formData, (newData) => {
  emit('change', newData as Contract)
}, { deep: true })

// 初始化
onMounted(() => {
  if (props.data && Object.keys(props.data).length > 0) {
    setFormData(props.data)
  }
})

// 暴露方法
defineExpose({
  showForm,
  resetForm,
  validate,
  getFormData,
  setFormData,
  setEditData
})
</script>

<style scoped>
.contract-form-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.contract-form {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  padding-top: 24px;
}

.form-actions .el-button {
  margin: 0 12px;
  min-width: 100px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  border-radius: 4px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}
</style>
```

## 👁️ 详情展示组件

### 合同详情组件

```vue
<!-- business-detail/crm_contract.vue -->
<template>
  <div class="contract-detail-container" v-loading="loading">
    <!-- 详情内容 -->
    <div class="contract-detail">
      <!-- 基本信息 -->
      <div class="detail-section">
        <div class="section-title">基本信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">
            <span class="contract-number">{{ data.contract_number || '-' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            <el-tag :type="getContractTypeColor(data.contract_type)">
              {{ getContractTypeName(data.contract_type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合同名称" :span="2">
            {{ data.contract_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ data.customer_name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="合同金额">
            <span class="amount-text">{{ formatAmount(data.contract_amount) }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 时间信息 -->
      <div class="detail-section">
        <div class="section-title">时间信息</div>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="签署日期">
            {{ formatDate(data.sign_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">
            {{ formatDate(data.start_date) }}
          </el-descriptions-item>
          <el-descriptions-item label="结束日期">
            {{ formatDate(data.end_date) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 详细信息 -->
      <div class="detail-section">
        <div class="section-title">详细信息</div>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="付款条款">
            <div class="text-content">{{ data.payment_terms || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="合同内容">
            <div class="text-content">{{ data.contract_content || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="附件信息" v-if="data.attachments && data.attachments.length > 0">
            <div class="attachments-list">
              <div
                v-for="file in data.attachments"
                :key="file.id"
                class="attachment-item"
              >
                <el-link :href="file.url" target="_blank" :underline="false">
                  <el-icon><Document /></el-icon>
                  {{ file.name }}
                </el-link>
                <span class="file-size">({{ formatFileSize(file.size) }})</span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="备注" v-if="data.remark">
            <div class="text-content">{{ data.remark }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <!-- 审批信息 -->
      <div class="detail-section" v-if="showWorkflowInfo">
        <div class="section-title">审批信息</div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="审批状态">
            <el-tag :type="getApprovalStatusColor(data.approval_status)">
              {{ getApprovalStatusText(data.approval_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="当前节点">
            {{ data.approval_node || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ formatDateTime(data.submitted_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="审批完成时间">
            {{ formatDateTime(data.approved_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="detail-actions" v-if="showActions">
      <el-button @click="handleRefresh" icon="Refresh">刷新</el-button>
      <el-button @click="handleExport" icon="Download">导出</el-button>
      <el-button @click="handlePrint" icon="Printer">打印</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Document } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import type { Contract, ContractType, ApprovalStatus } from '@/types/contract'

// 组件名称
defineOptions({
  name: 'CrmContractDetail'
})

// Props接口
interface Props {
  data: Partial<Contract>
  businessCode?: string
  loading?: boolean
  showActions?: boolean
  showWorkflowInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showActions: false,
  showWorkflowInfo: true,
  data: () => ({})
})

// Events接口
const emit = defineEmits<{
  refresh: []
  export: []
  print: []
}>()

// 格式化金额
const formatAmount = (amount?: number): string => {
  if (!amount) return '¥0.00'
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 格式化日期
const formatDate = (date?: string): string => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD')
}

// 格式化日期时间
const formatDateTime = (datetime?: string): string => {
  if (!datetime) return '-'
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size}B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
  return `${(size / (1024 * 1024)).toFixed(1)}MB`
}

// 获取合同类型名称
const getContractTypeName = (type?: ContractType): string => {
  const typeMap = {
    1: '销售合同',
    2: '采购合同',
    3: '服务合同'
  }
  return typeMap[type as keyof typeof typeMap] || '未知类型'
}

// 获取合同类型颜色
const getContractTypeColor = (type?: ContractType): string => {
  const colorMap = {
    1: 'success',  // 销售合同
    2: 'warning',  // 采购合同
    3: 'info'      // 服务合同
  }
  return colorMap[type as keyof typeof colorMap] || 'info'
}

// 获取审批状态文本
const getApprovalStatusText = (status?: ApprovalStatus): string => {
  const statusMap = {
    0: '草稿',
    1: '审批中',
    2: '已通过',
    3: '已驳回',
    4: '已终止',
    5: '已撤回'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}

// 获取审批状态颜色
const getApprovalStatusColor = (status?: ApprovalStatus): string => {
  const colorMap = {
    0: 'info',     // 草稿
    1: 'warning',  // 审批中
    2: 'success',  // 已通过
    3: 'danger',   // 已驳回
    4: 'info',     // 已终止
    5: 'warning'   // 已撤回
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

// 刷新数据
const handleRefresh = () => {
  emit('refresh')
}

// 导出数据
const handleExport = () => {
  emit('export')
}

// 打印数据
const handlePrint = () => {
  window.print()
}

// 暴露方法
defineExpose({
  refresh: handleRefresh,
  export: handleExport,
  print: handlePrint
})
</script>

<style scoped>
.contract-detail-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.contract-detail {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
}

.detail-section {
  margin-bottom: 32px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.contract-number {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #409eff;
}

.amount-text {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

.text-content {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.detail-actions {
  margin-top: 32px;
  text-align: center;
  border-top: 1px solid #e4e7ed;
  padding-top: 24px;
}

.detail-actions .el-button {
  margin: 0 12px;
  min-width: 100px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
  background-color: #fafafa;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

:deep(.el-descriptions-item__cell) {
  padding: 12px 16px;
}

/* 打印样式 */
@media print {
  .detail-actions {
    display: none;
  }
  
  .contract-detail-container {
    padding: 0;
    max-width: none;
  }
  
  .section-title {
    color: #000;
    border-bottom-color: #000;
  }
}
</style>
```

## 🔌 API接口实现

### 前端API接口

```typescript
// api/crm/contract.ts
import request from '@/utils/request'
import type { Contract } from '@/types/contract'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

export interface ContractListParams {
  page?: number
  limit?: number
  contract_number?: string
  contract_name?: string
  customer_id?: number
  contract_type?: number
  approval_status?: number
  start_date?: string
  end_date?: string
}

export const ContractApi = {
  // 获取合同列表
  list(params: ContractListParams): Promise<PaginatedResponse<Contract>> {
    return request.get('/crm/contract/list', { params })
  },

  // 获取合同详情
  detail(id: number): Promise<ApiResponse<Contract>> {
    return request.get(`/crm/contract/detail/${id}`)
  },

  // 创建合同
  create(data: Partial<Contract>): Promise<ApiResponse<{ id: number }>> {
    return request.post('/crm/contract/create', data)
  },

  // 更新合同
  update(id: number, data: Partial<Contract>): Promise<ApiResponse> {
    return request.put(`/crm/contract/update/${id}`, data)
  },

  // 删除合同
  delete(id: number): Promise<ApiResponse> {
    return request.delete(`/crm/contract/delete/${id}`)
  },

  // 导出合同
  export(params: ContractListParams): Promise<Blob> {
    return request.get('/crm/contract/export', {
      params,
      responseType: 'blob'
    })
  }
}
```

### 后端控制器实现

```php
<?php
// app/crm/controller/Contract.php
namespace app\crm\controller;

use app\common\controller\BaseController;
use app\crm\service\ContractService;
use app\crm\validate\ContractValidate;

class Contract extends BaseController
{
    protected $service;

    public function initialize()
    {
        parent::initialize();
        $this->service = new ContractService();
    }

    /**
     * 获取合同列表
     */
    public function list()
    {
        $params = $this->request->param();
        
        try {
            $result = $this->service->getPageList($params);
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取合同详情
     */
    public function detail($id)
    {
        try {
            $result = $this->service->getDetail($id);
            if (!$result) {
                return $this->error('合同不存在');
            }
            return $this->success('获取成功', $result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建合同
     */
    public function create()
    {
        $data = $this->request->param();
        
        try {
            // 数据验证
            $validate = new ContractValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $result = $this->service->create($data);
            return $this->success('创建成功', ['id' => $result]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新合同
     */
    public function update($id)
    {
        $data = $this->request->param();
        
        try {
            // 数据验证
            $validate = new ContractValidate();
            if (!$validate->scene('update')->check($data)) {
                return $this->error($validate->getError());
            }
            
            $this->service->update($id, $data);
            return $this->success('更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除合同
     */
    public function delete($id)
    {
        try {
            $this->service->delete($id);
            return $this->success('删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 导出合同
     */
    public function export()
    {
        $params = $this->request->param();
        
        try {
            $filePath = $this->service->export($params);
            return download($filePath);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 后端服务层实现

```php
<?php
// app/crm/service/ContractService.php
namespace app\crm\service;

use app\common\service\BaseService;
use app\crm\model\Contract;

class ContractService extends BaseService
{
    protected $model = Contract::class;

    /**
     * 获取分页列表
     */
    public function getPageList(array $params): array
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        
        $query = $this->model::where('deleted_at', null);
        
        // 搜索条件
        if (!empty($params['contract_number'])) {
            $query->where('contract_number', 'like', '%' . $params['contract_number'] . '%');
        }
        
        if (!empty($params['contract_name'])) {
            $query->where('contract_name', 'like', '%' . $params['contract_name'] . '%');
        }
        
        if (!empty($params['customer_id'])) {
            $query->where('customer_id', $params['customer_id']);
        }
        
        if (isset($params['contract_type']) && $params['contract_type'] !== '') {
            $query->where('contract_type', $params['contract_type']);
        }
        
        if (isset($params['approval_status']) && $params['approval_status'] !== '') {
            $query->where('approval_status', $params['approval_status']);
        }
        
        if (!empty($params['start_date'])) {
            $query->where('sign_date', '>=', $params['start_date']);
        }
        
        if (!empty($params['end_date'])) {
            $query->where('sign_date', '<=', $params['end_date']);
        }
        
        // 租户隔离
        $query->where('tenant_id', $this->getTenantId());
        
        // 分页查询
        $total = $query->count();
        $list = $query->order('id', 'desc')
                     ->page($page, $limit)
                     ->select()
                     ->toArray();
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 获取详情
     */
    public function getDetail(int $id): ?array
    {
        $contract = $this->model::where('id', $id)
                                ->where('tenant_id', $this->getTenantId())
                                ->where('deleted_at', null)
                                ->find();
        
        if (!$contract) {
            return null;
        }
        
        $data = $contract->toArray();
        
        // 处理附件信息
        if (!empty($data['attachments'])) {
            $data['attachments'] = json_decode($data['attachments'], true);
        }
        
        return $data;
    }

    /**
     * 创建合同
     */
    public function create(array $data): int
    {
        // 生成合同编号
        if (empty($data['contract_number'])) {
            $data['contract_number'] = $this->generateContractNumber();
        }
        
        // 处理附件信息
        if (!empty($data['attachments'])) {
            $data['attachments'] = json_encode($data['attachments']);
        }
        
        // 设置创建人和租户
        $data['created_id'] = $this->getUserId();
        $data['tenant_id'] = $this->getTenantId();
        
        $contract = $this->model::create($data);
        
        return $contract->id;
    }

    /**
     * 更新合同
     */
    public function update(int $id, array $data): bool
    {
        $contract = $this->model::where('id', $id)
                                ->where('tenant_id', $this->getTenantId())
                                ->find();
        
        if (!$contract) {
            throw new \Exception('合同不存在');
        }
        
        // 处理附件信息
        if (isset($data['attachments'])) {
            $data['attachments'] = json_encode($data['attachments']);
        }
        
        // 设置更新人
        $data['updated_id'] = $this->getUserId();
        
        return $contract->save($data);
    }

    /**
     * 删除合同（软删除）
     */
    public function delete(int $id): bool
    {
        $contract = $this->model::where('id', $id)
                                ->where('tenant_id', $this->getTenantId())
                                ->find();
        
        if (!$contract) {
            throw new \Exception('合同不存在');
        }
        
        return $contract->delete();
    }

    /**
     * 生成合同编号
     */
    private function generateContractNumber(): string
    {
        $prefix = 'CT';
        $date = date('Ymd');
        $sequence = str_pad($this->getNextSequence('contract'), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $date . $sequence;
    }

    /**
     * 导出合同数据
     */
    public function export(array $params): string
    {
        // 获取数据
        $params['limit'] = 10000; // 导出限制
        $result = $this->getPageList($params);
        
        // 生成Excel文件
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $headers = ['合同编号', '合同名称', '客户名称', '合同金额', '合同类型', '签署日期', '审批状态'];
        $sheet->fromArray($headers, null, 'A1');
        
        // 填充数据
        $row = 2;
        foreach ($result['list'] as $item) {
            $sheet->fromArray([
                $item['contract_number'],
                $item['contract_name'],
                $item['customer_name'],
                $item['contract_amount'],
                $this->getContractTypeName($item['contract_type']),
                $item['sign_date'],
                $this->getApprovalStatusName($item['approval_status'])
            ], null, 'A' . $row);
            $row++;
        }
        
        // 保存文件
        $fileName = '合同列表_' . date('YmdHis') . '.xlsx';
        $filePath = runtime_path('temp') . $fileName;
        
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);
        
        return $filePath;
    }
}
```

## 🧪 使用示例

### 在工作流中使用

```vue
<!-- 在FormManager中使用 -->
<template>
  <FormManager
    v-model="formVisible"
    type="crm_contract"
    :workflow-type-id="workflowTypeId"
    @success="handleFormSuccess"
    @cancel="handleFormCancel"
  />
</template>

<script setup lang="ts">
const formVisible = ref(false)
const workflowTypeId = ref(1) // 合同审批工作流ID

const handleFormSuccess = (data: any) => {
  console.log('表单提交成功:', data)
  formVisible.value = false
  // 刷新列表或其他操作
}

const handleFormCancel = () => {
  formVisible.value = false
}
</script>
```

### 在详情查看中使用

```vue
<!-- 在FormDataViewer中使用 -->
<template>
  <FormDataViewer
    :form-data="workflowData.form_data"
    :business-code="workflowData.business_code"
  />
</template>

<script setup lang="ts">
const workflowData = ref({
  business_code: 'crm_contract',
  form_data: {
    contract_number: 'CT20250124001',
    contract_name: '销售合同示例',
    customer_name: '示例客户',
    contract_amount: 100000,
    // 其他字段...
  }
})
</script>
```

## 📝 总结

这个基础业务表单示例展示了：

1. **完整的组件结构**：申请表单和详情展示组件
2. **统一的接口规范**：遵循组件开发规范
3. **类型安全**：完整的TypeScript类型定义
4. **数据验证**：前后端数据验证
5. **工作流集成**：与工作流系统无缝集成
6. **用户体验**：良好的交互和视觉效果

通过这个示例，开发者可以快速理解如何创建符合统一架构的业务表单组件。

---

**注意：** 请根据实际业务需求调整字段和验证规则，确保组件的实用性和可维护性。