# CRM客户页面列字段整合说明

## 整合概述

按照业务逻辑将相关字段进行分组整合，提升表格的可读性和空间利用率，同时保持重要状态信息的独立性。

## 整合策略

### 1. 基础信息整合
将客户的核心信息整合到一列中：
- 客户名称
- 所属行业  
- 客户来源
- 电话
- 官网（支持点击跳转）

### 2. 地址信息整合
将地理位置相关信息整合：
- 省份
- 城市
- 区/县
- 详细地址

### 3. 业务信息整合
将企业经营相关信息整合：
- 统一社会信用代码
- 年营业额
- 员工人数
- 注册资本

### 4. 跟进时间整合
将时间相关信息整合：
- 最后跟进时间
- 下次跟进时间

### 5. 状态信息保持独立
重要的状态信息保持单独列显示：
- 客户级别（TagColumn）
- 状态（SwitchColumn）
- 锁定状态（ElTag）

## 技术实现

### 基础信息列
```vue
<ElTableColumn label="基础信息" width="300">
  <template #default="scope">
    <div class="info-group">
      <div class="info-item">
        <span class="label">客户名称:</span>
        <span class="value">{{ scope.row.customer_name || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">所属行业:</span>
        <span class="value">{{ scope.row.industry || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">客户来源:</span>
        <span class="value">{{ scope.row.source || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">电话:</span>
        <span class="value">{{ scope.row.phone || '-' }}</span>
      </div>
      <div class="info-item" v-if="scope.row.website">
        <span class="label">官网:</span>
        <a :href="scope.row.website" target="_blank" class="link">
          {{ scope.row.website.length > 25 ? scope.row.website.substring(0, 25) + '...' : scope.row.website }}
        </a>
      </div>
    </div>
  </template>
</ElTableColumn>
```

### 地址信息列
```vue
<ElTableColumn label="地址信息" width="250">
  <template #default="scope">
    <div class="info-group">
      <div class="info-item">
        <span class="label">省份:</span>
        <span class="value">{{ scope.row.region_province || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">城市:</span>
        <span class="value">{{ scope.row.region_city || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">区/县:</span>
        <span class="value">{{ scope.row.region_district || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">详细地址:</span>
        <span class="value">{{ scope.row.address || '-' }}</span>
      </div>
    </div>
  </template>
</ElTableColumn>
```

### 业务信息列
```vue
<ElTableColumn label="业务信息" width="280">
  <template #default="scope">
    <div class="info-group">
      <div class="info-item">
        <span class="label">信用代码:</span>
        <span class="value">{{ scope.row.credit_code || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">年营业额:</span>
        <span class="value">{{ scope.row.annual_revenue || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">员工人数:</span>
        <span class="value">{{ scope.row.employee_count || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">注册资本:</span>
        <span class="value">{{ scope.row.registered_capital || '-' }}</span>
      </div>
    </div>
  </template>
</ElTableColumn>
```

### 跟进时间列
```vue
<ElTableColumn label="跟进时间" width="200">
  <template #default="scope">
    <div class="info-group">
      <div class="info-item">
        <span class="label">最后跟进:</span>
        <span class="value">{{ scope.row.last_followed_at || '-' }}</span>
      </div>
      <div class="info-item">
        <span class="label">下次跟进:</span>
        <span class="value">{{ scope.row.next_followed_at || '-' }}</span>
      </div>
    </div>
  </template>
</ElTableColumn>
```

## 样式设计

### 信息组样式
```scss
.info-group {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    line-height: 1.4;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #909399;
      margin-right: 6px;
      min-width: 60px;
      font-weight: 500;
    }

    .value {
      color: #303133;
      flex: 1;
      word-break: break-all;
    }

    .link {
      color: #409eff;
      text-decoration: none;
      flex: 1;
      word-break: break-all;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
```

## 最终列结构

| 列名 | 宽度 | 类型 | 说明 |
|------|------|------|------|
| 序号 | 60px | 系统列 | 自动编号 |
| ID | 80px | 基础列 | 客户ID |
| 基础信息 | 300px | 整合列 | 客户名称、行业、来源、电话、官网 |
| 地址信息 | 250px | 整合列 | 省市区县、详细地址 |
| 业务信息 | 280px | 整合列 | 信用代码、营业额、员工数、注册资本 |
| 备注 | 自适应 | 组件列 | LongTextColumn |
| 负责人ID | 自适应 | 基础列 | 负责人标识 |
| 客户级别 | 100px | 组件列 | TagColumn |
| 状态 | 100px | 组件列 | SwitchColumn |
| 跟进时间 | 200px | 整合列 | 最后跟进、下次跟进 |
| 锁定状态 | 100px | 基础列 | ElTag显示 |
| 创建时间 | 180px | 基础列 | 记录创建时间 |
| 操作 | 260px | 操作列 | 详情、编辑、更多 |

## 优势分析

### 1. 空间优化
- **减少列数**: 从原来的15+列减少到13列
- **信息密度**: 相关信息集中显示，提升信息密度
- **宽度控制**: 合理分配列宽，避免表格过宽

### 2. 用户体验
- **逻辑清晰**: 相关信息分组显示，符合用户认知
- **快速浏览**: 重要信息一目了然
- **操作便捷**: 状态信息保持独立，便于快速操作

### 3. 维护性
- **结构清晰**: 整合逻辑明确，易于理解
- **扩展性**: 新增字段可以轻松加入对应分组
- **一致性**: 可以作为其他页面的参考模板

## 注意事项

1. **数据为空处理**: 所有字段都有 `|| '-'` 的空值处理
2. **文本截断**: 长文本（如官网）自动截断并显示省略号
3. **链接处理**: 官网字段支持点击跳转，新窗口打开
4. **响应式**: 样式支持不同屏幕尺寸的适配
5. **性能考虑**: 避免过度嵌套，保持渲染性能
