<template>
  <ElDescriptions :column="2" border>
    <!-- 基本信息 -->
    <ElDescriptionsItem label="ID">
      {{ data.id || '-' }}
    </ElDescriptionsItem>

    <ElDescriptionsItem label="姓名">
      {{ data.lead_name || '-' }}
    </ElDescriptionsItem>

    <ElDescriptionsItem label="公司名称">
      {{ data.company || '-' }}
    </ElDescriptionsItem>

    <ElDescriptionsItem label="职位">
      {{ data.position || '-' }}
    </ElDescriptionsItem>

    <!-- 联系方式 -->
    <ElDescriptionsItem label="手机号">
      <div v-if="data.mobile" class="contact-info">
        <span>{{ data.mobile }}</span>
        <ElButton
          type="primary"
          link
          size="small"
          @click="copyToClipboard(data.mobile)"
          class="ml-2"
        >
          复制
        </ElButton>
      </div>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="电话">
      {{ data.phone || '-' }}
    </ElDescriptionsItem>

    <ElDescriptionsItem label="邮箱" :span="2">
      <ElLink v-if="data.email" :href="`mailto:${data.email}`" type="primary">
        {{ data.email }}
      </ElLink>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <!-- 状态信息 -->
    <ElDescriptionsItem label="状态">
      <ElTag :type="statusConfig.type" :icon="statusConfig.icon">
        {{ statusConfig.label }}
      </ElTag>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="优先级别">
      <ElTag :type="levelConfig.type" :icon="levelConfig.icon">
        {{ levelConfig.label }}
      </ElTag>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="线索来源">
      <ElTag v-if="data.source" type="info" size="small">
        {{ data.source }}
      </ElTag>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="所属行业">
      <ElTag v-if="data.industry" type="info" size="small">
        {{ data.industry }}
      </ElTag>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <!-- 线索池状态 (仅线索列表显示) -->
    <!--    <ElDescriptionsItem v-if="showPoolStatus" label="线索池状态">
          <ElTag :type="data.in_pool === 1 ? 'success' : 'warning'">
            {{ data.in_pool === 1 ? '在池中' : '已分配' }}
          </ElTag>
        </ElDescriptionsItem>-->

    <!-- 地址信息 -->
    <ElDescriptionsItem label="地址" :span="showPoolStatus ? 1 : 2">
      {{ data.address || '-' }}
    </ElDescriptionsItem>

    <!-- 备注信息 -->
    <ElDescriptionsItem label="备注" :span="2">
      <div v-if="data.remark" class="remark-content">
        {{ data.remark }}
      </div>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <!-- 转化信息 -->
    <template v-if="data.is_transformed === 1">
      <ElDescriptionsItem label="转化状态">
        <ElTag type="success" size="large">
          <ElIcon>
            <Check />
          </ElIcon>
          已转化
        </ElTag>
      </ElDescriptionsItem>

      <ElDescriptionsItem label="转化后的客户" v-if="data.transformed_id">
        <ElLink type="primary" @click="$emit('view-customer', data.transformed_id)">
          <ElIcon>
            <User />
          </ElIcon>
          查看客户 (ID: {{ data.transformed_id }})
        </ElLink>
      </ElDescriptionsItem>

      <ElDescriptionsItem label="转化时间" :span="data.transformed_id ? 2 : 1">
        <div v-if="data.transformed_time" class="time-info">
          <ElIcon>
            <Clock />
          </ElIcon>
          {{ data.transformed_time }}
        </div>
        <span v-else>-</span>
      </ElDescriptionsItem>
    </template>

    <template v-else>
      <ElDescriptionsItem label="转化状态" :span="2">
        <ElTag type="info">
          <ElIcon>
            <Close />
          </ElIcon>
          未转化
        </ElTag>
      </ElDescriptionsItem>
    </template>

    <!-- 管理信息 -->
    <!--    <ElDescriptionsItem label="负责人">
          {{ data.owner_user_name || data.owner_user_id || '-' }}
        </ElDescriptionsItem>-->

    <ElDescriptionsItem label="创建人">
      {{ data.creator_name || '-' }}
    </ElDescriptionsItem>

    <ElDescriptionsItem label="最后跟进时间">
      <div v-if="data.last_followed_at" class="time-info">
        <ElIcon>
          <Clock />
        </ElIcon>
        {{ data.last_followed_at }}
      </div>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="下次跟进时间">
      <div v-if="data.next_followed_at" class="time-info">
        <ElIcon>
          <Clock />
        </ElIcon>
        {{ data.next_followed_at }}
      </div>
      <span v-else>-</span>
    </ElDescriptionsItem>

    <ElDescriptionsItem label="创建时间">
      {{ data.created_at || '-' }}
    </ElDescriptionsItem>

    <!--    <ElDescriptionsItem label="更新时间">
          {{ data.updated_at || '-' }}
        </ElDescriptionsItem>-->
  </ElDescriptions>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import {
    ElDescriptions,
    ElDescriptionsItem,
    ElTag,
    ElLink,
    ElButton,
    ElIcon,
    ElMessage
  } from 'element-plus'
  import { Check, Close, User, Clock, Phone, Message } from '@element-plus/icons-vue'

  interface LeadData {
    id?: number
    lead_name?: string
    company?: string
    position?: string
    mobile?: string
    phone?: string
    email?: string
    status?: number
    level?: number
    source?: string
    industry?: string
    address?: string
    remark?: string
    in_pool?: number
    is_transformed?: number
    transformed_id?: number
    transformed_time?: string
    owner_user_id?: number
    owner_user_name?: string
    creator_name?: string
    last_followed_at?: string
    next_followed_at?: string
    created_at?: string
    updated_at?: string
  }

  interface Props {
    data: LeadData
    showPoolStatus?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    showPoolStatus: false
  })

  const emit = defineEmits<{
    'view-customer': [id: number]
  }>()

  // 状态配置
  const statusConfig = computed(() => {
    const statusMap = {
      0: { label: '无效', type: 'danger', icon: Close },
      1: { label: '未跟进', type: 'warning', icon: Clock },
      2: { label: '跟进中', type: 'primary', icon: Clock },
      3: { label: '已转化', type: 'success', icon: Check },
      4: { label: '已失效', type: 'info', icon: Close }
    }
    return (
      statusMap[props.data.status as keyof typeof statusMap] || {
        label: '未知',
        type: 'info',
        icon: Close
      }
    )
  })

  // 优先级别配置
  const levelConfig = computed(() => {
    const levelMap = {
      0: { label: '未知', type: 'info', icon: null },
      1: { label: '普通线索', type: 'info', icon: null },
      2: { label: '重点线索', type: 'warning', icon: null },
      3: { label: '优质线索', type: 'danger', icon: null }
    }
    return (
      levelMap[props.data.level as keyof typeof levelMap] || {
        label: '未知',
        type: 'info',
        icon: null
      }
    )
  })

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success('已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }
</script>

<style scoped lang="scss">
  .contact-info {
    display: flex;
    align-items: center;

    .ml-2 {
      margin-left: 8px;
    }
  }

  .time-info {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
      color: #909399;
    }
  }

  .remark-content {
    max-height: 100px;
    overflow-y: auto;
    line-height: 1.5;
    word-break: break-word;
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-descriptions) {
      .el-descriptions__body {
        .el-descriptions__table {
          .el-descriptions__cell {
            padding: 8px;
          }
        }
      }
    }
  }
</style>
