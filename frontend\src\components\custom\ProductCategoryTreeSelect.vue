<!--产品分类树形选择组件-->
<template>
  <el-tree-select
    v-model="selectedValue"
    :data="categoryOptions"
    :props="treeProps"
    :placeholder="placeholder"
    :disabled="disabled || loading"
    :loading="loading"
    :clearable="clearable"
    :filterable="filterable"
    :check-strictly="checkStrictly"
    :multiple="multiple"
    node-key="id"
    @change="handleChange"
    style="width: 100%"
    :height="200"
    default-expand-all
  >
    <template #default="{ node, data }">
      <span class="tree-node">
        <span>{{ node.label }}</span>
      </span>
    </template>
  </el-tree-select>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { CrmProductCategoryApi } from '@/api/crm/crmProductCategory'
  import { ApiStatus } from '@/utils/http/status'

  const props = defineProps({
    modelValue: {
      type: [String, Number, Array],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选择产品分类'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    // 排除的分类ID（编辑时排除自己）
    excludeId: {
      type: [String, Number],
      default: undefined
    }
  })

  const emit = defineEmits(['update:modelValue', 'change'])

  // 双向绑定
  const selectedValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })

  const categoryOptions = ref<any[]>([])
  const loading = ref(false)

  // 树形组件配置
  const treeProps = {
    label: 'name',
    children: 'children',
    value: 'id'
  }

  // 过滤树形数据，排除指定ID及其子节点
  const filterTreeData = (data: any[], excludeId: string | number | undefined): any[] => {
    if (!excludeId) return data

    return data.filter(item => {
      if (item.id == excludeId) {
        return false
      }
      if (item.children && item.children.length > 0) {
        item.children = filterTreeData(item.children, excludeId)
      }
      return true
    })
  }

  // 获取产品分类选项
  const getCategoryOptions = async () => {
    loading.value = true
    try {
      const res = await CrmProductCategoryApi.options()
      if (res.code === ApiStatus.success) {
        let data = res.data || []
        // 如果有排除ID，过滤数据
        if (props.excludeId) {
          data = filterTreeData(data, props.excludeId)
        }

        // 在顶部添加"顶级分类"选项
        const topLevelOption = {
          id: 0,
          name: '顶级分类',
          children: data
        }

        categoryOptions.value = [topLevelOption]
      } else {
        ElMessage.error(res.message || '获取产品分类列表失败')
      }
    } catch (error) {
      console.error('获取产品分类列表出错', error)
      ElMessage.error('获取产品分类列表失败')
    } finally {
      loading.value = false
    }
  }

  const handleChange = (value: any) => {
    emit('change', value)
  }

  // 监听excludeId变化，重新获取数据
  watch(() => props.excludeId, () => {
    getCategoryOptions()
  })

  onMounted(() => {
    // 使用nextTick确保组件完全挂载后再执行
    nextTick(() => {
      getCategoryOptions()
    })
  })
</script>

<style scoped>
  .tree-node {
    display: flex;
    align-items: center;
    width: 100%;
  }
</style>

<style>
  /* 确保树形选择器的下拉面板有固定高度和滚动 */
  .el-tree-select__popper .el-select-dropdown__list {
    max-height: 200px !important;
    overflow-y: auto !important;
  }

  .el-tree-select__popper .el-tree {
    max-height: 200px !important;
    overflow-y: auto !important;
  }
</style>
