# Workflow URL配置方案使用文档

## 📋 方案概述

本方案通过配置文件实现PC端和移动端URL的统一管理，对于暂时没有的移动端页面配置为空，支持渐进式开发。

## 🎯 方案优势

### ✅ **配置化管理**
- 统一的URL配置文件 `config/workflow_url.php`
- 支持PC端、移动端、APP端三种平台
- 暂未开发的页面可配置为空，不影响现有功能
- 易于维护和扩展

### ✅ **智能处理**
- 自动平台检测（基于User-Agent）
- 空URL安全处理（返回空字符串）
- 参数自动替换（如 `{instance_id}` → `123`）
- 调试日志支持

### ✅ **业务友好**
- 简单的API调用
- 批量URL生成（同时生成PC端和移动端URL）
- 向后兼容现有代码
- 渐进式开发支持

## 🚀 使用方法

### **1. 基础URL生成**

```php
use app\workflow\service\WorkflowUrlService;

// 生成实例详情URL
$pcUrl = WorkflowUrlService::generateInstanceDetailUrl(123, 'pc');
// 结果: /workflow/detail?id=123

$mobileUrl = WorkflowUrlService::generateInstanceDetailUrl(123, 'mobile');
// 结果: '' (暂时为空)

// 生成任务详情URL
$taskUrl = WorkflowUrlService::generateTaskDetailUrl(456, 'pc');
// 结果: /workflow/task/detail?id=456
```

### **2. 批量URL生成（推荐用于消息发送）**

```php
// 同时生成PC端和移动端URL
$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
    'instance_id' => 123
]);

// 结果:
// $urls['detail_url'] = '/workflow/detail?id=123'
// $urls['mobile_url'] = ''
```

### **3. 在消息发送中使用**

```php
// 在WorkflowEngine中发送消息
$urls = WorkflowUrlService::generateBatchUrls('instance.detail', [
    'instance_id' => $instance['id']
]);

$noticeService->send(
    WorkflowStatusConstant::MODULE_NAME,
    'task_void',
    $variables,
    [$submitterId],
    [
        'business_id' => (string)$instance['id'],
        'detail_url' => $urls['detail_url'],
        'mobile_url' => $urls['mobile_url'],
        'sender_name' => $operatorName
    ]
);
```

## 📊 配置文件结构

### **config/workflow_url.php**

```php
return [
    // 基础配置
    'base_url' => [
        'pc' => '/workflow',
        'mobile' => '/m/workflow',
        'app' => 'workflow://',
    ],
    
    // URL映射配置
    'url_mapping' => [
        'instance' => [
            'detail' => [
                'pc' => '/workflow/detail?id={instance_id}',
                'mobile' => '',  // 暂时为空
                'app' => '',
            ]
        ],
        'task' => [
            'detail' => [
                'pc' => '/workflow/task/detail?id={task_id}',
                'mobile' => '',  // 暂时为空
                'app' => '',
            ]
        ]
    ]
];
```

## 🔧 配置说明

### **1. 当前配置状态**
- **PC端URL**: ✅ 全部配置完成
- **移动端URL**: ⚠️ 暂时配置为空
- **APP端URL**: ⚠️ 预留配置，暂时为空

### **2. 空URL处理**
- 移动端页面暂未开发时，URL配置为空字符串 `''`
- 系统会安全处理空URL，不会报错
- 可以记录警告日志，提醒开发移动端页面

### **3. 渐进式开发**
当移动端页面开发完成后，只需要修改配置文件：

```php
// 原配置
'mobile' => '',

// 新配置
'mobile' => '/m/workflow/detail?id={instance_id}',
```

## 📱 平台检测机制

### **自动检测规则**
```php
// 检测APP
if (stripos($userAgent, 'WorkflowApp') !== false) {
    return 'app';
}

// 检测移动端
$mobileKeywords = ['Mobile', 'Android', 'iPhone', 'iPad'];
foreach ($mobileKeywords as $keyword) {
    if (stripos($userAgent, $keyword) !== false) {
        return 'mobile';
    }
}

// 默认PC端
return 'pc';
```

### **手动指定平台**
```php
// 强制使用PC端URL
$url = WorkflowUrlService::generateInstanceDetailUrl(123, 'pc');

// 强制使用移动端URL
$url = WorkflowUrlService::generateInstanceDetailUrl(123, 'mobile');

// 自动检测平台
$url = WorkflowUrlService::generateInstanceDetailUrl(123, 'auto');
```

## 🛠️ 实施步骤

### **第一阶段：基础实施**
1. ✅ 创建配置文件 `config/workflow_url.php`
2. ✅ 创建URL服务类 `WorkflowUrlService`
3. ✅ 配置PC端URL，移动端暂时为空
4. ✅ 测试验证功能正常

### **第二阶段：集成应用**
1. 修改workflow消息发送代码
2. 使用URL服务生成URL
3. 通过options传递URL到消息中心
4. 测试验证URL正确存储

### **第三阶段：移动端开发**
1. 开发移动端页面
2. 更新配置文件中的移动端URL
3. 测试移动端跳转功能
4. 完善APP端配置（如需要）

## 📋 配置清单

### **当前支持的URL类型**
- ✅ `instance.detail` - 实例详情页
- ✅ `instance.edit` - 实例编辑页
- ✅ `instance.history` - 实例历史页
- ✅ `task.detail` - 任务详情页
- ✅ `task.approval` - 任务审批页
- ✅ `task.list` - 任务列表页
- ✅ `process.detail` - 流程详情页
- ✅ `message.list` - 消息列表页

### **配置状态统计**
- 总配置数量: 30个
- PC端配置: 10个 ✅
- 移动端配置: 0个 ⚠️ (暂时为空)
- APP端配置: 0个 ⚠️ (预留)

## ⚠️ 注意事项

### **1. 配置文件位置**
确保配置文件位于正确位置：`config/workflow_url.php`

### **2. 参数命名规范**
URL模板中的参数使用大括号包围：`{instance_id}`, `{task_id}`

### **3. 空URL处理**
- 移动端URL为空时，系统返回空字符串
- 不会影响PC端功能
- 可以通过日志监控空URL的使用情况

### **4. 缓存机制**
配置文件会被缓存，修改后需要清除缓存：
```bash
php think clear
```

## 🎯 最佳实践

### **1. 统一使用批量生成**
```php
// 推荐
$urls = WorkflowUrlService::generateBatchUrls('instance.detail', $params);

// 不推荐
$pcUrl = WorkflowUrlService::generateInstanceDetailUrl($id, 'pc');
$mobileUrl = WorkflowUrlService::generateInstanceDetailUrl($id, 'mobile');
```

### **2. 在消息发送时统一处理**
```php
// 在所有消息发送位置使用相同的模式
$urls = WorkflowUrlService::generateBatchUrls($urlType, $params);
$options = array_merge($options, $urls);
```

### **3. 配置文件管理**
- 定期检查配置文件的完整性
- 移动端页面开发完成后及时更新配置
- 使用版本控制管理配置变更

---

**版本**: v1.0  
**更新时间**: 2025-07-16  
**适用范围**: Workflow模块URL管理
