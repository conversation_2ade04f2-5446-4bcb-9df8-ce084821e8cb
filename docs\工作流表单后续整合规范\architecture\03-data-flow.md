# 数据流与状态管理

## 📋 文档信息

**文档版本：** v1.0  
**创建日期：** 2025-01-24  
**更新日期：** 2025-01-24  
**文档状态：** 正式版

## 🎯 设计目标

### 核心目标
1. **数据一致性**：确保申请表单和详情展示使用相同的数据源
2. **状态同步**：工作流状态与业务数据状态实时同步
3. **性能优化**：通过合理的缓存和状态管理提升性能
4. **错误恢复**：提供完善的错误处理和数据恢复机制

## 🌊 整体数据流架构

### 数据流向图

```mermaid
graph TB
    subgraph "前端层"
        A1[用户界面] --> A2[表单组件]
        A2 --> A3[状态管理]
        A3 --> A4[API调用]
    end
    
    subgraph "网络层"
        B1[HTTP请求] --> B2[响应拦截]
        B2 --> B3[错误处理]
        B3 --> B4[数据转换]
    end
    
    subgraph "后端层"
        C1[控制器] --> C2[服务层]
        C2 --> C3[工作流引擎]
        C3 --> C4[数据持久化]
    end
    
    subgraph "数据层"
        D1[业务数据表] --> D2[工作流实例表]
        D2 --> D3[表单配置表]
        D3 --> D4[缓存层]
    end
    
    A4 --> B1
    B4 --> A3
    B1 --> C1
    C4 --> D1
    D4 --> C2
```

### 数据流分类

| 数据流类型 | 数据源 | 数据目标 | 流向 | 特点 |
|-----------|--------|----------|------|------|
| **申请数据流** | 用户输入 | 业务表+工作流表 | 前端→后端 | 创建新数据 |
| **详情数据流** | 数据库 | 前端展示 | 后端→前端 | 只读展示 |
| **状态数据流** | 工作流引擎 | 业务表状态 | 工作流→业务 | 状态同步 |
| **配置数据流** | 配置表 | 动态组件 | 后端→前端 | 组件配置 |

## 📝 申请表单数据流

### 新建申请流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FC as 表单组件
    participant SM as 状态管理
    participant API as API层
    participant WF as 工作流引擎
    participant DB as 数据库
    
    U->>FC: 填写表单
    FC->>FC: 本地验证
    FC->>SM: 更新表单状态
    
    U->>FC: 提交申请
    FC->>SM: 设置提交状态
    SM->>API: 调用提交接口
    
    API->>WF: 启动工作流
    WF->>DB: 创建工作流实例
    WF->>DB: 保存表单数据
    
    DB->>WF: 返回实例ID
    WF->>API: 返回结果
    API->>SM: 更新状态
    SM->>FC: 更新UI
    FC->>U: 显示提交结果
```

### 编辑申请流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant FC as 表单组件
    participant SM as 状态管理
    participant API as API层
    participant DB as 数据库
    
    U->>FC: 打开编辑表单
    FC->>SM: 请求表单数据
    SM->>API: 获取详情接口
    API->>DB: 查询表单数据
    
    DB->>API: 返回form_data
    API->>SM: 返回数据
    SM->>FC: 填充表单
    FC->>U: 显示表单数据
    
    U->>FC: 修改表单
    FC->>SM: 更新本地状态
    
    U->>FC: 保存/提交
    FC->>SM: 触发保存事件
    SM->>API: 调用更新接口
    API->>DB: 更新表单数据
    
    DB->>API: 返回结果
    API->>SM: 更新状态
    SM->>FC: 更新UI
    FC->>U: 显示操作结果
```

## 👁️ 详情查看数据流

### 工作流详情查看

```mermaid
sequenceDiagram
    participant U as 用户
    participant DV as 详情组件
    participant CM as 组件映射
    parameter DC as 详情组件
    participant API as API层
    participant DB as 数据库
    
    U->>DV: 查看详情
    DV->>API: 获取工作流详情
    API->>DB: 查询实例数据
    
    DB->>API: 返回详情数据
    Note over API: 包含business_code和form_data
    
    API->>DV: 返回详情
    DV->>CM: 映射详情组件
    CM->>DC: 加载对应组件
    
    DC->>DC: 渲染详情数据
    DC->>U: 显示详情界面
```

### 业务详情查看

```mermaid
sequenceDiagram
    participant U as 用户
    participant BL as 业务列表
    participant DC as 详情组件
    participant API as API层
    participant DB as 数据库
    
    U->>BL: 点击查看详情
    BL->>DC: 打开详情组件
    DC->>API: 获取业务详情
    API->>DB: 查询业务数据
    
    DB->>API: 返回业务数据
    API->>DC: 返回详情
    DC->>DC: 渲染详情数据
    DC->>U: 显示详情界面
```

## 🔄 状态同步机制

### 工作流状态同步

```mermaid
graph LR
    subgraph "工作流引擎"
        A1[审批节点] --> A2[状态变更]
        A2 --> A3[触发回调]
    end
    
    subgraph "状态同步服务"
        B1[接收回调] --> B2[解析状态]
        B2 --> B3[更新业务表]
        B3 --> B4[发送通知]
    end
    
    subgraph "前端状态"
        C1[监听通知] --> C2[更新本地状态]
        C2 --> C3[刷新界面]
    end
    
    A3 --> B1
    B4 --> C1
```

### 状态映射表

| 工作流状态 | 状态码 | 业务表状态 | 前端显示 | 操作权限 |
|-----------|--------|-----------|----------|----------|
| 草稿 | 0 | draft | 草稿 | 可编辑、可删除 |
| 审批中 | 1 | pending | 审批中 | 只读、可撤回 |
| 已通过 | 2 | approved | 已通过 | 只读 |
| 已驳回 | 3 | rejected | 已驳回 | 可重新提交 |
| 已终止 | 4 | terminated | 已终止 | 只读 |
| 已撤回 | 5 | withdrawn | 已撤回 | 可重新提交 |

## 💾 状态管理实现

### Pinia状态管理

```typescript
// stores/form.ts
import { defineStore } from 'pinia'

interface FormState {
  // 当前表单数据
  currentFormData: Record<string, any>
  // 表单状态
  formStatus: 'idle' | 'loading' | 'submitting' | 'success' | 'error'
  // 错误信息
  errorMessage: string
  // 表单配置缓存
  configCache: Map<string, any>
  // 组件缓存
  componentCache: Map<string, any>
}

export const useFormStore = defineStore('form', {
  state: (): FormState => ({
    currentFormData: {},
    formStatus: 'idle',
    errorMessage: '',
    configCache: new Map(),
    componentCache: new Map()
  }),

  getters: {
    isLoading: (state) => state.formStatus === 'loading',
    isSubmitting: (state) => state.formStatus === 'submitting',
    hasError: (state) => state.formStatus === 'error',
    isSuccess: (state) => state.formStatus === 'success'
  },

  actions: {
    // 设置表单数据
    setFormData(data: Record<string, any>) {
      this.currentFormData = { ...data }
    },

    // 更新表单字段
    updateField(field: string, value: any) {
      this.currentFormData[field] = value
    },

    // 设置表单状态
    setFormStatus(status: FormState['formStatus']) {
      this.formStatus = status
      if (status !== 'error') {
        this.errorMessage = ''
      }
    },

    // 设置错误信息
    setError(message: string) {
      this.formStatus = 'error'
      this.errorMessage = message
    },

    // 清除表单数据
    clearFormData() {
      this.currentFormData = {}
      this.formStatus = 'idle'
      this.errorMessage = ''
    },

    // 缓存表单配置
    cacheConfig(key: string, config: any) {
      this.configCache.set(key, {
        config,
        timestamp: Date.now()
      })
    },

    // 获取缓存配置
    getCachedConfig(key: string): any | null {
      const cached = this.configCache.get(key)
      const ttl = 5 * 60 * 1000 // 5分钟
      
      if (cached && Date.now() - cached.timestamp < ttl) {
        return cached.config
      }
      
      this.configCache.delete(key)
      return null
    },

    // 提交表单
    async submitForm(businessCode: string, formData: any) {
      this.setFormStatus('submitting')
      
      try {
        const response = await ApplicationApi.submit({
          business_code: businessCode,
          business_data: formData
        })
        
        this.setFormStatus('success')
        return response
      } catch (error) {
        this.setError(error.message || '提交失败')
        throw error
      }
    },

    // 保存草稿
    async saveDraft(businessCode: string, formData: any) {
      this.setFormStatus('loading')
      
      try {
        const response = await ApplicationApi.save({
          business_code: businessCode,
          business_data: formData
        })
        
        this.setFormStatus('success')
        return response
      } catch (error) {
        this.setError(error.message || '保存失败')
        throw error
      }
    }
  },

  // 持久化配置
  persist: {
    key: 'form-store',
    storage: sessionStorage,
    paths: ['currentFormData'] // 只持久化表单数据
  }
})
```

### 详情状态管理

```typescript
// stores/detail.ts
import { defineStore } from 'pinia'

interface DetailState {
  // 当前详情数据
  currentDetailData: Record<string, any>
  // 加载状态
  loading: boolean
  // 错误信息
  error: string | null
  // 详情缓存
  detailCache: Map<string, any>
}

export const useDetailStore = defineStore('detail', {
  state: (): DetailState => ({
    currentDetailData: {},
    loading: false,
    error: null,
    detailCache: new Map()
  }),

  actions: {
    // 加载详情数据
    async loadDetail(businessCode: string, id: number) {
      const cacheKey = `${businessCode}_${id}`
      
      // 检查缓存
      const cached = this.getCachedDetail(cacheKey)
      if (cached) {
        this.currentDetailData = cached
        return cached
      }
      
      this.loading = true
      this.error = null
      
      try {
        let response
        
        // 根据业务代码选择API
        if (businessCode.startsWith('fc_') || businessCode.startsWith('custom_form_')) {
          response = await ApplicationApi.detail(id)
        } else {
          // 业务详情API
          response = await this.getBusinessDetailApi(businessCode).detail(id)
        }
        
        this.currentDetailData = response.data
        this.cacheDetail(cacheKey, response.data)
        
        return response.data
      } catch (error) {
        this.error = error.message || '加载详情失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 缓存详情数据
    cacheDetail(key: string, data: any) {
      this.detailCache.set(key, {
        data,
        timestamp: Date.now()
      })
    },

    // 获取缓存详情
    getCachedDetail(key: string): any | null {
      const cached = this.detailCache.get(key)
      const ttl = 2 * 60 * 1000 // 2分钟缓存
      
      if (cached && Date.now() - cached.timestamp < ttl) {
        return cached.data
      }
      
      this.detailCache.delete(key)
      return null
    },

    // 清除缓存
    clearCache() {
      this.detailCache.clear()
    },

    // 获取业务详情API
    getBusinessDetailApi(businessCode: string) {
      const apiMap = {
        'crm_contract': () => import('@/api/crm/contract'),
        'crm_contract_receivable': () => import('@/api/crm/receivable'),
        'daily_price_order': () => import('@/api/daily/priceOrder'),
        'hr_leave': () => import('@/api/hr/leave'),
        'hr_travel': () => import('@/api/hr/travel')
      }
      
      return apiMap[businessCode] || ApplicationApi
    }
  }
})
```

## 🔄 数据同步策略

### 实时同步

```typescript
// composables/useDataSync.ts
import { useWebSocket } from '@vueuse/core'

export function useDataSync() {
  const formStore = useFormStore()
  const detailStore = useDetailStore()
  
  // WebSocket连接
  const { status, data, send } = useWebSocket('ws://localhost:8080/ws', {
    autoReconnect: true,
    heartbeat: {
      message: 'ping',
      interval: 30000
    }
  })
  
  // 监听数据变化
  watch(data, (newData) => {
    if (!newData) return
    
    try {
      const message = JSON.parse(newData)
      handleSyncMessage(message)
    } catch (error) {
      console.error('解析同步消息失败:', error)
    }
  })
  
  // 处理同步消息
  function handleSyncMessage(message: any) {
    switch (message.type) {
      case 'workflow_status_change':
        handleWorkflowStatusChange(message.data)
        break
      case 'form_data_update':
        handleFormDataUpdate(message.data)
        break
      case 'config_update':
        handleConfigUpdate(message.data)
        break
    }
  }
  
  // 处理工作流状态变化
  function handleWorkflowStatusChange(data: any) {
    const { instanceId, status, businessCode } = data
    
    // 更新详情缓存
    detailStore.clearCache()
    
    // 发送状态变化通知
    ElNotification({
      title: '状态更新',
      message: `您的${businessCode}申请状态已更新为：${getStatusText(status)}`,
      type: getNotificationType(status)
    })
  }
  
  // 处理表单数据更新
  function handleFormDataUpdate(data: any) {
    const { businessCode, formId } = data
    
    // 清除相关缓存
    formStore.configCache.delete(`${businessCode}_${formId}`)
    detailStore.clearCache()
  }
  
  // 处理配置更新
  function handleConfigUpdate(data: any) {
    const { businessCode } = data
    
    // 清除配置缓存
    formStore.configCache.clear()
    
    ElNotification({
      title: '配置更新',
      message: `${businessCode}表单配置已更新，请刷新页面`,
      type: 'info'
    })
  }
  
  return {
    status,
    send
  }
}
```

### 轮询同步

```typescript
// composables/usePollingSync.ts
export function usePollingSync(businessCode: string, instanceId: number) {
  const detailStore = useDetailStore()
  const interval = ref<NodeJS.Timeout>()
  const isPolling = ref(false)
  
  // 开始轮询
  function startPolling(intervalMs: number = 30000) {
    if (isPolling.value) return
    
    isPolling.value = true
    interval.value = setInterval(async () => {
      try {
        await detailStore.loadDetail(businessCode, instanceId)
      } catch (error) {
        console.error('轮询同步失败:', error)
      }
    }, intervalMs)
  }
  
  // 停止轮询
  function stopPolling() {
    if (interval.value) {
      clearInterval(interval.value)
      interval.value = undefined
    }
    isPolling.value = false
  }
  
  // 组件卸载时停止轮询
  onUnmounted(() => {
    stopPolling()
  })
  
  return {
    startPolling,
    stopPolling,
    isPolling: readonly(isPolling)
  }
}
```

## 🚨 错误处理与恢复

### 网络错误处理

```typescript
// utils/errorHandler.ts
class DataFlowErrorHandler {
  // 处理API错误
  handleApiError(error: any, context: string) {
    console.error(`${context} API错误:`, error)
    
    // 根据错误类型处理
    if (error.code === 'NETWORK_ERROR') {
      return this.handleNetworkError(error, context)
    } else if (error.code === 'TIMEOUT') {
      return this.handleTimeoutError(error, context)
    } else if (error.code === 'VALIDATION_ERROR') {
      return this.handleValidationError(error, context)
    } else {
      return this.handleGenericError(error, context)
    }
  }
  
  // 网络错误处理
  private handleNetworkError(error: any, context: string) {
    ElMessage.error('网络连接失败，请检查网络设置')
    
    // 提供重试机制
    return {
      canRetry: true,
      retryAction: () => this.retryLastAction(context)
    }
  }
  
  // 超时错误处理
  private handleTimeoutError(error: any, context: string) {
    ElMessage.error('请求超时，请稍后重试')
    
    return {
      canRetry: true,
      retryAction: () => this.retryLastAction(context)
    }
  }
  
  // 验证错误处理
  private handleValidationError(error: any, context: string) {
    const message = error.message || '数据验证失败'
    ElMessage.error(message)
    
    return {
      canRetry: false,
      validationErrors: error.errors || []
    }
  }
  
  // 通用错误处理
  private handleGenericError(error: any, context: string) {
    const message = error.message || '操作失败，请稍后重试'
    ElMessage.error(message)
    
    return {
      canRetry: true,
      retryAction: () => this.retryLastAction(context)
    }
  }
  
  // 重试最后一次操作
  private retryLastAction(context: string) {
    // 实现重试逻辑
    console.log(`重试操作: ${context}`)
  }
}
```

### 数据恢复机制

```typescript
// utils/dataRecovery.ts
class DataRecoveryManager {
  private localStorage = window.localStorage
  private sessionStorage = window.sessionStorage
  
  // 保存表单数据快照
  saveFormSnapshot(businessCode: string, formData: any) {
    const key = `form_snapshot_${businessCode}_${Date.now()}`
    const snapshot = {
      businessCode,
      formData,
      timestamp: Date.now()
    }
    
    this.localStorage.setItem(key, JSON.stringify(snapshot))
    
    // 清理过期快照
    this.cleanExpiredSnapshots()
  }
  
  // 恢复表单数据
  recoverFormData(businessCode: string): any | null {
    const keys = Object.keys(this.localStorage)
    const snapshots = keys
      .filter(key => key.startsWith(`form_snapshot_${businessCode}_`))
      .map(key => {
        try {
          return JSON.parse(this.localStorage.getItem(key) || '{}')
        } catch {
          return null
        }
      })
      .filter(Boolean)
      .sort((a, b) => b.timestamp - a.timestamp)
    
    return snapshots[0]?.formData || null
  }
  
  // 清理过期快照
  private cleanExpiredSnapshots() {
    const keys = Object.keys(this.localStorage)
    const expireTime = 24 * 60 * 60 * 1000 // 24小时
    const now = Date.now()
    
    keys.forEach(key => {
      if (key.startsWith('form_snapshot_')) {
        try {
          const snapshot = JSON.parse(this.localStorage.getItem(key) || '{}')
          if (now - snapshot.timestamp > expireTime) {
            this.localStorage.removeItem(key)
          }
        } catch {
          this.localStorage.removeItem(key)
        }
      }
    })
  }
  
  // 检查是否有可恢复的数据
  hasRecoverableData(businessCode: string): boolean {
    return this.recoverFormData(businessCode) !== null
  }
}
```

## 📊 性能监控

### 数据流性能指标

```typescript
// utils/performanceMonitor.ts
class DataFlowPerformanceMonitor {
  private metrics: Map<string, any> = new Map()
  
  // 开始监控
  startMonitoring(operation: string, context: any = {}) {
    const key = `${operation}_${Date.now()}`
    this.metrics.set(key, {
      operation,
      context,
      startTime: performance.now(),
      startMemory: this.getMemoryUsage()
    })
    return key
  }
  
  // 结束监控
  endMonitoring(key: string, success: boolean = true, additionalData: any = {}) {
    const metric = this.metrics.get(key)
    if (!metric) return
    
    const endTime = performance.now()
    const endMemory = this.getMemoryUsage()
    
    const result = {
      ...metric,
      endTime,
      duration: endTime - metric.startTime,
      endMemory,
      memoryDelta: endMemory - metric.startMemory,
      success,
      ...additionalData
    }
    
    this.recordMetric(result)
    this.metrics.delete(key)
  }
  
  // 获取内存使用情况
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize
    }
    return 0
  }
  
  // 记录性能指标
  private recordMetric(metric: any) {
    // 发送到监控系统
    if (window.performanceReporter) {
      window.performanceReporter.report('data_flow', metric)
    }
    
    // 开发环境日志
    if (process.env.NODE_ENV === 'development') {
      console.log('数据流性能指标:', metric)
    }
  }
}
```

---

**注意：** 数据流设计是系统稳定性的关键，确保所有数据操作都有适当的错误处理和恢复机制。