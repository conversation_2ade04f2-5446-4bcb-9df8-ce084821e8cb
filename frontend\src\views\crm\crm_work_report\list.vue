<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowDown } from '@element-plus/icons-vue'
  // import { useCheckedColumns } from '@/composables/useCheckedColumns'
  // import { BgColorEnum } from '@/enums/appEnum'
  import { CrmWorkReportApi } from '@/api/crm/crmWorkReport'
  import { ApiStatus } from '@/utils/http/status'

  // import { LongTextColumn } from '@/components/core/tables/columns'

  import FormDialog from './form-dialog.vue'

  import ImportExportDialog from './import-export-dialog.vue'
  import { isEmpty } from '@/utils/utils'

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 定义表单搜索初始值
  const initialSearchState = {
    // title: '',
    type: '',
    report_date: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    /*{
      prop: 'title',
      label: '报告标题',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入报告标题关键词'
      },
      onChange: handleFormChange
    },*/
    {
      prop: 'type',
      label: '报告类型',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择报告类型'
      },
      options: [
        { label: '📋 日报', value: 'daily' },
        { label: '📊 周报', value: 'weekly' },
        { label: '📈 月报', value: 'monthly' }
      ],
      onChange: handleFormChange
    },
    {
      prop: 'report_date',
      label: '报告日期',
      type: 'date',
      config: {
        clearable: true,
        placeholder: '请选择报告日期',
        type: 'date'
      },
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmWorkReportApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await CrmWorkReportApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      } else {
        ElMessage.error(res.message || '获取详情失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 这个函数已经在下面的条件块中定义了

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmWorkReportApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        getTableData()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      // 用户取消删除
    } finally {
      loading.value = false
    }
  }

  // 导入导出对话框引用
  const importExportDialogRef = ref()

  // 显示导入对话框
  const showImportDialog = () => {
    importExportDialogRef.value?.showDialog('import')
  }

  // 导入导出成功回调
  const handleImportExportSuccess = () => {
    getTableData()
  }

  // 显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }

  // 表单对话框引用
  const formDialogRef = ref()

  // 显示表单对话框
  const showFormDialog = (type: string, id?: number) => {
    formDialogRef.value?.showDialog(type, id)
  }

  // 表单提交成功回调
  const handleFormSubmitSuccess = () => {
    getTableData()
  }

  // 处理下拉菜单命令
  const handleCommand = (command: { action: string; id: number }) => {
    switch (command.action) {
      case 'edit':
        showFormDialog('edit', command.id)
        break
      case 'copy':
        // handleCopy(command.id) // 复制功能已注释
        break
      case 'delete':
        handleDelete(command.id)
        break
    }
  }

  // 复制汇报
  /*const handleCopy = async (id: number) => {
    try {
      const res = await CrmWorkReportApi.copy(id)
      if (res.code === ApiStatus.success) {
        ElMessage.success('复制成功')
        getTableData()
      } else {
        ElMessage.error(res.message || '复制失败')
      }
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }*/

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      daily: 'primary',
      weekly: 'success',
      monthly: 'warning'
    }
    return colors[type] || 'info'
  }

  // 获取类型文本
  const getTypeText = (type: string) => {
    const texts: Record<string, string> = {
      daily: '日报',
      weekly: '周报',
      monthly: '月报'
    }
    return texts[type] || type
  }

  // 获取附件数量
  const getAttachmentCount = (attachments: string) => {
    if (isEmpty(attachments)) return 0
    try {
      // 逗号分隔
      const list = attachments.split(',')
      return Array.isArray(list) ? list.length : 0
    } catch {
      return 0
    }
  }

  // 格式化时间
  const formatTime = (time: string) => {
    if (!time) return ''
    return new Date(time).toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-crmWorkReport-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <ElButton type="primary" @click="showFormDialog('add')">📝 写汇报</ElButton>

            <!--            <ElButton type="success" @click="showImportDialog">导入</ElButton>-->

            <!--            <ElButton type="warning" @click="showExportDialog">导出</ElButton>-->
          </template>
        </ArtTableHeader>

        <!-- 卡片列表 -->
        <div v-loading="loading" class="report-card-container">
          <div v-if="tableData.length === 0" class="empty-state">
            <el-empty description="暂无数据" />
          </div>
          <div v-else class="report-card-list">
            <div v-for="item in tableData" :key="item.id" class="report-card">
              <div class="card-header">
                <span class="report-date">📅 {{ item.report_date }}</span>
                <el-tag :type="getTypeColor(item.type)">{{ getTypeText(item.type) }}</el-tag>
                <span class="creator">👤 {{ item.creator_name || '未知' }}</span>
              </div>
              <div class="card-title">📝 {{ item.title }}</div>
              <div class="card-content"
                >💬 {{ item.content ? item.content.substring(0, 50) + '...' : '暂无内容' }}
              </div>
              <div class="card-footer">
                <span class="attachment-count"
                  >📎 {{ getAttachmentCount(item.attachments) }}个附件</span
                >
                <span class="create-time">⏰ {{ formatTime(item.created_at) }}</span>
                <div class="actions">
                  <el-button type="primary" link @click="showDetail(item.id)"> 详情</el-button>
                  <el-dropdown trigger="hover" @command="handleCommand">
                    <el-button type="primary" link>
                      更多
                      <el-icon>
                        <ArrowDown />
                      </el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{ action: 'edit', id: item.id }">
                          ✏️ 编辑
                        </el-dropdown-item>
                        <!--                        <el-dropdown-item :command="{action: 'copy', id: item.id}">📋 复制</el-dropdown-item>-->
                        <el-dropdown-item :command="{ action: 'delete', id: item.id }" divided>
                          🗑️ 删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>

        <!-- 详情抽屉 -->
        <el-drawer v-model="detailDialogVisible" title="📄 汇报详情" size="60%" destroy-on-close>
          <div class="detail-content">
            <!-- 头部信息 -->
            <div class="detail-header">
              <div class="report-info">
                <h2>{{ detailData.title || '未知标题' }}</h2>
                <div class="meta-info">
                  <el-tag :type="getTypeColor(detailData.type)"
                    >{{ getTypeText(detailData.type) }}
                  </el-tag>
                  <span>📅 {{ detailData.report_date }}</span>
                  <span>👤 {{ detailData.creator_name || '未知' }}</span>
                  <span>⏰ {{ formatTime(detailData.created_at) }}</span>
                </div>
              </div>
              <div class="actions">
                <el-button type="primary" plain @click="showFormDialog('edit', detailData.id)"
                  >✏️ 编辑
                </el-button>
                <!--                <el-button @click="handleCopy(detailData.id)">📋 复制</el-button>-->
                <el-button type="danger" plain @click="handleDelete(detailData.id)"
                  >🗑️ 删除
                </el-button>
              </div>
            </div>

            <!-- 内容区域 -->
            <div class="detail-body">
              <div class="content-section" v-if="detailData.content">
                <h3>📝 工作内容</h3>
                <div class="content-text">{{ detailData.content }}</div>
              </div>

              <div class="content-section" v-if="detailData.summary">
                <h3>📊 工作总结</h3>
                <div class="content-text">{{ detailData.summary }}</div>
              </div>

              <div class="content-section" v-if="detailData.plan">
                <h3>📅 下期计划</h3>
                <div class="content-text">{{ detailData.plan }}</div>
              </div>

              <div class="content-section" v-if="detailData.attachments">
                <h3>📎 附件列表</h3>
                <div class="attachment-list">
                  <div class="attachment-item">
                    <span>📄 {{ detailData.attachments }}</span>
                    <el-button type="primary" link size="small">📥 下载</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-drawer>

        <!-- 表单组件 -->
        <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" />

        <!-- 导入导出对话框 -->
        <ImportExportDialog ref="importExportDialogRef" @success="handleImportExportSuccess" />
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-crmWorkReport-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }
  }

  // 卡片列表样式
  .report-card-container {
    margin-top: 16px;
  }

  .report-card-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .report-card {
    background: white;
    border-radius: 8px;
    padding: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e4e7ed;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: #646a73;
  }

  .card-title {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #1f2329;
    line-height: 1.4;
  }

  .card-content {
    color: #646a73;
    margin-bottom: 10px;
    line-height: 1.5;
    font-size: 13px;
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #8f959e;
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  // 详情页面样式
  .detail-content {
    padding: 20px;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .report-info h2 {
    margin: 0 0 8px 0;
    color: #1f2329;
    font-size: 20px;
    font-weight: 600;
  }

  .meta-info {
    display: flex;
    gap: 12px;
    align-items: center;
    color: #646a73;
    font-size: 14px;
  }

  .content-section {
    margin-bottom: 24px;
  }

  .content-section h3 {
    margin: 0 0 12px 0;
    color: #1f2329;
    font-size: 16px;
    font-weight: 600;
  }

  .content-text {
    background: #f7f8fa;
    padding: 16px;
    border-radius: 8px;
    line-height: 1.6;
    color: #1f2329;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .attachment-list {
    background: #f7f8fa;
    padding: 16px;
    border-radius: 8px;
  }

  .attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e4e7ed;

    &:last-child {
      border-bottom: none;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .report-card-list {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .report-card {
      padding: 12px;
    }

    .card-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .actions {
      flex-wrap: wrap;
      gap: 4px;
    }

    .detail-header {
      flex-direction: column;
      gap: 16px;
    }

    .meta-info {
      flex-wrap: wrap;
    }
  }
</style>
