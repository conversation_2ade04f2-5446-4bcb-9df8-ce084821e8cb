<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="70%"
    top="5vh"
    destroy-on-close
    class="price-order-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <!-- 基础信息表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        :disabled="!canEdit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报价日期" prop="price_date">
              <el-date-picker
                v-model="formData.price_date"
                type="date"
                placeholder="请选择报价日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 明细表格 -->
      <el-card class="items-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">产品明细</span>
          </div>
        </template>

        <PriceItemTable
          v-model="priceItems"
          :readonly="!canEdit"
          :loading="itemsLoading"
          :order-id="formData.id"
          @change="onItemsChange"
          @validation-change="onValidationChange"
        />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          v-if="canEdit"
          type="primary"
          :loading="saving"
          :disabled="!canSave"
          @click="() => handleSave()"
        >
          保存
        </el-button>
        <el-button
          v-if="canSubmitApproval"
          type="success"
          :loading="submitting"
          @click="handleSubmitApproval"
        >
          提交审批
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
  import { DailyPriceOrderApi } from '@/api/daily/dailyPriceOrder'
  import type { DailyPriceOrder, DailyPriceItem } from '@/api/daily/dailyPriceOrder'
  import { ApiStatus } from '@/utils/http/status'
  import PriceItemTable from './components/PriceItemTable.vue'

  const emit = defineEmits(['success'])

  // 对话框状态
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add或edit
  const loading = ref(false)
  const saving = ref(false)
  const submitting = ref(false)
  const itemsLoading = ref(false)

  // 表单引用
  const formRef = ref<FormInstance>()

  // 表单数据
  const formData = reactive<DailyPriceOrder>({
    title: '',
    price_date: '',
    total_items: 0,
    remark: '',
    status: 1,
    approval_status: 0
  })

  // 明细数据
  const priceItems = ref<DailyPriceItem[]>([])

  // 验证状态
  const validationState = reactive({
    hasDuplicates: false,
    duplicateCount: 0
  })

  // 表单验证规则
  const rules = {
    price_date: [{ required: true, message: '请选择报价日期', trigger: 'change' }]
  }

  // 验证明细数据
  const validateItems = () => {
    if (!priceItems.value || priceItems.value.length === 0) {
      ElMessage.error('报价明细不能为空，请至少添加一条明细')
      return false
    }
    return true
  }

  // 计算属性
  const dialogTitle = computed(() => {
    return dialogType.value === 'add' ? '新增每日报价单' : '编辑每日报价单'
  })

  const canEdit = computed(() => {
    if (dialogType.value === 'add') return true
    return [0, 3, 5].includes(formData.approval_status || 0) // 草稿、已拒绝、已撤回
  })

  const canSubmitApproval = computed(() => {
    return (
      formData.approval_status === 0 &&
      priceItems.value.length > 0 &&
      !validationState.hasDuplicates
    )
  })

  const canSave = computed(() => {
    return !validationState.hasDuplicates
  })

  // 方法
  const showDialog = async (type: string, id?: number) => {
    dialogType.value = type
    loading.value = true

    try {
      if (type === 'edit' && id) {
        // 加载报价单详情（包含明细数据）
        const res = await DailyPriceOrderApi.detail(id)
        if (res.code === ApiStatus.success) {
          // 设置基础信息
          Object.assign(formData, res.data)

          // 设置明细数据（从详情数据中直接获取，避免重复请求）
          priceItems.value = res.data.items || []
        } else {
          ElMessage.error(res.message || '获取详情失败')
          return
        }
      } else {
        // 新增时重置表单，默认日期为今日
        const today = new Date()
        const year = today.getFullYear()
        const month = String(today.getMonth() + 1).padStart(2, '0')
        const day = String(today.getDate()).padStart(2, '0')
        const todayStr = `${year}-${month}-${day}`

        Object.assign(formData, {
          title: `${todayStr}报价`,
          price_date: todayStr,
          total_items: 0,
          remark: '',
          status: 1,
          approval_status: 0
        })
        priceItems.value = []
      }

      dialogVisible.value = true
    } finally {
      loading.value = false
    }
  }

  /*const onDateChange = async (date: string) => {
    if (date) {
      formData.title = `${date}报价`

      // 新增时检查当天是否已有报价单
      if (dialogType.value === 'add') {
        await checkDuplicateDate(date)
      }
    }
  }*/

  // 检查当天是否已有报价单
  /*const checkDuplicateDate = async (date: string) => {
    try {
      const res = await DailyPriceOrderApi.checkDuplicateDate(date)
      if (res.code === ApiStatus.success && res.data.exists) {
        ElMessage.warning('当天已存在报价单，不能重复创建')
        // 清空日期选择
        formData.price_date = ''
      }
    } catch (error) {
      console.error('检查重复日期失败:', error)
    }
  }*/

  // 处理验证变化
  const onValidationChange = (validation: { hasDuplicates: boolean; duplicateCount: number }) => {
    validationState.hasDuplicates = validation.hasDuplicates
    validationState.duplicateCount = validation.duplicateCount

    if (validation.hasDuplicates) {
      ElMessage.warning(`检测到 ${validation.duplicateCount} 组重复的供应商-产品组合`)
    }
  }

  const onItemsChange = (items: DailyPriceItem[]) => {
    formData.total_items = items.length
  }

  const handleSave = async (showMessage = true) => {
    if (!formRef.value) return

    try {
      // 验证表单
      await formRef.value.validate()

      // 验证明细数据
      if (!validateItems()) {
        return
      }

      saving.value = true

      // 准备包含明细的数据
      const saveData = {
        ...formData,
        items: priceItems.value
      }

      // 保存基础信息和明细
      let res
      if (dialogType.value === 'add') {
        res = await DailyPriceOrderApi.add(saveData)
        // 新增成功后，更新formData的id，用于后续提交审批
        if (res.code === ApiStatus.success && res.data?.id) {
          formData.id = res.data.id
        }
      } else {
        res = await DailyPriceOrderApi.update(saveData)
      }

      if (res.code === ApiStatus.success) {
        if (showMessage) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          dialogVisible.value = false
          emit('success')
        }
        return res
      }
    } catch (error) {
      console.error('保存失败:', error)
    } finally {
      saving.value = false
    }
  }

  const handleSubmitApproval = async () => {
    try {
      await ElMessageBox.confirm('确定要提交审批吗？', '确认提交', {
        type: 'warning'
      })

      submitting.value = true

      // 先保存（不显示保存成功提示）
      await handleSave(false)

      // 再提交审批
      if (formData.id) {
        const res = await DailyPriceOrderApi.submitApproval(formData.id)
        if (res.code === ApiStatus.success) {
          ElMessage.success('提交审批成功')
          dialogVisible.value = false
          emit('success')
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('提交审批失败:', error)
      }
    } finally {
      submitting.value = false
    }
  }

  // 暴露方法给父组件
  defineExpose({
    showDialog
  })
</script>

<style scoped>
  .price-order-dialog {
    .dialog-content {
      max-height: 70vh;
    }

    .status-card {
      margin-bottom: 20px;
    }

    .form-card,
    .items-card {
      margin-bottom: 20px;
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .price-order-dialog {
      width: 95% !important;
    }
  }
</style>
