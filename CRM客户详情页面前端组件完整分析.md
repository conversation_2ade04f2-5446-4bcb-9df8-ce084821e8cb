# CRM客户详情页面前端组件完整分析

## 📋 概述

本文档详细分析CRM客户详情页面权限系统实施中涉及的所有前端组件，包括组件层级结构、依赖关系、功能特性和使用方式。

## 🏗️ 组件架构总览

### 核心架构图
```
CRM客户详情页面组件架构
├── 页面入口组件
│   └── views/crm/crm_customer_my/list.vue
├── 详情抽屉组件
│   ├── CustomerDetailDrawer/index.vue (主组件)
│   └── CrmDetailDrawer/index.vue (基础组件)
├── 面板组件
│   ├── CustomerInfoPanel.vue (客户信息)
│   ├── CustomerContactPanel.vue (联系人)
│   ├── CustomerContractPanel.vue (合同)
│   └── CustomerFollowPanel.vue (跟进记录)
├── 通用组件
│   ├── CrmInfoCard (信息展示卡片)
│   ├── FormMediaSelector (媒体选择器)
│   ├── DepartmentPersonSelector (人员选择器)
│   └── ApiSelect (API选择器)
├── 表格组件
│   ├── ArtTable (核心表格)
│   └── 专用列组件 (16种)
├── 表单组件
│   ├── FormDrawer (表单抽屉)
│   └── FormDialog (表单对话框)
└── 权限组件
    ├── v-auth 指令
    ├── v-permission 指令
    └── useAuth 组合式函数
```

## 🎯 详情抽屉组件体系

### 1. CrmDetailDrawer (通用详情抽屉基础组件)
**文件路径**：`frontend/src/components/custom/CrmDetailDrawer/index.vue`

**功能特性**：
- 抽屉布局容器，支持右侧滑出
- 标签页管理，支持动态组件加载
- 统一的头部信息展示
- 事件传递机制 (action/refresh)
- 自动数据加载机制

**关键属性**：
```typescript
interface Props {
  modelValue: boolean                    // 显示/隐藏控制
  title: string                         // 抽屉标题
  size?: string | number                // 抽屉宽度 (默认60%)
  businessType: 'customer' | 'business' | 'contract' | 'contact' | 'lead'
  businessId: number | string          // 业务对象ID
  tabs: TabConfig[]                     // 标签页配置
  showHeader?: boolean                  // 是否显示头部
  autoLoad?: boolean                    // 是否自动加载数据
}
```

**事件系统**：
```typescript
// 事件定义
emit: {
  'update:modelValue': [value: boolean]
  close: []
  action: [action: string, data: any]   // 统一操作事件
  'tab-change': [tabName: string]
  refresh: []                           // 数据刷新事件
}
```

### 2. CustomerDetailDrawer (客户详情抽屉主组件)
**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/index.vue`

**功能特性**：
- 继承CrmDetailDrawer的所有功能
- 客户专用的头部信息展示
- 客户操作按钮 (转移、共享、回收)
- 标签页配置管理

**标签页配置**：
```typescript
const tabsConfig = ref([
  {
    name: 'info',
    label: '客户信息',
    component: markRaw(CustomerInfoPanel),
    props: {}
  },
  {
    name: 'contacts',
    label: '联系人',
    component: markRaw(CustomerContactPanel),
    props: {}
  },
  {
    name: 'contracts',
    label: '合同',
    component: markRaw(CustomerContractPanel),
    props: {}
  },
  {
    name: 'follows',
    label: '跟进记录',
    component: markRaw(CustomerFollowPanel),
    props: {}
  }
])
```

## 📱 面板组件详解

### 1. CustomerInfoPanel (客户信息面板)
**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerInfoPanel.vue`

**功能特性**：
- 使用CrmInfoCard组件展示信息
- 分组展示：基本信息、联系信息、企业信息、其他信息
- 支持编辑操作

**依赖组件**：
- `CrmInfoCard` - 信息展示卡片组件

### 2. CustomerContactPanel (联系人面板)
**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerContactPanel.vue`

**功能特性**：
- 联系人列表展示
- 联系人CRUD操作
- 分页和搜索功能
- 主要联系人标识

**需要权限控制的操作**：
- 新增联系人 - `crm:crm_customer_my:add_contact`
- 编辑联系人 - `crm:crm_customer_my:edit_contact`
- 删除联系人 - `crm:crm_customer_my:delete_contact`
- 联系人列表 - `crm:crm_customer_my:contact_list`

### 3. CustomerContractPanel (合同面板)
**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerContractPanel.vue`

**功能特性**：
- 合同列表展示 (表格形式)
- 合同CRUD操作
- 回款管理 (更多操作下拉菜单)
- 审批流程集成

**需要权限控制的操作**：
- 新增合同 - `crm:crm_customer_my:add_contract`
- 编辑合同 - `crm:crm_customer_my:edit_contract`
- 删除合同 - `crm:crm_customer_my:delete_contract`
- 合同详情 - `crm:crm_customer_my:contract_detail`
- 合同列表 - `crm:crm_customer_my:contract_list`
- 提交审批 - `crm:crm_customer_my:submit_approval`
- 回款相关操作 (7个权限)

### 4. CustomerFollowPanel (跟进记录面板)
**文件路径**：`frontend/src/components/custom/CustomerDetailDrawer/panels/CustomerFollowPanel.vue`

**功能特性**：
- 跟进记录时间线展示
- 跟进记录CRUD操作
- 附件支持
- 下次跟进提醒

**需要权限控制的操作**：
- 新增跟进 - `crm:crm_customer_my:add_follow`
- 编辑跟进 - `crm:crm_customer_my:edit_follow`
- 删除跟进 - `crm:crm_customer_my:delete_follow`
- 跟进详情 - `crm:crm_customer_my:follow_detail`

## 🧩 通用组件体系

### 1. CrmInfoCard (CRM信息展示卡片)
**文件路径**：`frontend/src/components/custom/CrmInfoCard/index.vue`

**功能特性**：
- 支持描述列表模式和表单模式
- 可配置的信息项展示
- 支持自定义组件渲染
- 编辑按钮集成

**使用方式**：
```vue
<CrmInfoCard
  title="基本信息"
  icon="User"
  mode="descriptions"
  :data="businessData"
  :items="basicInfoItems"
  :column="2"
  :border="true"
  @edit="handleEditBasic"
/>
```

### 2. FormMediaSelector (表单媒体选择器)
**文件路径**：`frontend/src/components/custom/FormMediaSelector/index.vue`

**功能特性**：
- 支持图片、视频、音频、文件四种媒体类型
- 媒体预览功能
- 拖拽排序
- 单选/多选模式
- 与表单组件集成

**使用方式**：
```vue
<FormMediaSelector 
  v-model="form.images" 
  media-type="image" 
  :max-count="5" 
/>
```

### 3. DepartmentPersonSelector (部门人员选择器)
**文件路径**：`frontend/src/components/custom/DepartmentPersonSelector.vue`

**功能特性**：
- 部门树形结构展示
- 人员列表展示
- 单选/多选模式
- 搜索功能
- 已选人员管理

**使用方式**：
```vue
<DepartmentPersonSelector
  v-model="visible"
  :selected-data="selectedPersons"
  :multiple="true"
  @confirm="handleConfirm"
/>
```

### 4. ApiSelect (API选择器)
**文件路径**：`frontend/src/components/core/forms/ApiSelect.vue`

**功能特性**：
- 基于API的下拉选择
- 支持搜索和分页
- 自定义字段映射
- 缓存机制
- 多选支持

**使用方式**：
```vue
<ApiSelect
  v-model="selectedUser"
  :api="{ url: '/api/users' }"
  label-field="name"
  value-field="id"
  placeholder="请选择用户"
/>
```

## 📊 表格组件体系

### 1. ArtTable (核心表格组件)
**功能特性**：
- 数据列表展示
- 搜索表单集成
- 分页功能
- 批量操作
- 列组件系统

### 2. 专用列组件 (16种)
**组件列表**：
- `SwitchColumn` - 开关组件
- `TagColumn` - 标签组件
- `ImageColumn` - 图片组件
- `DocumentColumn` - 文档组件
- `LinkColumn` - 链接组件
- `CurrencyColumn` - 货币组件
- `LongTextColumn` - 长文本组件
- `CopyableColumn` - 可复制组件
- `ProgressColumn` - 进度条组件
- `QrcodeColumn` - 二维码组件
- `MediaColumn` - 媒体组件
- `EditableColumn` - 可编辑组件
- `ColorColumn` - 颜色组件
- `DateColumn` - 日期组件
- `NumberColumn` - 数字组件
- `ActionColumn` - 操作组件

## 🔐 权限组件体系

### 1. 权限指令
**v-auth 指令**：
```vue
<el-button v-auth="'add'">新增</el-button>
```

**v-permission 指令** (需要扩展)：
```vue
<el-button v-permission="'crm:crm_customer_my:add_contact'">新增联系人</el-button>
```

### 2. 权限组合式函数
**useAuth**：
```typescript
const { hasAuth } = useAuth()
const canAdd = hasAuth('add')
```

**useCustomerPermission** (需要新增)：
```typescript
const { hasCustomerAccess, hasPermissionAndAccess } = useCustomerPermission()
const canEdit = hasPermissionAndAccess('crm:crm_customer_my:edit_contact', customerId, 'edit')
```

## 📋 表单组件体系

### 1. FormDrawer (表单抽屉)
**功能特性**：
- 抽屉式表单容器
- 表单验证集成
- 提交处理
- 成功回调

### 2. FormDialog (表单对话框)
**功能特性**：
- 对话框式表单容器
- 模态显示
- 表单验证
- 响应式布局

## 🔄 组件交互流程

### 数据流向
```
页面组件 → CustomerDetailDrawer → CrmDetailDrawer → 面板组件 → 通用组件
    ↓           ↓                    ↓              ↓           ↓
  事件触发 → 事件传递(action) → 统一处理 → API调用 → 数据更新
```

### 权限验证流程
```
用户操作 → 权限指令检查 → 数据权限验证 → API调用 → 后端权限验证
```

## 📦 组件依赖关系

### 核心依赖
- **Element Plus** - UI组件库
- **Vue 3** - 框架核心
- **@vueuse/core** - 组合式函数库

### 内部依赖
- 面板组件 → CrmInfoCard
- 表单组件 → FormMediaSelector, DepartmentPersonSelector
- 列表组件 → ArtTable + 列组件
- 权限控制 → 权限指令 + 组合式函数

## 🎯 实施重点

### 需要新增的组件
1. `useCustomerPermission` 组合式函数
2. `CrmCustomerDetailApi` API接口类
3. 权限指令扩展

### 需要修改的组件
1. 所有面板组件 - 添加权限控制
2. CustomerDetailDrawer - 扩展事件处理
3. 权限指令 - 支持数据权限验证

### 需要集成的功能
1. API调用替换模拟数据
2. 错误处理和用户反馈
3. 加载状态管理
4. 数据刷新机制

## 🔧 API接口组件

### 现有API接口文件
**已存在的API文件**：
1. `frontend/src/api/crm/crmCustomer.ts` - 客户基础CRUD
2. `frontend/src/api/crm/crmContact.ts` - 联系人管理
3. `frontend/src/api/crm/crmContract.ts` - 合同管理
4. `frontend/src/api/crm/crmFollowRecord.ts` - 跟进记录
5. `frontend/src/api/crm/crmCustomerSea.ts` - 公海客户
6. `frontend/src/api/crm/crmCustomerShare.ts` - 客户共享记录

### 需要新增的API文件
**CrmCustomerDetailApi** (需要创建)：
```typescript
// frontend/src/api/crm/crmCustomerDetail.ts
export class CrmCustomerDetailApi {
  // 联系人操作 (4个接口)
  static addContact(customerId: number, data: any)
  static editContact(contactId: number, data: any)
  static deleteContact(contactId: number)
  static getContactList(customerId: number, params?: any)

  // 合同操作 (6个接口)
  static addContract(customerId: number, data: any)
  static editContract(contractId: number, data: any)
  static deleteContract(contractId: number)
  static getContractDetail(contractId: number)
  static getContractList(customerId: number, params?: any)
  static submitApproval(contractId: number, data: any)

  // 回款操作 (7个接口)
  static addReceivable(contractId: number, data: any)
  static editReceivable(receivableId: number, data: any)
  static deleteReceivable(receivableId: number)
  static getReceivableDetail(receivableId: number)
  static getReceivableList(contractId: number, params?: any)
  static submitReceivableApproval(receivableId: number, data: any)
  static addReceivableMore(contractId: number, data: any)

  // 跟进记录 (4个接口)
  static addFollow(customerId: number, data: any)
  static editFollow(followId: number, data: any)
  static deleteFollow(followId: number)
  static getFollowDetail(followId: number)

  // 客户操作 (1个接口，2个预留)
  static recycleCustomer(customerId: number)
  // static transferCustomer(customerId: number, data: any) // 预留
  // static shareCustomer(customerId: number, data: any)    // 预留
}
```

## 🎨 UI组件库依赖

### Element Plus 组件使用清单
**布局组件**：
- `el-drawer` - 抽屉容器
- `el-card` - 卡片容器
- `el-row`, `el-col` - 栅格布局
- `el-tabs`, `el-tab-pane` - 标签页

**数据展示组件**：
- `el-table` - 表格 (通过ArtTable封装)
- `el-descriptions` - 描述列表
- `el-tag` - 标签
- `el-avatar` - 头像
- `el-image` - 图片
- `el-pagination` - 分页

**表单组件**：
- `el-form`, `el-form-item` - 表单
- `el-input` - 输入框
- `el-select` - 选择器
- `el-date-picker` - 日期选择器
- `el-upload` - 文件上传
- `el-switch` - 开关

**操作组件**：
- `el-button` - 按钮
- `el-dropdown` - 下拉菜单
- `el-dialog` - 对话框
- `el-message-box` - 消息确认框

**反馈组件**：
- `el-message` - 消息提示
- `el-loading` - 加载状态
- `el-empty` - 空状态

### 图标组件
**Element Plus Icons**：
- `Phone`, `Message`, `Link` - 联系方式图标
- `Plus`, `Edit`, `Delete` - 操作图标
- `User`, `OfficeBuilding`, `More` - 业务图标
- `Close` - 关闭图标

## 🔄 组件生命周期管理

### 数据加载时机
```typescript
// 组件挂载时
onMounted(() => {
  if (props.autoLoad) {
    loadData()
  }
})

// 监听业务ID变化
watch(() => props.businessId, (newId) => {
  if (newId) {
    loadData()
  }
})

// 监听刷新事件
const handleRefresh = () => {
  loadData()
}
```

### 权限验证时机
```typescript
// 组件渲染时
const canShowButton = computed(() => {
  return hasPermissionAndAccess(permission, customerId, operation)
})

// 操作执行前
const handleOperation = async () => {
  if (!hasPermissionAndAccess(permission, customerId, operation)) {
    ElMessage.error('无权限执行此操作')
    return
  }
  // 执行操作
}
```

## 📱 响应式设计

### 抽屉尺寸适配
```typescript
// 不同屏幕尺寸的抽屉宽度
const drawerSize = computed(() => {
  const width = window.innerWidth
  if (width < 768) return '100%'      // 移动端全屏
  if (width < 1200) return '80%'      // 平板端
  return '60%'                        // 桌面端
})
```

### 表格响应式
```typescript
// 表格列配置
const columns = computed(() => {
  const baseColumns = [...]

  // 移动端隐藏部分列
  if (isMobile.value) {
    return baseColumns.filter(col => col.essential)
  }

  return baseColumns
})
```

## 🧪 组件测试策略

### 单元测试覆盖
1. **权限验证逻辑测试**
2. **API调用测试**
3. **事件处理测试**
4. **数据格式化测试**

### 集成测试覆盖
1. **组件间通信测试**
2. **权限流程测试**
3. **数据流测试**
4. **用户交互测试**

## 📊 性能优化策略

### 组件懒加载
```typescript
// 动态导入面板组件
const CustomerContactPanel = defineAsyncComponent(() =>
  import('./panels/CustomerContactPanel.vue')
)
```

### 数据缓存
```typescript
// API数据缓存
const cache = new Map()
const getCachedData = (key: string) => {
  if (cache.has(key)) {
    return cache.get(key)
  }
  // 获取数据并缓存
}
```

### 虚拟滚动
```typescript
// 大数据量列表使用虚拟滚动
<el-virtual-list
  :data="largeDataList"
  :height="400"
  :item-size="50"
/>
```

---

**文档版本**：v1.0
**创建时间**：2025-01-14
**维护人员**：前端开发团队
